<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/Guru.php';

// Get teacher ID from logged in user
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Check if ID is provided
if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Initialize models
$kd = new KompetensiDasar();

// Get KD data
$kd_data = $kd->getById($_GET['id']);
if (!$kd_data || $kd_data['guru_id'] != $guru_id) {
    header("Location: index.php?error=1");
    exit();
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Detail Kompetensi Dasar</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="/absen/">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="index.php">Kompetensi Dasar</a></li>
                    <li class="breadcrumb-item active">Detail</li>
                </ol>
            </nav>
        </div>
        <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book-open"></i> 
                        Detail Kompetensi Dasar: <?= htmlspecialchars($kd_data['kode_kd']) ?>
                    </h5>
                    <div class="btn-group">
                        <a href="edit.php?id=<?= $kd_data['id'] ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="javascript:void(0)" onclick="confirmDelete(<?= $kd_data['id'] ?>)" 
                           class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> Hapus
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Main Information -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle"></i> Informasi Umum
                                </h6>
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>Kode KD:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge bg-primary fs-6"><?= htmlspecialchars($kd_data['kode_kd']) ?></span>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>Mata Pelajaran:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <?= htmlspecialchars($kd_data['nama_mapel']) ?>
                                        <small class="text-muted">(<?= htmlspecialchars($kd_data['kode_mapel']) ?>)</small>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>Kelas:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php if (!empty($kd_data['nama_kelas'])): ?>
                                            <span class="badge bg-info fs-6"><?= htmlspecialchars($kd_data['nama_kelas']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Semua Kelas</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>Semester:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge bg-<?= $kd_data['semester'] == '1' ? 'primary' : 'success' ?>">
                                            Semester <?= $kd_data['semester'] ?>
                                        </span>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>Tahun Ajaran:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <?= htmlspecialchars($kd_data['tahun_ajaran']) ?>
                                    </div>
                                </div>
                            </div>

                            <!-- KD Description -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-file-alt"></i> Deskripsi Kompetensi Dasar
                                </h6>
                                <div class="p-3 bg-light rounded">
                                    <p class="mb-0" style="text-align: justify; line-height: 1.6;">
                                        <?= nl2br(htmlspecialchars($kd_data['deskripsi_kd'])) ?>
                                    </p>
                                </div>
                            </div>

                            <!-- Tema/Subtema and Materi Pokok -->
                            <?php if (!empty($kd_data['tema_subtema']) || !empty($kd_data['materi_pokok'])): ?>
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-tags"></i> Tema dan Materi
                                </h6>
                                <div class="row">
                                    <?php if (!empty($kd_data['tema_subtema'])): ?>
                                    <div class="col-md-6">
                                        <div class="p-3 bg-light rounded">
                                            <h6 class="text-secondary mb-2">
                                                <i class="fas fa-bookmark"></i> Tema/Subtema
                                            </h6>
                                            <p class="mb-0" style="text-align: justify; line-height: 1.6;">
                                                <?= htmlspecialchars($kd_data['tema_subtema']) ?>
                                            </p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <?php if (!empty($kd_data['materi_pokok'])): ?>
                                    <div class="col-md-6">
                                        <div class="p-3 bg-light rounded">
                                            <h6 class="text-secondary mb-2">
                                                <i class="fas fa-book-open"></i> Materi Pokok
                                            </h6>
                                            <p class="mb-0" style="text-align: justify; line-height: 1.6;">
                                                <?= htmlspecialchars($kd_data['materi_pokok']) ?>
                                            </p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Learning Objectives -->
                            <?php if (!empty($kd_data['tujuan_pembelajaran'])): ?>
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-bullseye"></i> Tujuan Pembelajaran
                                </h6>
                                <div class="p-3 bg-light rounded">
                                    <p class="mb-0" style="text-align: justify; line-height: 1.6;">
                                        <?= nl2br(htmlspecialchars($kd_data['tujuan_pembelajaran'])) ?>
                                    </p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-4">
                            <!-- Metadata -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-clock"></i> Informasi Tambahan
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted">Dibuat pada:</small><br>
                                        <strong><?= date('d/m/Y H:i', strtotime($kd_data['created_at'])) ?></strong>
                                    </div>
                                    <?php if ($kd_data['updated_at'] != $kd_data['created_at']): ?>
                                    <div class="mb-3">
                                        <small class="text-muted">Terakhir diupdate:</small><br>
                                        <strong><?= date('d/m/Y H:i', strtotime($kd_data['updated_at'])) ?></strong>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <!-- Quick Actions -->
                                    <hr>
                                    <div class="d-grid gap-2">
                                        <a href="edit.php?id=<?= $kd_data['id'] ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> Edit KD
                                        </a>
                                        <a href="index.php?mapel_id=<?= $kd_data['mapel_id'] ?>&kelas_id=<?= $kd_data['kelas_id'] ?>&semester=<?= $kd_data['semester'] ?>&tahun_ajaran=<?= urlencode($kd_data['tahun_ajaran']) ?>"
                                           class="btn btn-info btn-sm">
                                            <i class="fas fa-list"></i> Lihat KD Lainnya
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics (if needed in future) -->
                            <div class="card bg-light mt-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-bar"></i> Statistik
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center">
                                        <div class="mb-2">
                                            <i class="fas fa-book fa-2x text-primary"></i>
                                        </div>
                                        <small class="text-muted">
                                            Kompetensi Dasar ini dapat digunakan sebagai referensi untuk pembuatan RPP dan penilaian
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="javascript:history.back()" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali
                                </a>
                                <div>
                                    <a href="edit.php?id=<?= $kd_data['id'] ?>" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="javascript:void(0)" onclick="confirmDelete(<?= $kd_data['id'] ?>)" 
                                       class="btn btn-danger">
                                        <i class="fas fa-trash"></i> Hapus
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus Kompetensi Dasar ini?\n\nData yang sudah dihapus tidak dapat dikembalikan.')) {
        window.location.href = 'delete.php?id=' + id;
    }
}
</script>

<?php require_once '../template/footer.php'; ?>
