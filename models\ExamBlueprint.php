<?php
require_once __DIR__ . '/../config/database.php';

class ExamBlueprint {
    private $conn;
    private $table_name = "exam_blueprints";

    public $id;
    public $multi_exam_id;
    public $rpp_id;
    public $guru_id;
    public $blueprint_title;
    public $exam_info;
    public $learning_objectives;
    public $question_distribution;
    public $cognitive_mapping;
    public $difficulty_distribution;
    public $blueprint_data;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                guru_id=:guru_id, multi_exam_id=:multi_exam_id, rpp_id=:rpp_id,
                blueprint_title=:blueprint_title, exam_info=:exam_info,
                learning_objectives=:learning_objectives, question_distribution=:question_distribution,
                cognitive_mapping=:cognitive_mapping, difficulty_distribution=:difficulty_distribution,
                blueprint_data=:blueprint_data";

        $stmt = $this->conn->prepare($query);

        // Bind
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":multi_exam_id", $this->multi_exam_id);
        $stmt->bindParam(":rpp_id", $this->rpp_id);
        $stmt->bindParam(":blueprint_title", $this->blueprint_title);
        $stmt->bindParam(":exam_info", $this->exam_info);
        $stmt->bindParam(":learning_objectives", $this->learning_objectives);
        $stmt->bindParam(":question_distribution", $this->question_distribution);
        $stmt->bindParam(":cognitive_mapping", $this->cognitive_mapping);
        $stmt->bindParam(":difficulty_distribution", $this->difficulty_distribution);
        $stmt->bindParam(":blueprint_data", $this->blueprint_data);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getAll() {
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru,
                        CASE 
                            WHEN eb.multi_exam_id IS NOT NULL THEN 'Multi-RPP'
                            ELSE 'Single RPP'
                        END as exam_scope
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 ORDER BY eb.created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllByGuru($guru_id) {
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru,
                        CASE 
                            WHEN eb.multi_exam_id IS NOT NULL THEN 'Multi-RPP'
                            ELSE 'Single RPP'
                        END as exam_scope
                FROM " . $this->table_name . " eb
                LEFT JOIN guru g ON eb.guru_id = g.id
                WHERE eb.guru_id = :guru_id
                ORDER BY eb.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getOne($id) {
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru,
                        CASE
                            WHEN eb.multi_exam_id IS NOT NULL THEN 'Multi-RPP'
                            ELSE 'Single RPP'
                        END as exam_scope
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 WHERE eb.id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByMultiExamId($multi_exam_id) {
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 WHERE eb.multi_exam_id = :multi_exam_id
                 ORDER BY eb.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':multi_exam_id', $multi_exam_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getByRppId($rpp_id) {
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 WHERE eb.rpp_id = :rpp_id
                 ORDER BY eb.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':rpp_id', $rpp_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                guru_id=:guru_id, multi_exam_id=:multi_exam_id, rpp_id=:rpp_id,
                blueprint_title=:blueprint_title, exam_info=:exam_info,
                learning_objectives=:learning_objectives, question_distribution=:question_distribution,
                cognitive_mapping=:cognitive_mapping, difficulty_distribution=:difficulty_distribution,
                blueprint_data=:blueprint_data
                WHERE id=:id";
    
        $stmt = $this->conn->prepare($query);

        // Bind
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":multi_exam_id", $this->multi_exam_id);
        $stmt->bindParam(":rpp_id", $this->rpp_id);
        $stmt->bindParam(":blueprint_title", $this->blueprint_title);
        $stmt->bindParam(":exam_info", $this->exam_info);
        $stmt->bindParam(":learning_objectives", $this->learning_objectives);
        $stmt->bindParam(":question_distribution", $this->question_distribution);
        $stmt->bindParam(":cognitive_mapping", $this->cognitive_mapping);
        $stmt->bindParam(":difficulty_distribution", $this->difficulty_distribution);
        $stmt->bindParam(":blueprint_data", $this->blueprint_data);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        
        return $stmt->execute();
    }

    public function getBlueprintStats($guru_id = null) {
        $where_clause = $guru_id ? "WHERE eb.guru_id = :guru_id" : "";
        
        $query = "SELECT 
                    COUNT(*) as total_blueprints,
                    SUM(CASE WHEN eb.multi_exam_id IS NOT NULL THEN 1 ELSE 0 END) as multi_rpp_blueprints,
                    SUM(CASE WHEN eb.rpp_id IS NOT NULL AND eb.multi_exam_id IS NULL THEN 1 ELSE 0 END) as single_rpp_blueprints,
                    COUNT(DISTINCT eb.guru_id) as total_teachers
                 FROM " . $this->table_name . " eb
                 " . $where_clause;
        
        $stmt = $this->conn->prepare($query);
        if ($guru_id) {
            $stmt->bindParam(':guru_id', $guru_id);
        }
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getRecentBlueprints($limit = 5, $guru_id = null) {
        $where_clause = $guru_id ? "WHERE eb.guru_id = :guru_id" : "";
        
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru,
                        CASE 
                            WHEN eb.multi_exam_id IS NOT NULL THEN 'Multi-RPP'
                            ELSE 'Single RPP'
                        END as exam_scope
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 " . $where_clause . "
                 ORDER BY eb.created_at DESC
                 LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        if ($guru_id) {
            $stmt->bindParam(':guru_id', $guru_id);
        }
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt;
    }

    public function searchBlueprints($search_term, $guru_id = null) {
        $where_clause = "WHERE (eb.blueprint_title LIKE :search OR eb.exam_info LIKE :search)";
        if ($guru_id) {
            $where_clause .= " AND eb.guru_id = :guru_id";
        }
        
        $query = "SELECT eb.*, g.nama_lengkap as nama_guru,
                        CASE 
                            WHEN eb.multi_exam_id IS NOT NULL THEN 'Multi-RPP'
                            ELSE 'Single RPP'
                        END as exam_scope
                 FROM " . $this->table_name . " eb
                 LEFT JOIN guru g ON eb.guru_id = g.id
                 " . $where_clause . "
                 ORDER BY eb.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $search_param = '%' . $search_term . '%';
        $stmt->bindParam(':search', $search_param);
        if ($guru_id) {
            $stmt->bindParam(':guru_id', $guru_id);
        }
        $stmt->execute();
        
        return $stmt;
    }


}
?>
