<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/Guru.php';

// Get teacher ID from logged in user
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    header("Location: index.php?error=1");
    exit();
}

// Check if ID is provided
if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$kd = new KompetensiDasar();
$kd_id = $_GET['id'];

// Get KD data to verify ownership
$kd_data = $kd->getById($kd_id);
if (!$kd_data || $kd_data['guru_id'] != $guru_id) {
    header("Location: index.php?error=1");
    exit();
}

// Set properties for deletion
$kd->id = $kd_id;
$kd->guru_id = $guru_id;

// Perform deletion
if ($kd->delete()) {
    header("Location: index.php?success=1");
} else {
    header("Location: index.php?error=1");
}
exit();
?>
