<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/TugasTambahan.php';

// Check if ID is provided
if(!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];

// Initialize models
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;

// Get tugas tambahan details
if(!$tugasTambahan->getOne()) {
    $_SESSION['error'] = "Tugas tambahan tidak ditemukan!";
    header("Location: index.php");
    exit();
}

// Store mapel_id, semester, and tahun_ajaran for redirection
$mapel_id = $tugasTambahan->mapel_id;
$semester = $tugasTambahan->semester;
$tahun_ajaran = $tugasTambahan->tahun_ajaran;

// Delete tugas tambahan
if($tugasTambahan->delete()) {
    $_SESSION['success'] = "Tugas tambahan berhasil dihapus!";
} else {
    $_SESSION['error'] = "Gagal menghapus tugas tambahan!";
}

// Redirect back to tugas.php
header("Location: tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
exit();
?>
