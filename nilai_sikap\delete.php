<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

$nilaiSikap = new NilaiSikap();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

if ($nilaiSikap->delete($_GET['id'])) {
    $_SESSION['success'] = "Data nilai sikap berhasil dihapus";
} else {
    $_SESSION['error'] = "Gagal menghapus data nilai sikap";
}

header("Location: index.php");
exit();
?>