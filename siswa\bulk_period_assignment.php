<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';
require_once '../models/PeriodeAktif.php';

// Check if user is admin
if (!in_array($_SESSION['role'], ['admin'])) {
    echo "<script>alert('Aks<PERSON> ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

$siswaModel = new Siswa();
$siswaPeriodeModel = new SiswaPeriode();
$kelasModel = new Kelas();
$tahunAjaranModel = new TahunAjaran();

$message = '';
$error = '';

// Get filter parameters
$filter_kelas = $_GET['filter_kelas'] ?? '';
$filter_status = $_GET['filter_status'] ?? 'unassigned';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'bulk_assign') {
        $student_ids = $_POST['student_ids'] ?? [];
        $tahun_ajaran = $_POST['tahun_ajaran'];
        $semester = $_POST['semester'];
        $kelas_id = $_POST['kelas_id'];
        
        if (empty($student_ids)) {
            $error = "Pilih minimal satu siswa untuk diproses";
        } else {
            $success_count = 0;
            $error_count = 0;
            
            foreach ($student_ids as $siswa_id) {
                try {
                    if ($siswaPeriodeModel->addSiswaToNewPeriode($siswa_id, $kelas_id, $tahun_ajaran, $semester)) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                } catch (Exception $e) {
                    $error_count++;
                }
            }
            
            if ($success_count > 0) {
                $message = "Berhasil mengatur periode untuk $success_count siswa";
            }
            if ($error_count > 0) {
                $error .= " Gagal memproses $error_count siswa";
            }
        }
    }
    
    if ($action === 'bulk_historical') {
        $student_ids = $_POST['student_ids'] ?? [];
        $start_tahun_ajaran = $_POST['start_tahun_ajaran'];
        $start_semester = $_POST['start_semester'];
        $start_kelas_id = $_POST['start_kelas_id'];
        
        if (empty($student_ids)) {
            $error = "Pilih minimal satu siswa untuk diproses";
        } else {
            $success_count = 0;
            $error_count = 0;
            
            foreach ($student_ids as $siswa_id) {
                try {
                    // Generate historical periods
                    $periods = $siswaPeriodeModel->generatePeriodSequence($start_tahun_ajaran, $start_semester, $start_kelas_id);
                    
                    if ($siswaPeriodeModel->createHistoricalPeriods($siswa_id, $periods)) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                } catch (Exception $e) {
                    $error_count++;
                }
            }
            
            if ($success_count > 0) {
                $message = "Berhasil membuat riwayat akademik untuk $success_count siswa";
            }
            if ($error_count > 0) {
                $error .= " Gagal memproses $error_count siswa";
            }
        }
    }
}

// Get students based on filter
$students = null;
if ($filter_status === 'unassigned') {
    $students = $siswaPeriodeModel->getSiswaWithoutPeriods($filter_kelas);
} else {
    // Get all students (for other filters)
    $students = $filter_kelas ? $siswaModel->getByKelas($filter_kelas) : $siswaModel->getAll();
}

// Get data for dropdowns
$kelas_list = $kelasModel->getAll()->fetchAll(PDO::FETCH_ASSOC);
$tahun_ajaran_list = $tahunAjaranModel->getAll()->fetchAll(PDO::FETCH_ASSOC);

// Get current active period
$periodeAktif = new PeriodeAktif();
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
$current_semester = $periodeAktif->semester ?: '1';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Pengaturan Periode Siswa Massal</h1>
        <div>
            <a href="create_historical.php" class="btn btn-info mr-2">
                <i class="fas fa-user-plus"></i> Daftar Siswa Historis
            </a>
            <a href="manage_periods.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kelola Periode
            </a>
        </div>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i> Filter Siswa
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-4">
                    <label for="filter_status">Status Periode</label>
                    <select name="filter_status" id="filter_status" class="form-control">
                        <option value="unassigned" <?= $filter_status === 'unassigned' ? 'selected' : '' ?>>
                            Belum Ada Periode
                        </option>
                        <option value="all" <?= $filter_status === 'all' ? 'selected' : '' ?>>
                            Semua Siswa
                        </option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="filter_kelas">Kelas</label>
                    <select name="filter_kelas" id="filter_kelas" class="form-control">
                        <option value="">Semua Kelas</option>
                        <?php foreach ($kelas_list as $kelas): ?>
                            <option value="<?= $kelas['id'] ?>" <?= $filter_kelas == $kelas['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label>&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Students List -->
    <?php if ($students && $students->rowCount() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users"></i> 
                Daftar Siswa (<?= $students->rowCount() ?> siswa)
            </h6>
        </div>
        <div class="card-body">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <button type="button" class="btn btn-success" onclick="showBulkAssignModal()">
                        <i class="fas fa-plus"></i> Tambah ke Periode Tertentu
                    </button>
                </div>
                <div class="col-md-6 text-right">
                    <button type="button" class="btn btn-warning" onclick="showHistoricalModal()">
                        <i class="fas fa-history"></i> Buat Riwayat Akademik
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="studentsTable">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" onchange="toggleAll()">
                            </th>
                            <th>No</th>
                            <th>NIS</th>
                            <th>Nama Siswa</th>
                            <th>Jenis Kelamin</th>
                            <th>Kelas</th>
                            <th>Status Periode</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $students_data = $students->fetchAll(PDO::FETCH_ASSOC);
                        foreach ($students_data as $student): 
                        ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="student_ids[]" value="<?= $student['siswa_id'] ?? $student['id'] ?>" class="student-checkbox">
                            </td>
                            <td><?= $no++ ?></td>
                            <td><?= htmlspecialchars($student['nis']) ?></td>
                            <td><?= htmlspecialchars($student['nama_siswa']) ?></td>
                            <td><?= $student['jenis_kelamin'] == 'L' ? 'Laki-laki' : 'Perempuan' ?></td>
                            <td><?= htmlspecialchars($student['nama_kelas']) ?></td>
                            <td>
                                <?php if ($filter_status === 'unassigned'): ?>
                                    <span class="badge badge-danger">Belum Ada Periode</span>
                                <?php else: ?>
                                    <span class="badge badge-success">Ada Periode</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="card shadow mb-4">
        <div class="card-body text-center">
            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
            <h5>Tidak ada siswa ditemukan</h5>
            <p class="text-muted">
                <?php if ($filter_status === 'unassigned'): ?>
                    Semua siswa sudah memiliki periode akademik.
                <?php else: ?>
                    Tidak ada siswa dengan kriteria yang dipilih.
                <?php endif; ?>
            </p>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Bulk Assign Modal -->
<div class="modal fade" id="bulkAssignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="bulkAssignForm">
                <input type="hidden" name="action" value="bulk_assign">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus"></i> Tambah Siswa ke Periode
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Tahun Ajaran</label>
                        <select name="tahun_ajaran" class="form-control" required>
                            <option value="">Pilih Tahun Ajaran</option>
                            <?php foreach ($tahun_ajaran_list as $ta): ?>
                                <option value="<?= $ta['tahun_ajaran'] ?>" <?= $ta['tahun_ajaran'] == $current_tahun_ajaran ? 'selected' : '' ?>>
                                    <?= $ta['tahun_ajaran'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Semester</label>
                        <select name="semester" class="form-control" required>
                            <option value="">Pilih Semester</option>
                            <option value="1" <?= $current_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                            <option value="2" <?= $current_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Kelas</label>
                        <select name="kelas_id" class="form-control" required>
                            <option value="">Pilih Kelas</option>
                            <?php foreach ($kelas_list as $kelas): ?>
                                <option value="<?= $kelas['id'] ?>"><?= $kelas['nama_kelas'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div id="selectedStudentsInfo"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Historical Modal -->
<div class="modal fade" id="historicalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="historicalForm">
                <input type="hidden" name="action" value="bulk_historical">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history"></i> Buat Riwayat Akademik
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Sistem akan membuat riwayat periode dari periode mulai hingga periode saat ini.
                    </div>
                    <div class="form-group">
                        <label>Tahun Ajaran Mulai</label>
                        <select name="start_tahun_ajaran" class="form-control" required>
                            <option value="">Pilih Tahun Ajaran</option>
                            <?php foreach ($tahun_ajaran_list as $ta): ?>
                                <option value="<?= $ta['tahun_ajaran'] ?>"><?= $ta['tahun_ajaran'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Semester Mulai</label>
                        <select name="start_semester" class="form-control" required>
                            <option value="">Pilih Semester</option>
                            <option value="1">Semester 1</option>
                            <option value="2">Semester 2</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Kelas Awal</label>
                        <select name="start_kelas_id" class="form-control" required>
                            <option value="">Pilih Kelas</option>
                            <?php foreach ($kelas_list as $kelas): ?>
                                <option value="<?= $kelas['id'] ?>"><?= $kelas['nama_kelas'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div id="selectedStudentsHistoricalInfo"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Buat Riwayat
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.student-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function getSelectedStudents() {
    const checkboxes = document.querySelectorAll('.student-checkbox:checked');
    const students = [];
    
    checkboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const nis = row.cells[2].textContent;
        const nama = row.cells[3].textContent;
        students.push({id: checkbox.value, nis: nis, nama: nama});
    });
    
    return students;
}

function showBulkAssignModal() {
    const selected = getSelectedStudents();
    if (selected.length === 0) {
        alert('Pilih minimal satu siswa');
        return;
    }
    
    // Add hidden inputs for selected students
    const form = document.getElementById('bulkAssignForm');
    const existingInputs = form.querySelectorAll('input[name="student_ids[]"]');
    existingInputs.forEach(input => input.remove());
    
    selected.forEach(student => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'student_ids[]';
        input.value = student.id;
        form.appendChild(input);
    });
    
    // Show selected students info
    let info = '<div class="alert alert-info"><strong>Siswa yang dipilih (' + selected.length + '):</strong><ul>';
    selected.forEach(student => {
        info += '<li>' + student.nis + ' - ' + student.nama + '</li>';
    });
    info += '</ul></div>';
    document.getElementById('selectedStudentsInfo').innerHTML = info;
    
    $('#bulkAssignModal').modal('show');
}

function showHistoricalModal() {
    const selected = getSelectedStudents();
    if (selected.length === 0) {
        alert('Pilih minimal satu siswa');
        return;
    }
    
    // Add hidden inputs for selected students
    const form = document.getElementById('historicalForm');
    const existingInputs = form.querySelectorAll('input[name="student_ids[]"]');
    existingInputs.forEach(input => input.remove());
    
    selected.forEach(student => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'student_ids[]';
        input.value = student.id;
        form.appendChild(input);
    });
    
    // Show selected students info
    let info = '<div class="alert alert-warning"><strong>Siswa yang dipilih (' + selected.length + '):</strong><ul>';
    selected.forEach(student => {
        info += '<li>' + student.nis + ' - ' + student.nama + '</li>';
    });
    info += '</ul></div>';
    document.getElementById('selectedStudentsHistoricalInfo').innerHTML = info;
    
    $('#historicalModal').modal('show');
}

$(document).ready(function() {
    $('#studentsTable').DataTable({
        "order": [[2, "asc"]], // Sort by NIS
        "columnDefs": [
            {
                "searchable": false,
                "orderable": false,
                "targets": [0, 1] // Checkbox and No columns
            }
        ],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
