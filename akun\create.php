<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$guru = new Guru();
$guru_list = $guru->getAllActive();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['create_account'])) {
        $user = new User();
        $user->username = $_POST['username'];
        $user->password = $_POST['password'];
        $user->nama_lengkap = $_POST['nama_lengkap'];
        $user->role = $_POST['role'];

        // Check if username already exists
        if ($user->usernameExists()) {
            $_SESSION['error'] = "Username sudah digunakan!";
        } else {
            if ($user->create()) {
                $_SESSION['message'] = "Akun berhasil dibuat!";
                header("Location: index.php");
                exit();
            } else {
                $_SESSION['error'] = "Gagal membuat akun!";
            }
        }
    }
}
?>

<div class="container">
    <h2 class="mb-4">Tambah Akun</h2>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Tambah Akun</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role" required onchange="toggleGuruSelect()">
                                <option value="">Pilih Role</option>
                                <option value="admin">Admin</option>
                                <option value="guru">Guru</option>
                                <option value="bendahara">Bendahara</option>
                            </select>
                        </div>

                        <div class="mb-3" id="guruSelectDiv" style="display: none;">
                            <label for="guru_id" class="form-label">Guru</label>
                            <select class="form-select" id="guru_id" name="guru_id" onchange="updateNamaLengkap()">
                                <option value="">Pilih Guru</option>
                                <?php foreach ($guru_list as $g): ?>
                                    <option value="<?php echo $g['id']; ?>" 
                                            data-nama="<?php echo htmlspecialchars($g['nama_lengkap']); ?>"
                                            data-nip="<?php echo htmlspecialchars($g['nip']); ?>">
                                        <?php echo htmlspecialchars($g['nama_lengkap']); ?> (<?php echo htmlspecialchars($g['nip']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="form-text">Password minimal 6 karakter.</div>
                        </div>

                        <div class="mb-3">
                            <label for="nama_lengkap" class="form-label">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" required>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" name="create_account" class="btn btn-primary">Simpan</button>
                            <a href="index.php" class="btn btn-secondary">Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleGuruSelect() {
    const role = document.getElementById('role').value;
    const guruDiv = document.getElementById('guruSelectDiv');
    const namaInput = document.getElementById('nama_lengkap');
    const usernameInput = document.getElementById('username');
    
    if (role === 'guru') {
        guruDiv.style.display = 'block';
        namaInput.readOnly = true;
        // Reset values when switching to guru
        namaInput.value = '';
        usernameInput.value = '';
        // Reset guru selection
        document.getElementById('guru_id').selectedIndex = 0;
    } else {
        guruDiv.style.display = 'none';
        namaInput.readOnly = false;
        // Clear values when switching from guru
        namaInput.value = '';
        usernameInput.value = '';
    }
}

function updateNamaLengkap() {
    const guruSelect = document.getElementById('guru_id');
    const namaInput = document.getElementById('nama_lengkap');
    const usernameInput = document.getElementById('username');
    
    if (guruSelect.value) {
        const selectedOption = guruSelect.options[guruSelect.selectedIndex];
        const nip = selectedOption.getAttribute('data-nip');
        namaInput.value = selectedOption.getAttribute('data-nama');
        usernameInput.value = 'guru' + nip;
    } else {
        namaInput.value = '';
        usernameInput.value = '';
    }
}
</script>

<?php
require_once '../template/footer.php';
?>
