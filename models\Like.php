<?php
require_once '/absen/config/database.php';

class Like {
    private $conn;
    private $table_berita = 'like_berita';
    private $table_komentar = 'like_komentar';

    public function __construct() {
        $db = new database();
        $this->conn = $db->getConnection();
    }

    public function likeBerita($berita_id, $user_id, $is_dislike = false) {
        // Cek apakah sudah ada like/dislike
        $query = "SELECT id, is_dislike FROM " . $this->table_berita . " WHERE berita_id = :berita_id AND user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            if ($existing['is_dislike'] == $is_dislike) {
                // Jika sudah like dan klik like lagi, atau sudah dislike dan klik dislike lagi, hapus
                $query = "DELETE FROM " . $this->table_berita . " WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":id", $existing['id']);
                return $stmt->execute();
            } else {
                // Jika sudah like dan klik dislike, atau sebaliknya, update
                $query = "UPDATE " . $this->table_berita . " SET is_dislike = :is_dislike WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":is_dislike", $is_dislike);
                $stmt->bindParam(":id", $existing['id']);
                return $stmt->execute();
            }
        } else {
            // Jika belum ada, insert baru
            $query = "INSERT INTO " . $this->table_berita . " (berita_id, user_id, is_dislike) VALUES (:berita_id, :user_id, :is_dislike)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":berita_id", $berita_id);
            $stmt->bindParam(":user_id", $user_id);
            $stmt->bindParam(":is_dislike", $is_dislike);
            return $stmt->execute();
        }
    }

    public function likeKomentar($komentar_id, $user_id, $is_dislike = false) {
        // Cek apakah sudah ada like/dislike
        $query = "SELECT id, is_dislike FROM " . $this->table_komentar . " WHERE komentar_id = :komentar_id AND user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $komentar_id);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            if ($existing['is_dislike'] == $is_dislike) {
                // Jika sudah like dan klik like lagi, atau sudah dislike dan klik dislike lagi, hapus
                $query = "DELETE FROM " . $this->table_komentar . " WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":id", $existing['id']);
                return $stmt->execute();
            } else {
                // Jika sudah like dan klik dislike, atau sebaliknya, update
                $query = "UPDATE " . $this->table_komentar . " SET is_dislike = :is_dislike WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":is_dislike", $is_dislike);
                $stmt->bindParam(":id", $existing['id']);
                return $stmt->execute();
            }
        } else {
            // Jika belum ada, insert baru
            $query = "INSERT INTO " . $this->table_komentar . " (komentar_id, user_id, is_dislike) VALUES (:komentar_id, :user_id, :is_dislike)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":komentar_id", $komentar_id);
            $stmt->bindParam(":user_id", $user_id);
            $stmt->bindParam(":is_dislike", $is_dislike);
            return $stmt->execute();
        }
    }

    public function isBeritaLiked($berita_id, $user_id) {
        $query = "SELECT is_dislike FROM " . $this->table_berita . " WHERE berita_id = :berita_id AND user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? ($row['is_dislike'] ? 'dislike' : 'like') : false;
    }

    public function isKomentarLiked($komentar_id, $user_id) {
        $query = "SELECT is_dislike FROM " . $this->table_komentar . " WHERE komentar_id = :komentar_id AND user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $komentar_id);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? ($row['is_dislike'] ? 'dislike' : 'like') : false;
    }

    public function getBeritaLikeCount($berita_id) {
        $query = "SELECT 
                    SUM(CASE WHEN is_dislike = 0 THEN 1 ELSE 0 END) as likes,
                    SUM(CASE WHEN is_dislike = 1 THEN 1 ELSE 0 END) as dislikes
                FROM " . $this->table_berita . " 
                WHERE berita_id = :berita_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getKomentarLikeCount($komentar_id) {
        $query = "SELECT 
                    SUM(CASE WHEN is_dislike = 0 THEN 1 ELSE 0 END) as likes,
                    SUM(CASE WHEN is_dislike = 1 THEN 1 ELSE 0 END) as dislikes
                FROM " . $this->table_komentar . " 
                WHERE komentar_id = :komentar_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $komentar_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
