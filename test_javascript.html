<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript getOptionsValue Function</title>
</head>
<body>
    <h2>Test JavaScript getOptionsValue Function</h2>
    <div id="results"></div>

    <script>
        function getOptionsValue(options) {
            // Helper function to properly format options for hidden input
            console.log('getOptionsValue called with:', options, 'Type:', typeof options);
            
            if (!options) {
                console.log('getOptionsValue: options is falsy, returning empty string');
                return '';
            }

            if (Array.isArray(options)) {
                const result = JSON.stringify(options);
                console.log('getOptionsValue: Array converted to JSON:', result);
                return result;
            } else if (typeof options === 'string') {
                // If it's already a JSON string, return as-is
                // If it's a comma-separated string, convert to JSON array
                if (options.trim() === '') {
                    console.log('getOptionsValue: Empty string, returning empty');
                    return '';
                }

                try {
                    // Test if it's valid JSON
                    JSON.parse(options);
                    console.log('getOptionsValue: Valid JSON string, returning as-is:', options);
                    return options; // Already valid JSON
                } catch (e) {
                    // Not valid JSON, treat as comma-separated string
                    if (options.includes(',')) {
                        const optionsArray = options.split(',').map(opt => opt.trim()).filter(opt => opt.length > 0);
                        const result = JSON.stringify(optionsArray);
                        console.log('getOptionsValue: Comma-separated string converted to JSON:', result);
                        return result;
                    } else {
                        // Single option, wrap in array
                        const result = JSON.stringify([options]);
                        console.log('getOptionsValue: Single option wrapped in array:', result);
                        return result;
                    }
                }
            }

            console.log('getOptionsValue: Unhandled type, returning empty string');
            return '';
        }

        // Test cases
        const testCases = [
            {
                name: 'Array input (typical from AI)',
                input: ['A. Option 1', 'B. Option 2', 'C. Option 3', 'D. Option 4'],
                expected: '["A. Option 1","B. Option 2","C. Option 3","D. Option 4"]'
            },
            {
                name: 'JSON string input',
                input: '["A. Option 1","B. Option 2","C. Option 3","D. Option 4"]',
                expected: '["A. Option 1","B. Option 2","C. Option 3","D. Option 4"]'
            },
            {
                name: 'Comma-separated string',
                input: 'A. Option 1, B. Option 2, C. Option 3',
                expected: '["A. Option 1","B. Option 2","C. Option 3"]'
            },
            {
                name: 'Single string',
                input: 'Single option',
                expected: '["Single option"]'
            },
            {
                name: 'Empty string',
                input: '',
                expected: ''
            },
            {
                name: 'Null input',
                input: null,
                expected: ''
            },
            {
                name: 'Undefined input',
                input: undefined,
                expected: ''
            }
        ];

        let resultsHTML = '<h3>Test Results:</h3>';
        let allPassed = true;

        testCases.forEach((testCase, index) => {
            console.log(`\n--- Test ${index + 1}: ${testCase.name} ---`);
            const result = getOptionsValue(testCase.input);
            const passed = result === testCase.expected;
            
            if (!passed) allPassed = false;
            
            resultsHTML += `
                <div style="border: 1px solid ${passed ? 'green' : 'red'}; padding: 10px; margin: 10px 0; background-color: ${passed ? '#d4edda' : '#f8d7da'};">
                    <h4>Test ${index + 1}: ${testCase.name}</h4>
                    <p><strong>Input:</strong> ${JSON.stringify(testCase.input)}</p>
                    <p><strong>Expected:</strong> ${testCase.expected}</p>
                    <p><strong>Actual:</strong> ${result}</p>
                    <p><strong>Status:</strong> ${passed ? '✅ PASSED' : '❌ FAILED'}</p>
                </div>
            `;
        });

        resultsHTML += `
            <div style="background-color: ${allPassed ? '#d4edda' : '#f8d7da'}; padding: 15px; border: 1px solid ${allPassed ? '#c3e6cb' : '#f5c6cb'}; border-radius: 5px; margin-top: 20px;">
                <h3>${allPassed ? '✅ All Tests Passed!' : '❌ Some Tests Failed'}</h3>
                <p>The getOptionsValue function is ${allPassed ? 'working correctly' : 'not working as expected'}.</p>
            </div>
        `;

        document.getElementById('results').innerHTML = resultsHTML;

        // Test with actual question data structure
        console.log('\n--- Testing with actual question structure ---');
        const sampleQuestion = {
            question_text: "Sample question?",
            question_type: "multiple_choice",
            options: ['A. Option 1', 'B. Option 2', 'C. Option 3', 'D. Option 4'],
            correct_answer: 'B',
            difficulty_level: 'regular',
            category: 'Generated'
        };

        console.log('Sample question:', sampleQuestion);
        const optionsValue = getOptionsValue(sampleQuestion.options);
        console.log('Options value for form:', optionsValue);

        // Test HTML escaping
        const htmlTest = `<input type="hidden" name="test_options" value="${optionsValue}">`;
        console.log('HTML input would be:', htmlTest);
    </script>
</body>
</html>
