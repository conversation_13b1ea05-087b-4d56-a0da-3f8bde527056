<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/TugasTambahan.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Kelas.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Get active period
$periode = new PeriodeAktif();
$periode->getActive();

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

// Get available tahun ajaran
$ta = new TahunAjaran();
$tahun_ajaran_list = $ta->getAllAsArray();

// Get guru_id if user is a teacher
$guru_id = null;
$user = new User();
if(isset($_SESSION['user_id']) && $_SESSION['role'] == 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get mapel list based on user role
$mapel = new MataPelajaran();
if($_SESSION['role'] == 'guru' && $guru_id) {
    $mapel_list = $mapel->getByGuru($guru_id);
} else {
    $mapel_list = $mapel->getAll();
}

// Handle success and error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Tugas Tambahan</h1>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filter</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="semester" class="form-label">Semester</label>
                            <select name="semester" id="semester" class="form-select">
                                <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                            <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                                <?php foreach($tahun_ajaran_list as $ta): ?>
                                    <option value="<?php echo $ta['tahun_ajaran']; ?>" <?php echo $tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : ''; ?>>
                                        <?php echo $ta['tahun_ajaran']; ?>
                                    </option>
                                <?php endforeach; ?>
                                <?php if(empty($tahun_ajaran_list)): ?>
                                    <option value="<?php echo date('Y').'/'.((int)date('Y')+1); ?>" selected>
                                        <?php echo date('Y').'/'.((int)date('Y')+1); ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if($success_msg): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if($error_msg): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Daftar Mata Pelajaran</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Kelas</th>
                                    <th>Mata Pelajaran</th>
                                    <th>KKM</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                while($row = $mapel_list->fetch(PDO::FETCH_ASSOC)):
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td>
                                        <?php
                                        // Get classes for this subject
                                        $kelas_list = $mapel->getKelasByMapel($row['id']);
                                        $kelas_names = [];
                                        while($kelas = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
                                            $kelas_names[] = $kelas['nama_kelas'];
                                        }
                                        echo !empty($kelas_names) ? implode(', ', $kelas_names) : '<span class="text-muted">Tidak ada kelas</span>';
                                        ?>
                                    </td>
                                    <td><?php echo $row['nama_mapel']; ?></td>
                                    <td><?php echo $row['kkm']; ?></td>
                                    <td>
                                        <a href="tugas.php?mapel_id=<?php echo $row['id']; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-tasks"></i> Kelola Tugas Tambahan
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
