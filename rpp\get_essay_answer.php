<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/EssayAnswer.php';
require_once '../models/RppQuestion.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
    exit();
}

try {
    // Check if getting by answer_id or question_id
    if (isset($_GET['answer_id'])) {
        // Get by answer ID (for management interface)
        $answer_id = $_GET['answer_id'];

        $essayAnswer = new EssayAnswer();
        $answer_data = $essayAnswer->getOne($answer_id);

        if (!$answer_data) {
            throw new Exception("Jawaban tidak ditemukan");
        }

        // Verify ownership
        $question_id = $answer_data['question_id'];
        $question_type = $answer_data['question_type'];

        // Get question details
        if ($question_type === 'rpp_question') {
            $rppQuestion = new RppQuestion();
            $question_data = $rppQuestion->getOne($question_id);

            if (!$question_data) {
                throw new Exception("Soal tidak ditemukan");
            }

            // Verify ownership through RPP
            $rpp = new Rpp();
            $rpp_data = $rpp->getOne($question_data['rpp_id']);
            if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
                throw new Exception("Anda tidak memiliki akses ke soal ini");
            }

            $answer_data['question_text'] = $question_data['question_text'];
        } else {
            // Handle multi-RPP questions
            $database = new Database();
            $conn = $database->getConnection();

            $query = "SELECT meq.*, me.guru_id
                     FROM multi_rpp_exam_questions meq
                     JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                     WHERE meq.id = :question_id";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(":question_id", $question_id);
            $stmt->execute();

            $question_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$question_data) {
                throw new Exception("Soal tidak ditemukan");
            }

            if ($question_data['guru_id'] != $guru_id) {
                throw new Exception("Anda tidak memiliki akses ke soal ini");
            }

            $answer_data['question_text'] = $question_data['question_text'];
        }

        // Parse JSON fields
        if (!empty($answer_data['scoring_rubric'])) {
            $answer_data['scoring_rubric'] = json_decode($answer_data['scoring_rubric'], true);
        }

        if (!empty($answer_data['generation_metadata'])) {
            $answer_data['generation_metadata'] = json_decode($answer_data['generation_metadata'], true);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Jawaban ditemukan',
            'answer' => $answer_data,
            'has_answer' => true
        ]);
        exit();
    }

    // Original logic for getting by question_id
    if (!isset($_GET['question_id'])) {
        throw new Exception("Question ID tidak ditemukan");
    }

    $question_id = $_GET['question_id'];
    $question_type = $_GET['question_type'] ?? 'rpp_question';

    // Verify ownership
    if ($question_type === 'rpp_question') {
        $rppQuestion = new RppQuestion();
        $question_data = $rppQuestion->getOne($question_id);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        // Verify ownership through RPP
        $rpp = new Rpp();
        $rpp_data = $rpp->getOne($question_data['rpp_id']);
        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }
    } else {
        // Handle multi-RPP questions
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT meq.*, me.guru_id 
                 FROM multi_rpp_exam_questions meq
                 JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                 WHERE meq.id = :question_id";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->execute();
        
        $question_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        if ($question_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }
    }

    // Get essay answer
    $essayAnswer = new EssayAnswer();
    $answer_data = $essayAnswer->getByQuestionId($question_id, $question_type);

    if (!$answer_data) {
        echo json_encode([
            'success' => false,
            'message' => 'Jawaban tidak ditemukan',
            'has_answer' => false
        ]);
        exit();
    }

    // Parse JSON fields
    if (!empty($answer_data['scoring_rubric'])) {
        $answer_data['scoring_rubric'] = json_decode($answer_data['scoring_rubric'], true);
    }

    if (!empty($answer_data['generation_metadata'])) {
        $answer_data['generation_metadata'] = json_decode($answer_data['generation_metadata'], true);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Jawaban ditemukan',
        'answer' => $answer_data,
        'has_answer' => true
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
