<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Kelas.php';

// Check if user is admin
if (!in_array($_SESSION['role'], ['admin'])) {
    echo "<script>alert('Akses ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

$debug_info = [];
$test_results = [];

// Test 1: Database Connection
try {
    $siswa = new Siswa();
    $debug_info[] = "✓ Database connection successful";
} catch (Exception $e) {
    $debug_info[] = "✗ Database connection failed: " . $e->getMessage();
}

// Test 2: Check if tables exist
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $tables = ['siswa', 'siswa_periode', 'periode_aktif', 'kelas'];
    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            $debug_info[] = "✓ Table '$table' exists";
        } else {
            $debug_info[] = "✗ Table '$table' missing";
        }
    }
} catch (Exception $e) {
    $debug_info[] = "✗ Table check failed: " . $e->getMessage();
}

// Test 3: Check siswa table structure
try {
    $query = "DESCRIBE siswa";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_columns = ['id', 'nis', 'nama_siswa', 'jenis_kelamin', 'kelas_id', 'tahun_ajaran_current', 'semester_current'];
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            $debug_info[] = "✓ Column 'siswa.$col' exists";
        } else {
            $debug_info[] = "✗ Column 'siswa.$col' missing";
        }
    }
} catch (Exception $e) {
    $debug_info[] = "✗ Siswa table structure check failed: " . $e->getMessage();
}

// Test 4: Check periode aktif
try {
    $periode = new PeriodeAktif();
    $periode->getActive();
    if ($periode->tahun_ajaran) {
        $debug_info[] = "✓ Active period found: {$periode->tahun_ajaran} - Semester {$periode->semester}";
    } else {
        $debug_info[] = "⚠ No active period found, will use default";
    }
} catch (Exception $e) {
    $debug_info[] = "✗ PeriodeAktif check failed: " . $e->getMessage();
}

// Test 5: Check if kelas exist
try {
    $kelas = new Kelas();
    $kelas_list = $kelas->getAll();
    $kelas_count = $kelas_list->rowCount();
    if ($kelas_count > 0) {
        $debug_info[] = "✓ Found $kelas_count classes";
    } else {
        $debug_info[] = "✗ No classes found - students need classes to be created";
    }
} catch (Exception $e) {
    $debug_info[] = "✗ Kelas check failed: " . $e->getMessage();
}

// Test 6: Try to create a test student (if form submitted)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_create'])) {
    try {
        $siswa = new Siswa();
        $siswa->nis = 'TEST' . time();
        $siswa->nama_siswa = 'Test Student';
        $siswa->jenis_kelamin = 'L';
        $siswa->kelas_id = $_POST['kelas_id'];
        $siswa->alamat = 'Test Address';
        $siswa->no_telp = '08123456789';

        if ($siswa->create()) {
            $test_results[] = "✓ Test student created successfully with NIS: " . $siswa->nis;
            
            // Clean up - delete test student
            $query = "DELETE FROM siswa WHERE nis = :nis";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':nis', $siswa->nis);
            $stmt->execute();
            $test_results[] = "✓ Test student cleaned up";
        } else {
            $test_results[] = "✗ Failed to create test student";
        }
    } catch (Exception $e) {
        $test_results[] = "✗ Test student creation error: " . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Debug Siswa Create</h1>
        <a href="create.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Form Create
        </a>
    </div>

    <!-- Debug Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-bug"></i> System Debug Information
            </h6>
        </div>
        <div class="card-body">
            <?php foreach ($debug_info as $info): ?>
                <p class="mb-1">
                    <?php if (strpos($info, '✓') === 0): ?>
                        <span class="text-success"><?= htmlspecialchars($info) ?></span>
                    <?php elseif (strpos($info, '✗') === 0): ?>
                        <span class="text-danger"><?= htmlspecialchars($info) ?></span>
                    <?php else: ?>
                        <span class="text-warning"><?= htmlspecialchars($info) ?></span>
                    <?php endif; ?>
                </p>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Test Results -->
    <?php if (!empty($test_results)): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-flask"></i> Test Results
            </h6>
        </div>
        <div class="card-body">
            <?php foreach ($test_results as $result): ?>
                <p class="mb-1">
                    <?php if (strpos($result, '✓') === 0): ?>
                        <span class="text-success"><?= htmlspecialchars($result) ?></span>
                    <?php else: ?>
                        <span class="text-danger"><?= htmlspecialchars($result) ?></span>
                    <?php endif; ?>
                </p>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Test Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-vial"></i> Test Student Creation
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted">This will create and immediately delete a test student to verify the create function works.</p>
            
            <form method="POST">
                <div class="form-group mb-3">
                    <label for="kelas_id">Select a Class for Test:</label>
                    <select name="kelas_id" id="kelas_id" class="form-control" required>
                        <option value="">Choose Class</option>
                        <?php
                        try {
                            $kelas = new Kelas();
                            $kelas_list = $kelas->getAll();
                            while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
                                echo "<option value='{$row['id']}'>{$row['nama_kelas']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>Error loading classes</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <button type="submit" name="test_create" class="btn btn-warning">
                    <i class="fas fa-flask"></i> Run Test
                </button>
            </form>
        </div>
    </div>

    <!-- Error Log -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-exclamation-triangle"></i> Recent Error Log
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted">Check your PHP error log for detailed error messages. Common locations:</p>
            <ul>
                <li><code>/xampp/apache/logs/error.log</code> (XAMPP)</li>
                <li><code>/var/log/apache2/error.log</code> (Linux)</li>
                <li>PHP error_log file in your project directory</li>
            </ul>
            
            <div class="alert alert-info">
                <strong>Tips for debugging:</strong>
                <ul class="mb-0">
                    <li>Check if all required tables exist in your database</li>
                    <li>Verify that the siswa table has the new columns (tahun_ajaran_current, semester_current)</li>
                    <li>Make sure at least one class exists in the kelas table</li>
                    <li>Check if there's an active period in periode_aktif table</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
