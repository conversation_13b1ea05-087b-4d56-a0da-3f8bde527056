<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

$perpustakaan = new Perpustakaan();

// Set default tanggal
$tanggal_awal = isset($_GET['tanggal_awal']) ? $_GET['tanggal_awal'] : date('Y-m-01');
$tanggal_akhir = isset($_GET['tanggal_akhir']) ? $_GET['tanggal_akhir'] : date('Y-m-t');

// Ambil data peminjaman
$peminjaman = $perpustakaan->getLaporanPeminjaman($tanggal_awal, $tanggal_akhir);
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><PERSON><PERSON><PERSON></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Laporan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Filter Laporan</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="GET" class="form-inline">
                                <div class="form-group mx-sm-3 mb-2">
                                    <label for="tanggal_awal" class="sr-only">Tanggal Awal</label>
                                    <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal; ?>">
                                </div>
                                <div class="form-group mx-sm-3 mb-2">
                                    <label for="tanggal_akhir" class="sr-only">Tanggal Akhir</label>
                                    <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir; ?>">
                                </div>
                                <button type="submit" class="btn btn-primary mb-2">Filter</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Laporan Peminjaman Buku</h5>
                            <div>
                                <a href="export_excel.php?tanggal_awal=<?= $tanggal_awal ?>&tanggal_akhir=<?= $tanggal_akhir ?>" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </a>
                                <a href="export_pdf.php?tanggal_awal=<?= $tanggal_awal ?>&tanggal_akhir=<?= $tanggal_akhir ?>" class="btn btn-danger">
                                    <i class="fas fa-file-pdf"></i> Export PDF
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="tableLaporan" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Nomor Peminjaman</th>
                                            <th>Tanggal Pinjam</th>
                                            <th>Tanggal Kembali</th>
                                            <th>Peminjam</th>
                                            <th>Buku</th>
                                            <th>Jumlah Pinjam Awal</th>
                                            <th>Sudah Kembali</th>
                                            <th>Sisa Pinjam</th>
                                            <th>Riwayat Pengembalian</th>
                                            <th>Status</th>
                                            <th>Keterlambatan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        $current_peminjaman = null;
                                        foreach ($peminjaman as $p) :
                                            // Skip jika ini adalah riwayat dari peminjaman yang sama
                                            if ($current_peminjaman == $p['id_peminjaman']) {
                                                continue;
                                            }
                                            $current_peminjaman = $p['id_peminjaman'];

                                            $status = 'Dipinjam';
                                            $badge_class = 'bg-warning';
                                            if ($p['status'] == 'kembali') {
                                                $status = 'Dikembalikan';
                                                $badge_class = 'bg-success';
                                            } elseif ($p['hari_terlambat'] > 0) {
                                                $status = 'Terlambat';
                                                $badge_class = 'bg-danger';
                                            }

                                            // Hitung sisa pinjaman
                                            $sisa_pinjam = $p['jumlah_pinjam_awal'] - $p['total_dikembalikan'];

                                            // Ambil semua riwayat pengembalian untuk peminjaman ini
                                            $riwayat = '';
                                            foreach ($peminjaman as $r) {
                                                if ($r['id_peminjaman'] == $p['id_peminjaman'] && $r['tanggal_pengembalian'] !== null) {
                                                    $riwayat .= date('d/m/Y', strtotime($r['tanggal_pengembalian'])) . 
                                                               ': ' . $r['jumlah_dikembalikan'] . ' buku<br>';
                                                }
                                            }
                                        ?>
                                            <tr>
                                                <td><?= $no++; ?></td>
                                                <td><?= $p['nomor_peminjaman']; ?></td>
                                                <td><?= date('d/m/Y', strtotime($p['tanggal_pinjam'])); ?></td>
                                                <td><?= date('d/m/Y', strtotime($p['tanggal_kembali'])); ?></td>
                                                <td><?= htmlspecialchars($p['nama_peminjam']); ?></td>
                                                <td><?= htmlspecialchars($p['judul_buku']); ?></td>
                                                <td><?= $p['jumlah_pinjam_awal']; ?></td>
                                                <td><?= $p['total_dikembalikan']; ?></td>
                                                <td><?= $sisa_pinjam; ?></td>
                                                <td><?= $riwayat ?: '-'; ?></td>
                                                <td><span class="badge <?= $badge_class ?>"><?= $status ?></span></td>
                                                <td>
                                                    <?php
                                                    if ($p['status'] == 'dipinjam' && $p['hari_terlambat'] > 0) {
                                                        echo $p['hari_terlambat'] . ' hari';
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>

<script>
$(document).ready(function() {
    $('#tableLaporan').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data laporan"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[1, "desc"]], // Urutkan berdasarkan tanggal pinjam
        "buttons": [
            {
                extend: 'excel',
                text: 'Export Excel',
                className: 'btn btn-success',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6]
                }
            },
            {
                extend: 'pdf',
                text: 'Export PDF',
                className: 'btn btn-danger',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6]
                }
            }
        ]
    });
});
</script>
