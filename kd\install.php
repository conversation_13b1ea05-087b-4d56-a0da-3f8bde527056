<?php
/**
 * Installation script for Kompetensi Dasar module
 * Run this file once to create the necessary database table
 */

require_once __DIR__ . '/../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Installing Kompetensi Dasar Module</h2>";
    echo "<p>Creating database table...</p>";
    
    // Read SQL file
    $sql = file_get_contents(__DIR__ . '/kompetensi_dasar.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $conn->exec($statement);
                echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Error executing: " . substr($statement, 0, 50) . "...</p>";
                echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>Installation completed!</h3>";
    echo "<p>You can now access the Kompetensi Dasar module from the teacher menu.</p>";
    echo "<p><a href='index.php'>Go to Kompetensi Dasar Module</a></p>";
    echo "<p><a href='../'>Back to Dashboard</a></p>";
    
    // Optional: Delete this installation file for security
    echo "<hr>";
    echo "<p><strong>Security Note:</strong> Consider deleting this install.php file after installation.</p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Installation failed!</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
