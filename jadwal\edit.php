<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();
require_once '../template/header.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/JamPelajaranConfig.php';
require_once '../models/DetailJamPelajaran.php';
require_once '../models/DetailJadwalJam.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';

$jadwal = new JadwalPelajaran();
$kelas = new Kelas();
$mapel = new MataPelajaran();
$guru = new Guru();
$jamConfig = new JamPelajaranConfig();
$detailJam = new DetailJamPelajaran();
$detailJadwalJam = new DetailJadwalJam();
$periodeAktif = new PeriodeAktif();
$tingkat = new Tingkat();
$jurusan = new Jurusan();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit;
}

$jadwal->id = $_GET['id'];
if (!$jadwal->getOne()) {
    header("Location: index.php");
    exit;
}

// Get current jurusan
$current_jurusan = $jadwal->getJurusanIds($jadwal->id);

// Update cara mengambil daftar kelas
$kelas_list = !empty($current_jurusan) ? 
    $kelas->getByTingkatAndJurusan($jadwal->tingkat_id, $current_jurusan[0]) : 
    $kelas->getEmptyResult();

$mapel_list = !empty($current_jurusan) ? 
    $mapel->getByTingkatAndJurusan($jadwal->tingkat_id, $current_jurusan[0]) : 
    $mapel->getEmptyResult();

$tingkat_list = $tingkat->getAll();
$jurusan_list = $jurusan->getAll();
$error_msg = "";
$conflict_warning = "";

// Get current jam pelajaran
$current_jam = $detailJadwalJam->getByJadwalId($jadwal->id);
$selected_jam = [];
while ($row = $current_jam->fetch(PDO::FETCH_ASSOC)) {
    $selected_jam[] = $row['jam_ke'];
}

$hari_list = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];

// Get current guru pengampu for the selected mapel
$guru_pengampu = $mapel->getGuruPengampuByMapelId($jadwal->mapel_id);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $jadwal->kelas_id = $_POST['kelas_id'];
    $jadwal->mapel_id = $_POST['mapel_id'];
    $jadwal->hari = $_POST['hari'];
    $jadwal->guru_id = $_POST['guru_id'];
    $jadwal->tingkat_id = $_POST['tingkat_id'];
    $jadwal->semester = $periodeAktif->getCurrentSemester();
    $jadwal->tahun_ajaran = $periodeAktif->getCurrentTahunAjaran();
    
    // Get selected jurusan
    $jurusan_id = isset($_POST['jurusan_ids']) ? $_POST['jurusan_ids'] : '';
    
    // Get selected jam pelajaran
    $selected_jam = isset($_POST['jam_ke']) ? $_POST['jam_ke'] : [];
    
    if (empty($selected_jam)) {
        $error_msg = "Pilih minimal satu jam pelajaran";
    } else {
        try {
            // Get the time details from configuration
            $config = $jamConfig->getByHari($jadwal->hari);
            if ($config) {
                $details = $detailJam->getByConfigId($config['id']);
                $time_details = [];
                while ($detail = $details->fetch(PDO::FETCH_ASSOC)) {
                    if (in_array($detail['jam_ke'], $selected_jam)) {
                        $time_details[] = $detail;
                    }
                }
                
                // Set first time slot for jadwal_pelajaran table
                if (!empty($time_details)) {
                    $jadwal->jam_mulai = $time_details[0]['jam_mulai'];
                    $jadwal->jam_selesai = end($time_details)['jam_selesai'];
                }
                
                // Check for conflicts
                $conflicts = $jadwal->checkConflicts($jadwal->id, $jadwal->hari, $jadwal->guru_id, $selected_jam);
                
                if (!empty($conflicts)) {
                    $conflict_warning = "<div class='alert alert-warning'><strong>Peringatan!</strong> Jadwal ini bertabrakan dengan:<ul>";
                    foreach ($conflicts as $conflict) {
                        $conflict_warning .= "<li>{$conflict['mapel']} - {$conflict['kelas']} (Jam ke-{$conflict['jam_ke']})</li>";
                    }
                    $conflict_warning .= "</ul>Anda masih bisa menyimpan jadwal ini, tapi mohon pertimbangkan kembali.</div>";
                }

                if (isset($_POST['confirm_save']) || empty($conflicts)) {
                    // Start transaction
                    $conn = $jadwal->getConnection();
                    
                    try {
                        $conn->beginTransaction();
                        
                        // 1. Update jadwal
                        if (!$jadwal->update()) {
                            throw new Exception("Gagal update jadwal");
                        }
                        
                        // 2. Update jadwal_jurusan relations
                        $stmt = $conn->prepare("DELETE FROM jadwal_jurusan WHERE jadwal_id = ?");
                        if (!$stmt->execute([$jadwal->id])) {
                            throw new Exception("Gagal hapus jadwal_jurusan lama");
                        }
                        
                        if (!empty($jurusan_id)) {
                            $stmt = $conn->prepare("INSERT INTO jadwal_jurusan (jadwal_id, jurusan_id) VALUES (?, ?)");
                            if (!$stmt->execute([$jadwal->id, $jurusan_id])) {
                                throw new Exception("Gagal simpan jadwal_jurusan");
                            }
                        }
                        
                        // 3. Update detail jam pelajaran
                        $stmt = $conn->prepare("DELETE FROM detail_jadwal_jam WHERE jadwal_id = ?");
                        if (!$stmt->execute([$jadwal->id])) {
                            throw new Exception("Gagal hapus detail jam lama");
                        }
                        
                        if (!empty($time_details)) {
                            $values = array_fill(0, count($time_details), "(?, ?, ?, ?)");
                            $sql = "INSERT INTO detail_jadwal_jam (jadwal_id, jam_ke, jam_mulai, jam_selesai) VALUES " . implode(", ", $values);
                            $stmt = $conn->prepare($sql);
                            
                            $params = [];
                            foreach ($time_details as $time) {
                                $params[] = $jadwal->id;
                                $params[] = $time['jam_ke'];
                                $params[] = $time['jam_mulai'];
                                $params[] = $time['jam_selesai'];
                            }
                            
                            if (!$stmt->execute($params)) {
                                throw new Exception("Gagal simpan detail jam");
                            }
                        }

                        $conn->commit();
                        header("Location: index.php?success=1");
                        exit;
                        
                    } catch (Exception $e) {
                        $conn->rollBack();
                        $error_msg = "Gagal menyimpan jadwal: " . $e->getMessage();
                    }
                }
            }
        } catch (Exception $e) {
            $error_msg = "Gagal menyimpan jadwal: " . $e->getMessage();
        }
    }
}
?>

<style>
select[multiple] {
    height: 150px;
    padding: 8px;
}
</style>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Jadwal Pelajaran</h5>
            </div>
            <div class="card-body">
                <?php if ($error_msg): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_msg; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($conflict_warning): ?>
                    <?php echo $conflict_warning; ?>
                    <form method="post" class="mb-3">
                        <input type="hidden" name="kelas_id" value="<?php echo htmlspecialchars($jadwal->kelas_id); ?>">
                        <input type="hidden" name="mapel_id" value="<?php echo htmlspecialchars($jadwal->mapel_id); ?>">
                        <input type="hidden" name="hari" value="<?php echo htmlspecialchars($jadwal->hari); ?>">
                        <input type="hidden" name="guru_id" value="<?php echo htmlspecialchars($jadwal->guru_id); ?>">
                        <input type="hidden" name="tingkat_id" value="<?php echo htmlspecialchars($jadwal->tingkat_id); ?>">
                        <input type="hidden" name="jurusan_ids" value="<?php echo htmlspecialchars($_POST['jurusan_ids']); ?>">
                        <?php foreach ($selected_jam as $jam): ?>
                            <input type="hidden" name="jam_ke[]" value="<?php echo htmlspecialchars($jam); ?>">
                        <?php endforeach; ?>
                        <input type="hidden" name="confirm_save" value="1">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> Simpan Meskipun Bertabrakan
                        </button>
                        <a href="edit.php?id=<?php echo $jadwal->id; ?>" class="btn btn-secondary">Ubah Jadwal</a>
                    </form>
                <?php endif; ?>

                <form action="" method="post">
                    <div class="mb-3">
                        <label for="tingkat_id" class="form-label">Tingkat</label>
                        <select class="form-select" id="tingkat_id" name="tingkat_id" required>
                            <option value="">Pilih Tingkat</option>
                            <?php while ($row = $tingkat_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>" <?php echo $row['id'] == $jadwal->tingkat_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_tingkat']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="jurusan_ids" class="form-label">Jurusan</label>
                        <select class="form-select" id="jurusan_ids" name="jurusan_ids" required>
                            <option value="">Pilih Jurusan</option>
                            <?php while ($row = $jurusan_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>" 
                                    <?php echo (!empty($current_jurusan) && $row['id'] == $current_jurusan[0]) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_jurusan']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="kelas_id" class="form-label">Kelas</label>
                        <select class="form-select" id="kelas_id" name="kelas_id" required>
                            <option value="">Pilih Kelas</option>
                            <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>" <?php echo $row['id'] == $jadwal->kelas_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                        <select class="form-select" id="mapel_id" name="mapel_id" required onchange="loadGuruPengampu()">
                            <option value="">Pilih Mata Pelajaran</option>
                            <?php while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>" <?php echo $row['id'] == $jadwal->mapel_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_mapel']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="hari" class="form-label">Hari</label>
                        <select class="form-select" id="hari" name="hari" required onchange="loadJamPelajaran()">
                            <option value="">Pilih Hari</option>
                            <?php foreach ($hari_list as $hari): ?>
                                <option value="<?php echo $hari; ?>" <?php echo $hari == $jadwal->hari ? 'selected' : ''; ?>>
                                    <?php echo $hari; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Jam Pelajaran</label>
                        <div id="jamPelajaranContainer" class="row">
                            <!-- Checkboxes will be loaded here -->
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="guru_id" class="form-label">Guru Pengampu</label>
                        <select class="form-select" id="guru_id" name="guru_id" required>
                            <?php if (count($guru_pengampu) === 0): ?>
                                <option value="">Tidak ada guru pengampu untuk mata pelajaran ini</option>
                            <?php else: ?>
                                <?php if (count($guru_pengampu) > 1): ?>
                                    <option value="">Pilih Guru Pengampu</option>
                                <?php endif; ?>
                                <?php foreach ($guru_pengampu as $guru): ?>
                                    <option value="<?php echo $guru['id']; ?>" <?php echo $guru['id'] == $jadwal->guru_id ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($guru['nama_lengkap']); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small class="text-muted">Guru pengampu akan otomatis terisi sesuai dengan mata pelajaran yang dipilih</small>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Add selected jam array for pre-selecting time slots
const selected_jam = <?php echo json_encode($selected_jam); ?>;

document.getElementById('hari').addEventListener('change', loadJamPelajaran);

function loadJamPelajaran() {
    const hari = document.getElementById('hari').value;
    if (hari) {
        fetch(`get_config.php?hari=${hari}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('jamPelajaranContainer');
                container.innerHTML = '';
                if (data.details && data.details.length > 0) {
                    data.details.forEach(jam => {
                        const col = document.createElement('div');
                        col.className = 'col-md-4 mb-2';
                        const isChecked = selected_jam.includes(jam.jam_ke.toString()) || selected_jam.includes(parseInt(jam.jam_ke));
                        col.innerHTML = `
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="jam_ke[]" 
                                       value="${jam.jam_ke}" id="jam_${jam.jam_ke}"
                                       ${isChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="jam_${jam.jam_ke}">
                                    Jam ke-${jam.jam_ke} (${jam.jam_mulai}-${jam.jam_selesai})
                                </label>
                            </div>
                        `;
                        container.appendChild(col);
                    });
                }
            });
    }
}

// Load jam pelajaran on page load if hari is already selected
document.addEventListener('DOMContentLoaded', function() {
    const hari = document.getElementById('hari').value;
    if (hari) {
        loadJamPelajaran();
    }
});

function loadGuruPengampu() {
    const mapelId = document.getElementById('mapel_id').value;
    const guruSelect = document.getElementById('guru_id');
    
    if (mapelId) {
        fetch(`get_guru_pengampu.php?mapel_id=${mapelId}`)
            .then(response => response.json())
            .then(data => {
                guruSelect.innerHTML = '';
                if (data.length === 0) {
                    guruSelect.innerHTML = '<option value="">Tidak ada guru pengampu untuk mata pelajaran ini</option>';
                } else if (data.length === 1) {
                    // If only one teacher, auto-select them
                    const guru = data[0];
                    guruSelect.innerHTML = `<option value="${guru.id}" selected>${guru.nama_lengkap}</option>`;
                } else {
                    // If multiple teachers, let user choose
                    guruSelect.innerHTML = '<option value="">Pilih Guru Pengampu</option>';
                    data.forEach(guru => {
                        guruSelect.innerHTML += `<option value="${guru.id}">${guru.nama_lengkap}</option>`;
                    });
                }
                guruSelect.disabled = false;
            });
    } else {
        guruSelect.innerHTML = '<option value="">Pilih Mata Pelajaran Terlebih Dahulu</option>';
        guruSelect.disabled = true;
    }
}

// Function to update kelas and mapel based on tingkat and jurusan
function updateKelasAndMapel() {
    const tingkat_id = document.getElementById('tingkat_id').value;
    const jurusan_id = document.getElementById('jurusan_ids').value;
    const kelas_select = document.getElementById('kelas_id');
    const mapel_select = document.getElementById('mapel_id');

    if (tingkat_id && jurusan_id) {
        // Update kelas
        fetch(`get_kelas.php?tingkat_id=${tingkat_id}&jurusan_id=${jurusan_id}`)
            .then(response => response.json())
            .then(data => {
                kelas_select.innerHTML = '<option value="">Pilih Kelas</option>';
                data.forEach(kelas => {
                    const selected = kelas.id == <?php echo $jadwal->kelas_id; ?> ? 'selected' : '';
                    kelas_select.innerHTML += `<option value="${kelas.id}" ${selected}>${kelas.nama_kelas}</option>`;
                });
            });

        // Update mapel
        fetch(`get_mapel.php?tingkat_id=${tingkat_id}&jurusan_id=${jurusan_id}`)
            .then(response => response.json())
            .then(data => {
                mapel_select.innerHTML = '<option value="">Pilih Mata Pelajaran</option>';
                data.forEach(mapel => {
                    const selected = mapel.id == <?php echo $jadwal->mapel_id; ?> ? 'selected' : '';
                    mapel_select.innerHTML += `<option value="${mapel.id}" ${selected}>${mapel.nama_mapel}</option>`;
                });
                // Trigger guru pengampu update
                loadGuruPengampu();
            });
    }
}

// Add event listeners
document.getElementById('tingkat_id').addEventListener('change', updateKelasAndMapel);
document.getElementById('jurusan_ids').addEventListener('change', updateKelasAndMapel);

// Load kelas and mapel on page load
document.addEventListener('DOMContentLoaded', function() {
    updateKelasAndMapel();
});
</script>

<?php include '../template/footer.php'; ?>
