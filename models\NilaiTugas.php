<?php
class NilaiTugas {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function getRataTugas($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT AVG(nt.nilai) as rata_rata
                 FROM nilai_tugas nt
                 JOIN tugas t ON nt.tugas_id = t.id
                 WHERE nt.siswa_id = :siswa_id
                 AND t.mapel_id = :mapel_id
                 AND t.semester = :semester
                 AND t.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['rata_rata'];
    }

    public function getNilaiTugasBySiswa($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.judul, nt.nilai
                 FROM nilai_tugas nt
                 JOIN tugas t ON nt.tugas_id = t.id
                 WHERE nt.siswa_id = :siswa_id
                 AND t.mapel_id = :mapel_id
                 AND t.semester = :semester
                 AND t.tahun_ajaran = :tahun_ajaran
                 ORDER BY t.tanggal ASC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getTugasSubmissionStatus($siswa_id, $kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT
                    t.id,
                    t.judul,
                    t.tanggal,
                    t.mapel_id,
                    mp.nama_mapel,
                    CASE WHEN nt.nilai > 0 THEN 1 ELSE 0 END as submitted
                FROM tugas t
                JOIN mata_pelajaran mp ON t.mapel_id = mp.id
                LEFT JOIN nilai_tugas nt ON t.id = nt.tugas_id AND nt.siswa_id = :siswa_id
                WHERE t.kelas_id = :kelas_id
                AND t.semester = :semester
                AND t.tahun_ajaran = :tahun_ajaran
                ORDER BY mp.nama_mapel ASC, t.tanggal DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getNilaiTugasSiswa($siswa_id, $tugas_id) {
        $query = "SELECT nt.*, t.judul, t.tanggal
                 FROM nilai_tugas nt
                 JOIN tugas t ON nt.tugas_id = t.id
                 WHERE nt.siswa_id = :siswa_id
                 AND nt.tugas_id = :tugas_id";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tugas_id', $tugas_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
