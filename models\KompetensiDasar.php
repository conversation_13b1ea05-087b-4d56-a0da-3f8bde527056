<?php
require_once __DIR__ . '/../config/database.php';

class KompetensiDasar {
    private $conn;
    private $table_name = "kompetensi_dasar";

    public $id;
    public $kode_kd;
    public $deskripsi_kd;
    public $tema_subtema;
    public $materi_pokok;
    public $tujuan_pembelajaran;
    public $mapel_id;
    public $guru_id;
    public $kelas_id;
    public $semester;
    public $tahun_ajaran;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get all KD for a specific teacher
    public function getAllByGuruId($guru_id, $mapel_id = null, $kelas_id = null, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT kd.*, mp.nama_mapel, mp.kode_mapel, k.nama_kelas
                  FROM " . $this->table_name . " kd
                  LEFT JOIN mata_pelajaran mp ON kd.mapel_id = mp.id
                  LEFT JOIN kelas k ON kd.kelas_id = k.id
                  WHERE kd.guru_id = :guru_id";

        if ($mapel_id) {
            $query .= " AND kd.mapel_id = :mapel_id";
        }
        if ($kelas_id) {
            $query .= " AND kd.kelas_id = :kelas_id";
        }
        if ($semester) {
            $query .= " AND kd.semester = :semester";
        }
        if ($tahun_ajaran) {
            $query .= " AND kd.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY mp.nama_mapel, k.nama_kelas, kd.semester, kd.kode_kd";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);

        if ($mapel_id) {
            $stmt->bindParam(":mapel_id", $mapel_id);
        }
        if ($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }
        if ($semester) {
            $stmt->bindParam(":semester", $semester);
        }
        if ($tahun_ajaran) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt;
    }

    // Get KD by ID
    public function getById($id) {
        $query = "SELECT kd.*, mp.nama_mapel, mp.kode_mapel, k.nama_kelas
                  FROM " . $this->table_name . " kd
                  LEFT JOIN mata_pelajaran mp ON kd.mapel_id = mp.id
                  LEFT JOIN kelas k ON kd.kelas_id = k.id
                  WHERE kd.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->id = $row['id'];
            $this->kode_kd = $row['kode_kd'];
            $this->deskripsi_kd = $row['deskripsi_kd'];
            $this->tema_subtema = $row['tema_subtema'];
            $this->materi_pokok = $row['materi_pokok'];
            $this->tujuan_pembelajaran = $row['tujuan_pembelajaran'];
            $this->mapel_id = $row['mapel_id'];
            $this->guru_id = $row['guru_id'];
            $this->kelas_id = $row['kelas_id'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return $row;
        }
        return false;
    }

    // Create new KD
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  (kode_kd, deskripsi_kd, tema_subtema, materi_pokok, tujuan_pembelajaran, mapel_id, guru_id, kelas_id, semester, tahun_ajaran)
                  VALUES (:kode_kd, :deskripsi_kd, :tema_subtema, :materi_pokok, :tujuan_pembelajaran, :mapel_id, :guru_id, :kelas_id, :semester, :tahun_ajaran)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->kode_kd = htmlspecialchars(strip_tags($this->kode_kd));
        $this->deskripsi_kd = htmlspecialchars(strip_tags($this->deskripsi_kd));
        $this->tema_subtema = htmlspecialchars(strip_tags($this->tema_subtema));
        $this->materi_pokok = htmlspecialchars(strip_tags($this->materi_pokok));
        $this->tujuan_pembelajaran = htmlspecialchars(strip_tags($this->tujuan_pembelajaran));

        // Bind parameters
        $stmt->bindParam(":kode_kd", $this->kode_kd);
        $stmt->bindParam(":deskripsi_kd", $this->deskripsi_kd);
        $stmt->bindParam(":tema_subtema", $this->tema_subtema);
        $stmt->bindParam(":materi_pokok", $this->materi_pokok);
        $stmt->bindParam(":tujuan_pembelajaran", $this->tujuan_pembelajaran);
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);

        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    // Update KD
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET kode_kd = :kode_kd,
                      deskripsi_kd = :deskripsi_kd,
                      tema_subtema = :tema_subtema,
                      materi_pokok = :materi_pokok,
                      tujuan_pembelajaran = :tujuan_pembelajaran,
                      mapel_id = :mapel_id,
                      kelas_id = :kelas_id,
                      semester = :semester,
                      tahun_ajaran = :tahun_ajaran
                  WHERE id = :id AND guru_id = :guru_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->kode_kd = htmlspecialchars(strip_tags($this->kode_kd));
        $this->deskripsi_kd = htmlspecialchars(strip_tags($this->deskripsi_kd));
        $this->tema_subtema = htmlspecialchars(strip_tags($this->tema_subtema));
        $this->materi_pokok = htmlspecialchars(strip_tags($this->materi_pokok));
        $this->tujuan_pembelajaran = htmlspecialchars(strip_tags($this->tujuan_pembelajaran));

        // Bind parameters
        $stmt->bindParam(":kode_kd", $this->kode_kd);
        $stmt->bindParam(":deskripsi_kd", $this->deskripsi_kd);
        $stmt->bindParam(":tema_subtema", $this->tema_subtema);
        $stmt->bindParam(":materi_pokok", $this->materi_pokok);
        $stmt->bindParam(":tujuan_pembelajaran", $this->tujuan_pembelajaran);
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":guru_id", $this->guru_id);

        return $stmt->execute();
    }

    // Delete KD
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id AND guru_id = :guru_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":guru_id", $this->guru_id);

        return $stmt->execute();
    }

    // Check if KD code exists for the same subject, class, semester, and academic year
    public function isKodeExists($kode_kd, $mapel_id, $kelas_id, $semester, $tahun_ajaran, $exclude_id = null) {
        $query = "SELECT id FROM " . $this->table_name . "
                  WHERE kode_kd = :kode_kd
                  AND mapel_id = :mapel_id
                  AND semester = :semester
                  AND tahun_ajaran = :tahun_ajaran";

        // Include kelas_id in uniqueness check (can be NULL)
        if ($kelas_id) {
            $query .= " AND kelas_id = :kelas_id";
        } else {
            $query .= " AND kelas_id IS NULL";
        }

        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kode_kd", $kode_kd);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);

        if ($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        if ($exclude_id) {
            $stmt->bindParam(":exclude_id", $exclude_id);
        }

        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    // Get count of KD for a teacher
    public function getCountByGuruId($guru_id) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE guru_id = :guru_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    // Get KD by subject for dropdown/selection
    public function getByMapelId($mapel_id, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE mapel_id = :mapel_id";
        
        if ($semester) {
            $query .= " AND semester = :semester";
        }
        if ($tahun_ajaran) {
            $query .= " AND tahun_ajaran = :tahun_ajaran";
        }
        
        $query .= " ORDER BY kode_kd";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        
        if ($semester) {
            $stmt->bindParam(":semester", $semester);
        }
        if ($tahun_ajaran) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt;
    }

    // Get classes for a teacher based on their subject assignments
    public function getKelasByGuruId($guru_id, $mapel_id = null) {
        // Try jadwal_pelajaran first (schedule-based)
        $query = "SELECT DISTINCT k.id, k.nama_kelas, k.tingkat_id, k.jurusan_id
                  FROM kelas k
                  INNER JOIN jadwal_pelajaran jp ON k.id = jp.kelas_id
                  WHERE jp.guru_id = :guru_id";

        if ($mapel_id) {
            $query .= " AND jp.mapel_id = :mapel_id";
        }

        $query .= " ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);

        if ($mapel_id) {
            $stmt->bindParam(':mapel_id', $mapel_id);
        }

        $stmt->execute();
        $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no classes found via schedule, try to get all classes for subjects assigned via mapel_guru
        if (empty($classes) && !$mapel_id) {
            $query = "SELECT DISTINCT k.id, k.nama_kelas, k.tingkat_id, k.jurusan_id
                      FROM kelas k
                      INNER JOIN jadwal_pelajaran jp ON k.id = jp.kelas_id
                      INNER JOIN mapel_guru mg ON jp.mapel_id = mg.mapel_id
                      WHERE mg.guru_id = :guru_id
                      ORDER BY k.nama_kelas ASC";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':guru_id', $guru_id);
            $stmt->execute();
            $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        return $classes;
    }

    // Get classes for a specific subject and teacher
    public function getKelasByMapelAndGuru($mapel_id, $guru_id) {
        $query = "SELECT DISTINCT k.id, k.nama_kelas, k.tingkat_id, k.jurusan_id
                  FROM kelas k
                  INNER JOIN jadwal_pelajaran jp ON k.id = jp.kelas_id
                  WHERE jp.mapel_id = :mapel_id AND jp.guru_id = :guru_id
                  ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get KD data for RPP auto-population based on subject, class, and teacher
    public function getKdForRpp($mapel_id, $kelas_id, $guru_id, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT kd.id, kd.kode_kd, kd.deskripsi_kd, kd.tema_subtema, kd.materi_pokok, kd.tujuan_pembelajaran, kd.guru_id
                  FROM " . $this->table_name . " kd
                  WHERE kd.mapel_id = :mapel_id
                  AND kd.guru_id = :guru_id";

        // Add kelas_id condition - handle both specific class and NULL (general KD)
        $query .= " AND (kd.kelas_id = :kelas_id OR kd.kelas_id IS NULL)";

        if ($semester) {
            $query .= " AND kd.semester = :semester";
        }
        if ($tahun_ajaran) {
            $query .= " AND kd.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY kd.kode_kd ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':guru_id', $guru_id);

        if ($semester) {
            $stmt->bindParam(':semester', $semester);
        }
        if ($tahun_ajaran) {
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get KD options for dropdown selection in RPP
    public function getKdOptionsForRpp($mapel_id, $kelas_id, $guru_id, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT kd.id, kd.kode_kd, kd.deskripsi_kd, kd.tema_subtema, kd.materi_pokok
                  FROM " . $this->table_name . " kd
                  WHERE kd.mapel_id = :mapel_id
                  AND kd.guru_id = :guru_id";

        // Add kelas_id condition - handle both specific class and NULL (general KD)
        $query .= " AND (kd.kelas_id = :kelas_id OR kd.kelas_id IS NULL)";

        if ($semester) {
            $query .= " AND kd.semester = :semester";
        }
        if ($tahun_ajaran) {
            $query .= " AND kd.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY kd.kode_kd ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':guru_id', $guru_id);

        if ($semester) {
            $stmt->bindParam(':semester', $semester);
        }
        if ($tahun_ajaran) {
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get KD data based on tema/subtema and materi pokok for RPP auto-population
    public function getKdByTemaMateri($mapel_id, $kelas_id, $guru_id, $tema_subtema = null, $materi_pokok = null, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT kd.id, kd.kode_kd, kd.deskripsi_kd, kd.tema_subtema, kd.materi_pokok, kd.tujuan_pembelajaran
                  FROM " . $this->table_name . " kd
                  WHERE kd.mapel_id = :mapel_id
                  AND kd.guru_id = :guru_id";

        // Add kelas_id condition - handle both specific class and NULL (general KD)
        $query .= " AND (kd.kelas_id = :kelas_id OR kd.kelas_id IS NULL)";

        // Add tema/subtema condition if provided
        if ($tema_subtema) {
            $query .= " AND kd.tema_subtema = :tema_subtema";
        }

        // Add materi pokok condition if provided
        if ($materi_pokok) {
            $query .= " AND kd.materi_pokok = :materi_pokok";
        }

        if ($semester) {
            $query .= " AND kd.semester = :semester";
        }
        if ($tahun_ajaran) {
            $query .= " AND kd.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY kd.kode_kd ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':guru_id', $guru_id);

        if ($tema_subtema) {
            $stmt->bindParam(':tema_subtema', $tema_subtema);
        }
        if ($materi_pokok) {
            $stmt->bindParam(':materi_pokok', $materi_pokok);
        }
        if ($semester) {
            $stmt->bindParam(':semester', $semester);
        }
        if ($tahun_ajaran) {
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Format KD data for RPP field
    public function formatKdForRpp($kd_data) {
        if (empty($kd_data)) {
            return '';
        }

        $formatted_kd = '';
        foreach ($kd_data as $kd) {
            $formatted_kd .= $kd['kode_kd'] . ' ' . $kd['deskripsi_kd'] . "\n";
        }

        return trim($formatted_kd);
    }
}
?>
