<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

$perpustakaan = new Perpustakaan();
$peminjaman = $perpustakaan->getAllPeminjaman();
$buku = $perpustakaan->getAllBuku();
$anggota = $perpustakaan->getAllAnggota();
$config = $perpustakaan->getKonfigurasi();

// Proses form jika ada POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['tambah'])) {
        $data = [
            'id_anggota' => $_POST['id_anggota'],
            'tipe_anggota' => $_POST['tipe_anggota'],
            'id_buku' => $_POST['id_buku'],
            'jumlah_buku' => $_POST['jumlah_buku'],
            'tanggal_pinjam' => $_POST['tanggal_pinjam'],
            'tanggal_kembali' => $_POST['tanggal_kembali']
        ];

        try {
            // Cek ketersediaan buku
            $buku_dipilih = $perpustakaan->getBukuById($data['id_buku']);
            if ($buku_dipilih['jumlah_buku'] < $data['jumlah_buku']) {
                throw new Exception("Stok buku tidak mencukupi");
            }

            // Cek maksimal peminjaman berdasarkan tipe anggota
            $max_pinjam = $data['tipe_anggota'] == 'siswa' ? 
                         $config['max_pinjam_siswa'] : 
                         $config['max_pinjam_guru'];
            
            if ($data['jumlah_buku'] > $max_pinjam) {
                throw new Exception("Jumlah buku melebihi batas maksimal peminjaman (" . $max_pinjam . " buku)");
            }

            // Kurangi stok buku
            $update_stok = [
                'judul_buku' => $buku_dipilih['judul_buku'],
                'id_kategori' => $buku_dipilih['id_kategori'],
                'pengarang' => $buku_dipilih['pengarang'],
                'penerbit' => $buku_dipilih['penerbit'],
                'tahun_terbit' => $buku_dipilih['tahun_terbit'],
                'isbn' => $buku_dipilih['isbn'],
                'jumlah_buku' => $buku_dipilih['jumlah_buku'] - $data['jumlah_buku'],
                'lokasi' => $buku_dipilih['lokasi']
            ];

            if ($perpustakaan->editBuku($data['id_buku'], $update_stok) && $perpustakaan->tambahPeminjaman($data)) {
                $_SESSION['success'] = "Peminjaman berhasil ditambahkan";
            } else {
                throw new Exception("Gagal menambahkan peminjaman");
            }
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: peminjaman.php");
        exit();
    }
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Peminjaman Buku</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Peminjaman</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Form Peminjaman</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="tipe_anggota_select">Tipe Anggota</label>
                                            <select class="form-control" id="tipe_anggota_select" required>
                                                <option value="">Pilih Tipe Anggota</option>
                                                <option value="siswa">Siswa</option>
                                                <option value="guru">Guru</option>
                                            </select>
                                        </div>
                                        <div class="form-group mb-3">
                                            <label for="id_anggota">Anggota</label>
                                            <select class="form-control select2" id="id_anggota" name="id_anggota" required>
                                                <option value="">Pilih Anggota</option>
                                                <?php foreach ($anggota as $a) : ?>
                                                    <option value="<?= $a['id_anggota']; ?>" 
                                                            data-tipe="<?= $a['tipe_anggota']; ?>" 
                                                            class="anggota-option <?= $a['tipe_anggota']; ?>"
                                                            style="display: none;">
                                                        <?= htmlspecialchars($a['nomor_induk'] . ' - ' . $a['nama_anggota']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <input type="hidden" name="tipe_anggota" id="tipe_anggota">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="id_buku">Buku</label>
                                            <select class="form-control select2" id="id_buku" name="id_buku" required>
                                                <option value="">Pilih Buku</option>
                                                <?php foreach ($buku as $b) : ?>
                                                    <?php if ($b['jumlah_buku'] > 0) : ?>
                                                        <option value="<?= $b['id_buku']; ?>" data-stok="<?= $b['jumlah_buku']; ?>">
                                                            <?= htmlspecialchars($b['judul_buku'] . ' (Stok: ' . $b['jumlah_buku'] . ')'); ?>
                                                        </option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="form-group mb-3">
                                            <label for="jumlah_buku">Jumlah Buku</label>
                                            <input type="number" class="form-control" id="jumlah_buku" name="jumlah_buku" min="1" value="1" required>
                                            <small class="form-text text-muted">
                                                Maksimal peminjaman: 
                                                Siswa (<?= $config['max_pinjam_siswa'] ?> buku), 
                                                Guru (<?= $config['max_pinjam_guru'] ?> buku)
                                            </small>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="tanggal_pinjam">Tanggal Pinjam</label>
                                                    <input type="date" class="form-control" id="tanggal_pinjam" name="tanggal_pinjam" value="<?= date('Y-m-d'); ?>" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="tanggal_kembali">Tanggal Kembali</label>
                                                    <input type="date" class="form-control" id="tanggal_kembali" name="tanggal_kembali" value="<?= date('Y-m-d', strtotime('+7 days')); ?>" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <button type="submit" name="tambah" class="btn btn-primary float-end">Simpan</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Daftar Peminjaman</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="tablePeminjaman" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Peminjam</th>
                                    <th>Buku</th>
                                    <th>Jumlah</th>
                                    <th>Tanggal Pinjam</th>
                                    <th>Tanggal Kembali</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                foreach ($peminjaman as $p) :
                                ?>
                                    <tr>
                                        <td><?= $no++; ?></td>
                                        <td><?= htmlspecialchars($p['nama_peminjam']); ?></td>
                                        <td><?= htmlspecialchars($p['judul_buku']); ?></td>
                                        <td><?= $p['jumlah_buku']; ?></td>
                                        <td><?= date('d/m/Y', strtotime($p['tanggal_pinjam'])); ?></td>
                                        <td><?= date('d/m/Y', strtotime($p['tanggal_kembali'])); ?></td>
                                        <td>
                                            <?php if ($p['status'] == 'dipinjam') : ?>
                                                <span class="badge bg-warning">Dipinjam</span>
                                            <?php else : ?>
                                                <span class="badge bg-success">Dikembalikan</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Inisialisasi Select2 dengan fitur pencarian
    $('#id_anggota').select2({
        width: '100%',
        theme: 'bootstrap4',
        templateResult: function(data) {
            if (!data.element) {
                return data.text;
            }
            var $element = $(data.element);
            var $wrapper = $('<span></span>');
            $wrapper.addClass($element.attr('class'));
            $wrapper.text(data.text);
            return $wrapper;
        }
    });

    $('#id_buku').select2({
        width: '100%',
        theme: 'bootstrap4'
    });

    // Filter anggota berdasarkan tipe
    $('#tipe_anggota_select').change(function() {
        var selectedType = $(this).val();
        var anggotaSelect = $('#id_anggota');
        
        // Reset pilihan anggota
        anggotaSelect.val('').trigger('change');
        
        // Sembunyikan semua opsi
        anggotaSelect.find('option').each(function() {
            var $option = $(this);
            if ($option.val() === '') return; // Skip placeholder option
            
            if (!selectedType || $option.data('tipe') === selectedType) {
                $option.prop('disabled', false);
                $option.show();
            } else {
                $option.prop('disabled', true);
                $option.hide();
            }
        });
        
        // Refresh Select2
        anggotaSelect.select2('destroy');
        anggotaSelect.select2({
            width: '100%',
            theme: 'bootstrap4',
            templateResult: function(data) {
                if (!data.element) {
                    return data.text;
                }
                var $element = $(data.element);
                var $wrapper = $('<span></span>');
                $wrapper.addClass($element.attr('class'));
                $wrapper.text(data.text);
                return $wrapper;
            }
        });
    });

    // Set tipe_anggota hidden input saat memilih anggota
    $('#id_anggota').change(function() {
        var selectedOption = $(this).find('option:selected');
        $('#tipe_anggota').val(selectedOption.data('tipe'));
    });

    // Trigger initial hide saat halaman dimuat
    $('#tipe_anggota_select').trigger('change');

    // Set minimum date untuk tanggal kembali
    $('#tanggal_pinjam').change(function() {
        var minDate = $(this).val();
        if ($('#tanggal_kembali').val() < minDate) {
            $('#tanggal_kembali').val(minDate);
        }
    });

    // Update maksimal jumlah buku berdasarkan tipe anggota dan stok
    function updateMaxJumlahBuku() {
        var selectedType = $('#tipe_anggota').val();
        var selectedBook = $('#id_buku option:selected');
        var stokBuku = selectedBook.data('stok') || 0;
        var maxPinjam = selectedType === 'siswa' ? <?= $config['max_pinjam_siswa'] ?> : <?= $config['max_pinjam_guru'] ?>;
        
        var maxJumlah = Math.min(stokBuku, maxPinjam);
        
        $('#jumlah_buku').attr('max', maxJumlah);
        
        // Sesuaikan nilai jika melebihi maksimal
        var currentValue = $('#jumlah_buku').val();
        if (currentValue > maxJumlah) {
            $('#jumlah_buku').val(maxJumlah);
        }
    }

    // Update maksimal saat tipe anggota atau buku berubah
    $('#id_anggota, #id_buku').change(function() {
        updateMaxJumlahBuku();
    });

    // Inisialisasi maksimal jumlah buku
    updateMaxJumlahBuku();

    // Inisialisasi DataTable
    $('#tablePeminjaman').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data peminjaman"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[4, "desc"]] // Urutkan berdasarkan tanggal pinjam
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
