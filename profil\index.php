<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../index.php");
    exit();
}

$user = new User();
$user->id = $_SESSION['user_id'];
$user->getOne();

$guru = new Guru();
$guru_id = $user->getGuruId($_SESSION['user_id']);
$guru->id = $guru_id;
$guru->getOne();

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_profile'])) {
        $guru->nama_lengkap = $_POST['nama_lengkap'];
        $guru->jenis_kelamin = $_POST['jenis_kelamin'];
        $guru->alamat = $_POST['alamat'];
        $guru->no_telp = $_POST['no_telp'];
        $guru->email = $_POST['email'];
        
        if ($guru->update()) {
            // Update user's nama_lengkap as well
            $user->nama_lengkap = $_POST['nama_lengkap'];
            $user->update();
            $message = "Profil berhasil diperbarui!";
        } else {
            $error = "Gagal memperbarui profil!";
        }
    }
}
?>

<div class="container">
    <h2 class="mb-4">Profil Guru</h2>

    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Akun</h5>
                </div>
                <div class="card-body">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user->username); ?></p>
                    <p><strong>Role:</strong> Guru</p>
                    <div class="d-grid gap-2">
                        <a href="ubah_username.php" class="btn btn-primary">Ubah Username</a>
                        <a href="ubah_password.php" class="btn btn-warning">Ubah Password</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Profil</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="nip" class="form-label">NIP</label>
                            <input type="text" class="form-control" id="nip" value="<?php echo htmlspecialchars($guru->nip); ?>" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="nama_lengkap" class="form-label">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" value="<?php echo htmlspecialchars($guru->nama_lengkap); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="jenis_kelamin" class="form-label">Jenis Kelamin</label>
                            <select class="form-select" id="jenis_kelamin" name="jenis_kelamin" required>
                                <option value="L" <?php echo $guru->jenis_kelamin == 'L' ? 'selected' : ''; ?>>Laki-laki</option>
                                <option value="P" <?php echo $guru->jenis_kelamin == 'P' ? 'selected' : ''; ?>>Perempuan</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="alamat" class="form-label">Alamat</label>
                            <textarea class="form-control" id="alamat" name="alamat" rows="3"><?php echo htmlspecialchars($guru->alamat); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="no_telp" class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp" value="<?php echo htmlspecialchars($guru->no_telp); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($guru->email); ?>">
                        </div>

                        <button type="submit" name="update_profile" class="btn btn-primary">Simpan Perubahan</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
