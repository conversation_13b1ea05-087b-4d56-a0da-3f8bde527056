<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/GeminiApi.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';

header('Content-Type: application/json');

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
    exit();
}

// Validasi input
$required_fields = ['tema_subtema', 'materi_pokok'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
        echo json_encode(['success' => false, 'message' => "Field '$field' wajib diisi"]);
        exit();
    }
}

$tema_subtema = trim($_POST['tema_subtema']);
$materi_pokok = trim($_POST['materi_pokok']);
$mapel_id = isset($_POST['mapel_id']) ? $_POST['mapel_id'] : '';
$kelas_id = isset($_POST['kelas_id']) ? $_POST['kelas_id'] : '';
$kompetensi_dasar = isset($_POST['kompetensi_dasar']) ? trim($_POST['kompetensi_dasar']) : '';
$kegiatan_count = isset($_POST['kegiatan_count']) ? intval($_POST['kegiatan_count']) : 1;

// Ambil nama mata pelajaran dan kelas untuk konteks yang lebih baik
$mapel_name = '';
$kelas_name = '';

if (!empty($mapel_id)) {
    try {
        $mapel = new MataPelajaran();
        $mapel->id = $mapel_id;
        $mapel_data = $mapel->getOne();
        if ($mapel_data) {
            $mapel_name = $mapel_data['nama_mapel'];
        }
    } catch (Exception $e) {
        // Ignore error, continue without mapel name
    }
}

if (!empty($kelas_id)) {
    try {
        $kelas = new Kelas();
        $kelas_data = $kelas->getById($kelas_id);
        if ($kelas_data) {
            $kelas_name = $kelas_data['nama_kelas'];
        }
    } catch (Exception $e) {
        // Ignore error, continue without kelas name
    }
}

try {
    // Log the request for debugging
    error_log("[RPP Generation] Starting generation for tema: $tema_subtema, materi: $materi_pokok");

    $geminiApi = new GeminiApi();
    $rpp_content = $geminiApi->generateRppContent($tema_subtema, $materi_pokok, $mapel_name, $kelas_name, $kompetensi_dasar, $kegiatan_count);

    error_log("[RPP Generation] Successfully generated RPP content");

    echo json_encode([
        'success' => true,
        'message' => 'RPP berhasil di-generate',
        'data' => $rpp_content
    ]);

} catch (Exception $e) {
    // Log the error for debugging
    error_log("[RPP Generation Error] " . $e->getMessage());
    error_log("[RPP Generation Error] Stack trace: " . $e->getTraceAsString());

    // Provide more user-friendly error messages
    $error_message = $e->getMessage();

    // Check for specific error types and provide better messages
    if (strpos($error_message, 'cURL Error') !== false || strpos($error_message, 'Koneksi ke API Gemini gagal') !== false) {
        $user_message = 'Tidak dapat terhubung ke server AI. Periksa koneksi internet Anda dan coba lagi.';
    } elseif (strpos($error_message, 'API key tidak ditemukan') !== false) {
        $user_message = 'Konfigurasi API key tidak ditemukan. Hubungi administrator sistem.';
    } elseif (strpos($error_message, 'HTTP 429') !== false || strpos($error_message, 'quota') !== false) {
        $user_message = 'Layanan AI sedang sibuk. Silakan tunggu beberapa menit dan coba lagi.';
    } elseif (strpos($error_message, 'HTTP 500') !== false || strpos($error_message, 'HTTP 502') !== false || strpos($error_message, 'HTTP 503') !== false) {
        $user_message = 'Server AI sedang mengalami gangguan. Silakan coba lagi dalam beberapa menit.';
    } elseif (strpos($error_message, 'timeout') !== false) {
        $user_message = 'Permintaan memakan waktu terlalu lama. Coba dengan materi yang lebih sederhana atau coba lagi nanti.';
    } else {
        $user_message = 'Terjadi kesalahan saat menghasilkan RPP: ' . $error_message;
    }

    echo json_encode([
        'success' => false,
        'message' => $user_message,
        'debug_message' => $error_message // Include original error for debugging
    ]);
}
?>
