<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();

// Handle AJAX request for blueprint generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_blueprint') {
    header('Content-Type: application/json');

    try {
        require_once '../config/database.php';
        require_once '../models/MultiRppExam.php';
        require_once '../models/GeminiApi.php';
        require_once '../models/Guru.php';

        $exam_id = $_POST['exam_id'] ?? '';
        if (empty($exam_id)) {
            throw new Exception("ID ujian tidak ditemukan.");
        }

        // Get guru_id
        $guru = new Guru();
        $stmt = $guru->getByUserId($_SESSION['user_id']);
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $guru_id = $row['id'];
        } else {
            throw new Exception("Data guru tidak ditemukan");
        }

        // Get exam data
        $multiRppExam = new MultiRppExam();
        $exam_data = $multiRppExam->getOne($exam_id);

        if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
            throw new Exception("Ujian tidak ditemukan atau bukan milik Anda.");
        }

        // Get RPP data
        $selected_rpp_data = $multiRppExam->getSelectedRppData($exam_id);
        $rpp_data_array = [];
        while ($rpp = $selected_rpp_data->fetch(PDO::FETCH_ASSOC)) {
            $rpp_data_array[] = $rpp;
        }

        // Get questions data
        $questions_stmt = $multiRppExam->getQuestionsByExamId($exam_id);
        $questions_data = [];
        while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
            $questions_data[] = $question;
        }

        if (empty($questions_data)) {
            throw new Exception("Tidak ada soal untuk membuat kisi-kisi. Silakan generate soal terlebih dahulu.");
        }

        // Generate blueprint
        $geminiApi = new GeminiApi();

        $config = [
            'exam_id' => $exam_id,
            'blueprint_title' => $_POST['blueprint_title'] ?? $exam_data['exam_title'] . ' - Kisi-kisi',
            'include_objectives' => isset($_POST['include_objectives']),
            'include_distribution' => isset($_POST['include_distribution']),
            'include_cognitive_mapping' => isset($_POST['include_cognitive_mapping']),
            'include_difficulty_analysis' => isset($_POST['include_difficulty_analysis'])
        ];

        $generated_blueprint = $geminiApi->generateMultiRppBlueprint($exam_data, $rpp_data_array, $questions_data, $config);

        echo json_encode([
            'success' => true,
            'blueprint' => $generated_blueprint,
            'message' => 'Kisi-kisi berhasil di-generate'
        ]);

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Multi-RPP Blueprint Generation Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'debug_info' => [
                'file' => basename($e->getFile()),
                'line' => $e->getLine(),
                'exam_id' => $_POST['exam_id'] ?? 'not provided'
            ]
        ]);
    }
    exit();
}

require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Guru.php';

// Validasi parameter
if (!isset($_GET['exam_id'])) {
    $_SESSION['error'] = "ID ujian tidak ditemukan.";
    header("Location: multi_rpp_list.php");
    exit();
}

$exam_id = $_GET['exam_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data ujian multi-RPP
$multiRppExam = new MultiRppExam();
$exam_data = $multiRppExam->getOne($exam_id);

if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    header("Location: multi_rpp_list.php");
    exit();
}

// Ambil data RPP yang terkait
$selected_rpp_data = $multiRppExam->getSelectedRppData($exam_id);
$rpp_data_array = [];
while ($rpp = $selected_rpp_data->fetch(PDO::FETCH_ASSOC)) {
    $rpp_data_array[] = $rpp;
}

// Ambil soal-soal ujian
$questions_stmt = $multiRppExam->getQuestionsByExamId($exam_id);
$questions_data = [];
while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
    $questions_data[] = $question;
}

if (empty($questions_data)) {
    $_SESSION['error'] = "Tidak ada soal untuk membuat kisi-kisi. Silakan generate soal terlebih dahulu.";
    header("Location: multi_rpp_detail.php?exam_id=" . $exam_id);
    exit();
}

// Check if blueprint already exists
$examBlueprint = new ExamBlueprint();
$existing_blueprint_stmt = $examBlueprint->getByMultiExamId($exam_id);
$existing_blueprint = $existing_blueprint_stmt->fetch(PDO::FETCH_ASSOC);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
$generation_error = ''; // Initialize the generation error variable
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-alt"></i> Generate Kisi-kisi Multi-RPP
            </h5>
            <div>
                <a href="multi_rpp_detail.php?exam_id=<?= $exam_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Detail
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($generation_error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error Generate Kisi-kisi</h6>
                    <p class="mb-0"><?= htmlspecialchars($generation_error) ?></p>
                </div>
            <?php endif; ?>

            <!-- Loading State (Hidden by default) -->
            <div id="loadingState" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 class="mt-3">Generating Blueprint...</h5>
                <p class="text-muted">AI sedang membuat kisi-kisi ujian dari <?= count($rpp_data_array) ?> RPP dan <?= count($questions_data) ?> soal. Mohon tunggu...</p>
                <div class="progress mt-3" style="max-width: 400px; margin: 0 auto;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                </div>
            </div>

            <!-- Generated Blueprint Display (Hidden by default) -->
            <div id="generatedBlueprintSection" style="display: none;">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Kisi-kisi Berhasil Di-generate</h6>
                    <p class="mb-0">Kisi-kisi ujian multi-RPP telah berhasil dibuat. Silakan review dan simpan jika sudah sesuai.</p>
                </div>

                <form action="multi_blueprint_save.php" method="POST" id="saveBlueprintForm">
                    <input type="hidden" name="exam_id" value="<?= $exam_id ?>">
                    <input type="hidden" name="blueprint_data" id="blueprintDataInput">

                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Preview Kisi-kisi</h6>
                        </div>
                        <div class="card-body" id="blueprintPreviewContainer">
                            <!-- Blueprint preview will be loaded here -->
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Simpan Kisi-kisi
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetForm()">
                            <i class="fas fa-redo"></i> Generate Ulang
                        </button>
                    </div>
                </form>
            </div>

            <!-- Main Form (Visible by default) -->
                <!-- Exam Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Ujian</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td width="150"><strong>Judul Ujian:</strong></td>
                                        <td><?= htmlspecialchars($exam_data['exam_title']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Jenis Ujian:</strong></td>
                                        <td><?= htmlspecialchars($exam_data['exam_type']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Semester:</strong></td>
                                        <td><?= htmlspecialchars($exam_data['semester']) ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td width="150"><strong>Total RPP:</strong></td>
                                        <td><?= count($rpp_data_array) ?> RPP</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Soal:</strong></td>
                                        <td><?= count($questions_data) ?> soal</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Durasi:</strong></td>
                                        <td><?= htmlspecialchars($exam_data['exam_duration']) ?> menit</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Existing Blueprint Check -->
                <?php if ($existing_blueprint): ?>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Kisi-kisi Sudah Ada</h6>
                        <p class="mb-2">Kisi-kisi untuk ujian ini sudah pernah dibuat pada <?= date('d/m/Y H:i', strtotime($existing_blueprint['created_at'])) ?>.</p>
                        <div class="d-flex gap-2">
                            <a href="multi_blueprint_result.php?blueprint_id=<?= $existing_blueprint['id'] ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> Lihat Kisi-kisi
                            </a>
                            <a href="multi_blueprint_export.php?blueprint_id=<?= $existing_blueprint['id'] ?>&format=pdf" class="btn btn-sm btn-outline-danger" target="_blank">
                                <i class="fas fa-file-pdf"></i> Export PDF
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Blueprint Generation Form -->
                <div id="mainFormSection">
                <form method="POST" id="blueprintForm">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cog"></i> Konfigurasi Kisi-kisi</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="blueprint_title" class="form-label">Judul Kisi-kisi</label>
                                <input type="text" class="form-control" id="blueprint_title" name="blueprint_title" 
                                       value="<?= htmlspecialchars($exam_data['exam_title']) ?> - Kisi-kisi" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Komponen yang Disertakan:</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_objectives" name="include_objectives" checked>
                                    <label class="form-check-label" for="include_objectives">
                                        Tujuan Pembelajaran dari setiap RPP
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_distribution" name="include_distribution" checked>
                                    <label class="form-check-label" for="include_distribution">
                                        Distribusi soal per chapter/RPP
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_cognitive_mapping" name="include_cognitive_mapping" checked>
                                    <label class="form-check-label" for="include_cognitive_mapping">
                                        Pemetaan level kognitif (Bloom's Taxonomy)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_difficulty_analysis" name="include_difficulty_analysis" checked>
                                    <label class="form-check-label" for="include_difficulty_analysis">
                                        Analisis distribusi tingkat kesulitan
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" name="generate_blueprint" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Generate Kisi-kisi Multi-RPP
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and AJAX submission
    document.getElementById('blueprintForm')?.addEventListener('submit', function(e) {
        e.preventDefault(); // Prevent default form submission

        const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
            alert('Silakan pilih minimal satu komponen untuk disertakan dalam kisi-kisi.');
            return false;
        }

        // Show loading state
        showLoadingState();

        // Prepare form data
        const formData = new FormData(this);
        formData.append('action', 'generate_blueprint');
        formData.append('exam_id', '<?= $exam_id ?>');

        // Make AJAX request
        fetch('multi_blueprint_generate.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoadingState();

            if (data.success) {
                showGeneratedBlueprint(data.blueprint);
            } else {
                let errorMessage = data.error || 'Terjadi kesalahan saat generate kisi-kisi';

                // Add debug info if available
                if (data.debug_info) {
                    console.error('Debug Info:', data.debug_info);
                    errorMessage += ` (Debug: ${data.debug_info.file}:${data.debug_info.line})`;
                }

                showError(errorMessage);
            }
        })
        .catch(error => {
            hideLoadingState();
            console.error('AJAX Error:', error);
            showError(`Terjadi kesalahan koneksi: ${error.message}. Silakan coba lagi.`);
        });
    });
});

function showLoadingState() {
    document.getElementById('blueprintForm').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('generatedBlueprintSection').style.display = 'none';
}

function hideLoadingState() {
    document.getElementById('loadingState').style.display = 'none';
}

function showGeneratedBlueprint(blueprint) {
    // Store blueprint data
    document.getElementById('blueprintDataInput').value = JSON.stringify(blueprint);

    // Generate preview HTML
    const previewHtml = generateBlueprintPreview(blueprint);
    document.getElementById('blueprintPreviewContainer').innerHTML = previewHtml;

    // Show the generated section
    document.getElementById('generatedBlueprintSection').style.display = 'block';
}

function showError(message) {
    // Create error alert
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-danger alert-dismissible fade show';
    errorAlert.innerHTML = `
        <h6><i class="fas fa-exclamation-triangle"></i> Error Generate Kisi-kisi</h6>
        <p class="mb-0">${message}</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert error before the form
    const form = document.getElementById('blueprintForm');
    form.parentNode.insertBefore(errorAlert, form);

    // Show the form again
    form.style.display = 'block';
}

function resetForm() {
    document.getElementById('generatedBlueprintSection').style.display = 'none';
    document.getElementById('blueprintForm').style.display = 'block';

    // Remove any error alerts
    const alerts = document.querySelectorAll('.alert-danger');
    alerts.forEach(alert => alert.remove());
}

function generateBlueprintPreview(blueprint) {
    let html = '<div class="blueprint-preview">';

    // Exam Information
    if (blueprint.exam_info) {
        html += `
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5 class="text-center mb-3">${blueprint.exam_info.title || 'Kisi-kisi Ujian'}</h5>
                    <table class="table table-bordered">
                        <tr>
                            <td width="150"><strong>Mata Pelajaran</strong></td>
                            <td>${blueprint.exam_info.subject || ''}</td>
                            <td width="150"><strong>Jenis Ujian</strong></td>
                            <td>${blueprint.exam_info.type || ''}</td>
                        </tr>
                        <tr>
                            <td><strong>Durasi</strong></td>
                            <td>${blueprint.exam_info.duration || ''}</td>
                            <td><strong>Total Skor</strong></td>
                            <td>${blueprint.exam_info.total_score || ''}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
    }

    // Learning Objectives
    if (blueprint.learning_objectives && blueprint.learning_objectives.length > 0) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-bullseye"></i> Tujuan Pembelajaran</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Chapter</th>
                                <th>Tujuan Pembelajaran</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        blueprint.learning_objectives.forEach(objective => {
            html += `
                <tr>
                    <td><strong>${objective.chapter || ''}</strong></td>
                    <td>
            `;
            if (objective.objectives && Array.isArray(objective.objectives)) {
                html += '<ul class="mb-0">';
                objective.objectives.forEach(obj => {
                    html += `<li>${obj}</li>`;
                });
                html += '</ul>';
            }
            html += `
                    </td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Cognitive Mapping
    if (blueprint.cognitive_mapping) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-brain"></i> Pemetaan Level Kognitif</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Level</th>
                                <th>Jumlah Soal</th>
                                <th>Persentase</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        const cognitiveDescriptions = {
            'C1': 'Mengingat (Remember)',
            'C2': 'Memahami (Understand)',
            'C3': 'Menerapkan (Apply)',
            'C4': 'Menganalisis (Analyze)',
            'C5': 'Mengevaluasi (Evaluate)',
            'C6': 'Mencipta (Create)'
        };

        const totalQuestions = Object.values(blueprint.cognitive_mapping).reduce((sum, count) => sum + count, 0);

        Object.entries(blueprint.cognitive_mapping).forEach(([level, count]) => {
            if (count > 0) {
                const percentage = totalQuestions > 0 ? Math.round((count / totalQuestions) * 100 * 10) / 10 : 0;
                html += `
                    <tr>
                        <td><strong>${level}</strong> - ${cognitiveDescriptions[level] || level}</td>
                        <td class="text-center">${count}</td>
                        <td class="text-center">${percentage}%</td>
                    </tr>
                `;
            }
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Blueprint Table
    if (blueprint.blueprint_table && blueprint.blueprint_table.length > 0) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-table"></i> Tabel Kisi-kisi</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Chapter</th>
                                <th>Tujuan Pembelajaran</th>
                                <th>Indikator</th>
                                <th>Level Kognitif</th>
                                <th>No. Soal</th>
                                <th>Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        blueprint.blueprint_table.forEach(row => {
            html += `
                <tr>
                    <td><strong>${row.chapter || ''}</strong></td>
                    <td>${row.learning_objective || ''}</td>
                    <td>${row.indicator || ''}</td>
                    <td class="text-center">${row.cognitive_level || ''}</td>
                    <td class="text-center">
                        ${Array.isArray(row.question_numbers) ? row.question_numbers.join(', ') : (row.question_numbers || '')}
                    </td>
                    <td class="text-center">${row.total_questions || 1}</td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Summary
    if (blueprint.summary) {
        html += `
            <div class="mb-4">
                <h6><i class="fas fa-chart-bar"></i> Ringkasan</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">${blueprint.summary.total_questions || 0}</h5>
                                <p class="card-text">Total Soal</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">${blueprint.summary.total_chapters || 0}</h5>
                                <p class="card-text">Total Chapter</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    ${blueprint.summary.multiple_choice || 0} : ${blueprint.summary.essay || 0}
                                </h5>
                                <p class="card-text">PG : Essay</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    html += '</div>';
    return html;
}
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Fix for table-dark header visibility */
.table-dark th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border-color: #32383e !important;
}

.table-dark th,
.table-dark td {
    border-color: #32383e !important;
}

/* Ensure blueprint preview table headers are visible */
.blueprint-preview .table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    font-weight: 600;
    text-align: center;
}

.blueprint-preview .table th {
    font-weight: 600;
    font-size: 0.9rem;
    color: inherit;
}

.card-title {
    color: #495057;
}

.form-check {
    margin-bottom: 0.5rem;
}

.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}
</style>

<?php require_once '../template/footer.php'; ?>
