<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance Mode - Website Sedang <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Background Animation */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.8" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.6" fill="white" opacity="0.12"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            max-width: 500px;
            width: 90%;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .maintenance-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            border-radius: 26px;
            z-index: -1;
        }

        .icon-container {
            margin-bottom: 30px;
            position: relative;
        }

        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 10px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .gear-icon {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 1.5rem;
            color: #764ba2;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        h1 {
            color: #2d3748;
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .subtitle {
            color: #667eea;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 25px;
        }

        .description {
            color: #4a5568;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 35px;
        }

        .progress-container {
            margin: 30px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            animation: progress 3s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 30%; }
            50% { width: 70%; }
            100% { width: 30%; }
        }

        .progress-text {
            color: #718096;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .contact-info {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 16px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid #e2e8f0;
        }

        .contact-title {
            color: #2d3748;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: #4a5568;
            font-size: 0.95rem;
        }

        .contact-item i {
            color: #667eea;
            width: 16px;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.85rem;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .maintenance-container {
                padding: 40px 25px;
                margin: 20px;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            .maintenance-icon {
                font-size: 3rem;
            }
            
            .contact-details {
                align-items: center;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: floatParticle 6s ease-in-out infinite;
        }

        .particle:nth-child(1) {
            width: 6px;
            height: 6px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            width: 4px;
            height: 4px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .particle:nth-child(3) {
            width: 8px;
            height: 8px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes floatParticle {
            0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
            50% { transform: translateY(-30px) translateX(20px); opacity: 0.8; }
        }
    </style>
</head>
<body>
    <!-- Floating particles -->
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>

    <div class="maintenance-container">
        <div class="icon-container">
            <i class="fas fa-tools maintenance-icon"></i>
            <i class="fas fa-cog gear-icon"></i>
        </div>
        
        <h1>Website Sedang Dalam Perbaikan</h1>
        <p class="subtitle">Maintenance Mode</p>
        
        <p class="description">
            Kami sedang melakukan pemeliharaan dan peningkatan sistem untuk memberikan pengalaman yang lebih baik. 
            Website akan kembali online dalam waktu dekat.
        </p>
        
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <p class="progress-text">Sedang dalam proses...</p>
        </div>
        
        <div class="contact-info">
            <div class="contact-title">
                <i class="fas fa-envelope"></i>
                Butuh Bantuan?
            </div>
            <div class="contact-details">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>+62 852 5678 0149</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>Estimasi selesai: <?php echo date('d M Y, H:i'); ?> WITA</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; <?php echo date('Y'); ?> Terima kasih atas kesabaran Anda.</p>
        </div>
    </div>

    <script>
        // Auto refresh setiap 5 menit
        setTimeout(function() {
            location.reload();
        }, 300000);
        
        // Update waktu estimasi setiap menit
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleString('id-ID', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.querySelector('.contact-item:last-child span').textContent = 'Estimasi selesai: ' + timeString + ' WIB';
        }, 60000);
    </script>
</body>
</html>
