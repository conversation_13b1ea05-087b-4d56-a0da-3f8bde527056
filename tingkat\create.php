<?php
require_once '../template/header.php';
require_once '../models/Tingkat.php';

// Check if user is admin
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/403.php");
    exit();
}

$tingkat = new Tingkat();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tingkat->nama_tingkat = $_POST['nama_tingkat'];

    if ($tingkat->create()) {
        $_SESSION['success'] = "Tingkat berhasil ditambahkan.";
        header("Location: index.php");
        exit();
    } else {
        $_SESSION['error'] = "Gagal menambahkan tingkat.";
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Tambah Tingkat</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> <PERSON><PERSON><PERSON>
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form action="" method="POST">
                <div class="mb-3">
                    <label for="nama_tingkat" class="form-label">Nama Tingkat</label>
                    <input type="text" class="form-control" id="nama_tingkat" name="nama_tingkat" required>
                </div>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </form>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>