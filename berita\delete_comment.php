<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../models/KomentarBerita.php';
require_once '../config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$id = $_POST['id'] ?? $input['id'] ?? null;

if (empty($id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID komentar diperlukan']);
    exit();
}

try {
    $komentar = new KomentarBerita();
    $komentar->id = $id;

    // Get the comment first to verify ownership
    if (!$komentar->getOne()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Komentar tidak ditemukan']);
        exit();
    }

    // Check if user owns the comment or is admin
    if ($komentar->user_id != $_SESSION['user_id'] && $_SESSION['role'] != 'admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki izin untuk menghapus komentar ini']);
        exit();
    }

    // Delete the comment and all its replies
    if ($komentar->delete()) {
        echo json_encode(['success' => true, 'message' => 'Komentar berhasil dihapus']);
    } else {
        throw new Exception('Gagal menghapus komentar');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
