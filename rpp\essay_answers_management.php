<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/EssayAnswer.php';
require_once '../models/RppQuestion.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Get all essay answers for this teacher
$database = new Database();
$conn = $database->getConnection();

// Query to get all essay answers with question details
$query = "
    SELECT 
        ea.*,
        CASE 
            WHEN ea.question_type = 'rpp_question' THEN rq.question_text
            ELSE meq.question_text
        END as question_text,
        CASE 
            WHEN ea.question_type = 'rpp_question' THEN r.tema_subtema
            ELSE me.exam_title
        END as source_title,
        CASE 
            WHEN ea.question_type = 'rpp_question' THEN r.materi_pokok
            ELSE 'Multi-RPP Exam'
        END as source_description,
        CASE 
            WHEN ea.question_type = 'rpp_question' THEN 'Single RPP'
            ELSE 'Multi-RPP'
        END as source_type_display
    FROM essay_answers ea
    LEFT JOIN rpp_questions rq ON ea.question_id = rq.id AND ea.question_type = 'rpp_question'
    LEFT JOIN rpp r ON rq.rpp_id = r.id
    LEFT JOIN multi_rpp_exam_questions meq ON ea.question_id = meq.id AND ea.question_type = 'multi_rpp_question'
    LEFT JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
    WHERE 
        (ea.question_type = 'rpp_question' AND r.guru_id = :guru_id)
        OR 
        (ea.question_type = 'multi_rpp_question' AND me.guru_id = :guru_id)
    ORDER BY ea.created_at DESC
";

$stmt = $conn->prepare($query);
$stmt->bindParam(":guru_id", $guru_id);
$stmt->execute();
$essay_answers = $stmt->fetchAll(PDO::FETCH_ASSOC);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-clipboard-list"></i> Manajemen Jawaban Essay
            </h5>
            <div>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke RPP
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($essay_answers)): ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> Total Jawaban: <span class="badge bg-primary"><?= count($essay_answers) ?></span></h6>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="filterAnswers('all')">
                                <i class="fas fa-list"></i> Semua
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="filterAnswers('rpp_question')">
                                <i class="fas fa-file-alt"></i> Single RPP
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="filterAnswers('multi_rpp_question')">
                                <i class="fas fa-layer-group"></i> Multi-RPP
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="answersTable">
                        <thead class="table-dark">
                            <tr>
                                <th width="50">No</th>
                                <th>Soal</th>
                                <th>Sumber</th>
                                <th>Tipe</th>
                                <th>Jawaban (Preview)</th>
                                <th>Dibuat</th>
                                <th width="120">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($essay_answers as $index => $answer): ?>
                                <tr data-type="<?= $answer['question_type'] ?>">
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="question-preview">
                                            <?= htmlspecialchars(substr($answer['question_text'], 0, 100)) ?>
                                            <?= strlen($answer['question_text']) > 100 ? '...' : '' ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($answer['source_title']) ?></strong><br>
                                        <small class="text-muted"><?= htmlspecialchars($answer['source_description']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $answer['question_type'] === 'rpp_question' ? 'info' : 'success' ?>">
                                            <?= $answer['source_type_display'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="answer-preview">
                                            <?= htmlspecialchars(substr($answer['expected_answer'], 0, 80)) ?>
                                            <?= strlen($answer['expected_answer']) > 80 ? '...' : '' ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small><?= date('d/m/Y H:i', strtotime($answer['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewAnswer(<?= $answer['id'] ?>)" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editAnswer(<?= $answer['id'] ?>)" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteAnswer(<?= $answer['id'] ?>)" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-clipboard-list fa-3x text-muted"></i>
                    </div>
                    <h5 class="text-muted">Belum Ada Jawaban Essay</h5>
                    <p class="text-muted mb-4">
                        Anda belum membuat jawaban untuk soal essay. Mulai dengan membuat soal essay 
                        dan generate jawabannya di halaman RPP atau Multi-RPP.
                    </p>
                    <div>
                        <a href="generate_questions.php" class="btn btn-primary me-2">
                            <i class="fas fa-file-alt"></i> Single RPP
                        </a>
                        <a href="multi_rpp_generate.php" class="btn btn-success">
                            <i class="fas fa-layer-group"></i> Multi-RPP
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Answer Detail Modal -->
<div class="modal fade" id="answerDetailModal" tabindex="-1" aria-labelledby="answerDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="answerDetailModalLabel">Detail Jawaban Essay</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="answerDetailContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="editFromDetailBtn">Edit Jawaban</button>
            </div>
        </div>
    </div>
</div>

<style>
.question-preview, .answer-preview {
    font-size: 0.9rem;
    line-height: 1.4;
}

.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
}

.fa-3x {
    font-size: 3rem;
}

/* Essay Answer Display Formatting */
.essay-answer-display {
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.essay-answer-display p {
    margin-bottom: 1rem;
}

.essay-answer-display ul, .essay-answer-display ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.essay-answer-display li {
    margin-bottom: 0.5rem;
}

.essay-answer-display strong {
    font-weight: 600;
    color: #2c3e50;
}

.essay-answer-display em {
    font-style: italic;
    color: #34495e;
}

/* Modal content formatting */
.modal-body .form-control, .modal-body div.border {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

.modal-body .bg-light {
    background-color: #f8f9fa !important;
}
</style>

<script>
function filterAnswers(type) {
    const rows = document.querySelectorAll('#answersTable tbody tr');
    const buttons = document.querySelectorAll('.btn-group button');
    
    // Update button states
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Filter rows
    rows.forEach(row => {
        if (type === 'all' || row.getAttribute('data-type') === type) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function viewAnswer(answerId) {
    // Implementation for viewing answer details
    fetch(`get_essay_answer.php?answer_id=${answerId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAnswerDetail(data.answer);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat mengambil data jawaban');
    });
}

function editAnswer(answerId) {
    // Redirect to edit page
    window.location.href = `edit_essay_answer.php?id=${answerId}`;
}

function deleteAnswer(answerId) {
    if (confirm('Apakah Anda yakin ingin menghapus jawaban ini?')) {
        // Show loading state
        const deleteBtn = document.querySelector(`button[onclick="deleteAnswer(${answerId})"]`);
        const originalText = deleteBtn.innerHTML;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;

        // Send delete request
        fetch('delete_essay_answer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `answer_id=${answerId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove row from table
                const row = deleteBtn.closest('tr');
                row.remove();

                // Show success message
                showAlert('success', data.message);

                // Update empty state if no rows left
                const tbody = document.querySelector('tbody');
                if (tbody.children.length === 0) {
                    location.reload();
                }
            } else {
                showAlert('danger', data.message);
                // Restore button
                deleteBtn.innerHTML = originalText;
                deleteBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Terjadi kesalahan saat menghapus jawaban');
            // Restore button
            deleteBtn.innerHTML = originalText;
            deleteBtn.disabled = false;
        });
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showAnswerDetail(answer) {
    // Implementation for showing answer detail in modal
    const modal = document.getElementById('answerDetailModal');
    const content = document.getElementById('answerDetailContent');

    // Format text with proper line breaks and structure
    const formatText = (text) => {
        if (!text) return 'Tidak ada data';
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^\s*[-•]\s*/gm, '• ')
            .replace(/^\s*\d+\.\s*/gm, (match) => `<strong>${match}</strong>`);
    };

    content.innerHTML = `
        <div class="mb-3">
            <strong>Soal:</strong>
            <div class="mt-2 p-3 bg-light rounded" style="line-height: 1.6;">${formatText(answer.question_text)}</div>
        </div>
        <div class="mb-3">
            <strong>Jawaban yang Diharapkan:</strong>
            <div class="mt-2 p-3 border rounded" style="line-height: 1.6; white-space: pre-wrap;">${formatText(answer.expected_answer)}</div>
        </div>
        <div class="mb-3">
            <strong>Poin-poin Kunci:</strong>
            <div class="mt-2 p-3 border rounded" style="line-height: 1.6; white-space: pre-wrap;">${formatText(answer.answer_points || 'Tidak ada poin khusus')}</div>
        </div>
    `;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}
</script>

<?php require_once '../template/footer.php'; ?>
