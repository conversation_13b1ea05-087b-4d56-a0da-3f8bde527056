# Modul Kompetensi Dasar (KD)

Modul ini memungkinkan guru untuk mengelola Kompetensi Dasar untuk mata pelajaran yang mereka ajarkan dalam sistem SIHADIR.

## Fitur Utama

### 1. Manajemen Kompetensi Dasar
- **Tambah KD**: Guru dapat menambahkan Kompetensi Dasar baru
- **Edit KD**: Mengubah data Kompetensi Dasar yang sudah ada
- **Hapus KD**: Menghapus Kompetensi Dasar yang tidak diperlukan
- **Lihat Detail**: Melihat detail lengkap Kompetensi Dasar

### 2. Filter dan Pencarian
- Filter berdasarkan mata pelajaran
- Filter berdasarkan semester (1 atau 2)
- Filter berdasarkan tahun ajaran
- Pencarian menggunakan DataTables

### 3. Validasi Data
- Validasi kode KD unik per mata pelajaran, semester, dan tahun ajaran
- Validasi input wajib dan opsional
- Sanitasi input untuk keamanan

### 4. Integrasi Sistem
- Terintegrasi dengan sistem mata pelajaran
- Terintegrasi dengan data guru
- Menggunakan periode aktif sistem
- Konsisten dengan desain UI/UX sistem

## Struktur File

```
kd/
├── index.php              # Halaman utama daftar KD
├── create.php             # Form tambah KD baru
├── edit.php               # Form edit KD
├── view.php               # Detail KD
├── delete.php             # Hapus KD
├── kompetensi_dasar.sql   # Struktur database
└── README.md              # Dokumentasi ini
```

## Database

### Tabel: kompetensi_dasar

| Field | Type | Description |
|-------|------|-------------|
| id | int(11) | Primary key auto increment |
| kode_kd | varchar(20) | Kode Kompetensi Dasar |
| deskripsi_kd | text | Deskripsi lengkap KD |
| tujuan_pembelajaran | text | Tujuan pembelajaran (opsional) |
| mapel_id | int(11) | Foreign key ke mata_pelajaran |
| guru_id | int(11) | Foreign key ke guru |
| semester | enum('1','2') | Semester |
| tahun_ajaran | varchar(9) | Tahun ajaran |
| created_at | timestamp | Waktu pembuatan |
| updated_at | timestamp | Waktu update terakhir |

### Constraints
- **unique_kd_mapel_semester**: Kode KD unik per mata pelajaran, semester, dan tahun ajaran
- **Foreign Keys**: Relasi ke mata_pelajaran dan guru dengan CASCADE delete

## Instalasi

1. **Import Database**
   ```sql
   -- Jalankan file kompetensi_dasar.sql
   SOURCE kd/kompetensi_dasar.sql;
   ```

2. **Update Navigation**
   - Menu KD sudah ditambahkan ke template/header.php
   - Accessible untuk role 'guru'

3. **Model Class**
   - File models/KompetensiDasar.php sudah tersedia
   - Menggunakan pola yang sama dengan model lain

## Penggunaan

### Untuk Guru

1. **Akses Menu**
   - Login sebagai guru
   - Klik menu "Kompetensi Dasar" di sidebar

2. **Tambah KD Baru**
   - Klik tombol "Tambah KD"
   - Isi form dengan data yang diperlukan
   - Klik "Simpan"

3. **Edit KD**
   - Klik tombol "Edit" pada KD yang ingin diubah
   - Ubah data sesuai kebutuhan
   - Klik "Update"

4. **Lihat Detail**
   - Klik tombol "Lihat Detail" untuk melihat informasi lengkap
   - Tersedia informasi metadata dan aksi cepat

5. **Filter Data**
   - Gunakan dropdown filter untuk menyaring data
   - Filter berdasarkan mata pelajaran, semester, atau tahun ajaran

## Keamanan

- **Autentikasi**: Hanya guru yang login dapat mengakses
- **Autorisasi**: Guru hanya dapat mengelola KD untuk mata pelajaran yang mereka ajarkan
- **Validasi**: Input disanitasi dan divalidasi
- **Ownership**: Guru hanya dapat edit/hapus KD yang mereka buat

## Integrasi dengan Modul Lain

### RPP Module
- KD dapat digunakan sebagai referensi dalam pembuatan RPP
- Data KD dapat diintegrasikan dengan sistem RPP yang ada

### Penilaian
- KD dapat dijadikan dasar untuk penilaian kompetensi siswa
- Integrasi dengan sistem nilai berdasarkan pencapaian KD

### Laporan
- Data KD dapat digunakan dalam laporan akademik
- Statistik pencapaian KD per mata pelajaran

## Teknologi

- **Backend**: PHP dengan PDO
- **Frontend**: HTML5, CSS3, Bootstrap 5
- **JavaScript**: jQuery, DataTables
- **Database**: MySQL/MariaDB
- **Icons**: Font Awesome

## Maintenance

### Backup Data
```sql
-- Backup tabel kompetensi_dasar
CREATE TABLE kompetensi_dasar_backup AS SELECT * FROM kompetensi_dasar;
```

### Update Version
- Update version number di footer.php jika diperlukan
- Dokumentasikan perubahan dalam changelog

## Troubleshooting

### Error Database
- Pastikan foreign key constraints terpenuhi
- Cek koneksi database di config/database.php

### Error Permission
- Pastikan user login sebagai guru
- Cek session dan role di middleware/auth.php

### Error Display
- Cek JavaScript console untuk error
- Pastikan Bootstrap dan jQuery ter-load dengan benar

## Changelog

### Version 1.0.0
- Initial release
- CRUD functionality
- Filter dan search
- Integration dengan sistem existing
- Security implementation

## Support

Untuk pertanyaan atau masalah terkait modul ini, silakan hubungi developer atau buat issue di repository sistem.
