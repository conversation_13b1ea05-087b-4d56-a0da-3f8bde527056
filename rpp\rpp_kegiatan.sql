CREATE TABLE `rpp_kegiatan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rpp_id` int(11) NOT NULL,
  `jenis_kegiatan` enum('pendahuluan','inti','penutup') NOT NULL,
  `deskripsi` text NOT NULL,
  `urutan` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `rpp_id` (`rpp_id`),
  CONSTRAINT `rpp_kegiatan_ibfk_1` FOREIGN KEY (`rpp_id`) REFERENCES `rpp` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;