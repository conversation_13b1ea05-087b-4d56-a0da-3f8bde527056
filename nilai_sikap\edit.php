<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

$nilaiSikap = new NilaiSikap();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

if (!$nilaiSikap->getById($_GET['id'])) {
    $_SESSION['error'] = "Data nilai sikap tidak ditemukan";
    header("Location: index.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nilaiSikap->nilai_spiritual = $_POST['nilai_spiritual'];
    $nilaiSikap->nilai_sosial = $_POST['nilai_sosial'];
    
    // Generate deskripsi otomatis
    $nilaiSikap->deskripsi_spiritual = $nilaiSikap->generateDeskripsiSpiritual($_POST['nilai_spiritual']);
    $nilaiSikap->deskripsi_sosial = $nilaiSikap->generateDeskripsiSosial($_POST['nilai_sosial']);

    if ($nilaiSikap->update()) {
        $_SESSION['success'] = "Data nilai sikap berhasil diperbarui";
        header("Location: index.php");
        exit();
    } else {
        $_SESSION['error'] = "Gagal memperbarui data nilai sikap";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Nilai Sikap</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="form-group">
                            <label>NIS</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($nilaiSikap->nis) ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label>Nama Siswa</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($nilaiSikap->nama_siswa) ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label>Kelas</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($nilaiSikap->nama_kelas) ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label>Semester</label>
                            <input type="text" class="form-control" value="Semester <?= htmlspecialchars($nilaiSikap->semester) ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label>Tahun Ajaran</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($nilaiSikap->tahun_ajaran) ?>" readonly>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nilai_spiritual">Nilai Spiritual</label>
                                    <select name="nilai_spiritual" id="nilai_spiritual" class="form-control" required>
                                        <option value="">Pilih Nilai</option>
                                        <option value="A" <?= $nilaiSikap->nilai_spiritual == 'A' ? 'selected' : '' ?>>A</option>
                                        <option value="B" <?= $nilaiSikap->nilai_spiritual == 'B' ? 'selected' : '' ?>>B</option>
                                        <option value="C" <?= $nilaiSikap->nilai_spiritual == 'C' ? 'selected' : '' ?>>C</option>
                                        <option value="D" <?= $nilaiSikap->nilai_spiritual == 'D' ? 'selected' : '' ?>>D</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nilai_sosial">Nilai Sosial</label>
                                    <select name="nilai_sosial" id="nilai_sosial" class="form-control" required>
                                        <option value="">Pilih Nilai</option>
                                        <option value="A" <?= $nilaiSikap->nilai_sosial == 'A' ? 'selected' : '' ?>>A</option>
                                        <option value="B" <?= $nilaiSikap->nilai_sosial == 'B' ? 'selected' : '' ?>>B</option>
                                        <option value="C" <?= $nilaiSikap->nilai_sosial == 'C' ? 'selected' : '' ?>>C</option>
                                        <option value="D" <?= $nilaiSikap->nilai_sosial == 'D' ? 'selected' : '' ?>>D</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Simpan</button>
                            <a href="index.php" class="btn btn-secondary">Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>