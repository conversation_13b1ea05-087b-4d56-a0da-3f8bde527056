-- Migration script to add tema_subtema and materi_pokok fields to kompetensi_dasar table
-- Run this script if you have an existing database that needs to be updated
-- This version avoids information_schema queries and dynamic SQL

-- Method 1: Direct ALTER TABLE (will show error if column exists, but won't break anything)
-- Uncomment the lines below if you want to see error messages for existing columns

-- ALTER TABLE kompetensi_dasar ADD COLUMN tema_subtema VARCHAR(255) DEFAULT NULL AFTER deskripsi_kd;
-- ALTER TABLE kompetensi_dasar ADD COLUMN materi_pokok VARCHAR(255) DEFAULT NULL AFTER tema_subtema;

-- Method 2: Safe approach using stored procedure (recommended)
DELIMITER $$

DROP PROCEDURE IF EXISTS AddColumnIfNotExists$$

CREATE PROCEDURE AddColumnIfNotExists()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;

    -- Try to add tema_subtema column
    ALTER TABLE kompetensi_dasar ADD COLUMN tema_subtema VARCHAR(255) DEFAULT NULL AFTER deskripsi_kd;

    -- Try to add materi_pokok column
    ALTER TABLE kompetensi_dasar ADD COLUMN materi_pokok VARCHAR(255) DEFAULT NULL AFTER tema_subtema;

END$$

DELIMITER ;

-- Execute the procedure
CALL AddColumnIfNotExists();

-- Clean up
DROP PROCEDURE AddColumnIfNotExists;

-- Show final table structure
DESCRIBE kompetensi_dasar;
