# Panduan Cepat: Filter Periode Akademik

## 🎯 Aks<PERSON>

| Modul | Lokasi Filter | Cara Menggunakan |
|-------|---------------|------------------|
| **Data Siswa** | Atas halaman | Pilih "Mode Periode" → Pilih TA & Semester → Filter |
| **Nilai** | Card "Filter Data" | Pilih Semester & TA → Filter |
| **Absensi** | Card "Filter Data" | Pilih Semester & TA → Filter |
| **Rekap Absensi** | Card "Filter Data" | Pilih Semester & TA & Kelas → Filter |
| **Tugas** | Card "Filter Data" | Pilih Semester & TA → Filter |
| **Interface Publik** | Bawah info siswa | Pilih TA & Semester → Cari |

## 🔍 Langkah Cepat Melihat Data Historis

### 1. **Melihat Siswa Periode Lama**
```
Siswa → Mode: "Berdasarkan Periode" → Pilih TA/Semester → Filter
```

### 2. **Melihat Nilai Periode Lama**
```
<PERSON><PERSON> → Filter: Pilih TA/Semester → Input/<PERSON>hat Nilai
```

### 3. **Melihat Absensi Periode Lama**
```
Rekap Absensi → Filter: Pilih TA/Semester/Kelas → Lihat Rekap
```

### 4. **Melihat Riwayat Lengkap Siswa**
```
Siswa → Mode Periode → Klik ikon "Riwayat" pada siswa
```

## ⚠️ Penting Diingat

- **Sistem promosi otomatis DINONAKTIFKAN** untuk mencegah kehilangan data
- **Selalu cek filter periode** sebelum input data
- **Data historis AMAN** - tidak akan hilang saat siswa naik kelas
- **Gunakan tombol Reset** untuk kembali ke periode aktif

## 🆘 Jika Ada Masalah

1. **Data kosong?** → Cek filter periode sudah benar
2. **Siswa tidak muncul?** → Pastikan siswa terdaftar di periode tersebut  
3. **Error sistem?** → Hubungi administrator

## 📞 Bantuan Teknis

- **Test sistem**: Jalankan `test_data_preservation.php`
- **Dokumentasi lengkap**: Baca `DOCUMENTATION_ACADEMIC_PERIODS.md`
- **Administrator**: Hubungi tim IT sekolah
