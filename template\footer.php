        </div><!-- /.container-fluid -->

        <!-- Footer -->
        <footer class="footer mt-auto py-3">
            <div class="container-fluid">
                <div class="text-muted text-center">
                    SIHADIR - Sistem Informasi Kehadiran Siswa &copy; <?php echo date('Y'); ?> | Versi 2.19.0 <br> Oleh: <PERSON><PERSON>
                </div>
            </div>
        </footer>
    </div><!-- /#content -->

    <style>
        .footer {
            background-color: #f8f9fc;
            border-top: 1px solid #e3e6f0;
            position: relative;
            margin-top: 2rem;
        }
    </style>

    <script>
        // Sidebar Toggle Functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
            document.getElementById('content').classList.toggle('expanded');
        });

        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target) && sidebar.classList.contains('active')) {
                    sidebar.classList.remove('active');
                    document.getElementById('content').classList.add('expanded');
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('active');
                document.getElementById('content').classList.remove('expanded');
            }
        });

        // Initialize DataTables if there are any tables with the 'datatable' class
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('.datatable').DataTable({
                    responsive: true,
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json'
                    }
                });
            }
        });
    </script>
</body>
</html><?php ob_end_flush(); ?>
