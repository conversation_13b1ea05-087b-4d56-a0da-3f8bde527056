<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$user = new User();
$user->id = $_GET['id'];

if (!$user->getOne()) {
    header("Location: index.php");
    exit();
}

// Don't allow editing admin account
if ($user->username === 'admin') {
    header("Location: index.php");
    exit();
}

$guru = new Guru();
$guru_list = $guru->getAllActive();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_account'])) {
        $old_username = $user->username;
        $new_username = $_POST['username'];

        // Check if username changed and if it already exists
        if ($old_username !== $new_username && $user->usernameExists($new_username)) {
            $_SESSION['error'] = "Username sudah digunakan!";
        } else {
            $user->username = $new_username;
            $user->nama_lengkap = $_POST['nama_lengkap'];
            $user->role = $_POST['role'];

            if ($user->update()) {
                $_SESSION['message'] = "Akun berhasil diupdate!";
                header("Location: index.php");
                exit();
            } else {
                $_SESSION['error'] = "Gagal mengupdate akun!";
            }
        }
    }
}
?>

<div class="container">
    <h2 class="mb-4">Edit Akun</h2>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Edit Akun</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="role" class="form-label">Role</label>
                            <?php if ($user->username === 'admin'): ?>
                                <input type="text" class="form-control" value="<?php echo ucfirst($user->role); ?>" readonly>
                                <input type="hidden" name="role" value="<?php echo $user->role; ?>">
                            <?php else: ?>
                                <select class="form-select" id="role" name="role" required onchange="toggleGuruSelect()">
                                    <option value="">Pilih Role</option>
                                    <option value="admin" <?php echo $user->role === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                    <option value="guru" <?php echo $user->role === 'guru' ? 'selected' : ''; ?>>Guru</option>
                                </select>
                            <?php endif; ?>
                        </div>

                        <?php if ($user->role === 'guru'): ?>
                            <div class="mb-3" id="guruSelectDiv">
                                <label for="guru_id" class="form-label">Guru</label>
                                <select class="form-select" id="guru_id" name="guru_id" onchange="updateNamaLengkap()" required>
                                    <option value="">Pilih Guru</option>
                                    <?php foreach ($guru_list as $g): ?>
                                        <option value="<?php echo $g['id']; ?>" 
                                                data-nama="<?php echo htmlspecialchars($g['nama_lengkap']); ?>"
                                                data-nip="<?php echo htmlspecialchars($g['nip']); ?>"
                                                <?php echo $g['nama_lengkap'] === $user->nama_lengkap ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($g['nama_lengkap']); ?> (<?php echo htmlspecialchars($g['nip']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user->username); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="nama_lengkap" class="form-label">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" value="<?php echo htmlspecialchars($user->nama_lengkap); ?>" required <?php echo $user->role === 'guru' ? 'readonly' : ''; ?>>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" name="update_account" class="btn btn-primary">Simpan</button>
                            <a href="index.php" class="btn btn-secondary">Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleGuruSelect() {
    const role = document.getElementById('role');
    const guruDiv = document.getElementById('guruSelectDiv');
    const namaInput = document.getElementById('nama_lengkap');
    
    if (role && role.value === 'guru') {
        if (guruDiv) {
            guruDiv.style.display = 'block';
        }
        namaInput.readOnly = true;
    } else {
        if (guruDiv) {
            guruDiv.style.display = 'none';
        }
        namaInput.readOnly = false;
        namaInput.value = '';
    }
}

function updateNamaLengkap() {
    const guruSelect = document.getElementById('guru_id');
    const namaInput = document.getElementById('nama_lengkap');
    const usernameInput = document.getElementById('username');
    
    if (guruSelect.value) {
        const selectedOption = guruSelect.options[guruSelect.selectedIndex];
        const nip = selectedOption.getAttribute('data-nip');
        namaInput.value = selectedOption.getAttribute('data-nama');
        usernameInput.value = 'guru' + nip;
    } else {
        namaInput.value = '';
        usernameInput.value = '';
    }
}

// Initialize form state
document.addEventListener('DOMContentLoaded', function() {
    toggleGuruSelect();
});
</script>

<?php
require_once '../template/footer.php';
?>
