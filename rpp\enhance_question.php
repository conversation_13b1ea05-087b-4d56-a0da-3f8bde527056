<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/GeminiApi.php';
require_once '../models/Guru.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Validate input
if (!isset($_POST['question_text']) || !isset($_POST['question_type']) || 
    !isset($_POST['enhancement_type']) || !isset($_POST['rpp_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data tidak lengkap']);
    exit();
}

$question_text = trim($_POST['question_text']);
$question_type = $_POST['question_type'];
$enhancement_type = $_POST['enhancement_type'];
$rpp_id = $_POST['rpp_id'];

// Validate question text
if (empty($question_text)) {
    echo json_encode(['success' => false, 'message' => 'Teks soal tidak boleh kosong']);
    exit();
}

// Validate question type
if (!in_array($question_type, ['multiple_choice', 'essay'])) {
    echo json_encode(['success' => false, 'message' => 'Jenis soal tidak valid']);
    exit();
}

// Validate enhancement type
$valid_enhancement_types = ['upgrade_to_hots', 'make_unique', 'general_improvement'];
if (!in_array($enhancement_type, $valid_enhancement_types)) {
    echo json_encode(['success' => false, 'message' => 'Jenis perbaikan tidak valid']);
    exit();
}

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
        exit();
    }

    // Validate RPP ownership
    $rpp = new Rpp();
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        echo json_encode(['success' => false, 'message' => 'RPP tidak ditemukan atau bukan milik Anda']);
        exit();
    }

    // Build RPP context for better enhancement
    $rpp_context = "Mata Pelajaran: " . $rpp_data['nama_mapel'] . "\n";
    $rpp_context .= "Kelas: " . $rpp_data['nama_kelas'] . "\n";
    $rpp_context .= "Materi Pokok: " . $rpp_data['materi_pokok'] . "\n";
    $rpp_context .= "Tujuan Pembelajaran: " . $rpp_data['tujuan_pembelajaran'] . "\n";
    $rpp_context .= "Kompetensi Dasar: " . $rpp_data['kompetensi_dasar'];

    // Enhance question using Gemini API
    $geminiApi = new GeminiApi();
    $enhancement = $geminiApi->enhanceQuestion($question_text, $question_type, $enhancement_type, $rpp_context);

    echo json_encode([
        'success' => true,
        'enhancement' => $enhancement,
        'message' => 'Perbaikan berhasil'
    ]);

} catch (Exception $e) {
    error_log("Question Enhancement Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
