<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';

if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "ID siswa tidak valid.";
    header('Location: index.php');
    exit();
}

$siswa_id = $_GET['id'];
$siswa = new Siswa();
$siswaPeriode = new SiswaPeriode();

// Get student data
$siswa->id = $siswa_id;
if (!$siswa->getOne()) {
    $_SESSION['error'] = "Data siswa tidak ditemukan.";
    header('Location: index.php');
    exit();
}

// Get all periods for this student
$periods = $siswaPeriode->getPeriodesBySiswa($siswa_id);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Riwayat Periode Akademik - <?php echo htmlspecialchars($siswa->nama_siswa); ?></h5>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
            <div class="card-body">
                <!-- Student Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>NIS</strong></td>
                                <td>: <?php echo htmlspecialchars($siswa->nis); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Nama</strong></td>
                                <td>: <?php echo htmlspecialchars($siswa->nama_siswa); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Jenis Kelamin</strong></td>
                                <td>: <?php echo $siswa->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan'; ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="120"><strong>Alamat</strong></td>
                                <td>: <?php echo htmlspecialchars($siswa->alamat ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>No. Telp</strong></td>
                                <td>: <?php echo htmlspecialchars($siswa->no_telp ?: '-'); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Periods History -->
                <h6 class="mb-3">Riwayat Periode Akademik</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tablePeriods">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tahun Ajaran</th>
                                <th>Semester</th>
                                <th>Kelas</th>
                                <th>Status</th>
                                <th>Periode Aktif</th>
                                <th>Tanggal Mulai</th>
                                <th>Tanggal Selesai</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $hasData = false;
                            while ($row = $periods->fetch(PDO::FETCH_ASSOC)) {
                                $hasData = true;
                                
                                // Status badge
                                $status_class = '';
                                $status_text = '';
                                switch($row['status']) {
                                    case 'aktif':
                                        $status_class = 'badge bg-success';
                                        $status_text = 'Aktif';
                                        break;
                                    case 'lulus':
                                        $status_class = 'badge bg-primary';
                                        $status_text = 'Lulus';
                                        break;
                                    case 'pindah':
                                        $status_class = 'badge bg-warning';
                                        $status_text = 'Pindah';
                                        break;
                                    case 'keluar':
                                        $status_class = 'badge bg-danger';
                                        $status_text = 'Keluar';
                                        break;
                                    default:
                                        $status_class = 'badge bg-secondary';
                                        $status_text = ucfirst($row['status']);
                                }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['tahun_ajaran']); ?></td>
                                <td>Semester <?php echo $row['semester']; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                <td><span class="<?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                                <td>
                                    <?php if ($row['is_current']): ?>
                                        <span class="badge bg-info">Ya</span>
                                    <?php else: ?>
                                        <span class="text-muted">Tidak</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $row['tanggal_mulai'] ? date('d/m/Y', strtotime($row['tanggal_mulai'])) : '-'; ?></td>
                                <td><?php echo $row['tanggal_selesai'] ? date('d/m/Y', strtotime($row['tanggal_selesai'])) : '-'; ?></td>
                                <td>
                                    <a href="../nilai/index.php?siswa_id=<?php echo $siswa_id; ?>&tahun_ajaran=<?php echo $row['tahun_ajaran']; ?>&semester=<?php echo $row['semester']; ?>" 
                                       class="btn btn-sm btn-primary" title="Lihat Nilai">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <a href="../absensi/rekap.php?siswa_id=<?php echo $siswa_id; ?>&tahun_ajaran=<?php echo $row['tahun_ajaran']; ?>&semester=<?php echo $row['semester']; ?>" 
                                       class="btn btn-sm btn-success" title="Lihat Absensi">
                                        <i class="fas fa-calendar-check"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                    <?php if (!$hasData): ?>
                        <p class="text-center mt-3">Tidak ada data periode akademik untuk siswa ini.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tablePeriods').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data periode",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "order": [[1, "desc"], [2, "desc"]], // Urutkan berdasarkan tahun ajaran dan semester terbaru
        "columnDefs": [
            {"orderable": false, "targets": 8}, // Kolom aksi tidak bisa diurutkan
            {"width": "100px", "targets": 8} // Atur lebar kolom aksi
        ],
        "pageLength": 10
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
