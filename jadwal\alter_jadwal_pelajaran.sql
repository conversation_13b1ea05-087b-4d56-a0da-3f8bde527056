-- Hapus foreign key yang ada jika ada
SET FOREIGN_KEY_CHECKS=0;

-- Hapus foreign key dan kolom jurusan_id
ALTER TABLE jadwal_pelajaran
DROP FOREIGN KEY IF EXISTS fk_jadwal_jurusan;

ALTER TABLE jadwal_pelajaran
DROP COLUMN IF EXISTS jurusan_id;

-- Hapus foreign key tingkat yang lama jika ada
ALTER TABLE jadwal_pelajaran
DROP FOREIGN KEY IF EXISTS fk_jadwal_tingkat;

-- <PERSON><PERSON> kolom tingkat_id jika belum ada
ALTER TABLE jadwal_pelajaran
ADD COLUMN IF NOT EXISTS tingkat_id INT NULL,
ADD CONSTRAINT fk_jadwal_tingkat FOREIGN KEY (tingkat_id) REFERENCES tingkat(id) ON DELETE SET NULL;

-- Buat tabel jadwal_jurusan untuk relasi many-to-many jika belum ada
DROP TABLE IF EXISTS jadwal_jurusan;
CREATE TABLE jadwal_jurusan (
    jadwal_id INT NOT NULL,
    jurusan_id INT NOT NULL,
    PRIMARY KEY (jadwal_id, jurusan_id),
    FOREIGN KEY (jadwal_id) REFERENCES jadwal_pelajaran(id) ON DELETE CASCADE,
    FOREIGN KEY (jurusan_id) REFERENCES jurusan(id) ON DELETE CASCADE
);

SET FOREIGN_KEY_CHECKS=1;