<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../models/Berita.php';
require_once '../config/database.php';

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$berita = new Berita();
$berita->id = $_GET['id'];

// Get the berita first
if (!$berita->getOne()) {
    header("Location: index.php");
    exit();
}

// Verify ownership or admin role
if ($_SESSION['user_id'] == $berita->created_by || $_SESSION['role'] == 'admin') {
    // Delete thumbnail if exists
    if ($berita->thumbnail && file_exists('../uploads/berita/' . $berita->thumbnail)) {
        unlink('../uploads/berita/' . $berita->thumbnail);
    }

    if ($berita->delete()) {
        header("Location: index.php?success=1");
    } else {
        header("Location: index.php?error=1");
    }
} else {
    header("Location: ../403.php");
}
exit();
