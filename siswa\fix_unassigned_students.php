<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/PeriodeAktif.php';

// Check if user is admin
if (!in_array($_SESSION['role'], ['admin'])) {
    echo "<script>alert('Aks<PERSON> ditolak! Hanya admin yang dapat mengakses halaman ini.'); window.location.href='/absen/';</script>";
    exit;
}

$siswaModel = new Siswa();
$siswaPeriodeModel = new SiswaPeriode();
$periodeAktifModel = new PeriodeAktif();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_students'])) {
    try {
        // Get current active period
        $periodeAktifModel->getActive();
        $current_tahun_ajaran = $periodeAktifModel->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
        $current_semester = $periodeAktifModel->semester ?: '1';

        // Get all students without period assignments
        $unassigned_students = $siswaPeriodeModel->getSiswaWithoutPeriods();
        $fixed_count = 0;
        $error_count = 0;

        if ($unassigned_students && $unassigned_students->rowCount() > 0) {
            while ($student = $unassigned_students->fetch(PDO::FETCH_ASSOC)) {
                try {
                    // Add student to current active period
                    $result = $siswaPeriodeModel->addSiswaToNewPeriode(
                        $student['siswa_id'], 
                        $student['kelas_id'], 
                        $current_tahun_ajaran, 
                        $current_semester
                    );
                    
                    if ($result) {
                        // Update current period reference in siswa table
                        $siswaModel->updateCurrentPeriod($student['siswa_id'], $current_tahun_ajaran, $current_semester);
                        $fixed_count++;
                    } else {
                        $error_count++;
                    }
                } catch (Exception $e) {
                    $error_count++;
                }
            }
        }

        if ($fixed_count > 0) {
            $message = "Berhasil memperbaiki $fixed_count siswa. Mereka sekarang aktif di periode $current_tahun_ajaran - Semester $current_semester.";
        }
        
        if ($error_count > 0) {
            $error = "Gagal memperbaiki $error_count siswa. Silakan coba lagi atau periksa log error.";
        }
        
        if ($fixed_count == 0 && $error_count == 0) {
            $message = "Tidak ada siswa yang perlu diperbaiki. Semua siswa sudah memiliki periode aktif.";
        }

    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get current statistics
$unassigned_count = $siswaPeriodeModel->getCountSiswaWithoutPeriods();
$total_students = 0;

try {
    $all_students = $siswaModel->getAll();
    $total_students = $all_students->rowCount();
} catch (Exception $e) {
    $error = "Error getting student count: " . $e->getMessage();
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Perbaiki Siswa Tanpa Periode</h1>
        <a href="manage_periods.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Kelola Periode
        </a>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <!-- Statistics Card -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Siswa</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_students ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-<?= $unassigned_count > 0 ? 'warning' : 'success' ?> shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-<?= $unassigned_count > 0 ? 'warning' : 'success' ?> text-uppercase mb-1">
                                Siswa Tanpa Periode
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $unassigned_count ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Siswa Aktif</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_students - $unassigned_count ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fix Students Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-tools"></i> Perbaiki Siswa Tanpa Periode
            </h6>
        </div>
        <div class="card-body">
            <?php if ($unassigned_count > 0): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Ditemukan <?= $unassigned_count ?> siswa yang belum diaktifkan untuk periode manapun.</strong>
                <br>
                Siswa-siswa ini kemungkinan diimpor sebelum sistem periode diimplementasikan atau terjadi error saat import.
                <br><br>
                <strong>Dampak:</strong>
                <ul class="mb-0">
                    <li>Siswa tidak muncul dalam daftar absensi</li>
                    <li>Siswa tidak dapat dinilai</li>
                    <li>Siswa tidak muncul dalam laporan</li>
                </ul>
            </div>

            <form method="POST" onsubmit="return confirm('Yakin ingin memperbaiki semua siswa tanpa periode? Mereka akan diaktifkan untuk periode aktif saat ini.')">
                <div class="mb-3">
                    <h6>Periode Aktif Saat Ini:</h6>
                    <?php
                    $periodeAktifModel->getActive();
                    $current_tahun_ajaran = $periodeAktifModel->tahun_ajaran ?: 'Tidak ada periode aktif';
                    $current_semester = $periodeAktifModel->semester ?: '-';
                    ?>
                    <p class="text-muted">
                        <strong>Tahun Ajaran:</strong> <?= htmlspecialchars($current_tahun_ajaran) ?><br>
                        <strong>Semester:</strong> <?= htmlspecialchars($current_semester) ?>
                    </p>
                </div>

                <button type="submit" name="fix_students" class="btn btn-warning">
                    <i class="fas fa-tools"></i> Perbaiki Semua Siswa (<?= $unassigned_count ?> siswa)
                </button>
            </form>
            <?php else: ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>Semua siswa sudah memiliki periode aktif!</strong>
                <br>
                Tidak ada siswa yang perlu diperbaiki.
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-info-circle"></i> Informasi
            </h6>
        </div>
        <div class="card-body">
            <h6>Kapan menggunakan tool ini?</h6>
            <ul>
                <li>Setelah mengimpor data siswa dari Excel dan siswa tidak muncul dalam periode management</li>
                <li>Ketika ada siswa yang tidak muncul dalam daftar absensi atau penilaian</li>
                <li>Setelah migrasi data atau pemulihan database</li>
            </ul>

            <h6 class="mt-3">Apa yang dilakukan tool ini?</h6>
            <ul>
                <li>Mencari siswa yang tidak memiliki entri di tabel siswa_periode</li>
                <li>Mengaktifkan mereka untuk periode akademik yang sedang aktif</li>
                <li>Memperbarui referensi periode di tabel siswa</li>
            </ul>

            <div class="alert alert-info mt-3">
                <i class="fas fa-lightbulb"></i>
                <strong>Tips:</strong> Setelah menjalankan perbaikan, siswa akan muncul di halaman "Kelola Periode Akademik Siswa" 
                dan dapat dikelola seperti biasa.
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
