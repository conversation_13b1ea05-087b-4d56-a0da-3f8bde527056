<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';
require_once '../models/PeriodeAktif.php';

// Check if user is admin
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

$error = '';
$success = '';

// Get required data
$kelas = new Kelas();
$kelas_list = $kelas->getAll()->fetchAll(PDO::FETCH_ASSOC);

$tahunAjaran = new TahunAjaran();
$tahun_ajaran_list = $tahunAjaran->getAll()->fetchAll(PDO::FETCH_ASSOC);

// Get current active period
$periodeAktif = new PeriodeAktif();
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
$current_semester = $periodeAktif->semester ?: '1';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate input
        $required_fields = ['nis', 'nama_siswa', 'jenis_kelamin', 'start_tahun_ajaran', 'start_semester', 'start_kelas_id'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Field $field harus diisi");
            }
        }

        // Create student first
        $siswa = new Siswa();
        $siswa->nis = $_POST['nis'];
        $siswa->nama_siswa = $_POST['nama_siswa'];
        $siswa->jenis_kelamin = $_POST['jenis_kelamin'];
        $siswa->kelas_id = $_POST['current_kelas_id'] ?: $_POST['start_kelas_id'];
        $siswa->alamat = $_POST['alamat'];
        $siswa->no_telp = $_POST['no_telp'];

        // Use a simpler create method that doesn't auto-create periods
        if ($siswa->createWithoutPeriod()) {
            $siswa_id = $siswa->conn->lastInsertId();

            // Generate historical periods
            $siswaPeriode = new SiswaPeriode();

            if ($_POST['generation_mode'] === 'auto') {
                // Auto-generate periods from start to current
                $periods = $siswaPeriode->generatePeriodSequence(
                    $_POST['start_tahun_ajaran'],
                    $_POST['start_semester'],
                    $_POST['start_kelas_id'],
                    $current_tahun_ajaran,
                    $current_semester
                );
            } else {
                // Manual period specification (to be implemented)
                $periods = [];
                // This would be expanded for manual period entry
            }

            // Validate periods before creating
            $validation_errors = $siswaPeriode->validateHistoricalPeriods($siswa_id, $periods);
            if (!empty($validation_errors)) {
                throw new Exception("Validasi gagal: " . implode(', ', $validation_errors));
            }

            // Create all periods
            if ($siswaPeriode->createHistoricalPeriods($siswa_id, $periods)) {
                $success = "Siswa berhasil didaftarkan dengan riwayat akademik lengkap (" . count($periods) . " periode)!";
                $_SESSION['success'] = $success;
                header('Location: index.php');
                exit;
            } else {
                throw new Exception("Gagal membuat riwayat periode akademik");
            }
        } else {
            throw new Exception("Gagal membuat data siswa");
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Daftarkan Siswa dengan Riwayat Akademik</h1>
        <div>
            <a href="create.php" class="btn btn-info mr-2">
                <i class="fas fa-user-plus"></i> Daftar Siswa Baru
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <!-- Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info">
            <h6 class="m-0 font-weight-bold text-white">
                <i class="fas fa-info-circle"></i> Informasi
            </h6>
        </div>
        <div class="card-body">
            <p><strong>Gunakan form ini untuk:</strong></p>
            <ul>
                <li>Mendaftarkan siswa yang sudah bersekolah sebelumnya tetapi baru diinput ke sistem</li>
                <li>Membuat riwayat akademik yang lengkap dan akurat</li>
                <li>Mengaitkan data absensi dan nilai dengan periode yang tepat</li>
            </ul>
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Penting:</strong> Pastikan data periode mulai bersekolah akurat karena ini akan mempengaruhi seluruh riwayat akademik siswa.
            </div>
        </div>
    </div>

    <!-- Registration Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-user-graduate"></i> Form Pendaftaran Siswa Historis
            </h6>
        </div>
        <div class="card-body">
            <form method="POST" id="historicalForm">
                <!-- Basic Student Information -->
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user"></i> Data Dasar Siswa
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="nis">NIS <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nis" name="nis" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="nama_siswa">Nama Lengkap <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_siswa" name="nama_siswa" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="jenis_kelamin">Jenis Kelamin <span class="text-danger">*</span></label>
                            <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" required>
                                <option value="">Pilih Jenis Kelamin</option>
                                <option value="L">Laki-laki</option>
                                <option value="P">Perempuan</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="no_telp">No. Telepon</label>
                            <input type="text" class="form-control" id="no_telp" name="no_telp">
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label for="alamat">Alamat</label>
                    <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                </div>

                <hr>

                <!-- Academic History -->
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-history"></i> Riwayat Akademik
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="start_tahun_ajaran">Tahun Ajaran Mulai Bersekolah <span class="text-danger">*</span></label>
                            <select class="form-control" id="start_tahun_ajaran" name="start_tahun_ajaran" required>
                                <option value="">Pilih Tahun Ajaran</option>
                                <?php foreach ($tahun_ajaran_list as $ta): ?>
                                    <option value="<?= $ta['tahun_ajaran'] ?>"><?= $ta['tahun_ajaran'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="start_semester">Semester Mulai <span class="text-danger">*</span></label>
                            <select class="form-control" id="start_semester" name="start_semester" required>
                                <option value="">Pilih Semester</option>
                                <option value="1">Semester 1</option>
                                <option value="2">Semester 2</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="start_kelas_id">Kelas Awal <span class="text-danger">*</span></label>
                            <select class="form-control" id="start_kelas_id" name="start_kelas_id" required>
                                <option value="">Pilih Kelas</option>
                                <?php foreach ($kelas_list as $k): ?>
                                    <option value="<?= $k['id'] ?>"><?= $k['nama_kelas'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label for="current_kelas_id">Kelas Saat Ini (Opsional)</label>
                    <select class="form-control" id="current_kelas_id" name="current_kelas_id">
                        <option value="">Sama dengan kelas awal</option>
                        <?php foreach ($kelas_list as $k): ?>
                            <option value="<?= $k['id'] ?>"><?= $k['nama_kelas'] ?></option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">
                        Jika kosong, sistem akan menggunakan kelas awal atau menghitung otomatis berdasarkan periode.
                    </small>
                </div>

                <div class="form-group mb-4">
                    <label>Mode Pembuatan Periode <span class="text-danger">*</span></label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="generation_mode" id="auto_mode" value="auto" checked>
                        <label class="form-check-label" for="auto_mode">
                            <strong>Otomatis</strong> - Sistem akan membuat semua periode dari awal hingga sekarang
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="generation_mode" id="manual_mode" value="manual" disabled>
                        <label class="form-check-label" for="manual_mode">
                            <strong>Manual</strong> - Tentukan periode secara manual (Coming Soon)
                        </label>
                    </div>
                </div>

                <!-- Current Period Info -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-calendar"></i> Periode Aktif Saat Ini</h6>
                    <p class="mb-0">
                        <strong>Tahun Ajaran:</strong> <?= htmlspecialchars($current_tahun_ajaran) ?><br>
                        <strong>Semester:</strong> <?= htmlspecialchars($current_semester) ?>
                    </p>
                </div>

                <hr>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Daftarkan Siswa dengan Riwayat
                    </button>
                    <button type="button" class="btn btn-info ml-2" onclick="previewPeriods()">
                        <i class="fas fa-eye"></i> Preview Periode
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-alt"></i> Preview Periode Akademik
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
function previewPeriods() {
    const startTahunAjaran = document.getElementById('start_tahun_ajaran').value;
    const startSemester = document.getElementById('start_semester').value;
    const startKelasId = document.getElementById('start_kelas_id').value;
    
    if (!startTahunAjaran || !startSemester || !startKelasId) {
        alert('Harap isi tahun ajaran mulai, semester, dan kelas awal terlebih dahulu');
        return;
    }
    
    // Show loading
    document.getElementById('previewContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    $('#previewModal').modal('show');
    
    // Make AJAX request to get preview
    fetch('preview_periods.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `start_tahun_ajaran=${startTahunAjaran}&start_semester=${startSemester}&start_kelas_id=${startKelasId}`
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('previewContent').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('previewContent').innerHTML = '<div class="alert alert-danger">Error loading preview</div>';
    });
}

// Form validation
document.getElementById('historicalForm').addEventListener('submit', function(e) {
    const startTahunAjaran = document.getElementById('start_tahun_ajaran').value;
    const currentTahunAjaran = '<?= $current_tahun_ajaran ?>';
    
    if (startTahunAjaran > currentTahunAjaran) {
        e.preventDefault();
        alert('Tahun ajaran mulai tidak boleh lebih besar dari tahun ajaran saat ini');
        return false;
    }
    
    return confirm('Yakin ingin mendaftarkan siswa dengan riwayat akademik? Proses ini akan membuat multiple periode akademik.');
});
</script>

<?php require_once '../template/footer.php'; ?>
