<?php
/**
 * Guide for updating school profile data
 * This script shows current school data and provides guidance for updating it
 */

require_once '../config/database.php';
require_once '../models/ProfilSekolah.php';
require_once '../models/Rapor.php';

echo "<h2>School Profile Update Guide</h2>";

// Show current data
$profilSekolah = new ProfilSekolah();
$raporModel = new Rapor();
$schoolInfo = $raporModel->getSchoolInfo();

echo "<h3>Current School Information (from database)</h3>";
echo "<div style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>";
echo "<strong>School Name:</strong> " . $schoolInfo['nama_sekolah'] . "<br>";
echo "<strong>NPSN:</strong> " . $schoolInfo['npsn'] . "<br>";
echo "<strong>Address:</strong> " . $schoolInfo['alamat_sekolah'] . "<br>";
echo "<strong>Phone:</strong> " . $schoolInfo['telepon'] . "<br>";
echo "<strong>Email:</strong> " . $schoolInfo['email'] . "<br>";
echo "<strong>Website:</strong> " . $schoolInfo['website'] . "<br>";
echo "<strong>Principal:</strong> " . $schoolInfo['kepala_sekolah'] . "<br>";
echo "<strong>Principal NIP:</strong> " . $schoolInfo['nip_kepala_sekolah'] . "<br>";
echo "<strong>Status:</strong> " . $schoolInfo['status_sekolah'] . "<br>";
echo "<strong>Education Level:</strong> " . $schoolInfo['jenjang_pendidikan'] . "<br>";
echo "</div>";

echo "<h3>How to Update School Profile</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border: 1px solid #bee5eb; margin: 10px 0;'>";
echo "<strong>Option 1: Through Admin Panel</strong><br>";
echo "1. Login to the admin panel<br>";
echo "2. Go to School Profile / Profil Sekolah menu<br>";
echo "3. Update the school information<br>";
echo "4. Save the changes<br>";
echo "<br>";

echo "<strong>Option 2: Direct Database Update (for developers)</strong><br>";
echo "You can update the data directly in the <code>profil_sekolah</code> table:<br>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
echo "UPDATE profil_sekolah SET 
    nama_sekolah = 'SMA Negeri 1 Jakarta',
    alamat_jalan = 'Jl. Merdeka No. 123',
    desa_kelurahan = 'Kelurahan Merdeka',
    kecamatan = 'Kecamatan Pusat',
    kabupaten_kota = 'Jakarta Pusat',
    provinsi = 'DKI Jakarta',
    kode_pos = '10110',
    no_telepon = '021-3456789',
    email = '<EMAIL>',
    website = 'https://www.sman1jakarta.sch.id',
    nama_kepala_sekolah = 'Dr. Ahmad Suryadi, M.Pd',
    nip_kepala_sekolah = '196801011990031001',
    npsn = '20104001'
WHERE id = 1;";
echo "</pre>";
echo "</div>";

echo "<h3>Report Card Preview</h3>";
echo "<div style='border: 2px solid #000; padding: 20px; margin: 15px 0; text-align: center; font-family: \"Times New Roman\", serif;'>";
echo "<h3 style='margin: 10px 0; font-size: 18px;'>" . $schoolInfo['nama_sekolah'] . "</h3>";
echo "<p style='margin: 5px 0; font-size: 14px;'>" . $schoolInfo['alamat_sekolah'] . "</p>";
echo "<div style='font-weight: bold; margin: 15px 0; font-size: 16px;'>RAPOR PESERTA DIDIK</div>";
echo "<div style='font-weight: bold; font-size: 16px;'>KURIKULUM 2013</div>";
echo "</div>";

echo "<h3>Integration Status</h3>";
$database = new Database();
$conn = $database->getConnection();

// Check if data exists
$query = "SELECT COUNT(*) as count FROM profil_sekolah";
$stmt = $conn->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);

echo "<div style='background: " . ($result['count'] > 0 ? "#d4edda" : "#f8d7da") . "; padding: 15px; border: 1px solid " . ($result['count'] > 0 ? "#c3e6cb" : "#f5c6cb") . "; margin: 10px 0;'>";

if ($result['count'] > 0) {
    echo "<strong>✓ Integration Status: WORKING</strong><br>";
    echo "- School profile data found in database<br>";
    echo "- Data is being correctly retrieved and displayed<br>";
    echo "- Report cards will show actual school information<br>";
    
    // Check if data looks like placeholder
    if (strpos($schoolInfo['nama_sekolah'], 'Nama Sekolah') !== false || 
        strpos($schoolInfo['kepala_sekolah'], 'Nama Kepala') !== false) {
        echo "<br><strong>⚠ Note:</strong> Current data appears to be placeholder values. Please update with real school information.";
    }
} else {
    echo "<strong>✗ Integration Status: NO DATA</strong><br>";
    echo "- No school profile data found<br>";
    echo "- System will use fallback default values<br>";
    echo "- Please add school profile data through admin panel<br>";
}

echo "</div>";

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li><strong>Update school profile data</strong> with real information (not placeholder values)</li>";
echo "<li><strong>Test the report card</strong> to see the updated information</li>";
echo "<li><strong>Verify all fields</strong> are displaying correctly</li>";
echo "</ol>";

echo "<p><em>After updating the school profile, all K13 report cards will automatically display the new information.</em></p>";
?>
