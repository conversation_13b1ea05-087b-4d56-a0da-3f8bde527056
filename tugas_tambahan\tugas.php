<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/TugasTambahan.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    header("Location: index.php");
    exit();
}

$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Get guru_id if user is a teacher
$guru_id = null;
$user = new User();
if(isset($_SESSION['user_id']) && $_SESSION['role'] == 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

$kelas = new Kelas();
$kelas_list = $kelas->getAll();

$tugasTambahan = new TugasTambahan();
if($_SESSION['role'] == 'guru' && $guru_id) {
    // Use the new method that filters by both teacher and subject
    $result_tugas = $tugasTambahan->getTugasTambahanGuruMapel($guru_id, $mapel_id, $semester, $tahun_ajaran);
} else {
    $result_tugas = $tugasTambahan->getTugasTambahanMapel($mapel_id, $semester, $tahun_ajaran);
}

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['add_tugas'])) {
        $tugasTambahan->mapel_id = $mapel_id;
        $tugasTambahan->guru_id = $guru_id;
        $tugasTambahan->judul = $_POST['judul'];
        $tugasTambahan->deskripsi = $_POST['deskripsi'];
        $tugasTambahan->tanggal = $_POST['tanggal'];
        $tugasTambahan->semester = $semester;
        $tugasTambahan->tahun_ajaran = $tahun_ajaran;

        if($tugasTambahan->create()) {
            $_SESSION['success'] = "Tugas tambahan berhasil ditambahkan!";
            header("Location: ?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
            exit();
        } else {
            $_SESSION['error'] = "Gagal menambahkan tugas tambahan!";
        }
    }
}

// Handle success and error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Tugas Tambahan - <?php echo $mapel->nama_mapel; ?></h1>
        <a href="index.php?semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if($success_msg): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if($error_msg): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tambah Tugas Tambahan</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="judul" class="form-label">Judul Tugas</label>
                                <input type="text" class="form-control" id="judul" name="judul" required>
                            </div>
                            <div class="col-md-6">
                                <label for="tanggal" class="form-label">Tanggal</label>
                                <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="deskripsi" class="form-label">Deskripsi Tugas</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"></textarea>
                        </div>
                        <div class="d-grid">
                            <button type="submit" name="add_tugas" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Tambah Tugas Tambahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Daftar Tugas Tambahan</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="tableTugas" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Judul</th>
                                    <th>Deskripsi</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                while($row = $result_tugas->fetch(PDO::FETCH_ASSOC)):
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo date('d-m-Y', strtotime($row['tanggal'])); ?></td>
                                    <td><?php echo $row['judul']; ?></td>
                                    <td><?php echo $row['deskripsi']; ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="assign.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-user-plus"></i> Pilih Siswa
                                            </a>
                                            <a href="nilai.php?id=<?php echo $row['id']; ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-edit"></i> Input Nilai
                                            </a>
                                            <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-pencil-alt"></i> Edit
                                            </a>
                                            <a href="delete.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm"
                                               onclick="return confirm('Apakah Anda yakin ingin menghapus tugas tambahan ini?');">
                                                <i class="fas fa-trash"></i> Hapus
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableTugas').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        },
        "order": [[1, "desc"]] // Sort by date descending
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
