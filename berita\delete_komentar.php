<?php
require_once '../template/header.php';
require_once '../models/KomentarBerita.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit();
}

$komentar = new KomentarBerita();

if (isset($_GET['id'])) {
    $komentar->id = $_GET['id'];
    if ($komentar->getOne()) {
        // Check if user owns the comment or is admin
        if ($komentar->user_id == $_SESSION['user_id'] || $_SESSION['role'] == 'admin') {
            $berita_id = $komentar->berita_id;
            if ($komentar->delete()) {
                $_SESSION['success_msg'] = "Komentar berhasil dihapus";
            } else {
                $_SESSION['error_msg'] = "Gagal menghapus komentar";
            }
            header("Location: view.php?id=" . $berita_id);
            exit();
        }
    }
}

header("Location: ../index.php");
exit();
