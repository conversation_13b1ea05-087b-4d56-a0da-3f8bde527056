<?php
require_once __DIR__ . '/../config/database.php';

class NilaiPengganti {
    public $conn; // Changed to public for debugging
    private $table_name = "nilai_pengganti";

    public $id;
    public $nilai_id;
    public $tugas_tambahan_siswa_id;
    public $jenis_nilai;
    public $is_average;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Create a new grade replacement
     */
    public function create() {
        // First, validate that the tugas_tambahan_siswa_id exists
        $validate_query = "SELECT id FROM tugas_tambahan_siswa WHERE id = :tugas_tambahan_siswa_id";
        $validate_stmt = $this->conn->prepare($validate_query);
        $validate_stmt->bindParam(":tugas_tambahan_siswa_id", $this->tugas_tambahan_siswa_id);
        $validate_stmt->execute();

        if ($validate_stmt->rowCount() == 0) {
            // The tugas_tambahan_siswa_id doesn't exist
            return false;
        }

        // Next, check if there's an existing replacement for this grade type
        $check_query = "SELECT id FROM " . $this->table_name . "
                      WHERE nilai_id = :nilai_id AND jenis_nilai = :jenis_nilai";

        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":nilai_id", $this->nilai_id);
        $check_stmt->bindParam(":jenis_nilai", $this->jenis_nilai);
        $check_stmt->execute();

        if ($check_stmt->rowCount() > 0) {
            // Update existing replacement
            $row = $check_stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $row['id'];
            return $this->update();
        }

        // Create new replacement
        $query = "INSERT INTO " . $this->table_name . "
                (nilai_id, tugas_tambahan_siswa_id, jenis_nilai, is_average)
                VALUES
                (:nilai_id, :tugas_tambahan_siswa_id, :jenis_nilai, :is_average)";

        $stmt = $this->conn->prepare($query);

        // Set default value for is_average if not set
        if (!isset($this->is_average)) {
            $this->is_average = 0;
        }

        // Bind values
        $stmt->bindParam(":nilai_id", $this->nilai_id);
        $stmt->bindParam(":tugas_tambahan_siswa_id", $this->tugas_tambahan_siswa_id);
        $stmt->bindParam(":jenis_nilai", $this->jenis_nilai);
        $stmt->bindParam(":is_average", $this->is_average);

        try {
            return $stmt->execute();
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log("Error in NilaiPengganti::create: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update an existing grade replacement
     */
    public function update() {
        // First, validate that the tugas_tambahan_siswa_id exists
        $validate_query = "SELECT id FROM tugas_tambahan_siswa WHERE id = :tugas_tambahan_siswa_id";
        $validate_stmt = $this->conn->prepare($validate_query);
        $validate_stmt->bindParam(":tugas_tambahan_siswa_id", $this->tugas_tambahan_siswa_id);
        $validate_stmt->execute();

        if ($validate_stmt->rowCount() == 0) {
            // The tugas_tambahan_siswa_id doesn't exist
            return false;
        }

        $query = "UPDATE " . $this->table_name . "
                SET tugas_tambahan_siswa_id = :tugas_tambahan_siswa_id, is_average = :is_average
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Set default value for is_average if not set
        if (!isset($this->is_average)) {
            $this->is_average = 0;
        }

        // Bind values
        $stmt->bindParam(":tugas_tambahan_siswa_id", $this->tugas_tambahan_siswa_id);
        $stmt->bindParam(":is_average", $this->is_average);
        $stmt->bindParam(":id", $this->id);

        try {
            return $stmt->execute();
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log("Error in NilaiPengganti::update: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a grade replacement
     */
    public function delete($nilai_id, $jenis_nilai) {
        $query = "DELETE FROM " . $this->table_name . "
                WHERE nilai_id = :nilai_id AND jenis_nilai = :jenis_nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nilai_id", $nilai_id);
        $stmt->bindParam(":jenis_nilai", $jenis_nilai);

        try {
            return $stmt->execute();
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log("Error in NilaiPengganti::delete: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all replacements for a specific grade
     */
    public function getByNilaiId($nilai_id) {
        $query = "SELECT np.*, tts.nilai as nilai_pengganti, tt.judul as judul_tugas
                FROM " . $this->table_name . " np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = :nilai_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nilai_id", $nilai_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get replacement for a specific grade type
     */
    public function getByNilaiIdAndJenis($nilai_id, $jenis_nilai) {
        $query = "SELECT np.*, tts.nilai as nilai_pengganti, tt.judul as judul_tugas
                FROM " . $this->table_name . " np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = :nilai_id AND np.jenis_nilai = :jenis_nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nilai_id", $nilai_id);
        $stmt->bindParam(":jenis_nilai", $jenis_nilai);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get all available additional assignments for a student that can be used as replacements
     */
    public function getAvailableTugasTambahan($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        // First, check if there are any tugas_tambahan records for this subject
        $check_query = "SELECT COUNT(*) as total FROM tugas_tambahan
                        WHERE mapel_id = :mapel_id
                        AND semester = :semester
                        AND tahun_ajaran = :tahun_ajaran";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":mapel_id", $mapel_id);
        $check_stmt->bindParam(":semester", $semester);
        $check_stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['total'] == 0) {
            // Log for debugging
            error_log("No tugas_tambahan records found for mapel_id: $mapel_id, semester: $semester, tahun_ajaran: $tahun_ajaran");
        }

        // Now check if this student has any tugas_tambahan_siswa records
        $check_siswa_query = "SELECT COUNT(*) as total FROM tugas_tambahan_siswa tts
                             JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                             WHERE tts.siswa_id = :siswa_id
                             AND tt.mapel_id = :mapel_id
                             AND tt.semester = :semester
                             AND tt.tahun_ajaran = :tahun_ajaran";
        $check_siswa_stmt = $this->conn->prepare($check_siswa_query);
        $check_siswa_stmt->bindParam(":siswa_id", $siswa_id);
        $check_siswa_stmt->bindParam(":mapel_id", $mapel_id);
        $check_siswa_stmt->bindParam(":semester", $semester);
        $check_siswa_stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $check_siswa_stmt->execute();
        $siswa_result = $check_siswa_stmt->fetch(PDO::FETCH_ASSOC);

        if ($siswa_result['total'] == 0) {
            // Log for debugging
            error_log("No tugas_tambahan_siswa records found for siswa_id: $siswa_id, mapel_id: $mapel_id");
        }

        // Get the available assignments
        $query = "SELECT tts.id as tugas_tambahan_siswa_id, tt.judul, tts.nilai, tt.tanggal
                FROM tugas_tambahan_siswa tts
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE tts.siswa_id = :siswa_id
                AND tt.mapel_id = :mapel_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                AND tts.status = 'sudah_dikerjakan'
                AND tts.nilai IS NOT NULL
                ORDER BY tt.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        // Log the number of results for debugging
        $count = $stmt->rowCount();
        error_log("Found $count available tugas_tambahan records for replacement");

        return $stmt;
    }

    /**
     * Calculate average grade from multiple additional assignments
     */
    public function calculateAverageNilai($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT AVG(tts.nilai) as rata_nilai, COUNT(tts.id) as jumlah_tugas
                FROM tugas_tambahan_siswa tts
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE tts.siswa_id = :siswa_id
                AND tt.mapel_id = :mapel_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                AND tts.status = 'sudah_dikerjakan'
                AND tts.nilai IS NOT NULL";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'rata_nilai' => $result['rata_nilai'] ? round($result['rata_nilai'], 2) : 0,
            'jumlah_tugas' => $result['jumlah_tugas'] ? $result['jumlah_tugas'] : 0
        ];
    }

    /**
     * Create a special record for average grade replacement
     */
    public function createAverageReplacement($nilai_id, $jenis_nilai, $siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        // First, check if there's an existing replacement for this grade type
        $check_query = "SELECT id FROM " . $this->table_name . "
                      WHERE nilai_id = :nilai_id AND jenis_nilai = :jenis_nilai";

        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":nilai_id", $nilai_id);
        $check_stmt->bindParam(":jenis_nilai", $jenis_nilai);
        $check_stmt->execute();

        if ($check_stmt->rowCount() > 0) {
            // Delete existing replacement first
            $this->delete($nilai_id, $jenis_nilai);
        }

        // Get the first tugas_tambahan_siswa_id to use as a reference
        $query = "SELECT tts.id as tugas_tambahan_siswa_id
                FROM tugas_tambahan_siswa tts
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE tts.siswa_id = :siswa_id
                AND tt.mapel_id = :mapel_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                AND tts.status = 'sudah_dikerjakan'
                AND tts.nilai IS NOT NULL
                ORDER BY tt.tanggal DESC
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return false;
        }

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $tugas_tambahan_siswa_id = $row['tugas_tambahan_siswa_id'];

        // Create new replacement
        $insert_query = "INSERT INTO " . $this->table_name . "
                (nilai_id, tugas_tambahan_siswa_id, jenis_nilai, is_average)
                VALUES
                (:nilai_id, :tugas_tambahan_siswa_id, :jenis_nilai, 1)";

        $insert_stmt = $this->conn->prepare($insert_query);

        // Bind values
        $insert_stmt->bindParam(":nilai_id", $nilai_id);
        $insert_stmt->bindParam(":tugas_tambahan_siswa_id", $tugas_tambahan_siswa_id);
        $insert_stmt->bindParam(":jenis_nilai", $jenis_nilai);

        try {
            return $insert_stmt->execute();
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log("Error in NilaiPengganti::createAverageReplacement: " . $e->getMessage());
            return false;
        }
    }
}
