<?php
class Perpustakaan {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    // Fungsi untuk kategori buku
    public function getAllKategori() {
        $query = "SELECT * FROM kategori_buku";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function tambahKategori($data) {
        $query = "INSERT INTO kategori_buku (nama_kategori) VALUES (:nama_kategori)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':nama_kategori', $data['nama_kategori']);
        return $stmt->execute();
    }

    public function editKategori($id, $data) {
        $query = "UPDATE kategori_buku SET nama_kategori = :nama_kategori WHERE id_kategori = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':nama_kategori', $data['nama_kategori']);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    public function hapusKategori($id) {
        $query = "DELETE FROM kategori_buku WHERE id_kategori = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    // Fungsi untuk buku
    public function getAllBuku() {
        $query = "SELECT b.*, k.nama_kategori 
                 FROM buku b 
                 JOIN kategori_buku k ON b.id_kategori = k.id_kategori";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getBukuById($id) {
        $query = "SELECT b.*, k.nama_kategori 
                 FROM buku b 
                 JOIN kategori_buku k ON b.id_kategori = k.id_kategori 
                 WHERE b.id_buku = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function tambahBuku($data) {
        $query = "INSERT INTO buku (judul_buku, id_kategori, pengarang, penerbit, tahun_terbit, 
                                  isbn, jumlah_buku, lokasi, tanggal_input) 
                 VALUES (:judul_buku, :id_kategori, :pengarang, :penerbit, :tahun_terbit, 
                         :isbn, :jumlah_buku, :lokasi, NOW())";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':judul_buku', $data['judul_buku']);
        $stmt->bindParam(':id_kategori', $data['id_kategori']);
        $stmt->bindParam(':pengarang', $data['pengarang']);
        $stmt->bindParam(':penerbit', $data['penerbit']);
        $stmt->bindParam(':tahun_terbit', $data['tahun_terbit']);
        $stmt->bindParam(':isbn', $data['isbn']);
        $stmt->bindParam(':jumlah_buku', $data['jumlah_buku']);
        $stmt->bindParam(':lokasi', $data['lokasi']);
        return $stmt->execute();
    }

    public function editBuku($id, $data) {
        $query = "UPDATE buku SET 
                 judul_buku = :judul_buku,
                 id_kategori = :id_kategori,
                 pengarang = :pengarang,
                 penerbit = :penerbit,
                 tahun_terbit = :tahun_terbit,
                 isbn = :isbn,
                 jumlah_buku = :jumlah_buku,
                 lokasi = :lokasi
                 WHERE id_buku = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':judul_buku', $data['judul_buku']);
        $stmt->bindParam(':id_kategori', $data['id_kategori']);
        $stmt->bindParam(':pengarang', $data['pengarang']);
        $stmt->bindParam(':penerbit', $data['penerbit']);
        $stmt->bindParam(':tahun_terbit', $data['tahun_terbit']);
        $stmt->bindParam(':isbn', $data['isbn']);
        $stmt->bindParam(':jumlah_buku', $data['jumlah_buku']);
        $stmt->bindParam(':lokasi', $data['lokasi']);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    public function hapusBuku($id) {
        $query = "DELETE FROM buku WHERE id_buku = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    // Fungsi untuk mendapatkan data anggota (siswa dan guru)
    public function getAllAnggota() {
        $query_siswa = "SELECT 
            id as id_anggota, 
            nis as nomor_induk, 
            nama_siswa as nama_anggota, 
            jenis_kelamin, 
            alamat, 
            no_telp, 
            'siswa' as tipe_anggota 
            FROM siswa 
            WHERE kelas_id IS NOT NULL";
        
        $query_guru = "SELECT 
            id as id_anggota, 
            nip as nomor_induk, 
            nama_lengkap as nama_anggota, 
            jenis_kelamin, 
            alamat, 
            no_telp, 
            'guru' as tipe_anggota 
            FROM guru 
            WHERE status = 'aktif'";
        
        $stmt_siswa = $this->db->prepare($query_siswa);
        $stmt_guru = $this->db->prepare($query_guru);
        
        $stmt_siswa->execute();
        $stmt_guru->execute();
        
        $siswa = $stmt_siswa->fetchAll(PDO::FETCH_ASSOC);
        $guru = $stmt_guru->fetchAll(PDO::FETCH_ASSOC);
        
        return array_merge($siswa, $guru);
    }

    public function getAnggotaById($id, $tipe) {
        if ($tipe == 'siswa') {
            $query = "SELECT 
                id as id_anggota, 
                nis as nomor_induk, 
                nama_siswa as nama_anggota, 
                jenis_kelamin, 
                alamat, 
                no_telp, 
                'siswa' as tipe_anggota 
                FROM siswa 
                WHERE id = :id";
        } else {
            $query = "SELECT 
                id as id_anggota, 
                nip as nomor_induk, 
                nama_lengkap as nama_anggota, 
                jenis_kelamin, 
                alamat, 
                no_telp, 
                'guru' as tipe_anggota 
                FROM guru 
                WHERE id = :id";
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Fungsi untuk peminjaman
    public function getAllPeminjaman() {
        $query = "SELECT p.*, b.judul_buku,
                 COALESCE(s.nama_siswa, g.nama_lengkap) as nama_peminjam,
                 DATEDIFF(CURRENT_DATE, p.tanggal_kembali) as hari_terlambat
                 FROM peminjaman p
                 JOIN buku b ON p.id_buku = b.id_buku
                 LEFT JOIN siswa s ON p.id_anggota = s.id AND p.tipe_anggota = 'siswa'
                 LEFT JOIN guru g ON p.id_anggota = g.id AND p.tipe_anggota = 'guru'
                 WHERE p.status = 'dipinjam'
                 ORDER BY p.tanggal_pinjam DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function generateNomorPeminjaman() {
        $config = $this->getKonfigurasi();
        $format = $config['format_nomor_pinjam'];
        
        // Dapatkan nomor urut terakhir untuk bulan ini
        $tahun = date('Y');
        $bulan = date('m');
        
        $query = "SELECT MAX(CAST(SUBSTRING_INDEX(nomor_peminjaman, '/', -1) AS UNSIGNED)) as last_number 
                 FROM peminjaman 
                 WHERE YEAR(tanggal_pinjam) = :tahun 
                 AND MONTH(tanggal_pinjam) = :bulan";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':tahun', $tahun);
        $stmt->bindParam(':bulan', $bulan);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $nomor = ($result['last_number'] ?? 0) + 1;
        
        // Replace placeholder dengan nilai aktual
        $nomor_peminjaman = str_replace(
            ['{TAHUN}', '{BULAN}', '{NOMOR}'],
            [$tahun, $bulan, str_pad($nomor, 4, '0', STR_PAD_LEFT)],
            $format
        );
        
        return $nomor_peminjaman;
    }

    public function tambahPeminjaman($data) {
        $this->db->beginTransaction();
        try {
            // Generate nomor peminjaman
            $nomor_peminjaman = $this->generateNomorPeminjaman();

            // Simpan data peminjaman
            $query = "INSERT INTO peminjaman (nomor_peminjaman, id_anggota, tipe_anggota, id_buku, jumlah_buku, jumlah_pinjam_awal, tanggal_pinjam, tanggal_kembali, status) 
                     VALUES (:nomor_peminjaman, :id_anggota, :tipe_anggota, :id_buku, :jumlah_buku, :jumlah_buku, :tanggal_pinjam, :tanggal_kembali, 'dipinjam')";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':nomor_peminjaman', $nomor_peminjaman);
            $stmt->bindParam(':id_anggota', $data['id_anggota']);
            $stmt->bindParam(':tipe_anggota', $data['tipe_anggota']);
            $stmt->bindParam(':id_buku', $data['id_buku']);
            $stmt->bindParam(':jumlah_buku', $data['jumlah_buku']);
            $stmt->bindParam(':tanggal_pinjam', $data['tanggal_pinjam']);
            $stmt->bindParam(':tanggal_kembali', $data['tanggal_kembali']);
            $stmt->execute();
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    public function getPeminjamanById($id) {
        $query = "SELECT p.*, b.judul_buku,
                 COALESCE(s.nama_siswa, g.nama_lengkap) as nama_peminjam
                 FROM peminjaman p
                 JOIN buku b ON p.id_buku = b.id_buku
                 LEFT JOIN siswa s ON p.id_anggota = s.id AND p.tipe_anggota = 'siswa'
                 LEFT JOIN guru g ON p.id_anggota = g.id AND p.tipe_anggota = 'guru'
                 WHERE p.id_peminjaman = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function updateStatusPeminjaman($id, $status) {
        $query = "UPDATE peminjaman SET status = :status WHERE id_peminjaman = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    public function updateJumlahPeminjaman($id, $jumlah_buku) {
        $query = "UPDATE peminjaman SET jumlah_buku = :jumlah_buku WHERE id_peminjaman = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':jumlah_buku', $jumlah_buku);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    public function perpanjangPeminjaman($id, $tanggal_kembali_baru) {
        $query = "UPDATE peminjaman SET tanggal_kembali = :tanggal_kembali WHERE id_peminjaman = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':tanggal_kembali', $tanggal_kembali_baru);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    // Fungsi untuk laporan
    public function getLaporanPeminjaman($tanggal_awal, $tanggal_akhir) {
        $query = "SELECT 
                    p.*,
                    b.judul_buku,
                    COALESCE(s.nama_siswa, g.nama_lengkap) as nama_peminjam,
                    DATEDIFF(CURRENT_DATE, p.tanggal_kembali) as hari_terlambat,
                    rp.tanggal_pengembalian,
                    rp.jumlah_dikembalikan,
                    (SELECT COALESCE(SUM(jumlah_dikembalikan), 0) 
                     FROM riwayat_pengembalian 
                     WHERE id_peminjaman = p.id_peminjaman) as total_dikembalikan,
                    p.jumlah_pinjam_awal as jumlah_pinjam_awal
                FROM peminjaman p
                JOIN buku b ON p.id_buku = b.id_buku
                LEFT JOIN siswa s ON p.id_anggota = s.id AND p.tipe_anggota = 'siswa'
                LEFT JOIN guru g ON p.id_anggota = g.id AND p.tipe_anggota = 'guru'
                LEFT JOIN riwayat_pengembalian rp ON p.id_peminjaman = rp.id_peminjaman
                WHERE p.tanggal_pinjam BETWEEN :tanggal_awal AND :tanggal_akhir
                ORDER BY p.tanggal_pinjam DESC, rp.tanggal_pengembalian ASC";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':tanggal_awal', $tanggal_awal);
        $stmt->bindParam(':tanggal_akhir', $tanggal_akhir);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function tambahRiwayatPengembalian($id_peminjaman, $jumlah_dikembalikan) {
        $query = "INSERT INTO riwayat_pengembalian (id_peminjaman, jumlah_dikembalikan, tanggal_pengembalian) 
                 VALUES (:id_peminjaman, :jumlah_dikembalikan, CURRENT_DATE)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id_peminjaman', $id_peminjaman);
        $stmt->bindParam(':jumlah_dikembalikan', $jumlah_dikembalikan);
        return $stmt->execute();
    }

    // Fungsi untuk konfigurasi
    public function getKonfigurasi() {
        $query = "SELECT kunci, nilai FROM konfigurasi_perpustakaan";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $config = [];
        foreach ($results as $row) {
            $config[$row['kunci']] = json_decode($row['nilai'], true);
        }
        
        // Jika konfigurasi kosong, gunakan default
        if (empty($config)) {
            $config = $this->getDefaultKonfigurasi();
            $this->simpanKonfigurasi($config);
        }
        
        return $config;
    }

    public function simpanKonfigurasi($config) {
        try {
            $this->db->beginTransaction();
            
            // Hapus konfigurasi lama
            $query = "DELETE FROM konfigurasi_perpustakaan";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            // Simpan konfigurasi baru
            $query = "INSERT INTO konfigurasi_perpustakaan (kunci, nilai) VALUES (:kunci, :nilai)";
            $stmt = $this->db->prepare($query);
            
            foreach ($config as $key => $value) {
                $stmt->execute([
                    'kunci' => $key,
                    'nilai' => json_encode($value)
                ]);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    public function getDefaultKonfigurasi() {
        return [
            'durasi_pinjam_default' => 7,
            'max_pinjam_siswa' => 2,
            'max_pinjam_guru' => 3,
            'denda_per_hari' => 1000,
            'format_nomor_pinjam' => 'PJM-{TAHUN}{BULAN}{NOMOR}',
            'status_peminjaman' => [
                'dipinjam' => 'Dipinjam',
                'kembali' => 'Dikembalikan',
                'terlambat' => 'Terlambat'
            ],
            'lokasi_rak' => [
                'RAK-A' => 'Rak A - Umum',
                'RAK-B' => 'Rak B - Pelajaran',
                'RAK-C' => 'Rak C - Referensi',
                'RAK-D' => 'Rak D - Fiksi',
                'RAK-E' => 'Rak E - Non-Fiksi'
            ],
            'pengaturan_pustakawan' => [
                'jam_buka' => '07:00',
                'jam_tutup' => '16:00',
                'hari_kerja' => ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'],
                'hak_akses' => [
                    'kelola_buku' => true,
                    'kelola_peminjaman' => true,
                    'kelola_anggota' => true,
                    'lihat_laporan' => true,
                    'export_data' => true
                ]
            ]
        ];
    }

    // Fungsi untuk pustakawan
    public function getPustakawan() {
        $query = "SELECT p.*, g.nip, g.nama_lengkap 
                 FROM pustakawan p 
                 JOIN guru g ON p.id_guru = g.id 
                 WHERE p.status = 'aktif'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function tambahPustakawan($id_guru) {
        // Cek apakah pustakawan sudah ada
        $query = "SELECT id FROM pustakawan WHERE id_guru = :id_guru";
        $stmt = $this->db->prepare($query);
        $stmt->execute(['id_guru' => $id_guru]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            // Update status jika sudah ada
            $query = "UPDATE pustakawan SET status = 'aktif' WHERE id_guru = :id_guru";
            $stmt = $this->db->prepare($query);
            return $stmt->execute(['id_guru' => $id_guru]);
        } else {
            // Tambah baru jika belum ada
            $query = "INSERT INTO pustakawan (id_guru, tanggal_mulai, status) VALUES (:id_guru, CURDATE(), 'aktif')";
            $stmt = $this->db->prepare($query);
            return $stmt->execute(['id_guru' => $id_guru]);
        }
    }

    public function hapusPustakawan($id_guru) {
        $query = "UPDATE pustakawan SET status = 'nonaktif' WHERE id_guru = :id_guru";
        $stmt = $this->db->prepare($query);
        return $stmt->execute(['id_guru' => $id_guru]);
    }
}
