<?php
require_once '../template/public_header.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';

// Get active period
$periode = new PeriodeAktif();
$periode->getActive();

// Get tahun ajaran
$tahunAjaran = new TahunAjaran();
$current_tahun_ajaran = $tahunAjaran->getActive();

// Handle error message
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['error']);
?>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-search me-2"></i> Cari Data Siswa
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error_msg)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_msg; ?>
                </div>
                <?php endif; ?>

                <p class="mb-4">
                    Masukkan NISN atau Nama Siswa untuk melihat data kehadiran, tugas, dan nilai akhir.
                </p>

                <form action="search_results.php" method="GET">
                    <div class="mb-3">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="search_type" id="search_type_nis" value="nis" checked>
                            <label class="form-check-label" for="search_type_nis">Cari berdasarkan NISN</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="search_type" id="search_type_nama" value="nama">
                            <label class="form-check-label" for="search_type_nama">Cari berdasarkan Nama</label>
                        </div>
                    </div>

                    <div class="mb-3" id="nis_input_container">
                        <label for="nis" class="form-label">NISN</label>
                        <input type="text" class="form-control" id="nis" name="nis" required
                               placeholder="Masukkan NISN">
                    </div>

                    <div class="mb-3" id="nama_input_container" style="display: none;">
                        <label for="nama" class="form-label">Nama Siswa</label>
                        <input type="text" class="form-control" id="nama" name="nama"
                               placeholder="Masukkan Nama Siswa">
                    </div>

                    <div class="mb-3">
                        <label for="semester" class="form-label">Semester</label>
                        <select class="form-select" id="semester" name="semester" required>
                            <option value="1" <?php echo $periode->semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                            <option value="2" <?php echo $periode->semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                        <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                            <?php
                            $tahun_ajaran_list = $tahunAjaran->getAll();
                            while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)) {
                                $selected = ($row['tahun_ajaran'] == $current_tahun_ajaran) ? 'selected' : '';
                                echo "<option value='" . htmlspecialchars($row['tahun_ajaran'], ENT_QUOTES) . "' {$selected}>{$row['tahun_ajaran']}</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i> Cari Data
                    </button>
                </form>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-info-circle me-2"></i> Informasi
                </h5>
            </div>
            <div class="card-body">
                <p>Halaman ini menampilkan:</p>
                <ul>
                    <li>Rata-rata kehadiran untuk setiap mata pelajaran</li>
                    <li>Status pengumpulan tugas (tanpa menampilkan nilai)</li>
                    <li>Tugas tambahan untuk siswa dengan nilai di bawah standar</li>
                    <li>Nilai akhir untuk setiap mata pelajaran</li>
                </ul>
                <p class="mb-0 text-muted">
                    <small>* Data yang ditampilkan sesuai dengan semester dan tahun ajaran yang dipilih.</small>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get radio buttons and input containers
    const searchTypeNis = document.getElementById('search_type_nis');
    const searchTypeNama = document.getElementById('search_type_nama');
    const nisContainer = document.getElementById('nis_input_container');
    const namaContainer = document.getElementById('nama_input_container');
    const nisInput = document.getElementById('nis');
    const namaInput = document.getElementById('nama');

    // Function to toggle input fields based on selected search type
    function toggleInputFields() {
        if (searchTypeNis && searchTypeNis.checked) {
            nisContainer.style.display = 'block';
            namaContainer.style.display = 'none';
            nisInput.setAttribute('required', '');
            namaInput.removeAttribute('required');
        } else if (searchTypeNama && searchTypeNama.checked) {
            nisContainer.style.display = 'none';
            namaContainer.style.display = 'block';
            nisInput.removeAttribute('required');
            namaInput.setAttribute('required', '');
        }
    }

    // Add event listeners to radio buttons if they exist
    if (searchTypeNis && searchTypeNama) {
        searchTypeNis.addEventListener('change', toggleInputFields);
        searchTypeNama.addEventListener('change', toggleInputFields);

        // Initial toggle based on default selection
        toggleInputFields();

        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                if (searchTypeNis.checked && !nisInput.value.trim()) {
                    e.preventDefault();
                    alert('Silakan masukkan NISN');
                } else if (searchTypeNama.checked && !namaInput.value.trim()) {
                    e.preventDefault();
                    alert('Silakan masukkan Nama Siswa');
                }
            });
        }
    }
});
</script>

<?php
require_once '../template/public_footer.php';
?>
