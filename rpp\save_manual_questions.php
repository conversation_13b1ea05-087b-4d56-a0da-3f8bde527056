<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Validate input
if (!isset($_POST['rpp_id']) || !isset($_POST['questions']) || empty($_POST['questions'])) {
    $_SESSION['error'] = "Data tidak lengkap atau tidak ada soal yang diisi.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_POST['rpp_id'];
$questions_data = $_POST['questions'];

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: generate_questions.php");
        exit();
    }

    // Validate RPP ownership
    $rpp = new Rpp();
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: generate_questions.php");
        exit();
    }

    // Process and validate questions
    $processed_questions = [];
    $valid_question_count = 0;
    $verb_warnings = [];

    foreach ($questions_data as $index => $question) {
        // Skip empty questions
        if (empty(trim($question['question_text']))) {
            continue;
        }

        // Validate required fields
        if (!isset($question['question_type']) || !isset($question['difficulty_level'])) {
            continue;
        }

        // Validate question type
        if (!in_array($question['question_type'], ['multiple_choice', 'essay'])) {
            continue;
        }

        // Validate difficulty level
        $valid_difficulties = ['regular', 'hots_easy', 'hots_medium', 'hots_hard'];
        if (!in_array($question['difficulty_level'], $valid_difficulties)) {
            continue;
        }

        // Validate verb appropriateness
        $verb_validation = validateQuestionVerbs(trim($question['question_text']), $question['question_type']);
        if (!$verb_validation['is_valid']) {
            $verb_warnings[] = "Soal " . ($index + 1) . ": " . $verb_validation['message'];
        }

        $processed_question = [
            'rpp_id' => $rpp_id,
            'question_text' => trim($question['question_text']),
            'question_type' => $question['question_type'],
            'difficulty_level' => $question['difficulty_level'],
            'category' => isset($question['category']) ? trim($question['category']) : 'Manual',
            'source_type' => 'manual',
            'analysis_data' => null,
            'options' => null,
            'correct_answer' => null
        ];

        // Process multiple choice options
        if ($question['question_type'] === 'multiple_choice') {
            if (isset($question['options']) && is_array($question['options'])) {
                // Filter out empty options
                $options = array_filter($question['options'], function($option) {
                    return !empty(trim($option));
                });

                if (count($options) >= 2) {
                    // Create formatted options with letters (support up to 5 options)
                    $formatted_options = [];
                    $letters = ['A', 'B', 'C', 'D', 'E'];
                    $option_index = 0;

                    foreach ($options as $option) {
                        if ($option_index < count($letters) && $option_index < 5) { // Max 5 options
                            $formatted_options[] = $letters[$option_index] . '. ' . trim($option);
                            $option_index++;
                        }
                    }

                    $processed_question['options'] = json_encode($formatted_options);

                    // Validate correct answer
                    if (isset($question['correct_answer']) &&
                        in_array($question['correct_answer'], array_slice($letters, 0, count($formatted_options)))) {
                        $processed_question['correct_answer'] = $question['correct_answer'];
                    } else {
                        // Default to first option if not specified
                        $processed_question['correct_answer'] = 'A';
                    }
                } else {
                    // Skip multiple choice questions with insufficient options
                    continue;
                }
            } else {
                // Skip multiple choice questions without options
                continue;
            }
        }

        $processed_questions[] = $processed_question;
        $valid_question_count++;
    }

    // Check if we have valid questions to save
    if (empty($processed_questions)) {
        $_SESSION['error'] = "Tidak ada soal yang valid untuk disimpan. Pastikan semua field diisi dengan benar.";
        header("Location: manual_questions.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Save questions using bulk insert
    $rppQuestion = new RppQuestion();
    $created_ids = $rppQuestion->createBulk($processed_questions);

    if (!empty($created_ids)) {
        $success_message = "Berhasil menyimpan $valid_question_count soal manual ke database.";
        if (!empty($verb_warnings)) {
            $success_message .= "\n\nPeringatan kata kerja:\n" . implode("\n", $verb_warnings);
        }
        $_SESSION['success'] = $success_message;
    } else {
        $_SESSION['error'] = "Gagal menyimpan soal ke database.";
    }

} catch (Exception $e) {
    error_log("Save Manual Questions Error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat menyimpan soal: " . $e->getMessage();
}

// Redirect back to questions list
header("Location: questions_list.php?rpp_id=" . $rpp_id);
exit();

function validateQuestionVerbs($question_text, $question_type) {
    $text = strtolower($question_text);

    // Define inappropriate verbs for multiple choice
    $inappropriate_for_mc = [
        'jelaskan', 'uraikan', 'analisis', 'evaluasi', 'deskripsikan',
        'paparkan', 'jabarkan', 'rincikan', 'elaborasi', 'sintesis',
        'ciptakan', 'rancang', 'buat', 'susun', 'kembangkan', 'simpulkan'
    ];

    // Define appropriate verbs for multiple choice
    $appropriate_for_mc = [
        'pilih', 'tentukan', 'sebutkan', 'identifikasi', 'tunjukkan',
        'bandingkan', 'kategorikan', 'klasifikasikan', 'bedakan',
        'prediksi', 'manakah', 'yang mana'
    ];

    if ($question_type === 'multiple_choice') {
        // Check for inappropriate verbs in multiple choice
        foreach ($inappropriate_for_mc as $verb) {
            if (strpos($text, $verb) !== false) {
                return [
                    'is_valid' => false,
                    'message' => "Menggunakan kata '$verb' yang memerlukan penjelasan tertulis. Gunakan kata kerja seperti: " . implode(', ', array_slice($appropriate_for_mc, 0, 5)) . ", dll."
                ];
            }
        }

        // Check if question has appropriate verbs
        $has_appropriate_verb = false;
        foreach ($appropriate_for_mc as $verb) {
            if (strpos($text, $verb) !== false) {
                $has_appropriate_verb = true;
                break;
            }
        }

        if (!$has_appropriate_verb && strpos($text, '?') !== false) {
            return [
                'is_valid' => false,
                'message' => 'Sebaiknya menggunakan kata kerja yang sesuai seperti: pilih, tentukan, manakah, identifikasi, dll.'
            ];
        }
    }

    return ['is_valid' => true, 'message' => ''];
}
?>
