<?php
/**
 * Wilayah Indonesia API Proxy
 * This script acts as a server-side proxy to fetch data from Indonesian regional APIs
 * to avoid CORS issues when calling external APIs from the browser.
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// API endpoints
$PRIMARY_API = 'https://api-alamat-wilayah-indonesia-2024.vercel.app';
$BACKUP_API = 'https://www.emsifa.com/api-wilayah-indonesia/api';

/**
 * Make HTTP request with error handling
 */
function makeRequest($url, $timeout = 10) {
    $context = stream_context_create([
        'http' => [
            'timeout' => $timeout,
            'method' => 'GET',
            'header' => [
                'Accept: application/json',
                'User-Agent: Mozilla/5.0 (compatible; WilayahProxy/1.0)'
            ],
            'ignore_errors' => true
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        error_log("Failed to fetch data from URL: $url");
        return null;
    }

    $data = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error for URL $url: " . json_last_error_msg());
        return null;
    }

    return $data;
}

/**
 * Get postal code for a village/kelurahan
 */
function getPostalCode($kodeDesa) {
    global $PRIMARY_API, $BACKUP_API;

    // Validate input
    if (empty($kodeDesa) || !is_string($kodeDesa)) {
        return [
            'success' => false,
            'message' => 'Invalid village code provided'
        ];
    }

    // Try primary API first
    $url = $PRIMARY_API . '/kodepos?kode_desa=' . urlencode($kodeDesa);
    $data = makeRequest($url);

    if ($data && is_array($data) && count($data) > 0 && isset($data[0]['kode_pos'])) {
        return [
            'success' => true,
            'data' => [
                'kode_pos' => $data[0]['kode_pos'],
                'source' => 'primary_api'
            ]
        ];
    }

    // Try backup API (note: backup API might not have postal code endpoint)
    $backupUrl = $BACKUP_API . '/kodepos?kode_desa=' . urlencode($kodeDesa);
    $backupData = makeRequest($backupUrl);

    if ($backupData && is_array($backupData) && count($backupData) > 0 && isset($backupData[0]['kode_pos'])) {
        return [
            'success' => true,
            'data' => [
                'kode_pos' => $backupData[0]['kode_pos'],
                'source' => 'backup_api'
            ]
        ];
    }

    // If no postal code found from APIs, try to get village name and extract postal code
    $villageUrl = $PRIMARY_API . '/desa/' . urlencode($kodeDesa);
    $villageData = makeRequest($villageUrl);

    if ($villageData && isset($villageData['nama_desa'])) {
        // Try to extract postal code from village name (some APIs include it)
        $villageName = $villageData['nama_desa'];
        if (preg_match('/\b(\d{5})\b/', $villageName, $matches)) {
            return [
                'success' => true,
                'data' => [
                    'kode_pos' => $matches[1],
                    'source' => 'extracted_from_name'
                ]
            ];
        }
    }

    // Try alternative approach: get district info and use common postal codes
    $districtCode = substr($kodeDesa, 0, 7); // Extract district code from village code
    $commonPostalCodes = getCommonPostalCodes($districtCode);

    if (!empty($commonPostalCodes)) {
        return [
            'success' => true,
            'data' => [
                'kode_pos' => $commonPostalCodes[0], // Use first common postal code
                'source' => 'district_common',
                'note' => 'Postal code estimated based on district'
            ]
        ];
    }

    return [
        'success' => false,
        'message' => 'Postal code not found for the specified village/kelurahan'
    ];
}

/**
 * Get common postal codes for a district (fallback method)
 */
function getCommonPostalCodes($districtCode) {
    // This is a simplified mapping of some common district codes to postal codes
    // In a real implementation, you would have a more comprehensive database
    $commonCodes = [
        // Jakarta examples
        '3171010' => ['10110', '10120'], // Jakarta Pusat
        '3171020' => ['10210', '10220'], // Jakarta Pusat
        '3172010' => ['12110', '12120'], // Jakarta Selatan
        '3173010' => ['11110', '11120'], // Jakarta Barat
        '3174010' => ['13110', '13120'], // Jakarta Timur
        '3175010' => ['14110', '14120'], // Jakarta Utara

        // Bandung examples
        '3273010' => ['40111', '40112'], // Bandung
        '3273020' => ['40113', '40114'],

        // Surabaya examples
        '3578010' => ['60111', '60112'], // Surabaya
        '3578020' => ['60113', '60114'],
    ];

    return isset($commonCodes[$districtCode]) ? $commonCodes[$districtCode] : [];
}

/**
 * Get provinces data
 */
function getProvinces() {
    global $PRIMARY_API, $BACKUP_API;

    // Try primary API first
    $url = $PRIMARY_API . '/provinsi';
    $data = makeRequest($url);

    if ($data && is_array($data) && count($data) > 0) {
        return [
            'success' => true,
            'data' => $data
        ];
    }

    // Try backup API
    $backupUrl = $BACKUP_API . '/provinces.json';
    $backupData = makeRequest($backupUrl);

    if ($backupData && is_array($backupData) && count($backupData) > 0) {
        // Convert backup API format to match primary API
        $converted = array_map(function($item) {
            return [
                'kode_provinsi' => $item['id'],
                'nama_provinsi' => $item['name']
            ];
        }, $backupData);

        return [
            'success' => true,
            'data' => $converted
        ];
    }

    return [
        'success' => false,
        'message' => 'Failed to fetch provinces data'
    ];
}

/**
 * Get regencies/cities data for a province
 */
function getRegencies($kodeProvinsi) {
    global $PRIMARY_API, $BACKUP_API;

    if (empty($kodeProvinsi)) {
        return [
            'success' => false,
            'message' => 'Province code is required'
        ];
    }

    // Try primary API first
    $url = $PRIMARY_API . '/kabupaten?kode_provinsi=' . urlencode($kodeProvinsi);
    $data = makeRequest($url);

    if ($data && is_array($data) && count($data) > 0) {
        return [
            'success' => true,
            'data' => $data
        ];
    }

    // Try backup API
    $backupUrl = $BACKUP_API . '/regencies/' . urlencode($kodeProvinsi) . '.json';
    $backupData = makeRequest($backupUrl);

    if ($backupData && is_array($backupData) && count($backupData) > 0) {
        // Convert backup API format to match primary API
        $converted = array_map(function($item) use ($kodeProvinsi) {
            return [
                'kode_kabupaten' => $item['id'],
                'nama_kabupaten' => $item['name'],
                'kode_provinsi' => $kodeProvinsi
            ];
        }, $backupData);

        return [
            'success' => true,
            'data' => $converted
        ];
    }

    return [
        'success' => false,
        'message' => 'Failed to fetch regencies data for province: ' . $kodeProvinsi
    ];
}

/**
 * Get districts data for a regency
 */
function getDistricts($kodeKabupaten) {
    global $PRIMARY_API, $BACKUP_API;

    if (empty($kodeKabupaten)) {
        return [
            'success' => false,
            'message' => 'Regency code is required'
        ];
    }

    // Try primary API first
    $url = $PRIMARY_API . '/kecamatan?kode_kabupaten=' . urlencode($kodeKabupaten);
    $data = makeRequest($url);

    if ($data && is_array($data) && count($data) > 0) {
        return [
            'success' => true,
            'data' => $data
        ];
    }

    // Try backup API
    $backupUrl = $BACKUP_API . '/districts/' . urlencode($kodeKabupaten) . '.json';
    $backupData = makeRequest($backupUrl);

    if ($backupData && is_array($backupData) && count($backupData) > 0) {
        // Convert backup API format to match primary API
        $converted = array_map(function($item) use ($kodeKabupaten) {
            return [
                'kode_kecamatan' => $item['id'],
                'nama_kecamatan' => $item['name'],
                'kode_kabupaten' => $kodeKabupaten
            ];
        }, $backupData);

        return [
            'success' => true,
            'data' => $converted
        ];
    }

    return [
        'success' => false,
        'message' => 'Failed to fetch districts data for regency: ' . $kodeKabupaten
    ];
}

/**
 * Get villages data for a district
 */
function getVillages($kodeKecamatan) {
    global $PRIMARY_API, $BACKUP_API;

    if (empty($kodeKecamatan)) {
        return [
            'success' => false,
            'message' => 'District code is required'
        ];
    }

    // Try primary API first
    $url = $PRIMARY_API . '/desa?kode_kecamatan=' . urlencode($kodeKecamatan);
    $data = makeRequest($url);

    if ($data && is_array($data) && count($data) > 0) {
        return [
            'success' => true,
            'data' => $data
        ];
    }

    // Try backup API
    $backupUrl = $BACKUP_API . '/villages/' . urlencode($kodeKecamatan) . '.json';
    $backupData = makeRequest($backupUrl);

    if ($backupData && is_array($backupData) && count($backupData) > 0) {
        // Convert backup API format to match primary API
        $converted = array_map(function($item) use ($kodeKecamatan) {
            return [
                'kode_desa' => $item['id'],
                'nama_desa' => $item['name'],
                'kode_kecamatan' => $kodeKecamatan
            ];
        }, $backupData);

        return [
            'success' => true,
            'data' => $converted
        ];
    }

    return [
        'success' => false,
        'message' => 'Failed to fetch villages data for district: ' . $kodeKecamatan
    ];
}

// Main request handler
try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('Invalid request format');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'get_postal_code':
            if (!isset($input['kode_desa'])) {
                throw new Exception('Missing kode_desa parameter');
            }
            $result = getPostalCode($input['kode_desa']);
            break;

        case 'get_provinces':
            $result = getProvinces();
            break;

        case 'get_regencies':
            if (!isset($input['kode_provinsi'])) {
                throw new Exception('Missing kode_provinsi parameter');
            }
            $result = getRegencies($input['kode_provinsi']);
            break;

        case 'get_districts':
            if (!isset($input['kode_kabupaten'])) {
                throw new Exception('Missing kode_kabupaten parameter');
            }
            $result = getDistricts($input['kode_kabupaten']);
            break;

        case 'get_villages':
            if (!isset($input['kode_kecamatan'])) {
                throw new Exception('Missing kode_kecamatan parameter');
            }
            $result = getVillages($input['kode_kecamatan']);
            break;

        default:
            throw new Exception('Unknown action: ' . $action);
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
