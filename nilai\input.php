<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Nilai.php';
require_once '../models/Siswa.php';
require_once '../models/Tugas.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../template/header.php';

if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    header("Location: index.php");
    exit();
}

$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Check if user has access to this subject
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);
    
    if ($guru_id) {
        $jadwal = new JadwalPelajaran();
        $query = "SELECT COUNT(*) as total FROM jadwal_pelajaran WHERE mapel_id = :mapel_id AND guru_id = :guru_id";
        $database = new Database();
        $conn = $database->getConnection();
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row['total'] == 0) {
            $_SESSION['error'] = "Anda tidak memiliki akses ke mata pelajaran ini.";
            header("Location: index.php");
            exit();
        }
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan untuk akun ini.";
        header("Location: index.php");
        exit();
    }
}

$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

$nilai = new Nilai();
$siswa = new Siswa();
$tugas = new Tugas();
$jadwal = new JadwalPelajaran();

// Process form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $success = true;
    $error_message = '';
    
    // Update KKM if changed
    if (isset($_POST['kkm']) && is_numeric($_POST['kkm'])) {
        $query = "UPDATE mata_pelajaran SET kkm = :kkm WHERE id = :mapel_id";
        $stmt = $conn->prepare($query);
        $kkm = floatval($_POST['kkm']);
        $stmt->bindParam(":kkm", $kkm);
        $stmt->bindParam(":mapel_id", $mapel_id);
        if (!$stmt->execute()) {
            $success = false;
            $error_message = "Gagal menyimpan nilai KKM.";
        }
    }
    
    $rumus_nilai = $_POST['rumus_nilai'];
    
    foreach($_POST['nilai'] as $siswa_id => $nilai_data) {
        $nilai = new Nilai();
        $nilai->siswa_id = $siswa_id;
        $nilai->mapel_id = $mapel_id;
        $nilai->nilai_tugas = $nilai_data['tugas'];
        $nilai->nilai_uts = $nilai_data['uts'];
        $nilai->nilai_uas = $nilai_data['uas'];
        $nilai->nilai_absen = $nilai_data['absen'];
        $nilai->rumus_nilai = $rumus_nilai;
        $nilai->semester = $semester;
        $nilai->tahun_ajaran = $tahun_ajaran;

        if(!$nilai->create()) {
            $success = false;
            $error_message = "Gagal menyimpan nilai untuk beberapa siswa.";
            break;
        }
    }

    if($success) {
        $_SESSION['success'] = "Nilai berhasil disimpan!";
        header("Location: view.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
        exit();
    }
}

// Get existing nilai
$existing_nilai = $nilai->getNilaiMapel($mapel_id, $semester, $tahun_ajaran);
$nilai_map = [];
$rumus_nilai = '';
while($row = $existing_nilai->fetch(PDO::FETCH_ASSOC)) {
    $nilai_map[$row['siswa_id']] = $row;
    if(empty($rumus_nilai) && !empty($row['rumus_nilai'])) {
        $rumus_nilai = $row['rumus_nilai'];
    }
}

// Get students from classes that have this subject in their schedule
// Use period-based retrieval to ensure we get the right students for the selected academic period
$query = "SELECT DISTINCT s.*, k.nama_kelas
          FROM siswa s
          JOIN siswa_periode sp ON s.id = sp.siswa_id
          JOIN kelas k ON sp.kelas_id = k.id
          JOIN jadwal_pelajaran j ON j.kelas_id = sp.kelas_id
          WHERE j.mapel_id = :mapel_id
          AND sp.tahun_ajaran = :tahun_ajaran
          AND sp.semester = :semester
          ORDER BY k.nama_kelas ASC, s.nama_siswa ASC";
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare($query);
$stmt->bindParam(":mapel_id", $mapel_id);
$stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
$stmt->bindParam(":semester", $semester);
$stmt->execute();
$result_siswa = $stmt;

// Function to calculate attendance score
function calculateAttendanceScore($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
    // Get the date range for the semester
    $start_date = '';
    $end_date = '';
    
    // Set date range based on semester
    if ($semester == '1') {
        // First semester typically July-December
        $year = substr($tahun_ajaran, 0, 4);
        $start_date = $year . '-07-01';
        $end_date = $year . '-12-31';
    } else {
        // Second semester typically January-June of the next year
        $year = substr($tahun_ajaran, 5, 4);
        $start_date = $year . '-01-01';
        $end_date = $year . '-06-30';
    }

    $query = "SELECT 
                COUNT(CASE WHEN da.status = 'Hadir' THEN 1 END) as hadir,
                COUNT(CASE WHEN da.status = 'Sakit' THEN 1 END) as sakit,
                COUNT(CASE WHEN da.status = 'Izin' THEN 1 END) as izin,
                COUNT(CASE WHEN da.status = 'Alpha' THEN 1 END) as alpha,
                COUNT(da.status) as total
              FROM absensi a
              JOIN detail_absensi da ON a.id = da.absensi_id
              WHERE da.siswa_id = :siswa_id 
              AND a.mapel_id = :mapel_id
              AND a.tanggal BETWEEN :start_date AND :end_date";
              
    $database = new Database();
    $conn = $database->getConnection();
    $stmt = $conn->prepare($query);
    $stmt->bindParam(":siswa_id", $siswa_id);
    $stmt->bindParam(":mapel_id", $mapel_id);
    $stmt->bindParam(":start_date", $start_date);
    $stmt->bindParam(":end_date", $end_date);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($row['total'] > 0) {
        // Calculate score: Hadir = 100%, Sakit/Izin = 75%, Alpha = 0%
        $score = (($row['hadir'] * 100) + (($row['sakit'] + $row['izin']) * 75)) / $row['total'];
        return round($score, 2);
    }
    
    return 0;
}

?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Input Nilai - <?php echo $mapel->nama_mapel; ?></h5>
                    <small class="text-muted">Semester <?php echo $semester; ?> - Tahun Ajaran <?php echo $tahun_ajaran; ?></small>
                </div>
            </div>
            <div class="card-body">
                <?php if(isset($error_message) && $error_message != ''): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <input type="hidden" name="rumus_nilai" id="rumus_nilai_hidden">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Rumus Perhitungan Nilai</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <strong>Petunjuk:</strong>
                                <ul class="mb-0">
                                    <li>Gunakan [TUGAS], [UTS], [UAS], dan [ABSEN] sebagai variabel nilai</li>
                                    <li>Contoh: ([TUGAS] * 0.3) + ([UTS] * 0.2) + ([UAS] * 0.4) + ([ABSEN] * 0.1)</li>
                                    <li>Jika rumus kosong, akan menggunakan rumus default: 30% Tugas + 30% UTS + 30% UAS + 10% Absen</li>
                                </ul>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <div class="form-group">
                                <label for="rumus_nilai" class="form-label">Rumus Nilai:</label>
                                <input type="text" class="form-control" id="rumus_nilai" name="rumus_nilai" 
                                       value="<?php echo htmlspecialchars($rumus_nilai); ?>" 
                                       placeholder="([TUGAS] * 0.3) + ([UTS] * 0.2) + ([UAS] * 0.4) + ([ABSEN] * 0.1)">
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Kriteria Ketuntasan Minimal (KKM)</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <strong>Petunjuk:</strong>
                                <ul class="mb-0">
                                    <li>KKM adalah nilai minimum yang harus dicapai siswa untuk dinyatakan tuntas</li>
                                    <li>Nilai KKM berlaku untuk semua penilaian pada mata pelajaran ini</li>
                                </ul>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <div class="form-group">
                                <label for="kkm" class="form-label">Nilai KKM:</label>
                                <input type="number" class="form-control" id="kkm" name="kkm" 
                                       min="0" max="100" step="0.01" value="<?php echo $mapel->kkm; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="tableInputNilai">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Siswa</th>
                                    <th>Kelas</th>
                                    <th>Nilai Tugas</th>
                                    <th>Nilai UTS</th>
                                    <th>Nilai UAS</th>
                                    <th>Nilai Absen</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                while($row = $result_siswa->fetch(PDO::FETCH_ASSOC)):
                                    $existing = isset($nilai_map[$row['id']]) ? $nilai_map[$row['id']] : null;
                                    
                                    // Get average assignment score
                                    $rata_rata_tugas = $tugas->getRataRataNilaiTugas(
                                        $row['id'], 
                                        $mapel_id, 
                                        $semester, 
                                        $tahun_ajaran
                                    );
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo $row['nis']; ?></td>
                                    <td><?php echo $row['nama_siswa']; ?></td>
                                    <td><?php echo $row['nama_kelas']; ?></td>
                                    <td>
                                        <input type="number" step="0.01" min="0" max="100" 
                                               class="form-control form-control-sm" 
                                               value="<?php echo $rata_rata_tugas; ?>"
                                               readonly
                                               title="Nilai tugas dihitung otomatis dari rata-rata nilai tugas">
                                        <input type="hidden" 
                                               name="nilai[<?php echo $row['id']; ?>][tugas]"
                                               value="<?php echo $rata_rata_tugas; ?>">
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" min="0" max="100" 
                                               class="form-control form-control-sm" 
                                               name="nilai[<?php echo $row['id']; ?>][uts]"
                                               value="<?php echo $existing ? $existing['nilai_uts'] : '0'; ?>"
                                               required>
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" min="0" max="100" 
                                               class="form-control form-control-sm" 
                                               name="nilai[<?php echo $row['id']; ?>][uas]"
                                               value="<?php echo $existing ? $existing['nilai_uas'] : '0'; ?>"
                                               required>
                                    </td>
                                    <td>
                                        <?php 
                                        $nilai_absen = calculateAttendanceScore(
                                            $row['id'], 
                                            $mapel_id, 
                                            $semester, 
                                            $tahun_ajaran
                                        );
                                        ?>
                                        <input type="number" step="0.01" min="0" max="100" 
                                               class="form-control form-control-sm" 
                                               name="nilai[<?php echo $row['id']; ?>][absen]"
                                               value="<?php echo $nilai_absen; ?>"
                                               readonly
                                               title="Nilai absen dihitung otomatis dari data absensi">
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-3">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary ms-2" onclick="setRumusNilai()">
                            <i class="fas fa-save"></i> Simpan Nilai
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableInputNilai').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        },
        "order": [[3, "asc"], [2, "asc"]] // Sort by Kelas then Nama
    });
});

function setRumusNilai() {
    document.getElementById('rumus_nilai_hidden').value = document.getElementById('rumus_nilai').value;
}
</script>

<?php require_once '../template/footer.php'; ?>
