############################################################
##
## PhpSpreadsheet - function name translations
##
## Português (Portuguese)
##
############################################################


##
## Funções de cubo (Cube Functions)
##
CUBEKPIMEMBER = MEMBROKPICUBO
CUBEMEMBER = MEMBROCUBO
CUBEMEMBERPROPERTY = PROPRIEDADEMEMBROCUBO
CUBERANKEDMEMBER = MEMBROCLASSIFICADOCUBO
CUBESET = CONJUNTOCUBO
CUBESETCOUNT = CONTARCONJUNTOCUBO
CUBEVALUE = VALORCUBO

##
## Funções de base de dados (Database Functions)
##
DAVERAGE = BDMÉDIA
DCOUNT = BDCONTAR
DCOUNTA = BDCONTAR.VAL
DGET = BDOBTER
DMAX = BDMÁX
DMIN = BDMÍN
DPRODUCT = BDMULTIPL
DSTDEV = BDDESVPAD
DSTDEVP = BDDESVPADP
DSUM = BDSOMA
DVAR = BDVAR
DVARP = BDVARP

##
## Funções de data e hora (Date & Time Functions)
##
DATE = DATA
DATEDIF = DATADIF
DATESTRING = DATA.CADEIA
DATEVALUE = DATA.VALOR
DAY = DIA
DAYS = DIAS
DAYS360 = DIAS360
EDATE = DATAM
EOMONTH = FIMMÊS
HOUR = HORA
ISOWEEKNUM = NUMSEMANAISO
MINUTE = MINUTO
MONTH = MÊS
NETWORKDAYS = DIATRABALHOTOTAL
NETWORKDAYS.INTL = DIATRABALHOTOTAL.INTL
NOW = AGORA
SECOND = SEGUNDO
THAIDAYOFWEEK = DIA.DA.SEMANA.TAILANDÊS
THAIMONTHOFYEAR = MÊS.DO.ANO.TAILANDÊS
THAIYEAR = ANO.TAILANDÊS
TIME = TEMPO
TIMEVALUE = VALOR.TEMPO
TODAY = HOJE
WEEKDAY = DIA.SEMANA
WEEKNUM = NÚMSEMANA
WORKDAY = DIATRABALHO
WORKDAY.INTL = DIATRABALHO.INTL
YEAR = ANO
YEARFRAC = FRAÇÃOANO

##
## Funções de engenharia (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BINADEC
BIN2HEX = BINAHEX
BIN2OCT = BINAOCT
BITAND = BIT.E
BITLSHIFT = BITDESL.ESQ
BITOR = BIT.OU
BITRSHIFT = BITDESL.DIR
BITXOR = BIT.XOU
COMPLEX = COMPLEXO
CONVERT = CONVERTER
DEC2BIN = DECABIN
DEC2HEX = DECAHEX
DEC2OCT = DECAOCT
DELTA = DELTA
ERF = FUNCERRO
ERF.PRECISE = FUNCERRO.PRECISO
ERFC = FUNCERROCOMPL
ERFC.PRECISE = FUNCERROCOMPL.PRECISO
GESTEP = DEGRAU
HEX2BIN = HEXABIN
HEX2DEC = HEXADEC
HEX2OCT = HEXAOCT
IMABS = IMABS
IMAGINARY = IMAGINÁRIO
IMARGUMENT = IMARG
IMCONJUGATE = IMCONJ
IMCOS = IMCOS
IMCOSH = IMCOSH
IMCOT = IMCOT
IMCSC = IMCSC
IMCSCH = IMCSCH
IMDIV = IMDIV
IMEXP = IMEXP
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMPOT
IMPRODUCT = IMPROD
IMREAL = IMREAL
IMSEC = IMSEC
IMSECH = IMSECH
IMSIN = IMSENO
IMSINH = IMSENOH
IMSQRT = IMRAIZ
IMSUB = IMSUBTR
IMSUM = IMSOMA
IMTAN = IMTAN
OCT2BIN = OCTABIN
OCT2DEC = OCTADEC
OCT2HEX = OCTAHEX

##
## Funções financeiras (Financial Functions)
##
ACCRINT = JUROSACUM
ACCRINTM = JUROSACUMV
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = CUPDIASINLIQ
COUPDAYS = CUPDIAS
COUPDAYSNC = CUPDIASPRÓX
COUPNCD = CUPDATAPRÓX
COUPNUM = CUPNÚM
COUPPCD = CUPDATAANT
CUMIPMT = PGTOJURACUM
CUMPRINC = PGTOCAPACUM
DB = BD
DDB = BDD
DISC = DESC
DOLLARDE = MOEDADEC
DOLLARFR = MOEDAFRA
DURATION = DURAÇÃO
EFFECT = EFETIVA
FV = VF
FVSCHEDULE = VFPLANO
INTRATE = TAXAJUROS
IPMT = IPGTO
IRR = TIR
ISPMT = É.PGTO
MDURATION = MDURAÇÃO
MIRR = MTIR
NOMINAL = NOMINAL
NPER = NPER
NPV = VAL
ODDFPRICE = PREÇOPRIMINC
ODDFYIELD = LUCROPRIMINC
ODDLPRICE = PREÇOÚLTINC
ODDLYIELD = LUCROÚLTINC
PDURATION = PDURAÇÃO
PMT = PGTO
PPMT = PPGTO
PRICE = PREÇO
PRICEDISC = PREÇODESC
PRICEMAT = PREÇOVENC
PV = VA
RATE = TAXA
RECEIVED = RECEBER
RRI = DEVOLVERTAXAJUROS
SLN = AMORT
SYD = AMORTD
TBILLEQ = OTN
TBILLPRICE = OTNVALOR
TBILLYIELD = OTNLUCRO
VDB = BDV
XIRR = XTIR
XNPV = XVAL
YIELD = LUCRO
YIELDDISC = LUCRODESC
YIELDMAT = LUCROVENC

##
## Funções de informação (Information Functions)
##
CELL = CÉL
ERROR.TYPE = TIPO.ERRO
INFO = INFORMAÇÃO
ISBLANK = É.CÉL.VAZIA
ISERR = É.ERROS
ISERROR = É.ERRO
ISEVEN = ÉPAR
ISFORMULA = É.FORMULA
ISLOGICAL = É.LÓGICO
ISNA = É.NÃO.DISP
ISNONTEXT = É.NÃO.TEXTO
ISNUMBER = É.NÚM
ISODD = ÉÍMPAR
ISREF = É.REF
ISTEXT = É.TEXTO
N = N
NA = NÃO.DISP
SHEET = FOLHA
SHEETS = FOLHAS
TYPE = TIPO

##
## Funções lógicas (Logical Functions)
##
AND = E
FALSE = FALSO
IF = SE
IFERROR = SE.ERRO
IFNA = SEND
IFS = SE.S
NOT = NÃO
OR = OU
SWITCH = PARÂMETRO
TRUE = VERDADEIRO
XOR = XOU

##
## Funções de pesquisa e referência (Lookup & Reference Functions)
##
ADDRESS = ENDEREÇO
AREAS = ÁREAS
CHOOSE = SELECIONAR
COLUMN = COL
COLUMNS = COLS
FORMULATEXT = FÓRMULA.TEXTO
GETPIVOTDATA = OBTERDADOSDIN
HLOOKUP = PROCH
HYPERLINK = HIPERLIGAÇÃO
INDEX = ÍNDICE
INDIRECT = INDIRETO
LOOKUP = PROC
MATCH = CORRESP
OFFSET = DESLOCAMENTO
ROW = LIN
ROWS = LINS
RTD = RTD
TRANSPOSE = TRANSPOR
VLOOKUP = PROCV
*RC = LC

##
## Funções matemáticas e trigonométricas (Math & Trig Functions)
##
ABS = ABS
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGREGAR
ARABIC = ÁRABE
ASIN = ASEN
ASINH = ASENH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = BASE
CEILING.MATH = ARRED.EXCESSO.MAT
CEILING.PRECISE = ARRED.EXCESSO.PRECISO
COMBIN = COMBIN
COMBINA = COMBIN.R
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMAL
DEGREES = GRAUS
ECMA.CEILING = ARRED.EXCESSO.ECMA
EVEN = PAR
EXP = EXP
FACT = FATORIAL
FACTDOUBLE = FATDUPLO
FLOOR.MATH = ARRED.DEFEITO.MAT
FLOOR.PRECISE = ARRED.DEFEITO.PRECISO
GCD = MDC
INT = INT
ISO.CEILING = ARRED.EXCESSO.ISO
LCM = MMC
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MATRIZ.DETERM
MINVERSE = MATRIZ.INVERSA
MMULT = MATRIZ.MULT
MOD = RESTO
MROUND = MARRED
MULTINOMIAL = POLINOMIAL
MUNIT = UNIDM
ODD = ÍMPAR
PI = PI
POWER = POTÊNCIA
PRODUCT = PRODUTO
QUOTIENT = QUOCIENTE
RADIANS = RADIANOS
RAND = ALEATÓRIO
RANDBETWEEN = ALEATÓRIOENTRE
ROMAN = ROMANO
ROUND = ARRED
ROUNDBAHTDOWN = ARREDOND.BAHT.BAIXO
ROUNDBAHTUP = ARREDOND.BAHT.CIMA
ROUNDDOWN = ARRED.PARA.BAIXO
ROUNDUP = ARRED.PARA.CIMA
SEC = SEC
SECH = SECH
SERIESSUM = SOMASÉRIE
SIGN = SINAL
SIN = SEN
SINH = SENH
SQRT = RAIZQ
SQRTPI = RAIZPI
SUBTOTAL = SUBTOTAL
SUM = SOMA
SUMIF = SOMA.SE
SUMIFS = SOMA.SE.S
SUMPRODUCT = SOMARPRODUTO
SUMSQ = SOMARQUAD
SUMX2MY2 = SOMAX2DY2
SUMX2PY2 = SOMAX2SY2
SUMXMY2 = SOMAXMY2
TAN = TAN
TANH = TANH
TRUNC = TRUNCAR

##
## Funções estatísticas (Statistical Functions)
##
AVEDEV = DESV.MÉDIO
AVERAGE = MÉDIA
AVERAGEA = MÉDIAA
AVERAGEIF = MÉDIA.SE
AVERAGEIFS = MÉDIA.SE.S
BETA.DIST = DIST.BETA
BETA.INV = INV.BETA
BINOM.DIST = DISTR.BINOM
BINOM.DIST.RANGE = DIST.BINOM.INTERVALO
BINOM.INV = INV.BINOM
CHISQ.DIST = DIST.CHIQ
CHISQ.DIST.RT = DIST.CHIQ.DIR
CHISQ.INV = INV.CHIQ
CHISQ.INV.RT = INV.CHIQ.DIR
CHISQ.TEST = TESTE.CHIQ
CONFIDENCE.NORM = INT.CONFIANÇA.NORM
CONFIDENCE.T = INT.CONFIANÇA.T
CORREL = CORREL
COUNT = CONTAR
COUNTA = CONTAR.VAL
COUNTBLANK = CONTAR.VAZIO
COUNTIF = CONTAR.SE
COUNTIFS = CONTAR.SE.S
COVARIANCE.P = COVARIÂNCIA.P
COVARIANCE.S = COVARIÂNCIA.S
DEVSQ = DESVQ
EXPON.DIST = DIST.EXPON
F.DIST = DIST.F
F.DIST.RT = DIST.F.DIR
F.INV = INV.F
F.INV.RT = INV.F.DIR
F.TEST = TESTE.F
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PREVISÃO.ETS
FORECAST.ETS.CONFINT = PREVISÃO.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PREVISÃO.ETS.SAZONALIDADE
FORECAST.ETS.STAT = PREVISÃO.ETS.ESTATÍSTICA
FORECAST.LINEAR = PREVISÃO.LINEAR
FREQUENCY = FREQUÊNCIA
GAMMA = GAMA
GAMMA.DIST = DIST.GAMA
GAMMA.INV = INV.GAMA
GAMMALN = LNGAMA
GAMMALN.PRECISE = LNGAMA.PRECISO
GAUSS = GAUSS
GEOMEAN = MÉDIA.GEOMÉTRICA
GROWTH = CRESCIMENTO
HARMEAN = MÉDIA.HARMÓNICA
HYPGEOM.DIST = DIST.HIPGEOM
INTERCEPT = INTERCETAR
KURT = CURT
LARGE = MAIOR
LINEST = PROJ.LIN
LOGEST = PROJ.LOG
LOGNORM.DIST = DIST.NORMLOG
LOGNORM.INV = INV.NORMALLOG
MAX = MÁXIMO
MAXA = MÁXIMOA
MAXIFS = MÁXIMO.SE.S
MEDIAN = MED
MIN = MÍNIMO
MINA = MÍNIMOA
MINIFS = MÍNIMO.SE.S
MODE.MULT = MODO.MÚLT
MODE.SNGL = MODO.SIMPLES
NEGBINOM.DIST = DIST.BINOM.NEG
NORM.DIST = DIST.NORMAL
NORM.INV = INV.NORMAL
NORM.S.DIST = DIST.S.NORM
NORM.S.INV = INV.S.NORM
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIL.EXC
PERCENTILE.INC = PERCENTIL.INC
PERCENTRANK.EXC = ORDEM.PERCENTUAL.EXC
PERCENTRANK.INC = ORDEM.PERCENTUAL.INC
PERMUT = PERMUTAR
PERMUTATIONA = PERMUTAR.R
PHI = PHI
POISSON.DIST = DIST.POISSON
PROB = PROB
QUARTILE.EXC = QUARTIL.EXC
QUARTILE.INC = QUARTIL.INC
RANK.AVG = ORDEM.MÉD
RANK.EQ = ORDEM.EQ
RSQ = RQUAD
SKEW = DISTORÇÃO
SKEW.P = DISTORÇÃO.P
SLOPE = DECLIVE
SMALL = MENOR
STANDARDIZE = NORMALIZAR
STDEV.P = DESVPAD.P
STDEV.S = DESVPAD.S
STDEVA = DESVPADA
STDEVPA = DESVPADPA
STEYX = EPADYX
T.DIST = DIST.T
T.DIST.2T = DIST.T.2C
T.DIST.RT = DIST.T.DIR
T.INV = INV.T
T.INV.2T = INV.T.2C
T.TEST = TESTE.T
TREND = TENDÊNCIA
TRIMMEAN = MÉDIA.INTERNA
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = DIST.WEIBULL
Z.TEST = TESTE.Z

##
## Funções de texto (Text Functions)
##
BAHTTEXT = TEXTO.BAHT
CHAR = CARÁT
CLEAN = LIMPARB
CODE = CÓDIGO
CONCAT = CONCAT
DOLLAR = MOEDA
EXACT = EXATO
FIND = LOCALIZAR
FIXED = FIXA
ISTHAIDIGIT = É.DÍGITO.TAILANDÊS
LEFT = ESQUERDA
LEN = NÚM.CARAT
LOWER = MINÚSCULAS
MID = SEG.TEXTO
NUMBERSTRING = NÚMERO.CADEIA
NUMBERVALUE = VALOR.NÚMERO
PHONETIC = FONÉTICA
PROPER = INICIAL.MAIÚSCULA
REPLACE = SUBSTITUIR
REPT = REPETIR
RIGHT = DIREITA
SEARCH = PROCURAR
SUBSTITUTE = SUBST
T = T
TEXT = TEXTO
TEXTJOIN = UNIRTEXTO
THAIDIGIT = DÍGITO.TAILANDÊS
THAINUMSOUND = SOM.NÚM.TAILANDÊS
THAINUMSTRING = CADEIA.NÚM.TAILANDÊS
THAISTRINGLENGTH = COMP.CADEIA.TAILANDÊS
TRIM = COMPACTAR
UNICHAR = UNICARÁT
UNICODE = UNICODE
UPPER = MAIÚSCULAS
VALUE = VALOR

##
## Funções da Web (Web Functions)
##
ENCODEURL = CODIFICAÇÃOURL
FILTERXML = FILTRARXML
WEBSERVICE = SERVIÇOWEB

##
## Funções de compatibilidade (Compatibility Functions)
##
BETADIST = DISTBETA
BETAINV = BETA.ACUM.INV
BINOMDIST = DISTRBINOM
CEILING = ARRED.EXCESSO
CHIDIST = DIST.CHI
CHIINV = INV.CHI
CHITEST = TESTE.CHI
CONCATENATE = CONCATENAR
CONFIDENCE = INT.CONFIANÇA
COVAR = COVAR
CRITBINOM = CRIT.BINOM
EXPONDIST = DISTEXPON
FDIST = DISTF
FINV = INVF
FLOOR = ARRED.DEFEITO
FORECAST = PREVISÃO
FTEST = TESTEF
GAMMADIST = DISTGAMA
GAMMAINV = INVGAMA
HYPGEOMDIST = DIST.HIPERGEOM
LOGINV = INVLOG
LOGNORMDIST = DIST.NORMALLOG
MODE = MODA
NEGBINOMDIST = DIST.BIN.NEG
NORMDIST = DIST.NORM
NORMINV = INV.NORM
NORMSDIST = DIST.NORMP
NORMSINV = INV.NORMP
PERCENTILE = PERCENTIL
PERCENTRANK = ORDEM.PERCENTUAL
POISSON = POISSON
QUARTILE = QUARTIL
RANK = ORDEM
STDEV = DESVPAD
STDEVP = DESVPADP
TDIST = DISTT
TINV = INVT
TTEST = TESTET
VAR = VAR
VARP = VARP
WEIBULL = WEIBULL
ZTEST = TESTEZ
