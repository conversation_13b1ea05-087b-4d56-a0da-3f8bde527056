<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';

// Hanya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Tentukan halaman redirect berdasarkan parameter
$redirect_page = isset($_POST['redirect']) ? $_POST['redirect'] : 'backup.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['file'])) {
    $filename = basename($_POST['file']);
    $backup_dir = __DIR__ . '/../database/backups/';
    $file_path = $backup_dir . $filename;

    // Validasi file extension
    $file_ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if ($file_ext !== 'sql') {
        $_SESSION['error'] = "Tipe file tidak valid. Hanya file .sql yang dapat dihapus.";
        header("Location: " . $redirect_page);
        exit();
    }

    // Validasi path file
    if (!file_exists($file_path) || !is_file($file_path)) {
        $_SESSION['error'] = "File backup tidak ditemukan: {$filename}";
        header("Location: " . $redirect_page);
        exit();
    }

    // Validasi path aman (file harus berada dalam direktori backup)
    $real_backup_dir = realpath($backup_dir);
    $real_file_dir = dirname(realpath($file_path));
    
    if ($real_backup_dir !== $real_file_dir) {
        $_SESSION['error'] = "Lokasi file tidak valid";
        header("Location: " . $redirect_page);
        exit();
    }

    try {
        if (unlink($file_path)) {
            $_SESSION['success'] = "File backup berhasil dihapus";
            error_log("File backup berhasil dihapus: {$file_path}");
        } else {
            $_SESSION['error'] = "Gagal menghapus file backup";
            error_log("Gagal menghapus file backup: {$file_path}");
        }
    } catch (Exception $e) {
        error_log("Error saat menghapus backup: " . $e->getMessage());
        $_SESSION['error'] = "Error saat menghapus backup";
    }
} else {
    $_SESSION['error'] = "Permintaan tidak valid";
}

header("Location: " . $redirect_page);
exit();
