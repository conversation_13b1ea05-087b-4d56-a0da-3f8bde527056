-- Table structure for Kompetensi Dasar module
-- Created for SIHADIR - Sistem Informasi Kehadiran Siswa

DROP TABLE IF EXISTS `kompetensi_dasar`;
CREATE TABLE `kompetensi_dasar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_kd` varchar(20) NOT NULL,
  `deskripsi_kd` text NOT NULL,
  `tema_subtema` varchar(255) DEFAULT NULL,
  `materi_pokok` varchar(255) DEFAULT NULL,
  `tujuan_pembelajaran` text DEFAULT NULL,
  `mapel_id` int(11) NOT NULL,
  `guru_id` int(11) NOT NULL,
  `kelas_id` int(11) DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_kd_mapel_kelas_semester` (`kode_kd`, `mapel_id`, `kelas_id`, `semester`, `tahun_ajaran`),
  KEY `mapel_id` (`mapel_id`),
  KEY `guru_id` (`guru_id`),
  KEY `kelas_id` (`kelas_id`),
  CONSTRAINT `kompetensi_dasar_ibfk_1` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kompetensi_dasar_ibfk_2` FOREIGN KEY (`guru_id`) REFERENCES `guru` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kompetensi_dasar_ibfk_3` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Sample data (optional - remove if not needed)
-- INSERT INTO `kompetensi_dasar` (`kode_kd`, `deskripsi_kd`, `tujuan_pembelajaran`, `mapel_id`, `guru_id`, `semester`, `tahun_ajaran`) VALUES
-- ('3.1', 'Memahami konsep dasar matematika', 'Siswa dapat memahami dan menerapkan konsep dasar matematika dalam kehidupan sehari-hari', 1, 1, '1', '2024/2025'),
-- ('4.1', 'Menerapkan konsep dasar matematika', 'Siswa dapat menerapkan konsep dasar matematika untuk menyelesaikan masalah', 1, 1, '1', '2024/2025');

-- Keterangan:
-- 
-- Tabel kompetensi_dasar:
-- - id: Primary key auto increment
-- - kode_kd: Kode Kompetensi Dasar (contoh: 3.1, 4.2, dll)
-- - deskripsi_kd: Deskripsi lengkap Kompetensi Dasar
-- - tujuan_pembelajaran: Tujuan pembelajaran (opsional)
-- - mapel_id: Foreign key ke tabel mata_pelajaran
-- - guru_id: Foreign key ke tabel guru (pembuat KD)
-- - semester: Semester (1 atau 2)
-- - tahun_ajaran: Tahun ajaran (format: 2024/2025)
-- - created_at: Timestamp pembuatan
-- - updated_at: Timestamp update terakhir
--
-- Constraints:
-- - unique_kd_mapel_semester: Memastikan kode KD unik per mata pelajaran, semester, dan tahun ajaran
-- - Foreign key ke mata_pelajaran dan guru dengan CASCADE delete
--
-- Fitur:
-- - CRUD Kompetensi Dasar untuk guru
-- - Filter berdasarkan mata pelajaran, semester, tahun ajaran
-- - Validasi duplikasi kode KD
-- - Integrasi dengan sistem mata pelajaran dan guru
-- - Riwayat pembuatan dan update
