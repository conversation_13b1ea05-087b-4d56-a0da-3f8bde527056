# Rapor K13 (Kurikulum 2013) Feature

## Overview
This feature implements a complete K13 (Kurikulum 2013) report card system for students in the attendance system. It provides both web view and PDF export functionality with proper K13 formatting.

## Features
- ✅ Official K13 report card format
- ✅ Student information display
- ✅ Academic grades (Pengetahuan dan <PERSON>)
- ✅ Character assessment (Sikap Spiritual dan So<PERSON>l)
- ✅ Attendance summary
- ✅ Extracurricular activities (if available)
- ✅ Student achievements (if available)
- ✅ Teacher notes
- ✅ Class promotion status (for semester 2)
- ✅ PDF export functionality
- ✅ Print-friendly layout
- ✅ Responsive design

## Files Created/Modified

### New Files
1. `public/rapor.php` - Main report card view
2. `public/rapor_pdf.php` - PDF export functionality
3. `public/assets/css/rapor-k13.css` - Dedicated CSS styling
4. `public/RAPOR_K13_README.md` - This documentation

### Modified Files
1. `models/Rapor.php` - Enhanced with K13 methods
2. `public/student_data.php` - Added navigation link

## Usage

### Accessing the Report Card
1. Go to the student dashboard (`public/student_data.php`)
2. Select the desired academic period (semester and year)
3. Click the "Lihat Rapor K13" button

### URL Parameters
- `nis` - Student's NIS (required)
- `semester` - Semester (1 or 2) (required)
- `tahun_ajaran` - Academic year (e.g., "2023/2024") (required)

### Example URLs
```
# View report card
public/rapor.php?nis=12345&semester=1&tahun_ajaran=2023/2024

# Export PDF
public/rapor_pdf.php?nis=12345&semester=1&tahun_ajaran=2023/2024
```

## K13 Report Card Sections

### A. SIKAP (Character Assessment)
- Sikap Spiritual (Spiritual Attitude)
- Sikap Sosial (Social Attitude)
- Grading scale: A (Sangat Baik), B (Baik), C (Cukup), D (Kurang)

### B. PENGETAHUAN DAN KETERAMPILAN (Knowledge and Skills)
- Subject grades with KKM (Minimum Passing Grade)
- Predicate system: A, B, C, D
- Description based on achievement level

### C. KETIDAKHADIRAN (Attendance)
- Sakit (Sick days)
- Izin (Permission days)
- Tanpa Keterangan (Absent without notice)

### D. EKSTRAKURIKULER (Extracurricular Activities)
- Activity name, grade, and description
- Only shown if data is available

### E. PRESTASI (Achievements)
- Achievement type and description
- Only shown if data is available

### F. CATATAN WALI KELAS (Homeroom Teacher Notes)
- Teacher's comments and observations

### G. KEPUTUSAN (Decision - Semester 2 only)
- Class promotion status
- Automatic determination based on grades, attendance, and behavior

## Database Requirements

The system works with existing database tables and gracefully handles missing optional tables:

### Required Tables
- `siswa` - Student data
- `kelas` - Class data
- `mata_pelajaran` - Subject data
- `nilai` - Academic grades
- `absensi` - Attendance data

### Optional Tables (with fallbacks)
- `penilaian_sikap` - Character assessment
- `ekstrakurikuler` - Extracurricular activities
- `prestasi` - Student achievements
- `catatan_wali_kelas` - Teacher notes
- `kenaikan_kelas` - Class promotion data
- `school_info` - School information

## Recent Improvements

### School Data Integration ✅ WORKING
The system now automatically retrieves school information from the existing school profile module (`profil_sekolah` table) instead of using hardcoded defaults. This includes:
- School name, NPSN, and status
- Complete address (street, village, district, city, province, postal code)
- Contact information (phone, email, website)
- Principal name and NIP
- School vision and mission

**Current Status:** The integration is working correctly and retrieving data from the database. If you see placeholder values like "Nama Sekolah" or "Nama Kepala Sekolah", these are the actual values stored in your database and should be updated through the school profile management module.

### Enhanced Grade Display Logic
- Missing or null grades now display as "-" instead of defaulting to any letter grade
- Prevents misleading grade assignments for subjects without recorded scores
- Maintains data integrity in report cards

### Smart Class Promotion Logic
- Automatically detects students in final grade (Grade 12/XII)
- Class promotion decision section is hidden for final year students (they graduate, not promote)
- Only shows promotion status for students in grades 10-11
- Considers student active status for promotion decisions

## Customization

### School Information
School information is automatically retrieved from the `profil_sekolah` table. To update:
1. Go to the school profile management module
2. Update the school details through the admin interface
3. Changes will automatically reflect in all report cards

### Grading System
The system now properly handles missing grades and uses the following thresholds:
- **Missing/Null grades**: Display as "-" (dash)
- **A**: 90-100 (Sangat Baik)
- **B**: 80-89 (Baik)
- **C**: KKM-79 (Cukup)
- **D**: Below KKM (Kurang)

### Grade Level Detection
The system automatically detects final year students using the `tingkat` table:
- Supports various naming conventions: "12", "XII", "Kelas 12", "Grade 12"
- Case-insensitive detection
- Automatically hides promotion decisions for final year students

### CSS Styling
Customize the appearance by editing `public/assets/css/rapor-k13.css`:
- Colors and fonts
- Layout spacing
- Print formatting
- Responsive breakpoints

## Print Functionality
- Click "Cetak" button for browser print dialog
- Optimized for A4 paper size
- Automatic page breaks
- Print-specific styling

## PDF Export
- Click "Export PDF" button for downloadable PDF
- Uses DomPDF library
- Maintains proper K13 formatting
- Includes all report card sections

## Error Handling
- Validates required parameters
- Graceful fallbacks for missing data
- User-friendly error messages
- Redirects to appropriate pages on errors

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Print functionality works across browsers
- Responsive design for mobile devices

## Troubleshooting

### Common Issues
1. **PDF not generating**: Check DomPDF installation and permissions
2. **Missing data**: Verify database tables and relationships
3. **Styling issues**: Check CSS file path and browser cache
4. **Print problems**: Ensure print CSS media queries are working

### Debug Mode
Add error reporting to troubleshoot issues:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Future Enhancements
- Digital signatures
- Batch PDF generation
- Email delivery
- Parent portal integration
- Multi-language support
- Custom report templates
