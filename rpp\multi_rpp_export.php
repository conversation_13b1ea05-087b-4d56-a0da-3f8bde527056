<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Style\Font;

// Validasi parameter
if (!isset($_GET['exam_id']) || !isset($_GET['format'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: multi_rpp_list.php");
    exit();
}

$exam_id = $_GET['exam_id'];
$format = $_GET['format'];

if (!in_array($format, ['pdf', 'word'])) {
    $_SESSION['error'] = "Format export tidak valid.";
    header("Location: multi_rpp_list.php");
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data ujian multi-RPP
$multiRppExam = new MultiRppExam();
$stmt = $multiRppExam->getOne($exam_id);
$exam_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    header("Location: multi_rpp_list.php");
    exit();
}

// Ambil soal-soal ujian
$questions_stmt = $multiRppExam->getQuestionsByExamId($exam_id);
$questions = [];
while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
    $questions[] = $question;
}

if (empty($questions)) {
    $_SESSION['error'] = "Tidak ada soal untuk diekspor.";
    header("Location: multi_rpp_detail.php?exam_id=" . $exam_id);
    exit();
}

// Decode JSON data
$selected_rpp_ids = json_decode($exam_data['selected_rpp_ids'], true);
$question_distribution = json_decode($exam_data['question_distribution'], true);

// Get application info
$app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
$app_version = "v2.19.0";

try {
    if ($format === 'pdf') {
        exportToPDF($exam_data, $questions, $app_name, $app_version);
    } else {
        exportToWord($exam_data, $questions, $app_name, $app_version);
    }
} catch (Exception $e) {
    $_SESSION['error'] = "Gagal export ujian: " . $e->getMessage();
    header("Location: multi_rpp_detail.php?exam_id=" . $exam_id);
    exit();
}

function exportToPDF($exam_data, $questions, $app_name, $app_version) {
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    $options->set('isHtml5ParserEnabled', true);

    $dompdf = new Dompdf($options);
    
    $html = generateExamHTML($exam_data, $questions, $app_name, $app_version);
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    $filename = 'Multi_RPP_Exam_' . str_replace(' ', '_', $exam_data['exam_title']) . '_' . date('Y-m-d') . '.pdf';
    $dompdf->stream($filename, array('Attachment' => true));
}

function exportToWord($exam_data, $questions, $app_name, $app_version) {
    $phpWord = new PhpWord();
    
    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator($app_name);
    $properties->setTitle('Multi-RPP Exam: ' . $exam_data['exam_title']);
    $properties->setDescription('Generated by ' . $app_name . ' ' . $app_version);
    
    $section = $phpWord->addSection([
        'marginTop' => 1134,
        'marginBottom' => 1134,
        'marginLeft' => 1134,
        'marginRight' => 1134
    ]);
    
    // Header
    $headerStyle = ['bold' => true, 'size' => 16, 'name' => 'Arial'];
    $section->addText($exam_data['exam_title'], $headerStyle, ['alignment' => 'center']);
    $section->addTextBreak();
    
    // Exam info
    $infoStyle = ['size' => 11, 'name' => 'Arial'];
    $section->addText('Jenis Ujian: ' . $exam_data['exam_type'], $infoStyle);
    $section->addText('Semester: ' . $exam_data['semester'], $infoStyle);
    $section->addText('Tahun Ajaran: ' . $exam_data['tahun_ajaran'], $infoStyle);
    $section->addText('Durasi: ' . $exam_data['exam_duration'] . ' menit', $infoStyle);
    $section->addText('Total Skor: ' . $exam_data['total_score'], $infoStyle);
    $section->addTextBreak(2);
    
    // Instructions
    $section->addText('PETUNJUK UMUM:', ['bold' => true, 'size' => 12, 'name' => 'Arial']);
    $section->addText('1. Bacalah setiap soal dengan teliti sebelum menjawab', $infoStyle);
    $section->addText('2. Pilihlah jawaban yang paling tepat untuk soal pilihan ganda', $infoStyle);
    $section->addText('3. Jawablah soal essay dengan jelas dan lengkap', $infoStyle);
    $section->addText('4. Periksa kembali jawaban Anda sebelum mengumpulkan', $infoStyle);
    $section->addTextBreak(2);
    
    // Questions
    $questionStyle = ['size' => 11, 'name' => 'Arial'];
    $questionNumberStyle = ['bold' => true, 'size' => 11, 'name' => 'Arial'];
    
    foreach ($questions as $index => $question) {
        $questionNumber = $index + 1;
        
        // Question number and text
        $section->addText($questionNumber . '. ' . $question['question_text'], $questionNumberStyle);
        $section->addTextBreak();
        
        // Options for multiple choice
        if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])) {
            $options = json_decode($question['options'], true);
            foreach ($options as $option) {
                $section->addText('   ' . $option, $questionStyle);
            }
            $section->addTextBreak();
        }
        
        // Source info
        $section->addText('(Sumber: ' . $question['tema_subtema'] . ')', 
                         ['italic' => true, 'size' => 9, 'name' => 'Arial']);
        $section->addTextBreak(2);
    }
    
    // Footer
    $section->addTextBreak(2);
    $section->addText('Generated by ' . $app_name . ' ' . $app_version . ' on ' . date('d/m/Y H:i'), 
                     ['size' => 8, 'name' => 'Arial', 'color' => '666666'], 
                     ['alignment' => 'center']);
    
    $filename = 'Multi_RPP_Exam_' . str_replace(' ', '_', $exam_data['exam_title']) . '_' . date('Y-m-d') . '.docx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $writer = IOFactory::createWriter($phpWord, 'Word2007');
    $writer->save('php://output');
    exit();
}

function generateExamHTML($exam_data, $questions, $app_name, $app_version) {
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Multi-RPP Exam: ' . htmlspecialchars($exam_data['exam_title']) . '</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                font-size: 12px; 
                line-height: 1.4; 
                margin: 20px;
            }
            .header { 
                text-align: center; 
                margin-bottom: 30px; 
                border-bottom: 2px solid #333;
                padding-bottom: 15px;
            }
            .exam-title { 
                font-size: 18px; 
                font-weight: bold; 
                margin-bottom: 10px; 
            }
            .exam-info { 
                margin-bottom: 20px; 
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
            }
            .exam-info table {
                width: 100%;
                border-collapse: collapse;
            }
            .exam-info td {
                padding: 5px;
                border: none;
            }
            .instructions {
                margin-bottom: 25px;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 5px;
            }
            .instructions h3 {
                margin-top: 0;
                color: #495057;
            }
            .question { 
                margin-bottom: 25px; 
                page-break-inside: avoid;
            }
            .question-number { 
                font-weight: bold; 
                margin-bottom: 8px; 
            }
            .question-text { 
                margin-bottom: 10px; 
                line-height: 1.5;
            }
            .options { 
                margin-left: 20px; 
                margin-bottom: 10px; 
            }
            .option { 
                margin-bottom: 5px; 
            }
            .source-info {
                font-style: italic;
                font-size: 10px;
                color: #666;
                margin-top: 10px;
            }
            .footer { 
                margin-top: 40px; 
                text-align: center; 
                font-size: 10px; 
                color: #666; 
                border-top: 1px solid #ddd;
                padding-top: 15px;
            }
            .page-break { 
                page-break-before: always; 
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="exam-title">' . htmlspecialchars($exam_data['exam_title']) . '</div>
        </div>
        
        <div class="exam-info">
            <table>
                <tr>
                    <td width="150"><strong>Jenis Ujian:</strong></td>
                    <td>' . htmlspecialchars($exam_data['exam_type']) . '</td>
                    <td width="150"><strong>Semester:</strong></td>
                    <td>' . htmlspecialchars($exam_data['semester']) . '</td>
                </tr>
                <tr>
                    <td><strong>Tahun Ajaran:</strong></td>
                    <td>' . htmlspecialchars($exam_data['tahun_ajaran']) . '</td>
                    <td><strong>Durasi:</strong></td>
                    <td>' . htmlspecialchars($exam_data['exam_duration']) . ' menit</td>
                </tr>
                <tr>
                    <td><strong>Total Skor:</strong></td>
                    <td>' . htmlspecialchars($exam_data['total_score']) . '</td>
                    <td><strong>Total Soal:</strong></td>
                    <td>' . count($questions) . ' soal</td>
                </tr>
            </table>
        </div>
        
        <div class="instructions">
            <h3>PETUNJUK UMUM:</h3>
            <ol>
                <li>Bacalah setiap soal dengan teliti sebelum menjawab</li>
                <li>Pilihlah jawaban yang paling tepat untuk soal pilihan ganda</li>
                <li>Jawablah soal essay dengan jelas dan lengkap</li>
                <li>Periksa kembali jawaban Anda sebelum mengumpulkan</li>
            </ol>
        </div>';
    
    foreach ($questions as $index => $question) {
        $questionNumber = $index + 1;
        
        $html .= '<div class="question">
            <div class="question-number">' . $questionNumber . '.</div>
            <div class="question-text">' . nl2br(htmlspecialchars($question['question_text'])) . '</div>';
        
        if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])) {
            $options = json_decode($question['options'], true);
            $html .= '<div class="options">';
            foreach ($options as $option) {
                $html .= '<div class="option">' . htmlspecialchars($option) . '</div>';
            }
            $html .= '</div>';
        }
        
        $html .= '<div class="source-info">Sumber: ' . htmlspecialchars($question['tema_subtema']) . '</div>
        </div>';
    }
    
    $html .= '<div class="footer">
            Generated by ' . $app_name . ' ' . $app_version . ' on ' . date('d/m/Y H:i') . '
        </div>
    </body>
    </html>';
    
    return $html;
}
?>
