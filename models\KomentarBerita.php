<?php
if (!defined('ABSEN_PATH')) {
    define('ABSEN_PATH', dirname(dirname(__FILE__)));
}
require_once ABSEN_PATH . '/config/database.php';

class KomentarBerita {
    private $conn;
    private $table_name = "komentar_berita";

    public $id;
    public $berita_id;
    public $user_id;
    public $komentar;
    public $parent_id;
    public $created_at;

    public function __construct() {
        $db = new database();
        $this->conn = $db->getConnection();
    }

    public function getConnection() {
        return $this->conn;
    }

    public function getOne() {
        $query = "SELECT k.*, u.nama_lengkap as nama_user 
                FROM " . $this->table_name . " k
                LEFT JOIN users u ON k.user_id = u.id
                WHERE k.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->berita_id = $row['berita_id'];
            $this->user_id = $row['user_id'];
            $this->komentar = $row['komentar'];
            $this->parent_id = $row['parent_id'];
            $this->created_at = $row['created_at'];
            return true;
        }
        return false;
    }

    public function getByBerita($berita_id) {
        $query = "WITH RECURSIVE comment_tree AS (
            -- Base case: get all root comments (no parent)
            SELECT 
                k.*,
                u.nama_lengkap as nama_user,
                0 as level,
                CAST(k.id AS CHAR(200)) as path
            FROM " . $this->table_name . " k
            LEFT JOIN users u ON k.user_id = u.id
            WHERE k.berita_id = :berita_id AND k.parent_id IS NULL
            
            UNION ALL
            
            -- Recursive case: get replies
            SELECT 
                k.*,
                u.nama_lengkap as nama_user,
                ct.level + 1,
                CONCAT(ct.path, ',', k.id)
            FROM " . $this->table_name . " k
            LEFT JOIN users u ON k.user_id = u.id
            INNER JOIN comment_tree ct ON k.parent_id = ct.id
            WHERE k.berita_id = :berita_id
        )
        SELECT * FROM comment_tree
        ORDER BY path, created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->execute();
        return $stmt;
    }

    public function getTotalKomentar($berita_id) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE berita_id = :berita_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getReplies($parent_id) {
        $query = "SELECT k.*, u.nama_lengkap as nama_user 
                FROM " . $this->table_name . " k
                LEFT JOIN users u ON k.user_id = u.id
                WHERE k.parent_id = :parent_id
                ORDER BY k.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":parent_id", $parent_id);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                (berita_id, user_id, komentar, parent_id) 
                VALUES (:berita_id, :user_id, :komentar, :parent_id)";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(":berita_id", $this->berita_id);
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":komentar", $this->komentar);
        $stmt->bindParam(":parent_id", $this->parent_id);

        if ($stmt->execute()) {
            // Update last_comment_at in berita table
            $query = "UPDATE berita SET last_comment_at = CURRENT_TIMESTAMP WHERE id = :berita_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":berita_id", $this->berita_id);
            $stmt->execute();
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                SET komentar = :komentar 
                WHERE id = :id AND user_id = :user_id";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(":komentar", $this->komentar);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":user_id", $this->user_id);

        return $stmt->execute();
    }

    public function delete() {
        // Delete all replies first
        $query = "DELETE FROM " . $this->table_name . " WHERE parent_id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        // Then delete the comment
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    public function getAll($offset = 0, $limit = 10) {
        $query = "SELECT k.*, u.nama_lengkap as nama_user, b.judul as judul_berita 
                FROM " . $this->table_name . " k
                LEFT JOIN users u ON k.user_id = u.id
                LEFT JOIN berita b ON k.berita_id = b.id
                ORDER BY k.created_at DESC
                LIMIT :offset, :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    public function getTotal() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }
}
