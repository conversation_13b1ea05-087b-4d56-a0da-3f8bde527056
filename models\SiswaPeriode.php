<?php
require_once __DIR__ . '/../config/database.php';

class SiswaPeriode {
    private $conn;
    private $table_name = "siswa_periode";
    private $siswa_table = "siswa";

    public $id;
    public $siswa_id;
    public $kelas_id;
    public $tahun_ajaran;
    public $semester;
    public $status;
    public $tanggal_mulai;
    public $tanggal_selesai;
    public $is_current;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get students by academic period
     */
    public function getSiswaByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status,
                    sp.is_current
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.is_current = 1";

        if ($kelas_id) {
            $query .= " AND sp.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        
        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }
        
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get all periods for a specific student
     */
    public function getPeriodesBySiswa($siswa_id) {
        $query = "SELECT 
                    sp.*,
                    k.nama_kelas,
                    s.nama_siswa,
                    s.nis
                FROM " . $this->table_name . " sp
                JOIN kelas k ON sp.kelas_id = k.id
                JOIN " . $this->siswa_table . " s ON sp.siswa_id = s.id
                WHERE sp.siswa_id = :siswa_id
                ORDER BY sp.tahun_ajaran DESC, sp.semester DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get current period for a student
     */
    public function getCurrentPeriodeBySiswa($siswa_id) {
        $query = "SELECT 
                    sp.*,
                    k.nama_kelas,
                    s.nama_siswa,
                    s.nis
                FROM " . $this->table_name . " sp
                JOIN kelas k ON sp.kelas_id = k.id
                JOIN " . $this->siswa_table . " s ON sp.siswa_id = s.id
                WHERE sp.siswa_id = :siswa_id
                AND sp.is_current = 1
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get students by academic period and class
     */
    public function getSiswaByPeriodeAndKelas($tahun_ajaran, $semester, $kelas_id) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status,
                    sp.is_current
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.kelas_id = :kelas_id
                AND sp.is_current = 1
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Deactivate current period for a student
     */
    public function deactivateCurrentPeriod($siswa_id) {
        $query = "UPDATE " . $this->table_name . "
                 SET is_current = 0
                 WHERE siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);

        return $stmt->execute();
    }

    /**
     * Add student to a new academic period
     */
    public function addSiswaToNewPeriode($siswa_id, $kelas_id, $tahun_ajaran, $semester) {
        try {
            $this->conn->beginTransaction();

            // Mark previous periods as not current
            $query = "UPDATE " . $this->table_name . "
                     SET is_current = 0
                     WHERE siswa_id = :siswa_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            if (!$stmt->execute()) {
                $errorInfo = $stmt->errorInfo();
                throw new Exception("Failed to update previous periods: " . $errorInfo[2]);
            }

            // Add new period
            $query = "INSERT INTO " . $this->table_name . "
                     (siswa_id, kelas_id, tahun_ajaran, semester, is_current, status)
                     VALUES (:siswa_id, :kelas_id, :tahun_ajaran, :semester, 1, 'aktif')";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
            $stmt->bindParam(':semester', $semester);

            if (!$stmt->execute()) {
                $errorInfo = $stmt->errorInfo();
                throw new Exception("Failed to insert new period: " . $errorInfo[2]);
            }

            // Update siswa current period reference
            $query = "UPDATE " . $this->siswa_table . "
                     SET kelas_id = :kelas_id,
                         tahun_ajaran_current = :tahun_ajaran,
                         semester_current = :semester
                     WHERE id = :siswa_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
            $stmt->bindParam(':semester', $semester);

            if (!$stmt->execute()) {
                $errorInfo = $stmt->errorInfo();
                throw new Exception("Failed to update siswa current period: " . $errorInfo[2]);
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            // Log error for debugging
            error_log("SiswaPeriode addSiswaToNewPeriode error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get available academic years and semesters
     */
    public function getAvailablePeriodes() {
        $query = "SELECT DISTINCT tahun_ajaran, semester
                 FROM " . $this->table_name . "
                 ORDER BY tahun_ajaran DESC, semester DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get student data by NIS for a specific period
     */
    public function getSiswaByNISAndPeriode($nis, $tahun_ajaran, $semester) {
        $query = "SELECT
                    s.*,
                    sp.kelas_id,
                    k.nama_kelas,
                    sp.tahun_ajaran,
                    sp.semester,
                    sp.status
                FROM " . $this->siswa_table . " s
                JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                WHERE s.nis = :nis
                AND sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':nis', $nis);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get students who don't have any period assignments
     */
    public function getSiswaWithoutPeriods($kelas_id = null) {
        $query = "SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.alamat,
                    s.no_telp,
                    s.kelas_id,
                    k.nama_kelas
                FROM " . $this->siswa_table . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                WHERE sp.siswa_id IS NULL";

        if ($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);

        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get count of students without period assignments
     */
    public function getCountSiswaWithoutPeriods($kelas_id = null) {
        $query = "SELECT COUNT(*) as total
                FROM " . $this->siswa_table . " s
                LEFT JOIN " . $this->table_name . " sp ON s.id = sp.siswa_id
                WHERE sp.siswa_id IS NULL";

        if ($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $stmt = $this->conn->prepare($query);

        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Create multiple academic periods for a student (for historical data)
     */
    public function createHistoricalPeriods($siswa_id, $periods_data) {
        try {
            $this->conn->beginTransaction();

            // First, deactivate all existing periods for this student
            $query = "UPDATE " . $this->table_name . "
                     SET is_current = 0
                     WHERE siswa_id = :siswa_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->execute();

            $latest_period = null;
            $latest_kelas_id = null;

            // Create each period
            foreach ($periods_data as $period) {
                $query = "INSERT INTO " . $this->table_name . "
                         (siswa_id, kelas_id, tahun_ajaran, semester, status, tanggal_mulai, tanggal_selesai, is_current)
                         VALUES (:siswa_id, :kelas_id, :tahun_ajaran, :semester, :status, :tanggal_mulai, :tanggal_selesai, :is_current)
                         ON DUPLICATE KEY UPDATE
                         kelas_id = VALUES(kelas_id),
                         status = VALUES(status),
                         tanggal_mulai = VALUES(tanggal_mulai),
                         tanggal_selesai = VALUES(tanggal_selesai),
                         is_current = VALUES(is_current)";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':siswa_id', $siswa_id);
                $stmt->bindParam(':kelas_id', $period['kelas_id']);
                $stmt->bindParam(':tahun_ajaran', $period['tahun_ajaran']);
                $stmt->bindParam(':semester', $period['semester']);
                $stmt->bindParam(':status', $period['status']);
                $stmt->bindParam(':tanggal_mulai', $period['tanggal_mulai']);
                $stmt->bindParam(':tanggal_selesai', $period['tanggal_selesai']);
                $stmt->bindParam(':is_current', $period['is_current']);

                if (!$stmt->execute()) {
                    $errorInfo = $stmt->errorInfo();
                    throw new Exception("Failed to create period {$period['tahun_ajaran']}-{$period['semester']}: " . $errorInfo[2]);
                }

                // Track the latest period for updating siswa table
                if ($period['is_current'] == 1) {
                    $latest_period = $period;
                    $latest_kelas_id = $period['kelas_id'];
                }
            }

            // Update siswa table with the current period info
            if ($latest_period) {
                $query = "UPDATE " . $this->siswa_table . "
                         SET kelas_id = :kelas_id,
                             tahun_ajaran_current = :tahun_ajaran,
                             semester_current = :semester
                         WHERE id = :siswa_id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':siswa_id', $siswa_id);
                $stmt->bindParam(':kelas_id', $latest_kelas_id);
                $stmt->bindParam(':tahun_ajaran', $latest_period['tahun_ajaran']);
                $stmt->bindParam(':semester', $latest_period['semester']);
                $stmt->execute();
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("SiswaPeriode createHistoricalPeriods error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate period sequence from start to current period
     */
    public function generatePeriodSequence($start_tahun_ajaran, $start_semester, $start_kelas_id, $end_tahun_ajaran = null, $end_semester = null) {
        $periods = [];

        // Get current active period if end not specified
        if (!$end_tahun_ajaran || !$end_semester) {
            $periode_aktif = new PeriodeAktif();
            $periode_aktif->getActive();
            $end_tahun_ajaran = $periode_aktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
            $end_semester = $periode_aktif->semester ?: '1';
        }

        // Parse start year
        $start_year = (int)substr($start_tahun_ajaran, 0, 4);
        $end_year = (int)substr($end_tahun_ajaran, 0, 4);

        $current_year = $start_year;
        $current_semester = (int)$start_semester;
        $current_kelas_id = $start_kelas_id;

        while (true) {
            $tahun_ajaran = $current_year . '/' . ($current_year + 1);

            // Check if we've reached the end
            if ($tahun_ajaran == $end_tahun_ajaran && $current_semester > (int)$end_semester) {
                break;
            }
            if ($current_year > $end_year) {
                break;
            }

            // Get semester dates
            $dates = $this->getSemesterDates($tahun_ajaran, $current_semester);

            $periods[] = [
                'tahun_ajaran' => $tahun_ajaran,
                'semester' => (string)$current_semester,
                'kelas_id' => $current_kelas_id,
                'status' => 'aktif',
                'tanggal_mulai' => $dates['mulai'],
                'tanggal_selesai' => $dates['selesai'],
                'is_current' => ($tahun_ajaran == $end_tahun_ajaran && $current_semester == (int)$end_semester) ? 1 : 0
            ];

            // Move to next semester
            if ($current_semester == 1) {
                $current_semester = 2;
            } else {
                $current_semester = 1;
                $current_year++;
                // Assume student moves up one grade level each year
                // This can be customized based on school rules
            }

            // Stop if we've passed the end period
            if ($tahun_ajaran == $end_tahun_ajaran && $current_semester > (int)$end_semester) {
                break;
            }
        }

        return $periods;
    }

    /**
     * Get semester dates for a given academic year and semester
     */
    private function getSemesterDates($tahun_ajaran, $semester) {
        try {
            $query = "SELECT
                        CASE WHEN :semester = 1 THEN semester_1_mulai ELSE semester_2_mulai END as mulai,
                        CASE WHEN :semester = 1 THEN semester_1_selesai ELSE semester_2_selesai END as selesai
                      FROM tahun_ajaran
                      WHERE tahun_ajaran = :tahun_ajaran";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
            $stmt->bindParam(':semester', $semester);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result) {
                return [
                    'mulai' => $result['mulai'],
                    'selesai' => $result['selesai']
                ];
            }
        } catch (Exception $e) {
            error_log("Error getting semester dates: " . $e->getMessage());
        }

        // Fallback to default dates if tahun_ajaran not found
        $year = (int)substr($tahun_ajaran, 0, 4);
        if ($semester == 1) {
            return [
                'mulai' => $year . '-07-01',
                'selesai' => $year . '-12-31'
            ];
        } else {
            return [
                'mulai' => ($year + 1) . '-01-01',
                'selesai' => ($year + 1) . '-06-30'
            ];
        }
    }

    /**
     * Update period status (e.g., mark as graduated)
     */
    public function updatePeriodeStatus($siswa_id, $tahun_ajaran, $semester, $status) {
        $query = "UPDATE " . $this->table_name . "
                 SET status = :status
                 WHERE siswa_id = :siswa_id
                 AND tahun_ajaran = :tahun_ajaran
                 AND semester = :semester";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':status', $status);
        return $stmt->execute();
    }

    /**
     * Get count of students by period
     */
    public function getCountByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $query = "SELECT COUNT(*) as total
                 FROM " . $this->table_name . " sp
                 WHERE sp.tahun_ajaran = :tahun_ajaran
                 AND sp.semester = :semester";
        
        if ($kelas_id) {
            $query .= " AND sp.kelas_id = :kelas_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        
        if ($kelas_id) {
            $stmt->bindParam(':kelas_id', $kelas_id);
        }
        
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    /**
     * Check if student exists in a specific period
     */
    public function existsInPeriode($siswa_id, $tahun_ajaran, $semester) {
        $query = "SELECT COUNT(*) as count
                 FROM " . $this->table_name . "
                 WHERE siswa_id = :siswa_id
                 AND tahun_ajaran = :tahun_ajaran
                 AND semester = :semester";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }

    /**
     * Validate historical periods data
     */
    public function validateHistoricalPeriods($siswa_id, $periods_data) {
        $errors = [];

        // Check for duplicate periods
        $period_keys = [];
        foreach ($periods_data as $index => $period) {
            $key = $period['tahun_ajaran'] . '-' . $period['semester'];
            if (in_array($key, $period_keys)) {
                $errors[] = "Periode duplikat ditemukan: {$period['tahun_ajaran']} Semester {$period['semester']}";
            }
            $period_keys[] = $key;
        }

        // Check if only one period is marked as current
        $current_count = count(array_filter($periods_data, function($p) { return $p['is_current'] == 1; }));
        if ($current_count > 1) {
            $errors[] = "Hanya satu periode yang boleh ditandai sebagai periode aktif";
        }

        // Check for existing periods in database
        if ($siswa_id) {
            foreach ($periods_data as $period) {
                if ($this->existsInPeriode($siswa_id, $period['tahun_ajaran'], $period['semester'])) {
                    $errors[] = "Periode {$period['tahun_ajaran']} Semester {$period['semester']} sudah ada untuk siswa ini";
                }
            }
        }

        return $errors;
    }

    /**
     * Validate single period data
     */
    public function validatePeriodData($period_data) {
        $errors = [];

        // Required fields
        $required_fields = ['tahun_ajaran', 'semester', 'kelas_id', 'status'];
        foreach ($required_fields as $field) {
            if (empty($period_data[$field])) {
                $errors[] = "Field $field harus diisi";
            }
        }

        // Validate tahun_ajaran format
        if (!empty($period_data['tahun_ajaran'])) {
            if (!preg_match('/^\d{4}\/\d{4}$/', $period_data['tahun_ajaran'])) {
                $errors[] = "Format tahun ajaran harus YYYY/YYYY (contoh: 2023/2024)";
            }
        }

        // Validate semester
        if (!empty($period_data['semester']) && !in_array($period_data['semester'], ['1', '2'])) {
            $errors[] = "Semester harus 1 atau 2";
        }

        // Validate status
        $valid_statuses = ['aktif', 'lulus', 'pindah', 'keluar'];
        if (!empty($period_data['status']) && !in_array($period_data['status'], $valid_statuses)) {
            $errors[] = "Status harus salah satu dari: " . implode(', ', $valid_statuses);
        }

        // Validate dates if provided
        if (!empty($period_data['tanggal_mulai']) && !empty($period_data['tanggal_selesai'])) {
            $start = strtotime($period_data['tanggal_mulai']);
            $end = strtotime($period_data['tanggal_selesai']);

            if ($start >= $end) {
                $errors[] = "Tanggal mulai harus lebih awal dari tanggal selesai";
            }
        }

        return $errors;
    }
}
