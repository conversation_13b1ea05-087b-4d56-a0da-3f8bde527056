<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../403.php");
    exit();
}

require_once '../template/header.php';
require_once '../models/PopupBerita.php';
require_once '../config/database.php';

$popupBerita = new PopupBerita();

// Get current settings
$hasSettings = $popupBerita->getSettings();

// Get all berita for selection
$allBerita = $popupBerita->getAllBeritaForSelection();

// Handle success/error messages
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Popup Berita</h2>
        <a href="/absen/" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
    </div>

    <?php if ($success_msg): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success_msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_msg): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error_msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Settings Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pengaturan Popup</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo $hasSettings ? 'update.php' : 'create.php'; ?>" method="POST">
                        <?php if ($hasSettings): ?>
                            <input type="hidden" name="id" value="<?php echo $popupBerita->id; ?>">
                        <?php endif; ?>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                    <?php echo ($hasSettings && $popupBerita->is_active) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">
                                    Aktifkan Popup Berita
                                </label>
                            </div>
                            <small class="text-muted">Popup akan muncul setelah login jika diaktifkan</small>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Judul Popup</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                value="<?php echo $hasSettings ? htmlspecialchars($popupBerita->title) : 'Berita Terbaru'; ?>" required>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_excerpt" name="show_excerpt" value="1"
                                    <?php echo (!$hasSettings || $popupBerita->show_excerpt) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_excerpt">
                                    Tampilkan Ringkasan Berita
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="excerpt_length" class="form-label">Panjang Ringkasan (karakter)</label>
                            <input type="number" class="form-control" id="excerpt_length" name="excerpt_length" 
                                value="<?php echo $hasSettings ? $popupBerita->excerpt_length : 150; ?>" min="50" max="500">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_show" name="auto_show" value="1"
                                    <?php echo (!$hasSettings || $popupBerita->auto_show) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="auto_show">
                                    Tampilkan Otomatis Setelah Login
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_dont_show_again" name="show_dont_show_again" value="1"
                                    <?php echo (!$hasSettings || $popupBerita->show_dont_show_again) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_dont_show_again">
                                    Tampilkan Opsi "Jangan Tampilkan Lagi"
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save"></i> <?php echo $hasSettings ? 'Update' : 'Simpan'; ?> Pengaturan
                        </button>
                    </form>
                </div>
            </div>

            <?php if ($hasSettings && $popupBerita->is_active): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Preview Popup</h6>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-info btn-sm w-100" onclick="showPopupPreview()">
                        <i class="fas fa-eye"></i> Lihat Preview
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- News Selection Panel -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Pilih Berita untuk Popup</h5>
                    <div>
                        <button type="button" class="btn btn-success btn-sm" onclick="selectAllBerita()">
                            <i class="fas fa-check-double"></i> Pilih Semua
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="clearAllBerita()">
                            <i class="fas fa-times"></i> Hapus Semua
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="beritaSelectionForm">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="tableBerita">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll" onchange="toggleAllBerita()">
                                        </th>
                                        <th>Judul Berita</th>
                                        <th>Pembuat</th>
                                        <th>Tanggal</th>
                                        <th>Status</th>
                                        <th width="100">Urutan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    $hasData = false;
                                    while ($row = $allBerita->fetch(PDO::FETCH_ASSOC)) {
                                        $hasData = true;
                                    ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="berita-checkbox" 
                                                value="<?php echo $row['id']; ?>" 
                                                <?php echo $row['is_selected'] ? 'checked' : ''; ?>
                                                onchange="toggleBerita(<?php echo $row['id']; ?>, this.checked)">
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($row['judul']); ?></strong>
                                            <?php if ($popupBerita->show_excerpt): ?>
                                            <br><small class="text-muted">
                                                <?php echo $popupBerita->truncateText($row['isi'], 100); ?>
                                            </small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($row['nama_pembuat']); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                        <td>
                                            <?php if ($row['is_selected']): ?>
                                                <span class="badge bg-success">Dipilih</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Tidak Dipilih</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($row['is_selected']): ?>
                                            <input type="number" class="form-control form-control-sm order-input" 
                                                value="0" min="0" max="999"
                                                onchange="updateOrder(<?php echo $row['id']; ?>, this.value)">
                                            <?php else: ?>
                                            <input type="number" class="form-control form-control-sm order-input" 
                                                value="0" min="0" max="999" disabled>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                    
                                    <?php if (!$hasData): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">Tidak ada data berita</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">Preview Popup Berita</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewModalBody">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize DataTable
$(document).ready(function() {
    $('#tableBerita').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "pageLength": 10,
        "order": [[ 3, "desc" ]]
    });
});

function toggleBerita(beritaId, isChecked) {
    const action = isChecked ? 'add' : 'remove';
    
    fetch('ajax_toggle_berita.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=${action}&berita_id=${beritaId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status badge
            const row = document.querySelector(`input[value="${beritaId}"]`).closest('tr');
            const statusCell = row.cells[4];
            const orderInput = row.querySelector('.order-input');
            
            if (isChecked) {
                statusCell.innerHTML = '<span class="badge bg-success">Dipilih</span>';
                orderInput.disabled = false;
            } else {
                statusCell.innerHTML = '<span class="badge bg-secondary">Tidak Dipilih</span>';
                orderInput.disabled = true;
                orderInput.value = 0;
            }
        } else {
            alert('Terjadi kesalahan: ' + data.message);
            // Revert checkbox state
            document.querySelector(`input[value="${beritaId}"]`).checked = !isChecked;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat memproses permintaan');
        // Revert checkbox state
        document.querySelector(`input[value="${beritaId}"]`).checked = !isChecked;
    });
}

function updateOrder(beritaId, order) {
    fetch('ajax_update_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `berita_id=${beritaId}&order=${order}`
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Terjadi kesalahan: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function toggleAllBerita() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.berita-checkbox');
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked !== selectAll.checked) {
            checkbox.checked = selectAll.checked;
            toggleBerita(checkbox.value, checkbox.checked);
        }
    });
}

function selectAllBerita() {
    document.getElementById('selectAll').checked = true;
    toggleAllBerita();
}

function clearAllBerita() {
    if (confirm('Apakah Anda yakin ingin menghapus semua berita dari popup?')) {
        fetch('ajax_clear_all.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Terjadi kesalahan: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memproses permintaan');
        });
    }
}

function showPopupPreview() {
    fetch('ajax_preview.php')
    .then(response => response.text())
    .then(html => {
        document.getElementById('previewModalBody').innerHTML = html;
        new bootstrap.Modal(document.getElementById('previewModal')).show();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat memuat preview');
    });
}
</script>

<?php require_once '../template/footer.php'; ?>
