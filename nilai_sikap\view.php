<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

$nilaiSikap = new NilaiSikap();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

if (!$nilaiSikap->getById($_GET['id'])) {
    $_SESSION['error'] = "Data nilai sikap tidak ditemukan";
    header("Location: index.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Detail <PERSON> Si<PERSON>p</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="200">NIS</th>
                                <td><?= htmlspecialchars($nilaiSikap->nis) ?></td>
                            </tr>
                            <tr>
                                <th>Nama Siswa</th>
                                <td><?= htmlspecialchars($nilaiSikap->nama_siswa) ?></td>
                            </tr>
                            <tr>
                                <th>Kelas</th>
                                <td><?= htmlspecialchars($nilaiSikap->nama_kelas) ?></td>
                            </tr>
                            <tr>
                                <th>Semester</th>
                                <td>Semester <?= htmlspecialchars($nilaiSikap->semester) ?></td>
                            </tr>
                            <tr>
                                <th>Tahun Ajaran</th>
                                <td><?= htmlspecialchars($nilaiSikap->tahun_ajaran) ?></td>
                            </tr>
                            <tr>
                                <th>Nilai Spiritual</th>
                                <td><?= htmlspecialchars($nilaiSikap->nilai_spiritual) ?></td>
                            </tr>
                            <tr>
                                <th>Deskripsi Spiritual</th>
                                <td><?= htmlspecialchars($nilaiSikap->deskripsi_spiritual) ?></td>
                            </tr>
                            <tr>
                                <th>Nilai Sosial</th>
                                <td><?= htmlspecialchars($nilaiSikap->nilai_sosial) ?></td>
                            </tr>
                            <tr>
                                <th>Deskripsi Sosial</th>
                                <td><?= htmlspecialchars($nilaiSikap->deskripsi_sosial) ?></td>
                            </tr>
                        </table>
                    </div>

                    <div class="mt-3">
                        <a href="edit.php?id=<?= $nilaiSikap->id ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="index.php" class="btn btn-secondary">Kembali</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>