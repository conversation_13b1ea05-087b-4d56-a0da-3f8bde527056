-- Simple Migration Script for KD Module Enhancement
-- This script adds tema_subtema and materi_pokok fields to kompetensi_dasar table
-- Use this if the main migration script fails due to permission issues

-- IMPORTANT: Before running this script, backup your database!
-- mysqldump -u root -p your_database_name > backup.sql

-- Step 1: Check current table structure
SELECT 'Current table structure:' as info;
DESCRIBE kompetensi_dasar;

-- Step 2: Add tema_subtema column
-- If this column already exists, you'll get an error but it won't break anything
ALTER TABLE kompetensi_dasar 
ADD COLUMN tema_subtema VARCHAR(255) DEFAULT NULL 
AFTER deskripsi_kd;

-- Step 3: Add materi_pokok column  
-- If this column already exists, you'll get an error but it won't break anything
ALTER TABLE kompetensi_dasar 
ADD COLUMN materi_pokok VARCHAR(255) DEFAULT NULL 
AFTER tema_subtema;

-- Step 4: Verify the changes
SELECT 'Updated table structure:' as info;
DESCRIBE kompetensi_dasar;

-- Step 5: Show sample data to verify everything is working
SELECT 'Sample data from updated table:' as info;
SELECT id, kode_kd, deskripsi_kd, tema_subtema, materi_pokok, mapel_id, semester, tahun_ajaran 
FROM kompetensi_dasar 
LIMIT 5;

-- Step 6: Verify column positions and types
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    ORDINAL_POSITION
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 'kompetensi_dasar' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
