<?php
session_start();
require_once '../../models/Like.php';

header('Content-Type: application/json');

// Pastikan request adalah POST dan user sudah login
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);
if (!$data || !isset($data['type']) || !isset($data['id']) || !isset($data['action'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required data']);
    exit;
}

$like = new Like();
$type = $data['type'];
$id = $data['id'];
$action = $data['action'];
$is_dislike = $action === 'dislike';

try {
    // Proses like/dislike berdasarkan tipe
    if ($type === 'berita') {
        $result = $like->likeBerita($id, $_SESSION['user_id'], $is_dislike);
        $count = $like->getBeritaLikeCount($id);
        $status = $like->isBeritaLiked($id, $_SESSION['user_id']);
    } else if ($type === 'komentar') {
        $result = $like->likeKomentar($id, $_SESSION['user_id'], $is_dislike);
        $count = $like->getKomentarLikeCount($id);
        $status = $like->isKomentarLiked($id, $_SESSION['user_id']);
    } else {
        throw new Exception('Invalid type');
    }

    if ($result) {
        echo json_encode([
            'success' => true,
            'count' => [
                'likes' => $count['likes'] ?? 0,
                'dislikes' => $count['dislikes'] ?? 0
            ],
            'status' => $status
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to process like/dislike']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
