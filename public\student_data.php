<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../template/public_header.php';
require_once '../models/Siswa.php';
require_once '../models/Absensi.php';
require_once '../models/Tugas.php';
require_once '../models/NilaiTugas.php';
require_once '../models/Nilai.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/TugasTambahan.php';

// Check if NIS is provided
if (!isset($_GET['nis']) || empty($_GET['nis'])) {
    $_SESSION['error'] = "NIS tidak boleh kosong.";
    header("Location: index.php");
    exit();
}

// Get parameters
$nis = $_GET['nis'];
$semester = $_GET['semester'] ?? '1';
$tahun_ajaran = $_GET['tahun_ajaran'] ?? '';

// Initialize models
$siswaModel = new Siswa();
$absensiModel = new Absensi();
$tugasModel = new Tugas();
$nilaiTugasModel = new NilaiTugas();
$nilaiModel = new Nilai();
$mapelModel = new MataPelajaran();
$kelasModel = new Kelas();

// Get student data - use period-based retrieval if academic period is specified
if ($tahun_ajaran && $semester) {
    $siswa = $siswaModel->getByNISAndPeriode($nis, $tahun_ajaran, $semester);
} else {
    $siswa = $siswaModel->getByNIS($nis);
}

if (!$siswa) {
    $_SESSION['error'] = "Siswa dengan NIS $nis tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Get class name - use the class from the period-based data if available
if (isset($siswa['nama_kelas']) && $siswa['nama_kelas']) {
    $nama_kelas = $siswa['nama_kelas'];
} else {
    // Fallback to getting class data separately
    $kelasModel->id = $siswa['kelas_id'];
    if ($kelasModel->getOne()) {
        $nama_kelas = $kelasModel->nama_kelas;
    } else {
        $nama_kelas = "Tidak diketahui";
    }
}

// Get attendance data per subject
$absensi_data = $absensiModel->getRekapBySiswaPerMapel($siswa['id'], $semester, $tahun_ajaran);
// Ensure we have a valid statement object
if (!$absensi_data) {
    $absensi_data = null;
}

// Get all subjects for the class - use the class from period-based data
$kelas_id_for_mapel = $siswa['kelas_id'];
$mapel_list = $mapelModel->getByKelas($kelas_id_for_mapel);

// Get all assignments for the student with submission status
$tugas_data = [];
$tugas_stmt = $nilaiTugasModel->getTugasSubmissionStatus($siswa['id'], $siswa['kelas_id'], $semester, $tahun_ajaran);

if ($tugas_stmt && $tugas_stmt->rowCount() > 0) {
    while ($tugas = $tugas_stmt->fetch(PDO::FETCH_ASSOC)) {
        $mapel_name = $tugas['nama_mapel'];

        if (!isset($tugas_data[$mapel_name])) {
            $tugas_data[$mapel_name] = [];
        }

        $tugas_data[$mapel_name][] = [
            'id' => $tugas['id'],
            'judul' => $tugas['judul'],
            'tanggal' => $tugas['tanggal'],
            'submitted' => (bool)$tugas['submitted'],
            'mapel_id' => $tugas['mapel_id']
        ];
    }
}

// Get final grades
$nilai_data = [];
if ($mapel_list && $mapel_list->rowCount() > 0) {
    while ($mapel = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
        $nilai = $nilaiModel->getNilaiSiswa($siswa['id'], $mapel['id'], $semester, $tahun_ajaran);
        if ($nilai) {
            $nilai_data[$mapel['nama_mapel']] = $nilai['nilai_akhir'];
        } else {
            $nilai_data[$mapel['nama_mapel']] = '-';
        }
    }
}

// Get additional assignments for the student
$tugasTambahanModel = new TugasTambahan();
$tugas_tambahan_stmt = $tugasTambahanModel->getTugasTambahanSiswa($siswa['id'], $semester, $tahun_ajaran);
?>

<div class="row mb-4">
    <div class="col-12">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali
        </a>
        <a href="rapor.php?nis=<?php echo urlencode($nis); ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-success">
            <i class="fas fa-file-alt me-2"></i> Lihat Rapor K13
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-user-graduate me-2"></i> Data Siswa
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">NIS</th>
                                <td>: <?php echo $siswa['nis']; ?></td>
                            </tr>
                            <tr>
                                <th>Nama</th>
                                <td>: <?php echo $siswa['nama_siswa']; ?></td>
                            </tr>
                            <tr>
                                <th>Kelas</th>
                                <td>: <?php echo $nama_kelas; ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Semester</th>
                                <td>: <?php echo $semester; ?></td>
                            </tr>
                            <tr>
                                <th>Tahun Ajaran</th>
                                <td>: <?php echo $tahun_ajaran; ?></td>
                            </tr>
                        </table>

                        <!-- Period Selection Form -->
                        <div class="mt-3">
                            <h6>Pilih Periode Akademik:</h6>
                            <form method="GET" class="row g-2">
                                <input type="hidden" name="nis" value="<?php echo htmlspecialchars($nis); ?>">
                                <div class="col-md-6">
                                    <select name="tahun_ajaran" class="form-select form-select-sm">
                                        <?php
                                        // Get available academic years for this student
                                        $available_periods = $siswaModel->getStudentPeriodes($siswa['id']);
                                        $tahun_ajaran_options = [];
                                        if ($available_periods) {
                                            while ($period = $available_periods->fetch(PDO::FETCH_ASSOC)) {
                                                $key = $period['tahun_ajaran'];
                                                if (!in_array($key, $tahun_ajaran_options)) {
                                                    $tahun_ajaran_options[] = $key;
                                                }
                                            }
                                        }

                                        foreach ($tahun_ajaran_options as $ta_option) {
                                            $selected = ($ta_option == $tahun_ajaran) ? 'selected' : '';
                                            echo "<option value='$ta_option' $selected>$ta_option</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select name="semester" class="form-select form-select-sm">
                                        <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                        <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Section -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-calendar-check me-2"></i> Rata-rata Kehadiran per Mata Pelajaran
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Mata Pelajaran</th>
                                <th>Total Pertemuan</th>
                                <th>Hadir</th>
                                <th>Sakit</th>
                                <th>Izin</th>
                                <th>Alpha</th>
                                <th>Persentase Kehadiran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            if ($absensi_data && $absensi_data->rowCount() > 0) {
                                while ($row = $absensi_data->fetch(PDO::FETCH_ASSOC)) {
                                    $total_pertemuan = $row['total_pertemuan'];
                                    $persentase = 0;

                                    if ($total_pertemuan > 0) {
                                        $persentase = round(($row['total_hadir'] / $total_pertemuan) * 100);
                                    }

                                    $badge_class = 'badge-success';
                                    if ($persentase < 80) $badge_class = 'badge-warning';
                                    if ($persentase < 60) $badge_class = 'badge-danger';
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $row['nama_mapel']; ?></td>
                                <td><?php echo $total_pertemuan; ?></td>
                                <td><?php echo $row['total_hadir']; ?></td>
                                <td><?php echo $row['total_sakit']; ?></td>
                                <td><?php echo $row['total_izin']; ?></td>
                                <td><?php echo $row['total_alpha']; ?></td>
                                <td>
                                    <span class="badge <?php echo $badge_class; ?>">
                                        <?php echo $persentase; ?>%
                                    </span>
                                </td>
                                <td>
                                    <a href="detail_absensi.php?siswa_id=<?php echo $siswa['id']; ?>&mapel_id=<?php echo $row['mapel_id']; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <?php
                                }
                            } else {
                            ?>
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data kehadiran</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignments Section -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-tasks me-2"></i> Status Pengumpulan Tugas
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($tugas_data)): ?>
                <div class="alert alert-info">
                    Tidak ada data tugas untuk semester dan tahun ajaran yang dipilih.
                </div>
                <?php else: ?>
                <div class="accordion" id="accordionTugas">
                    <?php
                    $i = 0;
                    foreach ($tugas_data as $mapel => $tugas_list):
                        $i++;
                    ?>
                    <div class="accordion-item mb-3 border">
                        <h2 class="accordion-header" id="heading<?php echo $i; ?>">
                            <button class="accordion-button <?php echo ($i > 1) ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $i; ?>" aria-expanded="<?php echo ($i == 1) ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $i; ?>">
                                <strong><?php echo $mapel; ?></strong> - <?php echo count($tugas_list); ?> tugas
                            </button>
                        </h2>
                        <div id="collapse<?php echo $i; ?>" class="accordion-collapse collapse <?php echo ($i == 1) ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $i; ?>" data-bs-parent="#accordionTugas">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>No</th>
                                                <th>Judul Tugas</th>
                                                <th>Tanggal</th>
                                                <th>Status</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $no = 1;
                                            foreach ($tugas_list as $tugas):
                                                $status_class = $tugas['submitted'] ? 'badge-success' : 'badge-danger';
                                                $status_text = $tugas['submitted'] ? 'Sudah Dikumpulkan' : 'Belum Dikumpulkan';
                                            ?>
                                            <tr>
                                                <td><?php echo $no++; ?></td>
                                                <td><?php echo $tugas['judul']; ?></td>
                                                <td><?php echo date('d-m-Y', strtotime($tugas['tanggal'])); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $status_text; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="detail_tugas.php?siswa_id=<?php echo $siswa['id']; ?>&mapel_id=<?php echo $tugas['mapel_id']; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> Detail
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Additional Assignments Section -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-clipboard-list me-2"></i> Tugas Tambahan
                </h5>
            </div>
            <div class="card-body">
                <?php if ($tugas_tambahan_stmt && $tugas_tambahan_stmt->rowCount() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Mata Pelajaran</th>
                                <th>Judul Tugas</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Nilai</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while ($tugas = $tugas_tambahan_stmt->fetch(PDO::FETCH_ASSOC)):
                                $status_text = $tugas['status'] == 'sudah_dikerjakan' ? 'Sudah Dikerjakan' : 'Belum Dikerjakan';
                                $status_class = $tugas['status'] == 'sudah_dikerjakan' ? 'badge-success' : 'badge-warning';
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $tugas['nama_mapel']; ?></td>
                                <td><?php echo $tugas['judul']; ?></td>
                                <td><?php echo date('d-m-Y', strtotime($tugas['tanggal'])); ?></td>
                                <td>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo $status_text; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    if ($tugas['status'] == 'sudah_dikerjakan' && $tugas['nilai'] !== null) {
                                        echo $tugas['nilai'];
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <a href="tugas_tambahan_detail.php?tugas_id=<?php echo $tugas['id']; ?>&siswa_id=<?php echo $siswa['id']; ?>&nis=<?php echo $siswa['nis']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    Tidak ada tugas tambahan untuk semester dan tahun ajaran yang dipilih.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Final Grades Section -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-line me-2"></i> Nilai Akhir
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Mata Pelajaran</th>
                                <th>Nilai Akhir</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            if (!empty($nilai_data)) {
                                foreach ($nilai_data as $mapel => $nilai):
                                    $keterangan = '-';
                                    $badge_class = 'badge-secondary';

                                    if ($nilai != '-') {
                                        if ($nilai >= 90) {
                                            $keterangan = 'Sangat Baik';
                                            $badge_class = 'badge-success';
                                        } elseif ($nilai >= 80) {
                                            $keterangan = 'Baik';
                                            $badge_class = 'badge-primary';
                                        } elseif ($nilai >= 70) {
                                            $keterangan = 'Cukup';
                                            $badge_class = 'badge-info';
                                        } elseif ($nilai >= 60) {
                                            $keterangan = 'Kurang';
                                            $badge_class = 'badge-warning';
                                        } else {
                                            $keterangan = 'Sangat Kurang';
                                            $badge_class = 'badge-danger';
                                        }
                                    }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $mapel; ?></td>
                                <td><?php echo $nilai; ?></td>
                                <td>
                                    <span class="badge <?php echo $badge_class; ?>">
                                        <?php echo $keterangan; ?>
                                    </span>
                                </td>
                            </tr>
                            <?php
                                endforeach;
                            } else {
                            ?>
                            <tr>
                                <td colspan="4" class="text-center">Tidak ada data nilai</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
