<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';
require_once '../vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;

$perpustakaan = new Perpustakaan();

// Set default tanggal
$tanggal_awal = isset($_GET['tanggal_awal']) ? $_GET['tanggal_awal'] : date('Y-m-01');
$tanggal_akhir = isset($_GET['tanggal_akhir']) ? $_GET['tanggal_akhir'] : date('Y-m-t');

// Ambil data peminjaman
$peminjaman = $perpustakaan->getLaporanPeminjaman($tanggal_awal, $tanggal_akhir);

// Buat konten HTML
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 9px;
        }
        h1 {
            text-align: center;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .periode {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 3px;
        }
        th {
            background-color: #f0f0f0;
        }
        .status-dipinjam {
            color: #e67e22;
        }
        .status-kembali {
            color: #27ae60;
        }
        .status-terlambat {
            color: #c0392b;
        }
        .footer {
            margin-top: 20px;
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .riwayat {
            font-size: 8px;
        }
    </style>
</head>
<body>
    <h1>LAPORAN PEMINJAMAN BUKU</h1>
    <div class="periode">
        Periode: ' . date('d/m/Y', strtotime($tanggal_awal)) . ' - ' . date('d/m/Y', strtotime($tanggal_akhir)) . '
    </div>
    
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Nomor Peminjaman</th>
                <th>Tanggal Pinjam</th>
                <th>Tanggal Kembali</th>
                <th>Peminjam</th>
                <th>Buku</th>
                <th>Jumlah Pinjam Awal</th>
                <th>Sudah Kembali</th>
                <th>Sisa Pinjam</th>
                <th>Riwayat Pengembalian</th>
                <th>Status</th>
                <th>Keterlambatan</th>
            </tr>
        </thead>
        <tbody>';

$no = 1;
$current_peminjaman = null;

foreach ($peminjaman as $p) {
    // Skip jika ini adalah riwayat dari peminjaman yang sama
    if ($current_peminjaman == $p['id_peminjaman']) {
        continue;
    }
    $current_peminjaman = $p['id_peminjaman'];

    $status_class = 'status-dipinjam';
    $status_text = 'Dipinjam';
    
    if ($p['status'] == 'kembali') {
        $status_class = 'status-kembali';
        $status_text = 'Dikembalikan';
    } elseif ($p['hari_terlambat'] > 0) {
        $status_class = 'status-terlambat';
        $status_text = 'Terlambat';
    }

    $keterlambatan = ($p['status'] == 'dipinjam' && $p['hari_terlambat'] > 0) ? $p['hari_terlambat'] . ' hari' : '-';

    // Hitung sisa pinjaman
    $sisa_pinjam = $p['jumlah_pinjam_awal'] - $p['total_dikembalikan'];

    // Ambil semua riwayat pengembalian untuk peminjaman ini
    $riwayat = '';
    foreach ($peminjaman as $r) {
        if ($r['id_peminjaman'] == $p['id_peminjaman'] && $r['tanggal_pengembalian'] !== null) {
            $riwayat .= date('d/m/Y', strtotime($r['tanggal_pengembalian'])) . 
                       ': ' . $r['jumlah_dikembalikan'] . ' buku<br>';
        }
    }
    
    $html .= '
            <tr>
                <td class="text-center">' . $no++ . '</td>
                <td>' . $p['nomor_peminjaman'] . '</td>
                <td class="text-center">' . date('d/m/Y', strtotime($p['tanggal_pinjam'])) . '</td>
                <td class="text-center">' . date('d/m/Y', strtotime($p['tanggal_kembali'])) . '</td>
                <td>' . htmlspecialchars($p['nama_peminjam']) . '</td>
                <td>' . htmlspecialchars($p['judul_buku']) . '</td>
                <td class="text-center">' . $p['jumlah_pinjam_awal'] . '</td>
                <td class="text-center">' . $p['total_dikembalikan'] . '</td>
                <td class="text-center">' . $sisa_pinjam . '</td>
                <td class="riwayat">' . ($riwayat ?: '-') . '</td>
                <td class="text-center ' . $status_class . '">' . $status_text . '</td>
                <td class="text-center">' . $keterlambatan . '</td>
            </tr>';
}

$html .= '
        </tbody>
    </table>
    
    <div class="footer">
        <p>Dicetak pada: ' . date('d/m/Y H:i:s') . '</p>
    </div>
</body>
</html>';

// Konfigurasi DOMPDF
$options = new Options();
$options->set('isHtml5ParserEnabled', true);
$options->set('isPhpEnabled', true);

// Buat instance DOMPDF
$dompdf = new Dompdf($options);

// Load HTML
$dompdf->loadHtml($html);

// Set ukuran dan orientasi kertas
$dompdf->setPaper('A4', 'landscape');

// Render PDF
$dompdf->render();

// Set nama file
$filename = 'Laporan_Peminjaman_' . date('Y-m-d_H-i-s') . '.pdf';

// Output PDF
$dompdf->stream($filename, array('Attachment' => true));
