<?php
require_once '../middleware/auth.php';
checkAdminAccess();
require_once '../models/Kelas.php';

header('Content-Type: application/json');

$kelas = new Kelas();

if (isset($_GET['tingkat_id']) && isset($_GET['jurusan_id'])) {
    $result = $kelas->getByTingkatAndJurusan($_GET['tingkat_id'], $_GET['jurusan_id']);
    $kelas_list = [];
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $kelas_list[] = [
            'id' => $row['id'],
            'nama_kelas' => $row['nama_kelas']
        ];
    }
    
    echo json_encode($kelas_list);
} else {
    echo json_encode([]);
}
