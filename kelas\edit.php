<?php
require_once '../template/header.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';

// Check if user is admin
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/403.php");
    exit();
}

$kelas = new Kelas();
$guru = new Guru();
$tahun_ajaran = new TahunAjaran();
$tingkat = new Tingkat();
$jurusan = new Jurusan();

// Check if id exists
if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$id = $_GET['id'];
$data = $kelas->getById($id);

// If data not found
if (!$data) {
    header("Location: index.php");
    exit();
}

// Get all teachers for wali kelas selection
$guru_list = $guru->getAll();

// Get all tahun ajaran
$tahun_ajaran_list = $tahun_ajaran->getAll();

// Get all tingkat
$tingkat_list = $tingkat->getAll();

// Get all jurusan
$jurusan_list = $jurusan->getAll();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $kelas->id = $id;
    $kelas->nama_kelas = $_POST['nama_kelas'];
    $kelas->guru_id = $_POST['guru_id'];
    $kelas->tahun_ajaran = $_POST['tahun_ajaran'];
    $kelas->tingkat_id = $_POST['tingkat_id'];
    $kelas->jurusan_id = $_POST['jurusan_id'];

    if ($kelas->update()) {
        $_SESSION['success'] = "Kelas berhasil diperbarui.";
        header("Location: index.php");
        exit();
    } else {
        $_SESSION['error'] = "Gagal memperbarui kelas.";
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Edit Kelas</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form action="edit.php?id=<?php echo $id; ?>" method="POST">
                <div class="mb-3">
                    <label for="tingkat_id" class="form-label">Tingkat</label>
                    <select class="form-select" id="tingkat_id" name="tingkat_id" required>
                        <option value="">Pilih Tingkat</option>
                        <?php while ($row = $tingkat_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo ($data['tingkat_id'] == $row['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($row['nama_tingkat']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="jurusan_id" class="form-label">Jurusan</label>
                    <select class="form-select" id="jurusan_id" name="jurusan_id" required>
                        <option value="">Pilih Jurusan</option>
                        <?php while ($row = $jurusan_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo ($data['jurusan_id'] == $row['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($row['kode_jurusan'] . ' - ' . $row['nama_jurusan']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="nama_kelas" class="form-label">Nama Kelas</label>
                    <input type="text" class="form-control" id="nama_kelas" name="nama_kelas" value="<?php echo htmlspecialchars($data['nama_kelas']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="guru_id" class="form-label">Wali Kelas</label>
                    <select class="form-select" id="guru_id" name="guru_id" required>
                        <option value="">Pilih Wali Kelas</option>
                        <?php while ($row = $guru_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo ($data['guru_id'] == $row['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($row['nama_lengkap']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                    <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                        <option value="">Pilih Tahun Ajaran</option>
                        <?php while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?php echo htmlspecialchars($row['tahun_ajaran']); ?>" <?php echo ($data['tahun_ajaran'] == $row['tahun_ajaran']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($row['tahun_ajaran']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Simpan Perubahan
                </button>
            </form>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
