<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';

// Check if user is admin or guru
if (!in_array($_SESSION['role'], ['admin', 'guru'])) {
    echo "<script>alert('Aks<PERSON> ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit;
}

$siswa_id = $_GET['id'];
$siswaModel = new Siswa();
$siswaPeriodeModel = new SiswaPeriode();
$kelasModel = new Kelas();

// Get student data
$siswaModel->id = $siswa_id;
if (!$siswaModel->getOne()) {
    $_SESSION['error'] = "Data siswa tidak ditemukan.";
    header("Location: index.php");
    exit;
}

$message = '';
$error = '';

// Handle form submission for editing periods
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_period' && $_SESSION['role'] === 'admin') {
        try {
            $period_id = $_POST['period_id'];
            $kelas_id = $_POST['kelas_id'];
            $status = $_POST['status'];
            $tanggal_mulai = $_POST['tanggal_mulai'];
            $tanggal_selesai = $_POST['tanggal_selesai'];
            
            $query = "UPDATE siswa_periode 
                     SET kelas_id = :kelas_id, 
                         status = :status,
                         tanggal_mulai = :tanggal_mulai,
                         tanggal_selesai = :tanggal_selesai
                     WHERE id = :period_id AND siswa_id = :siswa_id";
            
            $stmt = $siswaPeriodeModel->conn->prepare($query);
            $stmt->bindParam(':period_id', $period_id);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':tanggal_mulai', $tanggal_mulai);
            $stmt->bindParam(':tanggal_selesai', $tanggal_selesai);
            
            if ($stmt->execute()) {
                $message = "Periode berhasil diperbarui";
            } else {
                $error = "Gagal memperbarui periode";
            }
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    }
    
    if ($_POST['action'] === 'set_current' && $_SESSION['role'] === 'admin') {
        try {
            $period_id = $_POST['period_id'];
            
            // Deactivate all periods for this student
            $query = "UPDATE siswa_periode SET is_current = 0 WHERE siswa_id = :siswa_id";
            $stmt = $siswaPeriodeModel->conn->prepare($query);
            $stmt->bindParam(':siswa_id', $siswa_id);
            $stmt->execute();
            
            // Set selected period as current
            $query = "UPDATE siswa_periode SET is_current = 1 WHERE id = :period_id";
            $stmt = $siswaPeriodeModel->conn->prepare($query);
            $stmt->bindParam(':period_id', $period_id);
            $stmt->execute();
            
            // Update siswa table
            $query = "UPDATE siswa s 
                     JOIN siswa_periode sp ON s.id = sp.siswa_id 
                     SET s.kelas_id = sp.kelas_id,
                         s.tahun_ajaran_current = sp.tahun_ajaran,
                         s.semester_current = sp.semester
                     WHERE sp.id = :period_id";
            $stmt = $siswaPeriodeModel->conn->prepare($query);
            $stmt->bindParam(':period_id', $period_id);
            
            if ($stmt->execute()) {
                $message = "Periode aktif berhasil diubah";
            } else {
                $error = "Gagal mengubah periode aktif";
            }
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    }
}

// Get student periods
$periods = $siswaPeriodeModel->getPeriodesBySiswa($siswa_id);
$periods_data = $periods ? $periods->fetchAll(PDO::FETCH_ASSOC) : [];

// Get all classes for dropdown
$kelas_list = $kelasModel->getAll()->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Riwayat Akademik Siswa</h1>
        <div>
            <?php if ($_SESSION['role'] === 'admin'): ?>
            <a href="bulk_period_assignment.php" class="btn btn-warning mr-2">
                <i class="fas fa-users-cog"></i> Kelola Massal
            </a>
            <?php endif; ?>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <!-- Student Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary">
            <h6 class="m-0 font-weight-bold text-white">
                <i class="fas fa-user"></i> Informasi Siswa
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td width="120"><strong>NIS</strong></td>
                            <td>: <?= htmlspecialchars($siswaModel->nis) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Nama</strong></td>
                            <td>: <?= htmlspecialchars($siswaModel->nama_siswa) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Jenis Kelamin</strong></td>
                            <td>: <?= $siswaModel->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td width="150"><strong>Periode Saat Ini</strong></td>
                            <td>: <?= htmlspecialchars($siswaModel->tahun_ajaran_current ?? 'Tidak ada') ?> - Semester <?= htmlspecialchars($siswaModel->semester_current ?? '-') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total Periode</strong></td>
                            <td>: <?= count($periods_data) ?> periode</td>
                        </tr>
                        <tr>
                            <td><strong>No. Telepon</strong></td>
                            <td>: <?= htmlspecialchars($siswaModel->no_telp ?: '-') ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Academic History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-history"></i> Riwayat Periode Akademik
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($periods_data)): ?>
            <div class="text-center py-4">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5>Belum Ada Riwayat Periode</h5>
                <p class="text-muted">Siswa ini belum memiliki riwayat periode akademik.</p>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                <a href="create_historical.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buat Riwayat Akademik
                </a>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Tahun Ajaran</th>
                            <th>Semester</th>
                            <th>Kelas</th>
                            <th>Status</th>
                            <th>Tanggal Mulai</th>
                            <th>Tanggal Selesai</th>
                            <th>Periode Aktif</th>
                            <?php if ($_SESSION['role'] === 'admin'): ?>
                            <th>Aksi</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        // Sort periods by tahun_ajaran and semester
                        usort($periods_data, function($a, $b) {
                            if ($a['tahun_ajaran'] == $b['tahun_ajaran']) {
                                return $a['semester'] <=> $b['semester'];
                            }
                            return $a['tahun_ajaran'] <=> $b['tahun_ajaran'];
                        });
                        
                        foreach ($periods_data as $period): 
                        ?>
                        <tr class="<?= $period['is_current'] ? 'table-success' : '' ?>">
                            <td><?= $no++ ?></td>
                            <td><?= htmlspecialchars($period['tahun_ajaran']) ?></td>
                            <td>Semester <?= $period['semester'] ?></td>
                            <td><?= htmlspecialchars($period['nama_kelas']) ?></td>
                            <td>
                                <span class="badge badge-<?= $period['status'] == 'aktif' ? 'success' : ($period['status'] == 'lulus' ? 'primary' : 'secondary') ?>">
                                    <?= ucfirst($period['status']) ?>
                                </span>
                            </td>
                            <td><?= $period['tanggal_mulai'] ? date('d/m/Y', strtotime($period['tanggal_mulai'])) : '-' ?></td>
                            <td><?= $period['tanggal_selesai'] ? date('d/m/Y', strtotime($period['tanggal_selesai'])) : '-' ?></td>
                            <td>
                                <?php if ($period['is_current']): ?>
                                    <span class="badge badge-success">Ya</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">Tidak</span>
                                <?php endif; ?>
                            </td>
                            <?php if ($_SESSION['role'] === 'admin'): ?>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="editPeriod(<?= htmlspecialchars(json_encode($period)) ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <?php if (!$period['is_current']): ?>
                                <button type="button" class="btn btn-sm btn-success ml-1" onclick="setCurrent(<?= $period['id'] ?>)">
                                    <i class="fas fa-check"></i>
                                </button>
                                <?php endif; ?>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Card -->
    <?php if (!empty($periods_data)): ?>
    <div class="row">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Periode</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= count($periods_data) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Periode Aktif</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count(array_filter($periods_data, function($p) { return $p['status'] == 'aktif'; })) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Periode Lulus</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count(array_filter($periods_data, function($p) { return $p['status'] == 'lulus'; })) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Tahun Bersekolah</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php
                                $years = array_unique(array_map(function($p) { 
                                    return substr($p['tahun_ajaran'], 0, 4); 
                                }, $periods_data));
                                echo count($years);
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if ($_SESSION['role'] === 'admin'): ?>
<!-- Edit Period Modal -->
<div class="modal fade" id="editPeriodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="editPeriodForm">
                <input type="hidden" name="action" value="update_period">
                <input type="hidden" name="period_id" id="edit_period_id">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> Edit Periode Akademik
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Tahun Ajaran</label>
                        <input type="text" class="form-control" id="edit_tahun_ajaran" readonly>
                    </div>
                    <div class="form-group">
                        <label>Semester</label>
                        <input type="text" class="form-control" id="edit_semester" readonly>
                    </div>
                    <div class="form-group">
                        <label>Kelas</label>
                        <select name="kelas_id" id="edit_kelas_id" class="form-control" required>
                            <?php foreach ($kelas_list as $kelas): ?>
                                <option value="<?= $kelas['id'] ?>"><?= $kelas['nama_kelas'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select name="status" id="edit_status" class="form-control" required>
                            <option value="aktif">Aktif</option>
                            <option value="lulus">Lulus</option>
                            <option value="pindah">Pindah</option>
                            <option value="keluar">Keluar</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Mulai</label>
                        <input type="date" name="tanggal_mulai" id="edit_tanggal_mulai" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Tanggal Selesai</label>
                        <input type="date" name="tanggal_selesai" id="edit_tanggal_selesai" class="form-control">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPeriod(period) {
    document.getElementById('edit_period_id').value = period.id;
    document.getElementById('edit_tahun_ajaran').value = period.tahun_ajaran;
    document.getElementById('edit_semester').value = 'Semester ' + period.semester;
    document.getElementById('edit_kelas_id').value = period.kelas_id;
    document.getElementById('edit_status').value = period.status;
    document.getElementById('edit_tanggal_mulai').value = period.tanggal_mulai;
    document.getElementById('edit_tanggal_selesai').value = period.tanggal_selesai;
    
    $('#editPeriodModal').modal('show');
}

function setCurrent(periodId) {
    if (confirm('Yakin ingin mengubah periode aktif siswa?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="set_current">
            <input type="hidden" name="period_id" value="${periodId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php endif; ?>

<?php require_once '../template/footer.php'; ?>
