<?php
require_once __DIR__ . '/../config/database.php';

class EssayAnswer {
    private $conn;
    private $table_name = "essay_answers";

    public $id;
    public $question_id;
    public $question_type;
    public $expected_answer;
    public $answer_points;
    public $scoring_rubric;
    public $generation_metadata;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                question_id=:question_id, question_type=:question_type,
                expected_answer=:expected_answer, answer_points=:answer_points,
                scoring_rubric=:scoring_rubric, generation_metadata=:generation_metadata";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->question_id = htmlspecialchars(strip_tags($this->question_id));
        $this->question_type = htmlspecialchars(strip_tags($this->question_type));
        $this->expected_answer = htmlspecialchars(strip_tags($this->expected_answer));

        // Handle answer_points - could be string or array
        if (is_array($this->answer_points)) {
            $this->answer_points = implode("\n", $this->answer_points);
        }
        $this->answer_points = htmlspecialchars(strip_tags($this->answer_points ?? ''));

        // Handle JSON fields
        $scoring_rubric_json = null;
        if (!empty($this->scoring_rubric)) {
            if (is_array($this->scoring_rubric)) {
                $scoring_rubric_json = json_encode($this->scoring_rubric);
            } else {
                $scoring_rubric_json = $this->scoring_rubric;
            }
        }

        $generation_metadata_json = null;
        if (!empty($this->generation_metadata)) {
            if (is_array($this->generation_metadata)) {
                $generation_metadata_json = json_encode($this->generation_metadata);
            } else {
                $generation_metadata_json = $this->generation_metadata;
            }
        }

        // Bind
        $stmt->bindParam(":question_id", $this->question_id);
        $stmt->bindParam(":question_type", $this->question_type);
        $stmt->bindParam(":expected_answer", $this->expected_answer);
        $stmt->bindParam(":answer_points", $this->answer_points);
        $stmt->bindParam(":scoring_rubric", $scoring_rubric_json);
        $stmt->bindParam(":generation_metadata", $generation_metadata_json);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function getByQuestionId($question_id, $question_type = 'rpp_question') {
        $query = "SELECT * FROM " . $this->table_name . " 
                 WHERE question_id = :question_id AND question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getOne($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                expected_answer=:expected_answer, answer_points=:answer_points,
                scoring_rubric=:scoring_rubric, generation_metadata=:generation_metadata
                WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->expected_answer = htmlspecialchars(strip_tags($this->expected_answer));

        // Handle answer_points - could be string or array
        if (is_array($this->answer_points)) {
            $this->answer_points = implode("\n", $this->answer_points);
        }
        $this->answer_points = htmlspecialchars(strip_tags($this->answer_points ?? ''));

        // Handle JSON fields
        $scoring_rubric_json = null;
        if (!empty($this->scoring_rubric)) {
            if (is_array($this->scoring_rubric)) {
                $scoring_rubric_json = json_encode($this->scoring_rubric);
            } else {
                $scoring_rubric_json = $this->scoring_rubric;
            }
        }

        $generation_metadata_json = null;
        if (!empty($this->generation_metadata)) {
            if (is_array($this->generation_metadata)) {
                $generation_metadata_json = json_encode($this->generation_metadata);
            } else {
                $generation_metadata_json = $this->generation_metadata;
            }
        }

        // Bind
        $stmt->bindParam(":expected_answer", $this->expected_answer);
        $stmt->bindParam(":answer_points", $this->answer_points);
        $stmt->bindParam(":scoring_rubric", $scoring_rubric_json);
        $stmt->bindParam(":generation_metadata", $generation_metadata_json);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        
        return $stmt->execute();
    }

    public function deleteByQuestionId($question_id, $question_type = 'rpp_question') {
        $query = "DELETE FROM " . $this->table_name . " 
                 WHERE question_id = :question_id AND question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        
        return $stmt->execute();
    }

    public function getAnswersByQuestionIds($question_ids, $question_type = 'rpp_question') {
        if (empty($question_ids)) {
            return [];
        }

        $placeholders = str_repeat('?,', count($question_ids) - 1) . '?';
        $query = "SELECT * FROM " . $this->table_name . " 
                 WHERE question_id IN ($placeholders) AND question_type = ?";
        
        $stmt = $this->conn->prepare($query);
        $params = array_merge($question_ids, [$question_type]);
        $stmt->execute($params);
        
        $results = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $results[$row['question_id']] = $row;
        }
        
        return $results;
    }

    public function createBulk($answers_data) {
        try {
            $this->conn->beginTransaction();

            $query = "INSERT INTO " . $this->table_name . " SET
                    question_id=:question_id, question_type=:question_type,
                    expected_answer=:expected_answer, answer_points=:answer_points,
                    scoring_rubric=:scoring_rubric, generation_metadata=:generation_metadata
                    ON DUPLICATE KEY UPDATE
                    expected_answer=VALUES(expected_answer),
                    answer_points=VALUES(answer_points),
                    scoring_rubric=VALUES(scoring_rubric),
                    generation_metadata=VALUES(generation_metadata)";

            $stmt = $this->conn->prepare($query);
            $created_ids = [];

            foreach ($answers_data as $answer_data) {
                $question_id = $answer_data['question_id'];
                $question_type = $answer_data['question_type'] ?? 'rpp_question';
                $expected_answer = htmlspecialchars(strip_tags($answer_data['expected_answer']));

                // Handle answer_points - could be string or array
                $answer_points_raw = $answer_data['answer_points'] ?? '';
                if (is_array($answer_points_raw)) {
                    $answer_points_raw = implode("\n", $answer_points_raw);
                }
                $answer_points = htmlspecialchars(strip_tags($answer_points_raw));

                // Handle JSON fields
                $scoring_rubric_json = null;
                if (!empty($answer_data['scoring_rubric'])) {
                    if (is_array($answer_data['scoring_rubric'])) {
                        $scoring_rubric_json = json_encode($answer_data['scoring_rubric']);
                    } else {
                        $scoring_rubric_json = $answer_data['scoring_rubric'];
                    }
                }

                $generation_metadata_json = null;
                if (!empty($answer_data['generation_metadata'])) {
                    if (is_array($answer_data['generation_metadata'])) {
                        $generation_metadata_json = json_encode($answer_data['generation_metadata']);
                    } else {
                        $generation_metadata_json = $answer_data['generation_metadata'];
                    }
                }

                // Bind
                $stmt->bindParam(":question_id", $question_id);
                $stmt->bindParam(":question_type", $question_type);
                $stmt->bindParam(":expected_answer", $expected_answer);
                $stmt->bindParam(":answer_points", $answer_points);
                $stmt->bindParam(":scoring_rubric", $scoring_rubric_json);
                $stmt->bindParam(":generation_metadata", $generation_metadata_json);

                if ($stmt->execute()) {
                    $created_ids[] = $this->conn->lastInsertId();
                } else {
                    throw new Exception("Gagal menyimpan jawaban untuk question ID: " . $question_id);
                }
            }

            $this->conn->commit();
            return $created_ids;

        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function hasAnswer($question_id, $question_type = 'rpp_question') {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                 WHERE question_id = :question_id AND question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->bindParam(":question_type", $question_type);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
    }

    public function getAnswersCountByType($question_type = 'rpp_question') {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                 WHERE question_type = :question_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":question_type", $question_type);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }
}
?>
