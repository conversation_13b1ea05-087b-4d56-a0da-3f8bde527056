<?php
include '../config/database.php';

session_start();

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
$database = new Database();
$db = $database->getConnection();

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get guru data
$query = "SELECT nip, nama_lengkap FROM guru WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $id);
$stmt->execute();

if ($stmt && $stmt->rowCount() > 0) {
    $guru = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        // Update guru status
        $update_guru = "UPDATE guru SET status = 'nonaktif' WHERE id = :id";
        $stmt = $db->prepare($update_guru);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        // Delete user account
        $delete_user = "DELETE FROM users WHERE username = :username";
        $username = 'guru' . $guru['nip'];
        $stmt = $db->prepare($delete_user);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $db->commit();
        $_SESSION['success'] = "Akun guru berhasil dinonaktifkan!";
    } catch (Exception $e) {
        $db->rollBack();
        $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
    }
} else {
    $_SESSION['error'] = "Data guru tidak ditemukan!";
}

header('Location: index.php');
