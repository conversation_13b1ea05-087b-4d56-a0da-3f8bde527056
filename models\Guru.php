<?php
require_once __DIR__ . '/../config/database.php';

class Guru {
    private $conn;
    private $table_name = "guru";

    public $id;
    public $nip;
    public $nama_lengkap;
    public $jenis_kelamin;
    public $alamat;
    public $no_telp;
    public $email;
    public $status;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY nama_lengkap ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->nip = $row['nip'];
            $this->nama_lengkap = $row['nama_lengkap'];
            $this->jenis_kelamin = $row['jenis_kelamin'];
            $this->alamat = $row['alamat'];
            $this->no_telp = $row['no_telp'];
            $this->email = $row['email'];
            $this->status = $row['status'];
            return true;
        }
        return false;
    }

    public function getStatusByNama($nama_lengkap) {
        $query = "SELECT status FROM " . $this->table_name . " WHERE nama_lengkap = :nama_lengkap LIMIT 1";
        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $nama_lengkap = htmlspecialchars(strip_tags($nama_lengkap));
        $stmt->bindParam(":nama_lengkap", $nama_lengkap);

        $stmt->execute();

        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            return $row['status'] === 'aktif';
        }
        return false;
    }

    public function getAllActive() {
        $query = "SELECT * FROM " . $this->table_name . "
                ORDER BY nama_lengkap ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    public function setStatusByNama($nama_lengkap, $status) {
        $query = "UPDATE " . $this->table_name . "
                SET status = :status
                WHERE nama_lengkap = :nama_lengkap";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $nama_lengkap = htmlspecialchars(strip_tags($nama_lengkap));
        $status = htmlspecialchars(strip_tags($status));

        // Bind parameters
        $stmt->bindParam(":nama_lengkap", $nama_lengkap);
        $stmt->bindParam(":status", $status);

        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET nama_lengkap = :nama_lengkap,
                    jenis_kelamin = :jenis_kelamin,
                    alamat = :alamat,
                    no_telp = :no_telp,
                    email = :email
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nama_lengkap = htmlspecialchars(strip_tags($this->nama_lengkap));
        $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
        $this->alamat = htmlspecialchars(strip_tags($this->alamat));
        $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));
        $this->email = htmlspecialchars(strip_tags($this->email));

        // Bind parameters
        $stmt->bindParam(":nama_lengkap", $this->nama_lengkap);
        $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
        $stmt->bindParam(":alamat", $this->alamat);
        $stmt->bindParam(":no_telp", $this->no_telp);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":id", $this->id);

        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function isWaliKelas($guru_id) {
        $query = "SELECT COUNT(*) as total FROM kelas WHERE guru_id = :guru_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] > 0;
    }

    public function getKelasAsWaliKelas($guru_id, $tahun_ajaran = null, $semester = null) {
        // If period is not specified, use active period
        if ($tahun_ajaran === null || $semester === null) {
            require_once __DIR__ . '/PeriodeAktif.php';
            $periode = new PeriodeAktif();
            if ($periode->getActive()) {
                $tahun_ajaran = $tahun_ajaran ?? $periode->tahun_ajaran;
                $semester = $semester ?? $periode->semester;
            }
        }

        $query = "SELECT k.*, g.nama_lengkap as nama_guru
                FROM kelas k
                LEFT JOIN guru g ON k.guru_id = g.id
                WHERE k.guru_id = :guru_id";

        // Add period filtering if available
        if ($tahun_ajaran && $semester) {
            $query .= " AND k.tahun_ajaran = :tahun_ajaran AND k.semester = :semester";
        }

        $query .= " ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);

        if ($tahun_ajaran && $semester) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
            $stmt->bindParam(":semester", $semester);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getByUserId($user_id) {
        // Step 1: Get the nama_lengkap from users table
        $user_query = "SELECT nama_lengkap FROM users WHERE id = :user_id LIMIT 1";
        $user_stmt = $this->conn->prepare($user_query);
        $user_stmt->bindParam(':user_id', $user_id);
        $user_stmt->execute();

        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
        if (!$user_data) {
            // Return empty result set if user not found
            $empty_stmt = $this->conn->prepare("SELECT * FROM " . $this->table_name . " WHERE 1=0");
            $empty_stmt->execute();
            return $empty_stmt;
        }

        // Step 2: Find matching guru record by exact nama_lengkap match
        $guru_query = "SELECT * FROM " . $this->table_name . "
                       WHERE nama_lengkap = :nama_lengkap
                       AND status = 'aktif'
                       LIMIT 1";

        $guru_stmt = $this->conn->prepare($guru_query);
        $guru_stmt->bindParam(':nama_lengkap', $user_data['nama_lengkap']);
        $guru_stmt->execute();

        return $guru_stmt;
    }

    public function getIdByUserId($user_id) {
        $stmt = $this->getByUserId($user_id);
        $guru = $stmt->fetch(PDO::FETCH_ASSOC);
        return $guru ? $guru['id'] : false;
    }

    public function getOneNew() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$this->id]);

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->nip = $row['nip'];
            $this->nama_lengkap = $row['nama_lengkap'];
            $this->jenis_kelamin = $row['jenis_kelamin'];
            $this->email = $row['email'];
            return $row;
        }
        return false;
    }
}
