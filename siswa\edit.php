<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

$siswa = new Siswa();
$kelas = new Kelas();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit;
}

$siswa->id = $_GET['id'];
if (!$siswa->getOne()) {
    $_SESSION['error'] = "Data siswa tidak ditemukan.";
    header("Location: index.php");
    exit;
}

$kelas_list = $kelas->getAll();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $siswa->nis = $_POST['nis'];
    $siswa->nama_siswa = $_POST['nama_siswa'];
    $siswa->jenis_kelamin = $_POST['jenis_kelamin'];
    $siswa->kelas_id = $_POST['kelas_id'];
    $siswa->alamat = $_POST['alamat'];
    $siswa->no_telp = $_POST['no_telp'];

    if ($siswa->update()) {
        $_SESSION['success'] = "Data siswa berhasil diperbarui!";
        header("Location: index.php");
        exit;
    } else {
        $_SESSION['error'] = "Gagal memperbarui data siswa.";
    }
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Data Siswa</h1>
    </div>

    <?php if (isset($_SESSION['error'])) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Form Edit Siswa</h6>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="form-group mb-3">
                    <label for="nis">NIS <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nis" name="nis" 
                           value="<?= htmlspecialchars($siswa->nis) ?>" required>
                </div>
                <div class="form-group mb-3">
                    <label for="nama_siswa">Nama Lengkap <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nama_siswa" name="nama_siswa" 
                           value="<?= htmlspecialchars($siswa->nama_siswa) ?>" required>
                </div>
                <div class="form-group mb-3">
                    <label for="jenis_kelamin">Jenis Kelamin <span class="text-danger">*</span></label>
                    <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" required>
                        <option value="">Pilih Jenis Kelamin</option>
                        <option value="L" <?= $siswa->jenis_kelamin == 'L' ? 'selected' : '' ?>>Laki-laki</option>
                        <option value="P" <?= $siswa->jenis_kelamin == 'P' ? 'selected' : '' ?>>Perempuan</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="kelas_id">Kelas <span class="text-danger">*</span></label>
                    <select class="form-control" id="kelas_id" name="kelas_id" required>
                        <option value="">Pilih Kelas</option>
                        <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?= $row['id'] ?>" <?= $siswa->kelas_id == $row['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($row['nama_kelas']) ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="alamat">Alamat</label>
                    <textarea class="form-control" id="alamat" name="alamat" rows="3"><?= htmlspecialchars($siswa->alamat) ?></textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="no_telp">No. Telepon</label>
                    <input type="text" class="form-control" id="no_telp" name="no_telp" 
                           value="<?= htmlspecialchars($siswa->no_telp) ?>">
                </div>
                <hr>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
