<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/EssayAnswer.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Validate input
if (!isset($_POST['answer_id'])) {
    echo json_encode(['success' => false, 'message' => 'Answer ID tidak ditemukan']);
    exit();
}

$answer_id = $_POST['answer_id'];

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
        exit();
    }

    // Get answer data to verify ownership
    $essayAnswer = new EssayAnswer();
    $answer_data = $essayAnswer->getOne($answer_id);

    if (!$answer_data) {
        echo json_encode(['success' => false, 'message' => 'Jawaban tidak ditemukan']);
        exit();
    }

    // Verify ownership
    if ($answer_data['question_type'] === 'rpp_question') {
        // Verify through RPP ownership
        $rppQuestion = new RppQuestion();
        $question_data = $rppQuestion->getOne($answer_data['question_id']);
        
        if (!$question_data) {
            echo json_encode(['success' => false, 'message' => 'Soal tidak ditemukan']);
            exit();
        }

        // Check RPP ownership
        $database = new Database();
        $conn = $database->getConnection();
        
        $verify_query = "SELECT guru_id FROM rpp WHERE id = :rpp_id";
        $verify_stmt = $conn->prepare($verify_query);
        $verify_stmt->bindParam(":rpp_id", $question_data['rpp_id']);
        $verify_stmt->execute();
        $rpp_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk menghapus jawaban ini']);
            exit();
        }
    } else {
        // Handle multi-RPP questions
        $database = new Database();
        $conn = $database->getConnection();
        
        $verify_query = "SELECT me.guru_id 
                        FROM multi_rpp_exam_questions meq
                        JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                        WHERE meq.id = :question_id";
        $verify_stmt = $conn->prepare($verify_query);
        $verify_stmt->bindParam(":question_id", $answer_data['question_id']);
        $verify_stmt->execute();
        $exam_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
            echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk menghapus jawaban ini']);
            exit();
        }
    }

    // Delete the answer
    if ($essayAnswer->delete($answer_id)) {
        echo json_encode([
            'success' => true,
            'message' => 'Jawaban berhasil dihapus'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Gagal menghapus jawaban'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
