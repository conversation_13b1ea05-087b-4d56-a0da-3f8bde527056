<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../config/database.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../template/header.php';

$periode = new PeriodeAktif();
$tahun_ajaran = new TahunAjaran();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['set_active'])) {
        $periode->tahun_ajaran_id = $_POST['tahun_ajaran_id'];
        $periode->semester = $_POST['semester'];
        
        if($periode->setActive()) {
            $_SESSION['success'] = "Periode aktif berhasil diubah!";
            header("Location: index.php");
            exit();
        } else {
            $_SESSION['error'] = "Gagal mengubah periode aktif!";
        }
    }
}

// Get active period
$active_period = new PeriodeAktif();
$active_period->getActive();

// Get all tahun ajaran
$tahun_ajaran_list = $tahun_ajaran->getAll();

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Pengaturan Periode Aktif</h5>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Periode Aktif Saat Ini</h5>
                            </div>
                            <div class="card-body">
                                <?php if($active_period->id): ?>
                                    <table class="table">
                                        <tr>
                                            <th>Tahun Ajaran</th>
                                            <td><?php echo $active_period->tahun_ajaran; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Semester</th>
                                            <td><?php echo $active_period->semester; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Periode</th>
                                            <td>
                                                <?php 
                                                echo $active_period->formatDate($active_period->tanggal_mulai) . ' s/d ' . 
                                                     $active_period->formatDate($active_period->tanggal_selesai); 
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Status</th>
                                            <td><span class="badge bg-success">Aktif</span></td>
                                        </tr>
                                    </table>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        Belum ada periode aktif yang diatur
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Ubah Periode Aktif</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="row g-3">
                                    <div class="col-md-12">
                                        <label for="tahun_ajaran_id" class="form-label">Tahun Ajaran</label>
                                        <select class="form-select" id="tahun_ajaran_id" name="tahun_ajaran_id" required>
                                            <?php 
                                            $tahun_ajaran_list = $tahun_ajaran->getAll();
                                            while($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): 
                                                $semester1_period = "(" . $tahun_ajaran->formatDate($row['semester_1_mulai']) . 
                                                                  " s/d " . $tahun_ajaran->formatDate($row['semester_1_selesai']) . ")";
                                                $semester2_period = "(" . $tahun_ajaran->formatDate($row['semester_2_mulai']) . 
                                                                  " s/d " . $tahun_ajaran->formatDate($row['semester_2_selesai']) . ")";
                                            ?>
                                                <option value="<?php echo $row['id']; ?>" 
                                                        data-semester1="<?php echo $semester1_period; ?>"
                                                        data-semester2="<?php echo $semester2_period; ?>"
                                                        <?php echo ($active_period->tahun_ajaran_id == $row['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $row['tahun_ajaran']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <label for="semester" class="form-label">Semester</label>
                                        <select class="form-select" id="semester" name="semester" required>
                                            <option value="1" <?php echo ($active_period->semester == '1') ? 'selected' : ''; ?>>
                                                Semester 1 <span id="semester1_period"></span>
                                            </option>
                                            <option value="2" <?php echo ($active_period->semester == '2') ? 'selected' : ''; ?>>
                                                Semester 2 <span id="semester2_period"></span>
                                            </option>
                                        </select>
                                        <div class="form-text" id="semester_period"></div>
                                    </div>

                                    <div class="col-12">
                                        <button type="submit" name="set_active" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Simpan Perubahan
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Riwayat Perubahan Periode</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="tablePeriode">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Semester</th>
                                        <th>Tahun Ajaran</th>
                                        <th>Status</th>
                                        <th>Tanggal Update</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    $history = $periode->getAll();
                                    while($row = $history->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo $row['semester']; ?></td>
                                        <td><?php echo $row['tahun_ajaran']; ?></td>
                                        <td>
                                            <?php if($row['is_active']): ?>
                                                <span class="badge bg-success">Aktif</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Tidak Aktif</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tablePeriode').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});

function updateSemesterPeriod() {
    var select = document.getElementById('tahun_ajaran_id');
    var option = select.options[select.selectedIndex];
    var semester = document.getElementById('semester').value;
    var periodText = document.getElementById('semester_period');
    
    if(semester == '1') {
        periodText.textContent = option.getAttribute('data-semester1');
    } else {
        periodText.textContent = option.getAttribute('data-semester2');
    }
}

document.getElementById('tahun_ajaran_id').addEventListener('change', updateSemesterPeriod);
document.getElementById('semester').addEventListener('change', updateSemesterPeriod);

// Initialize period text
updateSemesterPeriod();
</script>

<?php require_once '../template/footer.php'; ?>
