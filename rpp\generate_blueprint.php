<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Get RPP ID
if (!isset($_GET['rpp_id'])) {
    $_SESSION['error'] = "RPP ID tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: generate_questions.php");
        exit();
    }

    // Get RPP data
    $rpp = new Rpp();
    $rpp_data = $rpp->getOne($rpp_id);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: generate_questions.php");
        exit();
    }

    // Get questions data
    $rppQuestion = new RppQuestion();
    $stmt = $rppQuestion->getByRppId($rpp_id);
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate question statistics
    $stats = [
        'total' => count($questions),
        'multiple_choice' => 0,
        'essay' => 0,
        'regular' => 0,
        'hots_easy' => 0,
        'hots_medium' => 0,
        'hots_hard' => 0
    ];

    foreach ($questions as $question) {
        $stats[$question['question_type']]++;
        $stats[$question['difficulty_level']]++;
    }

} catch (Exception $e) {
    $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
    header("Location: generate_questions.php");
    exit();
}

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);

require_once '../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Generate Kisi-kisi Soal</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="generate_questions.php">RPP</a></li>
                        <li class="breadcrumb-item"><a href="questions_list.php?rpp_id=<?= $rpp_id ?>">Soal</a></li>
                        <li class="breadcrumb-item active">Generate Kisi-kisi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <?php if ($success_msg): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($success_msg) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_msg): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error_msg) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- RPP Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> Informasi RPP</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td width="150"><strong>Mata Pelajaran</strong></td>
                            <td>: <?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Kelas</strong></td>
                            <td>: <?= htmlspecialchars($rpp_data['nama_kelas']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tema/Subtema</strong></td>
                            <td>: <?= htmlspecialchars($rpp_data['tema_subtema']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Materi Pokok</strong></td>
                            <td>: <?= htmlspecialchars($rpp_data['materi_pokok']) ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td width="150"><strong>Total Soal</strong></td>
                            <td>: <?= $stats['total'] ?> soal</td>
                        </tr>
                        <tr>
                            <td><strong>Pilihan Ganda</strong></td>
                            <td>: <?= $stats['multiple_choice'] ?> soal</td>
                        </tr>
                        <tr>
                            <td><strong>Essay</strong></td>
                            <td>: <?= $stats['essay'] ?> soal</td>
                        </tr>
                        <tr>
                            <td><strong>HOTS</strong></td>
                            <td>: <?= ($stats['hots_easy'] + $stats['hots_medium'] + $stats['hots_hard']) ?> soal</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Question Statistics -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="fas fa-chart-bar"></i> Distribusi Soal</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Berdasarkan Jenis Soal</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-primary" style="width: <?= $stats['total'] > 0 ? ($stats['multiple_choice'] / $stats['total']) * 100 : 0 ?>%">
                            Pilihan Ganda (<?= $stats['multiple_choice'] ?>)
                        </div>
                        <div class="progress-bar bg-success" style="width: <?= $stats['total'] > 0 ? ($stats['essay'] / $stats['total']) * 100 : 0 ?>%">
                            Essay (<?= $stats['essay'] ?>)
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>Berdasarkan Tingkat Kesulitan</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-info" style="width: <?= $stats['total'] > 0 ? ($stats['regular'] / $stats['total']) * 100 : 0 ?>%">
                            Regular (<?= $stats['regular'] ?>)
                        </div>
                        <div class="progress-bar bg-warning" style="width: <?= $stats['total'] > 0 ? ($stats['hots_easy'] / $stats['total']) * 100 : 0 ?>%">
                            HOTS Mudah (<?= $stats['hots_easy'] ?>)
                        </div>
                        <div class="progress-bar bg-orange" style="width: <?= $stats['total'] > 0 ? ($stats['hots_medium'] / $stats['total']) * 100 : 0 ?>%">
                            HOTS Sedang (<?= $stats['hots_medium'] ?>)
                        </div>
                        <div class="progress-bar bg-danger" style="width: <?= $stats['total'] > 0 ? ($stats['hots_hard'] / $stats['total']) * 100 : 0 ?>%">
                            HOTS Tinggi (<?= $stats['hots_hard'] ?>)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Generate Blueprint -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="fas fa-robot"></i> Generate Kisi-kisi dengan AI</h5>
        </div>
        <div class="card-body">
            <?php if ($stats['total'] == 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Tidak ada soal!</strong> Anda perlu membuat soal terlebih dahulu sebelum dapat menggenerate kisi-kisi.
                    <br><br>
                    <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate Soal AI
                    </a>
                    <a href="manual_questions.php?rpp_id=<?= $rpp_id ?>" class="btn btn-success">
                        <i class="fas fa-plus"></i> Tambah Soal Manual
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center">
                    <div class="mb-4">
                        <h6><i class="fas fa-magic"></i> Generate Kisi-kisi Ujian</h6>
                        <p class="text-muted mb-3">
                            AI akan menganalisis RPP dan soal-soal yang ada untuk menghasilkan kisi-kisi ujian yang komprehensif dengan format standar.
                            Kisi-kisi akan mencakup pemetaan soal ke indikator pembelajaran, distribusi tingkat kognitif, dan panduan penilaian.
                        </p>
                    </div>

                    <div class="d-flex justify-content-center gap-3">
                        <a href="questions_list.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Soal
                        </a>
                        <a href="single_blueprint_generate.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Generate Kisi-kisi
                        </a>
                    </div>

                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Fitur: Save ke database, Export PDF/Word, Format standar, Preview interaktif
                        </small>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>



<?php require_once '../template/footer.php'; ?>
