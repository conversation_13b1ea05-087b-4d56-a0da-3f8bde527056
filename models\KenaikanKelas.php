<?php
require_once __DIR__ . '/../config/database.php';

class KenaikanKelas {
    private $conn;
    private $table_name = "kenaikan_kelas";

    public $id;
    public $siswa_id;
    public $tahun_ajaran;
    public $status;
    public $alasan;
    public $alasan_lain;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();

        // Create table if not exists
        $this->createTableIfNotExists();
    }

    private function createTableIfNotExists() {
        // Check if table exists
        $tableExists = false;
        $query = "SHOW TABLES LIKE '" . $this->table_name . "'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            $tableExists = true;
        }

        if (!$tableExists) {
            // Create table if it doesn't exist
            $query = "CREATE TABLE IF NOT EXISTS " . $this->table_name . " (
                id INT(11) NOT NULL AUTO_INCREMENT,
                siswa_id INT(11) NOT NULL,
                tahun_ajaran VARCHAR(10) NOT NULL,
                status VARCHAR(50) NOT NULL,
                alasan TEXT NULL,
                alasan_lain TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY (siswa_id, tahun_ajaran)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            $this->conn->exec($query);
        } else {
            // Check if columns exist
            $query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'alasan'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            if ($stmt->rowCount() == 0) {
                // Add alasan column if it doesn't exist
                $query = "ALTER TABLE " . $this->table_name . " ADD COLUMN alasan TEXT NULL AFTER status";
                $this->conn->exec($query);
            }

            $query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'alasan_lain'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            if ($stmt->rowCount() == 0) {
                // Add alasan_lain column if it doesn't exist
                $query = "ALTER TABLE " . $this->table_name . " ADD COLUMN alasan_lain TEXT NULL AFTER alasan";
                $this->conn->exec($query);
            }
        }
    }

    public function getByStudent($siswa_id, $tahun_ajaran) {
        $query = "SELECT * FROM " . $this->table_name . "
                WHERE siswa_id = :siswa_id
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function save() {
        // Check if record exists
        $existing = $this->getByStudent($this->siswa_id, $this->tahun_ajaran);

        if ($existing) {
            // Update
            $query = "UPDATE " . $this->table_name . "
                    SET status = :status,
                        alasan = :alasan,
                        alasan_lain = :alasan_lain
                    WHERE siswa_id = :siswa_id
                    AND tahun_ajaran = :tahun_ajaran";
        } else {
            // Insert
            $query = "INSERT INTO " . $this->table_name . "
                    (siswa_id, tahun_ajaran, status, alasan, alasan_lain)
                    VALUES
                    (:siswa_id, :tahun_ajaran, :status, :alasan, :alasan_lain)";
        }

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind
        $this->siswa_id = htmlspecialchars(strip_tags($this->siswa_id));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));
        $this->status = htmlspecialchars(strip_tags($this->status));

        $stmt->bindParam(':siswa_id', $this->siswa_id);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':alasan', $this->alasan);
        $stmt->bindParam(':alasan_lain', $this->alasan_lain);

        return $stmt->execute();
    }

    public function delete($siswa_id, $tahun_ajaran) {
        $query = "DELETE FROM " . $this->table_name . "
                WHERE siswa_id = :siswa_id
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);

        return $stmt->execute();
    }

    public function getByClass($kelas_id, $tahun_ajaran) {
        $query = "SELECT kk.*, s.nama_siswa, s.nis
                FROM " . $this->table_name . " kk
                JOIN siswa s ON kk.siswa_id = s.id
                WHERE s.kelas_id = :kelas_id
                AND kk.tahun_ajaran = :tahun_ajaran
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }
}
