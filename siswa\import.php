<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $file = $_FILES['file']['tmp_name'];
        $reader = new Xlsx();
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];

        // Get class data for mapping
        $kelas = new Kelas();
        $kelas_list = $kelas->getAll();
        $kelas_map = [];
        while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
            $kelas_map[strtolower($row['nama_kelas'])] = $row['id'];
        }

        // Start from row 2 (after header)
        foreach ($worksheet->getRowIterator(2) as $row) {
            $rowData = [];
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }

            // Skip empty rows
            if (empty($cells[0]) && empty($cells[1])) {
                continue;
            }

            // Get kelas_id from nama_kelas
            $nama_kelas = strtolower(trim($cells[3]));
            $kelas_id = isset($kelas_map[$nama_kelas]) ? $kelas_map[$nama_kelas] : null;

            if (!$kelas_id) {
                throw new Exception("Kelas '$cells[3]' tidak ditemukan");
            }

            $rowData = [
                'nis' => $cells[0],
                'nama_siswa' => $cells[1],
                'jenis_kelamin' => $cells[2],
                'kelas_id' => $kelas_id,
                'alamat' => $cells[4],
                'no_telp' => $cells[5]
            ];
            
            $data[] = $rowData;
        }

        $siswa = new Siswa();
        if ($siswa->importFromArray($data)) {
            $_SESSION['flash_message'] = "Data siswa berhasil diimpor";
            $_SESSION['flash_type'] = "success";
            echo "<script>
                alert('Data siswa berhasil diimpor');
                window.location.href = 'index.php';
            </script>";
            exit;
        } else {
            $error = "Gagal mengimpor data";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Check for flash messages
if (isset($_SESSION['flash_message'])) {
    $success = $_SESSION['flash_message'];
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Import Data Siswa</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">File Excel</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        <div class="form-text">
                            Format file: .xlsx<br>
                            Kolom yang dibutuhkan:
                            <ul>
                                <li>NIS (kolom A)</li>
                                <li>Nama Siswa (kolom B)</li>
                                <li>Jenis Kelamin (L/P) (kolom C)</li>
                                <li>Nama Kelas (kolom D)</li>
                                <li>Alamat (kolom E)</li>
                                <li>No. Telp (kolom F)</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Pastikan data kelas sudah ada sebelum melakukan import data siswa.
                            </div>
                            <a href="template_siswa.xlsx" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
