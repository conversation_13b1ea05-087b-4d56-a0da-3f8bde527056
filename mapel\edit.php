<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';
require_once '../models/Guru.php';

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

$mapel = new MataPelajaran();
$guru = new Guru();
$tingkat = new Tingkat();
$jurusan = new Jurusan();

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$id = $_GET['id'];
$data = $mapel->getById($id);
$guru_list = $guru->getAllActive();
$tingkat_list = $tingkat->getAll();
$jurusan_list = $jurusan->getAll();
$selected_guru = $mapel->getGuruPengampuByMapelId($id);
$selected_jurusan = $mapel->getJurusanByMapel($id);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $mapel->id = $id;
    $mapel->kode_mapel = $_POST['kode_mapel'];
    $mapel->nama_mapel = $_POST['nama_mapel'];
    $mapel->kkm = $_POST['kkm'] ?? 75; // Set default KKM jika tidak diisi
    $mapel->tingkat_id = $_POST['tingkat_id'];
    $guru_ids = isset($_POST['guru_ids']) ? $_POST['guru_ids'] : [];
    $jurusan_ids = isset($_POST['jurusan_ids']) ? $_POST['jurusan_ids'] : [];

    if ($mapel->update()) {
        $mapel->updateGuruPengampu($mapel->id, $guru_ids);
        $mapel->updateJurusan($mapel->id, $jurusan_ids);
        $_SESSION['success'] = "Data mata pelajaran berhasil diperbarui!";
        header('Location: index.php');
        exit;
    } else {
        $_SESSION['error'] = "Gagal memperbarui data mata pelajaran.";
    }
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Mata Pelajaran</h1>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Form Edit Mata Pelajaran</h6>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
        <div class="card-body">
            <form action="" method="post">
                <div class="form-group mb-3">
                    <label for="kode_mapel">Kode Mata Pelajaran <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="kode_mapel" name="kode_mapel" 
                           value="<?= htmlspecialchars($data['kode_mapel']) ?>" required>
                </div>
                <div class="form-group mb-3">
                    <label for="nama_mapel">Nama Mata Pelajaran <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nama_mapel" name="nama_mapel"
                           value="<?= htmlspecialchars($data['nama_mapel']) ?>" required>
                </div>
                <div class="form-group mb-3">
                    <label for="kkm">KKM (Kriteria Ketuntasan Minimal)</label>
                    <input type="number" class="form-control" id="kkm" name="kkm" min="0" max="100"
                           value="<?= htmlspecialchars($data['kkm'] ?? 75) ?>" placeholder="75">
                    <small class="form-text text-muted">Nilai KKM antara 0-100. Default: 75</small>
                </div>
                <div class="form-group mb-3">
                    <label for="tingkat_id">Tingkat <span class="text-danger">*</span></label>
                    <select class="form-control" id="tingkat_id" name="tingkat_id" required>
                        <option value="">Pilih Tingkat</option>
                        <?php foreach ($tingkat_list as $t): ?>
                            <option value="<?= $t['id'] ?>" <?= ($data['tingkat_id'] == $t['id']) ? 'selected' : '' ?>><?= htmlspecialchars($t['nama_tingkat']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="jurusan_ids">Jurusan <span class="text-danger">*</span></label>
                    <select class="form-control select2" id="jurusan_ids" name="jurusan_ids[]" multiple required>
                        <option value="">Pilih jurusan</option>
                        <?php 
                        $selected_jurusan_ids = array_column($selected_jurusan, 'id');
                        foreach ($jurusan_list as $j): 
                        ?>
                            <option value="<?= $j['id'] ?>" <?= in_array($j['id'], $selected_jurusan_ids) ? 'selected' : '' ?>><?= htmlspecialchars($j['nama_jurusan']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="guru_ids">Guru Pengampu <span class="text-danger">*</span></label>
                    <select class="form-control select2" id="guru_ids" name="guru_ids[]" multiple required>
                        <?php 
                        $selected_guru_ids = array_column($selected_guru, 'id');
                        while ($row = $guru_list->fetch(PDO::FETCH_ASSOC)): 
                        ?>
                            <option value="<?= $row['id'] ?>" 
                                    <?= in_array($row['id'], $selected_guru_ids) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($row['nama_lengkap']) ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                    <small class="form-text text-muted">Pilih satu atau lebih guru pengampu mata pelajaran ini</small>
                </div>
                <hr>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: function() {
            return $(this).data('placeholder') || 'Pilih opsi';
        },
        allowClear: true
    });

    $('#jurusan_ids').select2({
        placeholder: 'Pilih jurusan',
        allowClear: true
    });

    $('#guru_ids').select2({
        placeholder: 'Pilih guru pengampu',
        allowClear: true
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
