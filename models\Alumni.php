<?php
require_once __DIR__ . '/../config/database.php';

class Alumni {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    public function addAlumni($data) {
        $query = "INSERT INTO alumni (nis, nama_siswa, jenis_kelamin, alamat, no_telp, tahun_lulus, kelas_terakhir) 
                  VALUES (:nis, :nama_siswa, :jenis_kelamin, :alamat, :no_telp, :tahun_lulus, :kelas_terakhir)";
        
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([
                ':nis' => $data['nis'],
                ':nama_siswa' => $data['nama_siswa'],
                ':jenis_kelamin' => $data['jenis_kelamin'],
                ':alamat' => $data['alamat'],
                ':no_telp' => $data['no_telp'],
                ':tahun_lulus' => $data['tahun_lulus'],
                ':kelas_terakhir' => $data['kelas_terakhir']
            ]);
            return true;
        } catch(PDOException $e) {
            return false;
        }
    }

    public function getAlumniByNIS($nis) {
        $query = "SELECT * FROM alumni WHERE nis = :nis";
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':nis' => $nis]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return false;
        }
    }

    public function getAllAlumni() {
        $query = "SELECT * FROM alumni ORDER BY tahun_lulus DESC, nama_siswa ASC";
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }
}
