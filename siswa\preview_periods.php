<?php
require_once '../models/SiswaPeriode.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';

// Check if this is an AJAX request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    exit('Invalid request');
}

try {
    $start_tahun_ajaran = $_POST['start_tahun_ajaran'];
    $start_semester = $_POST['start_semester'];
    $start_kelas_id = $_POST['start_kelas_id'];

    if (!$start_tahun_ajaran || !$start_semester || !$start_kelas_id) {
        throw new Exception('Missing required parameters');
    }

    // Get class name
    $kelas = new Kelas();
    $kelas->id = $start_kelas_id;
    $kelas->getOne();
    $start_kelas_name = $kelas->nama_kelas;

    // Generate periods
    $siswaPeriode = new SiswaPeriode();
    $periods = $siswaPeriode->generatePeriodSequence($start_tahun_ajaran, $start_semester, $start_kelas_id);

    if (empty($periods)) {
        echo '<div class="alert alert-warning">Tidak ada periode yang akan dibuat.</div>';
        exit;
    }

    // Get all class names for display
    $kelas_list = $kelas->getAll()->fetchAll(PDO::FETCH_ASSOC);
    $kelas_map = [];
    foreach ($kelas_list as $k) {
        $kelas_map[$k['id']] = $k['nama_kelas'];
    }

    echo '<div class="alert alert-info">';
    echo '<h6><i class="fas fa-info-circle"></i> Ringkasan</h6>';
    echo '<p>Sistem akan membuat <strong>' . count($periods) . ' periode akademik</strong> untuk siswa ini:</p>';
    echo '<ul>';
    echo '<li><strong>Periode Mulai:</strong> ' . htmlspecialchars($start_tahun_ajaran) . ' - Semester ' . $start_semester . '</li>';
    echo '<li><strong>Kelas Awal:</strong> ' . htmlspecialchars($start_kelas_name) . '</li>';
    echo '<li><strong>Periode Terakhir:</strong> ' . htmlspecialchars($periods[count($periods)-1]['tahun_ajaran']) . ' - Semester ' . $periods[count($periods)-1]['semester'] . '</li>';
    echo '</ul>';
    echo '</div>';

    echo '<div class="table-responsive">';
    echo '<table class="table table-bordered table-sm">';
    echo '<thead class="thead-light">';
    echo '<tr>';
    echo '<th>No</th>';
    echo '<th>Tahun Ajaran</th>';
    echo '<th>Semester</th>';
    echo '<th>Kelas</th>';
    echo '<th>Tanggal Mulai</th>';
    echo '<th>Tanggal Selesai</th>';
    echo '<th>Status</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    $no = 1;
    foreach ($periods as $period) {
        $is_current = $period['is_current'] == 1;
        $row_class = $is_current ? 'table-success' : '';
        
        echo '<tr class="' . $row_class . '">';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . htmlspecialchars($period['tahun_ajaran']) . '</td>';
        echo '<td>Semester ' . $period['semester'] . '</td>';
        echo '<td>' . htmlspecialchars($kelas_map[$period['kelas_id']] ?? 'Unknown') . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($period['tanggal_mulai'])) . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($period['tanggal_selesai'])) . '</td>';
        echo '<td>';
        if ($is_current) {
            echo '<span class="badge badge-success">Periode Aktif</span>';
        } else {
            echo '<span class="badge badge-secondary">' . ucfirst($period['status']) . '</span>';
        }
        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';

    echo '<div class="alert alert-warning mt-3">';
    echo '<h6><i class="fas fa-exclamation-triangle"></i> Catatan Penting</h6>';
    echo '<ul class="mb-0">';
    echo '<li>Periode yang ditandai hijau adalah periode aktif saat ini</li>';
    echo '<li>Sistem mengasumsikan siswa naik kelas setiap tahun ajaran baru</li>';
    echo '<li>Jika ada perubahan kelas di tengah periode, dapat diubah nanti melalui manajemen periode</li>';
    echo '<li>Data absensi dan nilai dapat diinput untuk setiap periode yang dibuat</li>';
    echo '</ul>';
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-triangle"></i> Error: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
