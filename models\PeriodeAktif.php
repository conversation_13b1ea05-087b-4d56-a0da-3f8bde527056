<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/TahunAjaran.php';

class PeriodeAktif {
    private $conn;
    private $table_name = "periode_aktif";

    public $id;
    public $tahun_ajaran_id;
    public $semester;
    public $tahun_ajaran;
    public $tanggal_mulai;
    public $tanggal_selesai;
    public $is_active;
    public $created_at;

    private static $instance = null;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    private static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getActive() {
        $query = "SELECT pa.*, ta.tahun_ajaran,
                    CASE 
                        WHEN pa.semester = 1 THEN ta.semester_1_mulai 
                        ELSE ta.semester_2_mulai 
                    END as tanggal_mulai,
                    CASE 
                        WHEN pa.semester = 1 THEN ta.semester_1_selesai
                        ELSE ta.semester_2_selesai
                    END as tanggal_selesai
                 FROM " . $this->table_name . " pa
                 JOIN tahun_ajaran ta ON pa.tahun_ajaran_id = ta.id
                 WHERE pa.is_active = 1
                 LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->id = $row['id'];
            $this->tahun_ajaran_id = $row['tahun_ajaran_id'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            $this->tanggal_mulai = $row['tanggal_mulai'];
            $this->tanggal_selesai = $row['tanggal_selesai'];
            $this->is_active = $row['is_active'];
            $this->created_at = $row['created_at'];
            return true;
        }
        return false;
    }

    public function setActive() {
        try {
            $this->conn->beginTransaction();

            // Deactivate current active period
            $query = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE is_active = 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            // Insert new active period
            $query = "INSERT INTO " . $this->table_name . "
                    (tahun_ajaran_id, semester, is_active)
                    VALUES (:tahun_ajaran_id, :semester, 1)";

            $stmt = $this->conn->prepare($query);

            $stmt->bindParam(":tahun_ajaran_id", $this->tahun_ajaran_id);
            $stmt->bindParam(":semester", $this->semester);

            $result = $stmt->execute();
            
            $this->conn->commit();
            return $result;
        } catch(Exception $e) {
            $this->conn->rollBack();
            return false;
        }
    }

    public function getAll() {
        $query = "SELECT pa.*, ta.tahun_ajaran,
                    CASE 
                        WHEN pa.semester = 1 THEN ta.semester_1_mulai 
                        ELSE ta.semester_2_mulai 
                    END as tanggal_mulai,
                    CASE 
                        WHEN pa.semester = 1 THEN ta.semester_1_selesai
                        ELSE ta.semester_2_selesai
                    END as tanggal_selesai
                 FROM " . $this->table_name . " pa
                 JOIN tahun_ajaran ta ON pa.tahun_ajaran_id = ta.id
                 ORDER BY pa.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public static function getCurrentSemester() {
        $instance = self::getInstance();
        $ta = new TahunAjaran();
        $result = $ta->getAll();
        
        $currentDate = date('Y-m-d');
        while($row = $result->fetch(PDO::FETCH_ASSOC)) {
            // Check semester 1
            if($currentDate >= $row['semester_1_mulai'] && $currentDate <= $row['semester_1_selesai']) {
                $instance->tahun_ajaran_id = $row['id'];
                return 1;
            }
            // Check semester 2
            if($currentDate >= $row['semester_2_mulai'] && $currentDate <= $row['semester_2_selesai']) {
                $instance->tahun_ajaran_id = $row['id'];
                return 2;
            }
        }
        
        // If no match found, use the latest tahun ajaran and set semester based on month
        if($result = $ta->getAll()) {
            if($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $instance->tahun_ajaran_id = $row['id'];
                return (date('n') >= 7) ? 1 : 2;
            }
        }
        
        return 1; // Default to semester 1
    }

    public static function getCurrentTahunAjaran() {
        $instance = self::getInstance();
        if($instance->tahun_ajaran_id) {
            $query = "SELECT tahun_ajaran FROM tahun_ajaran WHERE id = :id";
            $stmt = $instance->conn->prepare($query);
            $stmt->bindParam(":id", $instance->tahun_ajaran_id);
            $stmt->execute();
            
            if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                return $row['tahun_ajaran'];
            }
        }
        
        // If no tahun_ajaran_id or not found, get the latest
        $ta = new TahunAjaran();
        if($result = $ta->getAll()) {
            if($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $instance->tahun_ajaran_id = $row['id'];
                return $row['tahun_ajaran'];
            }
        }
        
        // If no tahun ajaran exists, create default
        $currentYear = date('Y');
        $ta = new TahunAjaran();
        $ta->tahun_ajaran = $currentYear . '/' . ($currentYear + 1);
        $dates = $ta->getDefaultDates($currentYear);
        $ta->semester_1_mulai = $dates['semester_1_mulai'];
        $ta->semester_1_selesai = $dates['semester_1_selesai'];
        $ta->semester_2_mulai = $dates['semester_2_mulai'];
        $ta->semester_2_selesai = $dates['semester_2_selesai'];
        
        if($ta->create()) {
            $instance->tahun_ajaran_id = $instance->conn->lastInsertId();
            return $ta->tahun_ajaran;
        }
        
        return date('Y') . '/' . (date('Y') + 1);
    }

    public function formatDate($date) {
        return date('d F Y', strtotime($date));
    }
}
