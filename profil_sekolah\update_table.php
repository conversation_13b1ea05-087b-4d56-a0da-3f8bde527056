<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

// SQL to add new columns
$sql = "
ALTER TABLE profil_sekolah 
ADD COLUMN kode_provinsi VARCHAR(10) NULL AFTER misi,
ADD COLUMN kode_kabupaten VARCHAR(10) NULL AFTER kode_provinsi,
ADD COLUMN kode_kecamatan VARCHAR(10) NULL AFTER kode_kabupaten,
ADD COLUMN kode_desa VARCHAR(10) NULL AFTER kode_kecamatan;
";

// Execute SQL
try {
    $conn->exec($sql);
    echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<h2 style='color: #4CAF50;'>Berhasil!</h2>";
    echo "<p>Kolom-kolom baru telah ditambahkan ke tabel profil_sekolah:</p>";
    echo "<ul>";
    echo "<li>kode_provinsi</li>";
    echo "<li>kode_kabupaten</li>";
    echo "<li>kode_kecamatan</li>";
    echo "<li>kode_desa</li>";
    echo "</ul>";
    echo "<p>Sekarang Anda dapat menggunakan fitur API Wilayah Indonesia.</p>";
    echo "<p><a href='index.php' style='display: inline-block; background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Kembali ke Profil Sekolah</a></p>";
    echo "</div>";
} catch(PDOException $e) {
    if ($e->getCode() == '42S21') {
        // Kolom sudah ada
        echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<h2 style='color: #2196F3;'>Informasi</h2>";
        echo "<p>Kolom-kolom yang diperlukan sudah ada di tabel profil_sekolah.</p>";
        echo "<p><a href='index.php' style='display: inline-block; background-color: #2196F3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Kembali ke Profil Sekolah</a></p>";
        echo "</div>";
    } else {
        // Error lain
        echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<h2 style='color: #F44336;'>Error!</h2>";
        echo "<p>Terjadi kesalahan saat menambahkan kolom baru:</p>";
        echo "<p><code>" . $e->getMessage() . "</code></p>";
        echo "<p>Jika tabel profil_sekolah belum ada, silakan jalankan <a href='initialize.php'>initialize.php</a> terlebih dahulu.</p>";
        echo "<p><a href='index.php' style='display: inline-block; background-color: #F44336; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Kembali ke Profil Sekolah</a></p>";
        echo "</div>";
    }
}
?>
