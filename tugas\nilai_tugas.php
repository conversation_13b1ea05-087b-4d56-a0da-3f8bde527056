<?php
require_once '../config/database.php';
require_once '../models/Tugas.php';
require_once '../models/Siswa.php';
require_once '../template/header.php';

if(!isset($_GET['tugas_id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['tugas_id'];

$tugas = new Tugas();
$tugas->id = $tugas_id;
if (!$tugas->getOne()) {
    $_SESSION['error'] = "Tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

$mapel_id = $tugas->mapel_id;
$semester = $tugas->semester;
$tahun_ajaran = $tugas->tahun_ajaran;
$kelas_id = $tugas->kelas_id;

// Get students from the specific class for the specific academic period
$siswa = new Siswa();
$result_siswa = $siswa->getByPeriode($tahun_ajaran, $semester, $kelas_id);

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $success = true;
    foreach($_POST['nilai'] as $siswa_id => $nilai) {
        if(!$tugas->updateNilai($tugas_id, $siswa_id, $nilai)) {
            $success = false;
            break;
        }
    }

    if($success) {
        $_SESSION['success'] = "Nilai tugas berhasil disimpan!";
        header("Location: tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
        exit();
    } else {
        $_SESSION['error'] = "Gagal menyimpan nilai tugas!";
    }
}

// Get existing nilai
$result_nilai = $tugas->getNilaiTugas($tugas_id);
$nilai_map = [];
while($row = $result_nilai->fetch(PDO::FETCH_ASSOC)) {
    $nilai_map[$row['siswa_id']] = $row;
}

// Get messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Input Nilai Tugas</h5>
                    <small class="text-muted">
                        <?php echo htmlspecialchars($tugas->judul); ?> - <?php echo date('d/m/Y', strtotime($tugas->tanggal)); ?>
                    </small>
                </div>
                <a href="tugas.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" 
                   class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>

        <?php if ($success_msg): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <?php if ($result_siswa->rowCount() > 0): ?>
                    <form method="POST" action="">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="tableNilaiTugas">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Siswa</th>
                                        <th width="150">Nilai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while($row = $result_siswa->fetch(PDO::FETCH_ASSOC)):
                                        // Handle different column names from different queries
                                        $student_id = isset($row['siswa_id']) ? $row['siswa_id'] : $row['id'];
                                        $existing = isset($nilai_map[$student_id]) ? $nilai_map[$student_id] : null;
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo htmlspecialchars($row['nis']); ?></td>
                                        <td><?php echo htmlspecialchars($row['nama_siswa']); ?></td>
                                        <td>
                                            <input type="number" step="0.01" min="0" max="100"
                                                   class="form-control form-control-sm"
                                                   name="nilai[<?php echo $student_id; ?>]"
                                                   value="<?php echo $existing ? $existing['nilai'] : '0'; ?>"
                                                   required>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Nilai
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        Tidak ada siswa di kelas ini.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableNilaiTugas').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        },
        "order": [[2, "asc"]] // Sort by student name
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
