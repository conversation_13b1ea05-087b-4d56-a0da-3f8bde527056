<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Siswa.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/PeriodeAktif.php';

$absensi = new Absensi();
$kelas = new Kelas();
$mapel = new MataPelajaran();
$siswa = new Siswa();
$jadwal = new JadwalPelajaran();
$user = new User();
$periodeAktif = new PeriodeAktif();

// Get current active period
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
$current_semester = $periodeAktif->semester ?: '1';

// Get jadwal_id from URL if coming from dashboard
$jadwal_id = isset($_GET['jadwal_id']) ? $_GET['jadwal_id'] : null;
$jadwal_info = null;

if ($jadwal_id) {
    // Get schedule info
    $jadwal->id = $jadwal_id;
    if ($jadwal->getOne()) {
        // For teachers, check if they teach this subject in any class
        if ($_SESSION['role'] === 'guru') {
            $guru_id = $user->getGuruId($_SESSION['user_id']);
            $authorized = false;
            $stmt = $jadwal->getByMapelAndGuru($jadwal->mapel_id, $guru_id);
            if ($stmt->rowCount() > 0) {
                $authorized = true;
            }
            
            if (!$authorized) {
                echo "<script>alert('Anda tidak memiliki akses untuk mengisi absensi mata pelajaran ini!'); window.location.href='index.php';</script>";
                exit;
            }
        }
        
        // Get custom date from URL if provided
        $custom_date = isset($_GET['tanggal']) ? $_GET['tanggal'] : date('Y-m-d');
        
        $jadwal_info = [
            'kelas_id' => $jadwal->kelas_id,
            'mapel_id' => $jadwal->mapel_id,
            'tanggal' => $custom_date
        ];
    }
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $absensi->tanggal = $_POST['tanggal'];
    $absensi->kelas_id = $_POST['kelas_id'];
    $absensi->mapel_id = $_POST['mapel_id'];
    $absensi->semester = $current_semester;
    $absensi->tahun_ajaran = $current_tahun_ajaran;

    if ($absensi->create()) {
        $absensi_id = $absensi->id;
        $success = true;
        
        // Insert attendance details
        if (isset($_POST['status']) && is_array($_POST['status'])) {
            foreach ($_POST['status'] as $siswa_id => $status) {
                if (!empty($status)) { // Only process if status is not empty
                    if (!$absensi->createDetail($absensi_id, $siswa_id, $status)) {
                        $success = false;
                        break;
                    }
                }
            }
        } else {
            $success = false;
            $error = "Tidak ada data kehadiran siswa yang dikirim";
        }
        
        if ($success) {
            echo "<script>
                alert('Absensi berhasil disimpan!');
                window.location.href = 'index.php';
            </script>";
            exit;
        } else {
            $error = "Gagal menyimpan detail absensi";
        }
    } else {
        $error = "Gagal menyimpan data absensi";
    }
}

// Get lists for dropdowns
$kelas_list = $kelas->getAll();
$mapel_list = $mapel->getAll();
?>

<style>
/* Override browser's default validation styling */
input:invalid, select:invalid {
    box-shadow: none !important;
    background-color: white !important;
}

/* Custom validation styling */
.is-invalid {
    border-color: #dc3545;
    background-color: white !important;
}
</style>

<div class="container-fluid">
    <h2 class="mb-4">Input Absensi</h2>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Form Input Absensi</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" id="absensiForm">
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" 
                               value="<?php echo $jadwal_info ? $jadwal_info['tanggal'] : date('Y-m-d'); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="kelas_id" class="form-label">Kelas</label>
                        <?php if ($_SESSION['role'] === 'guru'): ?>
                            <input type="hidden" name="kelas_id" value="<?php echo $jadwal_info ? $jadwal_info['kelas_id'] : ''; ?>">
                        <?php endif; ?>
                        <select name="<?php echo ($_SESSION['role'] === 'guru' ? 'kelas_id_display' : 'kelas_id'); ?>" id="kelas_id" class="form-select" <?php echo ($_SESSION['role'] === 'guru') ? 'disabled' : ''; ?>>
                            <option value="">Pilih Kelas</option>
                            <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>" 
                                        <?php echo ($jadwal_info && $jadwal_info['kelas_id'] == $row['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                            <?php if ($_SESSION['role'] === 'guru' && isset($jadwal_info)): ?>
                            <input type="hidden" name="mapel_id" value="<?php echo $jadwal_info['mapel_id']; ?>">
                            <select name="mapel_id_display" id="mapel_id" class="form-select" disabled>
                                <option value="">Pilih Mata Pelajaran</option>
                            </select>
                            <?php else: ?>
                            <select name="mapel_id" id="mapel_id" class="form-select">
                                <option value="">Pilih Mata Pelajaran</option>
                            </select>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div id="studentList" class="table-responsive">
                    <!-- Student list will be loaded here -->
                </div>

                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load students when class is selected
    $('#kelas_id').change(function() {
        loadStudents();
        loadMapel();
    });

    // Initial load if class is pre-selected
    if ($('#kelas_id').val()) {
        loadStudents();
        loadMapel();
    }

    function loadMapel() {
        var kelas_id = $('#kelas_id').val() || $('#kelas_id_display').val();
        if (kelas_id) {
            $.get('get_mapel.php', { kelas_id: kelas_id }, function(data) {
                $('#mapel_id').html(data);
                <?php if ($jadwal_info): ?>
                $('#mapel_id').val('<?php echo $jadwal_info['mapel_id']; ?>');
                <?php endif; ?>
            });
        } else {
            $('#mapel_id').html('<option value="">Pilih Mata Pelajaran</option>');
        }
    }

    function loadStudents() {
        var kelas_id = $('#kelas_id').val() || $('#kelas_id_display').val();
        if (kelas_id) {
            // Show loading indicator
            $('#studentList').html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Memuat daftar siswa...</div>');

            $.get('get_students.php', {
                kelas_id: kelas_id,
                tahun_ajaran: '<?php echo $current_tahun_ajaran; ?>',
                semester: '<?php echo $current_semester; ?>'
            }, function(data) {
                $('#studentList').html(data);

                // Wait a moment for DOM to be fully updated, then verify elements
                setTimeout(function() {
                    var statusSelects = $('#studentList .status-select, #studentList select[name^="status"]');
                    var quickSelects = $('#studentList .quick-select');

                    console.log('Students loaded successfully!');
                    console.log('- Status-select elements found:', statusSelects.length);
                    console.log('- Quick-select buttons found:', quickSelects.length);
                    console.log('- Student table found:', $('#studentList table').length > 0);
                    console.log('- Student rows found:', $('#studentList tbody tr').length);

                    if (statusSelects.length === 0) {
                        console.warn('Warning: No status-select elements found after loading students');
                    }
                }, 100);
            }).fail(function(xhr, status, error) {
                console.error('Failed to load students:', error);
                $('#studentList').html('<div class="alert alert-danger">Gagal memuat daftar siswa. Silakan coba lagi. Error: ' + error + '</div>');
            });
        } else {
            $('#studentList').html('');
        }
    }

    // Quick select buttons - Direct and simple approach
    $(document).on('click', '.quick-select', function(e) {
        e.preventDefault();
        var status = $(this).data('status');
        var $button = $(this);

        console.log('Quick select clicked, status:', status);

        // Direct approach: find all select elements with status in their name
        // Use a more direct jQuery selector that should always work
        var statusSelects = $('select[name*="status"]');

        console.log('Found status select elements:', statusSelects.length);

        // If no elements found, try alternative selectors
        if (statusSelects.length === 0) {
            statusSelects = $('.status-select');
            console.log('Alternative selector found:', statusSelects.length);
        }

        if (statusSelects.length === 0) {
            statusSelects = $('#studentList select');
            console.log('Generic select elements found:', statusSelects.length);
        }

        // Set the status regardless of validation - if user clicked the button,
        // they can see the students are loaded
        if (statusSelects.length > 0) {
            statusSelects.val(status).trigger('change');

            // Visual feedback
            $button.addClass('btn-outline-secondary').removeClass('btn-success btn-warning btn-info btn-danger');
            setTimeout(function() {
                $button.removeClass('btn-outline-secondary');
                switch(status) {
                    case 'hadir': $button.addClass('btn-success'); break;
                    case 'sakit': $button.addClass('btn-warning'); break;
                    case 'izin': $button.addClass('btn-info'); break;
                    case 'alpha': $button.addClass('btn-danger'); break;
                }
            }, 200);

            console.log('Successfully set ' + statusSelects.length + ' students to status: ' + status);
        } else {
            // Only show error if there's really no content
            var hasContent = $('#studentList').html().trim().length > 50;
            if (hasContent) {
                console.warn('Content exists but no select elements found - trying force approach');
                // Force approach: wait and try again
                setTimeout(function() {
                    var forceSelects = $('select').filter(function() {
                        return $(this).attr('name') && $(this).attr('name').includes('status');
                    });
                    if (forceSelects.length > 0) {
                        forceSelects.val(status).trigger('change');
                        console.log('Force approach successful:', forceSelects.length);
                    } else {
                        alert('Tidak dapat menemukan elemen status siswa. Silakan refresh halaman.');
                    }
                }, 100);
            } else {
                alert('Silakan pilih kelas terlebih dahulu untuk memuat daftar siswa.');
            }
        }
    });

    // Form validation
    $('#absensiForm').on('submit', function(e) {
        e.preventDefault(); // Prevent form submission
        
        let isValid = true;
        let message = '';
        
        // Validate tanggal
        if (!$('#tanggal').val()) {
            isValid = false;
            message += '- Tanggal harus diisi\n';
        }
        
        // Validate kelas
        if (!$('#kelas_id').val() && !$('#kelas_id_display').val()) {
            isValid = false;
            message += '- Kelas harus dipilih\n';
        }
        
        // Validate mapel
        if (!$('#mapel_id').val() && !$('#mapel_id_display').val()) {
            isValid = false;
            message += '- Mata Pelajaran harus dipilih\n';
        }
        
        if (!isValid) {
            alert('Mohon lengkapi data berikut:\n' + message);
            return false;
        }
        
        // If valid, submit the form
        this.submit();
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
