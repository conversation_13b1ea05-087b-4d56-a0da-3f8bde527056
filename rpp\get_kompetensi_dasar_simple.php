<?php
// Simplified version of KD endpoint for debugging
// This version removes dependencies that might cause issues

error_reporting(E_ALL);
ini_set('display_errors', 0);

// Start output buffering
ob_start();

// Basic includes only
try {
    require_once '../middleware/auth.php';
    checkGuruAccess();
    require_once '../config/database.php';
    require_once '../models/KompetensiDasar.php';
} catch (Exception $e) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error loading files: ' . $e->getMessage(),
        'debug' => 'Include error'
    ]);
    exit();
}

ob_clean();
header('Content-Type: application/json');

// Check method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Only POST method allowed']);
    exit();
}

// Check parameters
if (!isset($_POST['mapel_id']) || !isset($_POST['kelas_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Missing required parameters',
        'debug' => 'mapel_id and kelas_id required',
        'received' => array_keys($_POST)
    ]);
    exit();
}

// Check session
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'No valid session',
        'debug' => 'user_id not in session'
    ]);
    exit();
}

$mapel_id = $_POST['mapel_id'];
$kelas_id = $_POST['kelas_id'];
$guru_id = $_SESSION['user_id'];

// Validate values
if (empty($mapel_id) || empty($kelas_id)) {
    echo json_encode([
        'success' => false,
        'message' => 'Empty parameter values',
        'debug' => [
            'mapel_id' => $mapel_id,
            'kelas_id' => $kelas_id
        ]
    ]);
    exit();
}

try {
    // Test database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed'
        ]);
        exit();
    }
    
    // Create KD instance
    $kd = new KompetensiDasar();
    
    // Use simple parameters without PeriodeAktif dependency
    $semester = isset($_POST['semester']) && !empty($_POST['semester']) ? $_POST['semester'] : null;
    $tahun_ajaran = isset($_POST['tahun_ajaran']) && !empty($_POST['tahun_ajaran']) ? $_POST['tahun_ajaran'] : null;
    
    // Get KD data
    $kd_data = $kd->getKdForRpp($mapel_id, $kelas_id, $guru_id, $semester, $tahun_ajaran);
    
    if (empty($kd_data)) {
        echo json_encode([
            'success' => true,
            'message' => 'No KD data found for this combination',
            'data' => [
                'kd_list' => [],
                'formatted_kd' => '',
                'count' => 0
            ],
            'debug' => [
                'mapel_id' => $mapel_id,
                'kelas_id' => $kelas_id,
                'guru_id' => $guru_id,
                'semester' => $semester,
                'tahun_ajaran' => $tahun_ajaran,
                'query_executed' => true
            ]
        ]);
        exit();
    }
    
    // Format KD data
    $formatted_kd = $kd->formatKdForRpp($kd_data);
    
    echo json_encode([
        'success' => true,
        'message' => 'KD data loaded successfully',
        'data' => [
            'kd_list' => $kd_data,
            'formatted_kd' => $formatted_kd,
            'count' => count($kd_data),
            'semester' => $semester,
            'tahun_ajaran' => $tahun_ajaran
        ]
    ]);
    
} catch (Exception $e) {
    // Log the error for debugging
    error_log("KD Auto-population Error: " . $e->getMessage());
    error_log("File: " . $e->getFile() . " Line: " . $e->getLine());
    error_log("Trace: " . $e->getTraceAsString());

    echo json_encode([
        'success' => false,
        'message' => 'Exception occurred: ' . $e->getMessage(),
        'debug' => [
            'error_type' => get_class($e),
            'error_file' => basename($e->getFile()),
            'error_line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'parameters' => [
                'mapel_id' => $mapel_id ?? 'not set',
                'kelas_id' => $kelas_id ?? 'not set',
                'guru_id' => $guru_id ?? 'not set',
                'semester' => $semester ?? 'not set',
                'tahun_ajaran' => $tahun_ajaran ?? 'not set'
            ]
        ]
    ]);
}
?>
