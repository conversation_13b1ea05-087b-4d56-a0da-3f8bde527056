<?php
require_once '../config/database.php';

echo "<h2>Check Fix Result</h2>\n";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get the most recent questions
    $stmt = $conn->prepare("
        SELECT id, question_text, question_type, options, correct_answer, created_at 
        FROM rpp_questions 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
        ORDER BY id DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recent_questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Questions Created in Last 10 Minutes</h3>\n";
    
    if (empty($recent_questions)) {
        echo "<p style='color: orange;'>⚠️ No questions created in the last 10 minutes</p>\n";
        echo "<p>Generate some new questions to test the fix.</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background-color: #f8f9fa;'>\n";
        echo "<th>ID</th><th>Type</th><th>Options</th><th>Correct Answer</th><th>Created</th><th>Status</th>\n";
        echo "</tr>\n";
        
        $fixed_count = 0;
        $broken_count = 0;
        
        foreach ($recent_questions as $question) {
            $options_status = "❌ NULL";
            $options_display = "NULL";
            $is_fixed = false;
            
            if ($question['options'] !== null) {
                $options_display = htmlspecialchars($question['options']);
                
                // Test if it's valid JSON
                $decoded = json_decode($question['options'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $options_status = "✅ Valid JSON (" . count($decoded) . " items)";
                    $is_fixed = true;
                    $fixed_count++;
                } else {
                    $options_status = "❌ Invalid JSON";
                    $broken_count++;
                }
            } else {
                $broken_count++;
            }
            
            $row_color = $is_fixed ? "#d4edda" : "#f8d7da";
            
            echo "<tr style='background-color: $row_color;'>\n";
            echo "<td>" . $question['id'] . "</td>\n";
            echo "<td>" . $question['question_type'] . "</td>\n";
            echo "<td style='max-width: 300px; word-wrap: break-word; font-size: 12px;'>" . $options_display . "</td>\n";
            echo "<td>" . ($question['correct_answer'] ?? 'NULL') . "</td>\n";
            echo "<td>" . $question['created_at'] . "</td>\n";
            echo "<td>" . $options_status . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        echo "<h3>Summary</h3>\n";
        if ($fixed_count > 0) {
            echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
            echo "<p style='color: green;'><strong>🎉 SUCCESS!</strong></p>\n";
            echo "<p><strong>Fixed questions:</strong> $fixed_count</p>\n";
            echo "<p><strong>Still broken:</strong> $broken_count</p>\n";
            echo "<p>The fix is working! New questions now have valid options.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>\n";
            echo "<p style='color: red;'><strong>❌ Still Not Working</strong></p>\n";
            echo "<p><strong>Broken questions:</strong> $broken_count</p>\n";
            echo "<p>The fix needs more investigation.</p>\n";
            echo "</div>\n";
        }
    }
    
    // Check debug log
    echo "<h3>Debug Log</h3>\n";
    if (file_exists('debug_save_questions.log')) {
        $log_content = file_get_contents('debug_save_questions.log');
        if (!empty($log_content)) {
            echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 300px; overflow-y: auto; font-size: 12px;'>";
            echo htmlspecialchars($log_content);
            echo "</pre>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ Debug log file exists but is empty</p>\n";
        }
    } else {
        echo "<p style='color: gray;'>ℹ️ No debug log file found</p>\n";
    }
    
    echo "<p><em>Auto-refresh in 15 seconds...</em></p>\n";
    echo "<script>setTimeout(function(){ location.reload(); }, 15000);</script>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
}
?>
