<?php
require_once '../template/header.php';
require_once '../models/Jurusan.php';

// Check if user is admin
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/403.php");
    exit();
}

$jurusan = new Jurusan();

// Check if id exists
if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$id = $_GET['id'];
$data = $jurusan->getById($id);

// If data not found
if (!$data) {
    header("Location: index.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $jurusan->id = $id;
    $jurusan->nama_jurusan = $_POST['nama_jurusan'];
    $jurusan->kode_jurusan = $_POST['kode_jurusan'];

    if ($jurusan->update()) {
        $_SESSION['success'] = "Jurusan berhasil diperbarui.";
        header("Location: index.php");
        exit();
    } else {
        $_SESSION['error'] = "Gagal memperbarui jurusan.";
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Edit Jurusan</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <form action="" method="POST">
                <div class="mb-3">
                    <label for="nama_jurusan" class="form-label">Nama Jurusan</label>
                    <input type="text" class="form-control" id="nama_jurusan" name="nama_jurusan" value="<?php echo htmlspecialchars($data['nama_jurusan']); ?>" required>
                </div>
                <div class="mb-3">
                    <label for="kode_jurusan" class="form-label">Kode Jurusan</label>
                    <input type="text" class="form-control" id="kode_jurusan" name="kode_jurusan" value="<?php echo htmlspecialchars($data['kode_jurusan']); ?>" required>
                </div>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </form>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>