<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Method not allowed";
    header("Location: generate_questions.php");
    exit();
}

// Validate required parameters
if (!isset($_POST['rpp_id']) || !isset($_POST['blueprint_data'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_POST['rpp_id'];
$blueprint_data_json = $_POST['blueprint_data'];

// Decode blueprint data
$blueprint_data = json_decode($blueprint_data_json, true);
if (!$blueprint_data) {
    $_SESSION['error'] = "Data kisi-kisi tidak valid.";
    header("Location: single_blueprint_generate.php?rpp_id=" . $rpp_id);
    exit();
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    $conn->beginTransaction();
    
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        throw new Exception("Data guru tidak ditemukan");
    }
    
    // Get RPP data
    $rpp = new Rpp();
    $rpp_data = $rpp->getOne($rpp_id);
    
    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        throw new Exception("RPP tidak ditemukan atau bukan milik Anda.");
    }
    
    // Check if blueprint already exists
    $examBlueprint = new ExamBlueprint();
    $existing_blueprint_stmt = $examBlueprint->getByRppId($rpp_id);
    $existing_blueprint = $existing_blueprint_stmt->fetch(PDO::FETCH_ASSOC);
    
    $blueprint_id = null;
    
    if ($existing_blueprint) {
        // Update existing blueprint
        $examBlueprint->id = $existing_blueprint['id'];
        $examBlueprint->guru_id = $guru_id;
        $examBlueprint->rpp_id = $rpp_id;
        $examBlueprint->multi_exam_id = null; // Single RPP blueprint
        $examBlueprint->blueprint_title = $blueprint_data['exam_info']['title'] ?? $rpp_data['tema_subtema'] . ' - Kisi-kisi';
        $examBlueprint->exam_info = json_encode($blueprint_data['exam_info'] ?? []);
        $examBlueprint->learning_objectives = json_encode($blueprint_data['learning_objectives'] ?? []);
        $examBlueprint->question_distribution = json_encode($blueprint_data['question_distribution'] ?? []);
        $examBlueprint->cognitive_mapping = json_encode($blueprint_data['cognitive_mapping'] ?? []);
        $examBlueprint->difficulty_distribution = json_encode($blueprint_data['summary']['difficulty_distribution'] ?? []);
        $examBlueprint->blueprint_data = json_encode($blueprint_data);
        
        if (!$examBlueprint->update()) {
            throw new Exception("Gagal mengupdate kisi-kisi ujian.");
        }
        
        $blueprint_id = $existing_blueprint['id'];
        $_SESSION['success'] = "Kisi-kisi ujian RPP berhasil diperbarui.";
        
    } else {
        // Create new blueprint
        $examBlueprint->guru_id = $guru_id;
        $examBlueprint->rpp_id = $rpp_id;
        $examBlueprint->multi_exam_id = null; // Single RPP blueprint
        $examBlueprint->blueprint_title = $blueprint_data['exam_info']['title'] ?? $rpp_data['tema_subtema'] . ' - Kisi-kisi';
        $examBlueprint->exam_info = json_encode($blueprint_data['exam_info'] ?? []);
        $examBlueprint->learning_objectives = json_encode($blueprint_data['learning_objectives'] ?? []);
        $examBlueprint->question_distribution = json_encode($blueprint_data['question_distribution'] ?? []);
        $examBlueprint->cognitive_mapping = json_encode($blueprint_data['cognitive_mapping'] ?? []);
        $examBlueprint->difficulty_distribution = json_encode($blueprint_data['summary']['difficulty_distribution'] ?? []);
        $examBlueprint->blueprint_data = json_encode($blueprint_data);
        
        $blueprint_id = $examBlueprint->create();
        
        if (!$blueprint_id) {
            throw new Exception("Gagal menyimpan kisi-kisi ujian.");
        }
        
        $_SESSION['success'] = "Kisi-kisi ujian RPP berhasil disimpan.";
    }
    
    $conn->commit();
    
    // Redirect to blueprint result page
    header("Location: single_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
    
} catch (Exception $e) {
    $conn->rollBack();
    $_SESSION['error'] = "Gagal menyimpan kisi-kisi: " . $e->getMessage();
    
    // Redirect back to generate page
    header("Location: single_blueprint_generate.php?rpp_id=" . $rpp_id);
    exit();
}
?>
