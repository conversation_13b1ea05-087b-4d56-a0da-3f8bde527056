<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/EssayAnswer.php';
require_once '../models/Guru.php';

// Cek parameter rpp_id
if (!isset($_GET['rpp_id'])) {
    $_SESSION['error'] = "ID RPP tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP
$rpp = new Rpp();
$rpp_data = $rpp->getOne($rpp_id);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Get filter parameter
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$valid_filters = ['all', 'multiple_choice', 'essay'];
if (!in_array($filter, $valid_filters)) {
    $filter = 'all';
}

// Ambil daftar soal dengan filter
$rppQuestion = new RppQuestion();
$questions_list = $rppQuestion->getByRppIdWithFilter($rpp_id, $filter);
$stats = $rppQuestion->getStatsByRppId($rpp_id);

// Get essay answers for questions
$essayAnswer = new EssayAnswer();
$all_questions = $rppQuestion->getByRppIdWithFilter($rpp_id, 'all');
$question_ids = [];
while ($q = $all_questions->fetch(PDO::FETCH_ASSOC)) {
    if ($q['question_type'] === 'essay') {
        $question_ids[] = $q['id'];
    }
}
$essay_answers = [];
if (!empty($question_ids)) {
    $essay_answers = $essayAnswer->getAnswersByQuestionIds($question_ids, 'rpp_question');
}

// Process stats
$stats_data = [
    'total' => 0,
    'by_type' => ['multiple_choice' => 0, 'essay' => 0],
    'by_difficulty' => ['regular' => 0, 'hots_easy' => 0, 'hots_medium' => 0, 'hots_hard' => 0]
];

while ($stat = $stats->fetch(PDO::FETCH_ASSOC)) {
    $stats_data['total'] += $stat['count'];
    $stats_data['by_type'][$stat['question_type']] += $stat['count'];
    $stats_data['by_difficulty'][$stat['difficulty_level']] += $stat['count'];
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['question_id'])) {
    $question_id = $_GET['question_id'];
    if ($rppQuestion->delete($question_id)) {
        $_SESSION['success'] = "Soal berhasil dihapus.";
    } else {
        $_SESSION['error'] = "Gagal menghapus soal.";
    }
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}

// Handle bulk delete action
if (isset($_GET['action']) && $_GET['action'] === 'bulk_delete' && isset($_POST['question_ids'])) {
    $question_ids = $_POST['question_ids'];
    $deleted_count = 0;

    foreach ($question_ids as $question_id) {
        if ($rppQuestion->delete($question_id)) {
            $deleted_count++;
        }
    }

    if ($deleted_count > 0) {
        $_SESSION['success'] = "Berhasil menghapus $deleted_count soal.";
    } else {
        $_SESSION['error'] = "Gagal menghapus soal yang dipilih.";
    }
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}

// Handle delete all action
if (isset($_GET['action']) && $_GET['action'] === 'delete_all') {
    $deleted_count = $rppQuestion->deleteAllByRppId($rpp_id);

    if ($deleted_count > 0) {
        $_SESSION['success'] = "Berhasil menghapus semua $deleted_count soal.";
    } else {
        $_SESSION['error'] = "Tidak ada soal yang dihapus atau terjadi kesalahan.";
    }
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Daftar Soal RPP</h5>
            <div>
                <div class="btn-group me-2" role="group">
                    <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate Soal AI
                    </a>
                    <a href="manual_questions.php?rpp_id=<?= $rpp_id ?>" class="btn btn-success">
                        <i class="fas fa-plus"></i> Tambah Soal Manual
                    </a>
                    <a href="generate_blueprint.php?rpp_id=<?= $rpp_id ?>" class="btn btn-info">
                        <i class="fas fa-file-alt"></i> Generate Kisi-kisi
                    </a>
                    <button type="button" class="btn btn-warning" onclick="generateAllEssayAnswers()" id="bulkGenerateAnswersBtn">
                        <i class="fas fa-magic"></i> Generate Semua Jawaban Essay
                    </button>

                </div>
                <a href="generate_questions.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- RPP Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-alt"></i> Informasi RPP</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Mata Pelajaran:</strong> <?= htmlspecialchars($rpp_data['nama_mapel']) ?></p>
                            <p><strong>Kelas:</strong> <?= htmlspecialchars($rpp_data['nama_kelas']) ?></p>
                            <p><strong>Materi Pokok:</strong> <?= htmlspecialchars($rpp_data['materi_pokok']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Semester:</strong> <?= htmlspecialchars($rpp_data['semester']) ?></p>
                            <p><strong>Tahun Ajaran:</strong> <?= htmlspecialchars($rpp_data['tahun_ajaran']) ?></p>
                            <p><strong>Total Soal:</strong> <span class="badge bg-primary"><?= $stats_data['total'] ?></span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Type Navigation -->
            <?php if ($stats_data['total'] > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-filter"></i> Filter Soal</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <ul class="nav nav-pills mb-3" id="questionTypeNav" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link <?= $filter === 'all' ? 'active' : '' ?>"
                                           href="?rpp_id=<?= $rpp_id ?>&filter=all">
                                            <i class="fas fa-list"></i> Semua Soal
                                            <span class="badge bg-light text-dark ms-1"><?= $stats_data['total'] ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link <?= $filter === 'multiple_choice' ? 'active' : '' ?>"
                                           href="?rpp_id=<?= $rpp_id ?>&filter=multiple_choice">
                                            <i class="fas fa-check-circle"></i> Pilihan Ganda
                                            <span class="badge bg-light text-dark ms-1"><?= $stats_data['by_type']['multiple_choice'] ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link <?= $filter === 'essay' ? 'active' : '' ?>"
                                           href="?rpp_id=<?= $rpp_id ?>&filter=essay">
                                            <i class="fas fa-edit"></i> Essay
                                            <span class="badge bg-light text-dark ms-1"><?= $stats_data['by_type']['essay'] ?></span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-danger" onclick="exportQuestions('pdf')">
                                        <i class="fas fa-file-pdf"></i> Export PDF
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="exportQuestions('docx')">
                                        <i class="fas fa-file-word"></i> Export Word
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Berdasarkan Jenis</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Pilihan Ganda:</span>
                                    <span class="badge bg-primary"><?= $stats_data['by_type']['multiple_choice'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Essay:</span>
                                    <span class="badge bg-success"><?= $stats_data['by_type']['essay'] ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-layer-group"></i> Berdasarkan Tingkat Kesulitan</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Regular:</span>
                                    <span class="badge bg-secondary"><?= $stats_data['by_difficulty']['regular'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>HOTS Mudah:</span>
                                    <span class="badge bg-info"><?= $stats_data['by_difficulty']['hots_easy'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>HOTS Sedang:</span>
                                    <span class="badge bg-warning"><?= $stats_data['by_difficulty']['hots_medium'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Tinggi:</span>
                                    <span class="badge bg-danger"><?= $stats_data['by_difficulty']['hots_hard'] ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Questions List -->
            <?php if ($questions_list && $questions_list->rowCount() > 0): ?>
                <!-- Bulk Actions -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">
                                        <strong>Pilih Semua Soal</strong>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-danger" id="bulkDeleteBtn" disabled>
                                        <i class="fas fa-trash"></i> Hapus Terpilih (<span id="selectedCount">0</span>)
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="confirmDeleteAll()">
                                        <i class="fas fa-trash-alt"></i> Hapus Semua Soal
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="questions-container">
                    <?php $question_number = 1; ?>
                    <?php while ($question = $questions_list->fetch(PDO::FETCH_ASSOC)): ?>
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Soal <?= $question_number ?></strong>
                                    <span class="badge bg-<?= $question['question_type'] === 'multiple_choice' ? 'primary' : 'success' ?> ms-2">
                                        <?= $question['question_type'] === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay' ?>
                                    </span>
                                    <span class="badge bg-<?= getDifficultyColor($question['difficulty_level']) ?> ms-1">
                                        <?= getDifficultyLabel($question['difficulty_level']) ?>
                                    </span>
                                    <?php if (isset($question['source_type'])): ?>
                                        <span class="badge bg-<?= $question['source_type'] === 'manual' ? 'warning' : 'info' ?> ms-1">
                                            <?= $question['source_type'] === 'manual' ? 'Manual' : 'AI Generated' ?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($question['question_type'] === 'essay' && isset($essay_answers[$question['id']])): ?>
                                        <span class="badge bg-success ms-1" title="Memiliki jawaban yang diharapkan">
                                            <i class="fas fa-check"></i> Jawaban Tersedia
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="form-check me-3">
                                        <input class="form-check-input question-checkbox" type="checkbox"
                                               value="<?= $question['id'] ?>" id="question_<?= $question['id'] ?>">
                                        <label class="form-check-label" for="question_<?= $question['id'] ?>"></label>
                                    </div>
                                    <div class="btn-group">
                                        <?php if ($question['question_type'] === 'essay'): ?>
                                            <?php $has_answer = isset($essay_answers[$question['id']]); ?>
                                            <button type="button" class="btn btn-<?= $has_answer ? 'success' : 'warning' ?> btn-sm"
                                                    onclick="<?= $has_answer ? 'viewEssayAnswer' : 'generateEssayAnswer' ?>(<?= $question['id'] ?>)"
                                                    title="<?= $has_answer ? 'Lihat Jawaban' : 'Generate Jawaban' ?>">
                                                <i class="fas fa-<?= $has_answer ? 'eye' : 'magic' ?>"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="editQuestion(<?= $question['id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm"
                                                onclick="confirmDelete(<?= $question['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="question-text mb-3">
                                    <?= nl2br(htmlspecialchars($question['question_text'])) ?>
                                </div>

                                <?php if ($question['question_type'] === 'multiple_choice' && $question['options']): ?>
                                    <?php $options = json_decode($question['options'], true); ?>
                                    <?php if ($options): ?>
                                        <div class="options mb-3">
                                            <?php foreach ($options as $option): ?>
                                                <div class="option-item mb-1 
                                                    <?= (substr($option, 0, 1) === $question['correct_answer']) ? 'correct-answer' : '' ?>">
                                                    <?= htmlspecialchars($option) ?>
                                                    <?php if (substr($option, 0, 1) === $question['correct_answer']): ?>
                                                        <i class="fas fa-check-circle text-success ms-2"></i>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <div class="answer-key">
                                            <small class="text-muted">
                                                <strong>Kunci Jawaban:</strong> <?= htmlspecialchars($question['correct_answer']) ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if (isset($question['analysis_data']) && !empty($question['analysis_data'])): ?>
                                    <?php $analysis = json_decode($question['analysis_data'], true); ?>
                                    <?php if ($analysis): ?>
                                        <div class="analysis-info mt-3">
                                            <div class="alert alert-info small">
                                                <h6><i class="fas fa-chart-line"></i> Analisis AI</h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Level Kognitif:</strong> <?= htmlspecialchars($analysis['cognitive_level'] ?? 'N/A') ?></p>
                                                        <p class="mb-1"><strong>HOTS:</strong> <?= ($analysis['is_hots'] ?? false) ? 'Ya' : 'Tidak' ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Confidence:</strong> <?= isset($analysis['confidence_score']) ? round($analysis['confidence_score'] * 100) . '%' : 'N/A' ?></p>
                                                    </div>
                                                </div>
                                                <?php if (isset($analysis['reasoning'])): ?>
                                                    <p class="mb-0"><strong>Alasan:</strong> <?= htmlspecialchars($analysis['reasoning']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <div class="question-meta mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> Dibuat: <?= date('d/m/Y H:i', strtotime($question['created_at'])) ?>
                                        <?php if ($question['category']): ?>
                                            | <i class="fas fa-tag"></i> <?= htmlspecialchars($question['category']) ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php $question_number++; ?>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <h5><i class="fas fa-info-circle"></i> Belum Ada Soal</h5>
                    <p class="mb-3">Belum ada soal yang dibuat untuk RPP ini.</p>
                    <a href="configure_generation.php?rpp_id=<?= $rpp_id ?>" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate Soal Pertama
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Apakah Anda yakin ingin menghapus soal ini? Tindakan ini tidak dapat dibatalkan.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <a href="#" id="deleteConfirmBtn" class="btn btn-danger">Hapus</a>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkDeleteModalLabel">Konfirmasi Hapus Massal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus <strong><span id="bulkDeleteCount">0</span> soal</strong> yang dipilih?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Tindakan ini tidak dapat dibatalkan.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="bulkDeleteConfirmBtn">Hapus Semua</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Confirmation Modal -->
<div class="modal fade" id="deleteAllModal" tabindex="-1" aria-labelledby="deleteAllModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAllModalLabel">Konfirmasi Hapus Semua Soal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus <strong>SEMUA SOAL</strong> untuk RPP ini?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Tindakan ini akan menghapus semua soal dan tidak dapat dibatalkan.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <a href="questions_list.php?rpp_id=<?= $rpp_id ?>&action=delete_all" class="btn btn-danger">Hapus Semua Soal</a>
            </div>
        </div>
    </div>
</div>

<!-- Essay Answer Modal -->
<div class="modal fade" id="essayAnswerModal" tabindex="-1" aria-labelledby="essayAnswerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="essayAnswerModalLabel">Jawaban Essay</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="essayAnswerContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="editAnswerBtn" style="display: none;">Edit Jawaban</button>
                <button type="button" class="btn btn-success" id="saveAnswerBtn" style="display: none;">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Question Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Edit Soal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editQuestionForm" action="edit_question.php" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="question_id" id="edit_question_id">
                    <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Jenis Soal</label>
                                <select class="form-select" name="question_type" id="edit_question_type" onchange="toggleEditOptions()">
                                    <option value="multiple_choice">Pilihan Ganda</option>
                                    <option value="essay">Essay</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tingkat Kesulitan</label>
                                <select class="form-select" name="difficulty_level" id="edit_difficulty_level">
                                    <option value="regular">Regular (C1-C3: Mengingat, Memahami, Menerapkan)</option>
                                    <option value="hots_easy">HOTS Mudah (C4: Menganalisis)</option>
                                    <option value="hots_medium">HOTS Sedang (C4-C5: Menganalisis, Mengevaluasi)</option>
                                    <option value="hots_hard">HOTS Tinggi (C5-C6: Mengevaluasi, Mencipta)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Kategori</label>
                        <input type="text" class="form-control" name="category" id="edit_category" placeholder="Kategori soal">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Teks Soal</label>
                        <textarea class="form-control" name="question_text" id="edit_question_text" rows="4" required></textarea>
                    </div>

                    <!-- Multiple Choice Options -->
                    <div id="edit_options_container">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Opsi Jawaban</label>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Jumlah Opsi</label>
                                <select class="form-select" id="edit_option_count" onchange="updateEditOptions()">
                                    <option value="2">2 Opsi (A-B)</option>
                                    <option value="3">3 Opsi (A-C)</option>
                                    <option value="4" selected>4 Opsi (A-D)</option>
                                    <option value="5">5 Opsi (A-E)</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text">A.</span>
                                        <input type="text" class="form-control option-input" name="options[]" id="edit_option_a" placeholder="Opsi A" required>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text">B.</span>
                                        <input type="text" class="form-control option-input" name="options[]" id="edit_option_b" placeholder="Opsi B" required>
                                    </div>
                                </div>
                                <div class="mb-2" id="edit_option_c_container">
                                    <div class="input-group">
                                        <span class="input-group-text">C.</span>
                                        <input type="text" class="form-control option-input" name="options[]" id="edit_option_c" placeholder="Opsi C">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2" id="edit_option_d_container">
                                    <div class="input-group">
                                        <span class="input-group-text">D.</span>
                                        <input type="text" class="form-control option-input" name="options[]" id="edit_option_d" placeholder="Opsi D">
                                    </div>
                                </div>
                                <div class="mb-2" id="edit_option_e_container" style="display: none;">
                                    <div class="input-group">
                                        <span class="input-group-text">E.</span>
                                        <input type="text" class="form-control option-input" name="options[]" id="edit_option_e" placeholder="Opsi E">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kunci Jawaban</label>
                            <select class="form-select" name="correct_answer" id="edit_correct_answer">
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                                <option value="E">E</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

.option-item {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
}

.option-item.correct-answer {
    background-color: #d4edda;
    border-left-color: #28a745;
    font-weight: 500;
}

.question-meta {
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
}

.badge {
    font-size: 0.8rem;
}

.questions-container .card {
    transition: box-shadow 0.2s;
}

.questions-container .card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Navigation Pills Styling */
.nav-pills .nav-link {
    border-radius: 20px;
    margin-right: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.nav-pills .nav-link:not(.active) {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.nav-pills .nav-link:not(.active):hover {
    color: #495057;
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
    border-color: #007bff;
}

.nav-pills .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

/* Export Buttons Styling */
.btn-group .btn {
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Filter Card Styling */
.card-header h6 {
    color: #495057;
    font-weight: 600;
}

/* Loading Animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-pills {
        flex-direction: column;
    }

    .nav-pills .nav-link {
        margin-right: 0;
        margin-bottom: 8px;
        text-align: center;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 8px;
        border-radius: 0.375rem !important;
    }
}

/* Essay Answer Display Formatting */
.essay-answer-display {
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.essay-answer-display p {
    margin-bottom: 1rem;
}

.essay-answer-display ul, .essay-answer-display ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.essay-answer-display li {
    margin-bottom: 0.5rem;
}

.essay-answer-display strong {
    font-weight: 600;
    color: #2c3e50;
}

.essay-answer-display em {
    font-style: italic;
    color: #34495e;
}

/* Modal content formatting */
.modal-body .form-control {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

.modal-body .form-control[readonly] {
    background-color: #f8f9fa;
}
</style>

<script>
function confirmDelete(questionId) {
    const deleteUrl = `questions_list.php?rpp_id=<?= $rpp_id ?>&action=delete&question_id=${questionId}`;
    document.getElementById('deleteConfirmBtn').href = deleteUrl;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function exportQuestions(format) {
    const currentFilter = '<?= $filter ?>';
    const rppId = '<?= $rpp_id ?>';

    // Show loading state
    const exportButtons = document.querySelectorAll('.btn-group button');
    exportButtons.forEach(btn => {
        btn.disabled = true;
        if (btn.onclick && btn.onclick.toString().includes(format)) {
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        }
    });

    // Create export URL
    const exportUrl = `export_questions.php?rpp_id=${rppId}&format=${format}&filter=${currentFilter}`;

    // Create hidden iframe for download
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = exportUrl;
    document.body.appendChild(iframe);

    // Reset buttons after delay
    setTimeout(() => {
        exportButtons.forEach(btn => {
            btn.disabled = false;
        });

        // Reset button text
        const pdfBtn = document.querySelector('button[onclick*="pdf"]');
        const docxBtn = document.querySelector('button[onclick*="docx"]');

        if (pdfBtn) {
            pdfBtn.innerHTML = '<i class="fas fa-file-pdf"></i> Export PDF';
        }
        if (docxBtn) {
            docxBtn.innerHTML = '<i class="fas fa-file-word"></i> Export Word';
        }

        // Remove iframe
        document.body.removeChild(iframe);
    }, 3000);
}

// Bulk delete and edit functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectedCountSpan = document.getElementById('selectedCount');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteButton();
        });
    }

    // Individual checkbox functionality
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkDeleteButton();
        });
    });

    // Bulk delete button functionality
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            const selectedIds = getSelectedQuestionIds();
            if (selectedIds.length > 0) {
                document.getElementById('bulkDeleteCount').textContent = selectedIds.length;
                const modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
                modal.show();
            }
        });
    }

    // Bulk delete confirm button
    const bulkDeleteConfirmBtn = document.getElementById('bulkDeleteConfirmBtn');
    if (bulkDeleteConfirmBtn) {
        bulkDeleteConfirmBtn.addEventListener('click', function() {
            const selectedIds = getSelectedQuestionIds();
            if (selectedIds.length > 0) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'questions_list.php?rpp_id=<?= $rpp_id ?>&action=bulk_delete';

                selectedIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'question_ids[]';
                    input.value = id;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
        const totalCount = questionCheckboxes.length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    }

    function updateBulkDeleteButton() {
        const selectedCount = document.querySelectorAll('.question-checkbox:checked').length;

        if (selectedCountSpan) {
            selectedCountSpan.textContent = selectedCount;
        }

        if (bulkDeleteBtn) {
            bulkDeleteBtn.disabled = selectedCount === 0;
        }
    }

    function getSelectedQuestionIds() {
        const selectedCheckboxes = document.querySelectorAll('.question-checkbox:checked');
        return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
    }

    // Add filter change animation
    const navLinks = document.querySelectorAll('#questionTypeNav .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading animation to clicked tab
            if (!this.classList.contains('active')) {
                this.innerHTML += ' <i class="fas fa-spinner fa-spin ms-1"></i>';
            }
        });
    });
});

function confirmDeleteAll() {
    const modal = new bootstrap.Modal(document.getElementById('deleteAllModal'));
    modal.show();
}

function editQuestion(questionId) {
    // Show loading state
    const editBtn = document.querySelector(`button[onclick="editQuestion(${questionId})"]`);
    const originalText = editBtn.innerHTML;
    editBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    editBtn.disabled = true;

    // Fetch question data via AJAX
    fetch(`get_question.php?id=${questionId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data); // Debug log
            if (data.success) {
                populateEditForm(data.question);
                const modal = new bootstrap.Modal(document.getElementById('editModal'));
                modal.show();
            } else {
                console.error('Server error:', data.message);
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            alert('Terjadi kesalahan saat mengambil data soal: ' + error.message);
        })
        .finally(() => {
            // Restore button state
            editBtn.innerHTML = originalText;
            editBtn.disabled = false;
        });
}

function populateEditForm(question) {
    console.log('Populating form with question:', question); // Debug log

    document.getElementById('edit_question_id').value = question.id;
    document.getElementById('edit_question_type').value = question.question_type;
    document.getElementById('edit_difficulty_level').value = question.difficulty_level;
    document.getElementById('edit_category').value = question.category || '';
    document.getElementById('edit_question_text').value = question.question_text;

    // Clear all option fields first
    document.getElementById('edit_option_a').value = '';
    document.getElementById('edit_option_b').value = '';
    document.getElementById('edit_option_c').value = '';
    document.getElementById('edit_option_d').value = '';
    document.getElementById('edit_option_e').value = '';

    // Handle multiple choice options
    if (question.question_type === 'multiple_choice' && question.options) {
        try {
            const options = typeof question.options === 'string' ? JSON.parse(question.options) : question.options;
            console.log('Parsed options:', options); // Debug log

            // Set the correct option count based on existing options
            const optionCount = options.length;
            document.getElementById('edit_option_count').value = optionCount;
            updateEditOptions(); // Update visibility of option fields

            // Populate options
            options.forEach((option, index) => {
                let optionText = option;
                // Remove "A. ", "B. ", etc. if present
                if (option.match(/^[A-E]\.\s/)) {
                    optionText = option.substring(3);
                }

                const optionIds = ['edit_option_a', 'edit_option_b', 'edit_option_c', 'edit_option_d', 'edit_option_e'];
                if (index < optionIds.length) {
                    document.getElementById(optionIds[index]).value = optionText;
                }
            });

            document.getElementById('edit_correct_answer').value = question.correct_answer || 'A';
        } catch (error) {
            console.error('Error parsing options:', error);
            console.log('Raw options data:', question.options);
        }
    }

    // Toggle options visibility
    toggleEditOptions();
}

function toggleEditOptions() {
    const questionType = document.getElementById('edit_question_type').value;
    const optionsContainer = document.getElementById('edit_options_container');

    if (questionType === 'essay') {
        optionsContainer.style.display = 'none';
        // Clear required attribute from option inputs
        optionsContainer.querySelectorAll('input[name="options[]"]').forEach(input => {
            input.removeAttribute('required');
        });
    } else {
        optionsContainer.style.display = 'block';
        updateEditOptions(); // Update option visibility based on count
    }
}

function updateEditOptions() {
    const optionCount = parseInt(document.getElementById('edit_option_count').value);
    const containers = [
        'edit_option_c_container',
        'edit_option_d_container',
        'edit_option_e_container'
    ];
    const correctAnswerSelect = document.getElementById('edit_correct_answer');

    // Show/hide option containers based on count
    containers.forEach((containerId, index) => {
        const container = document.getElementById(containerId);
        const input = container.querySelector('input');

        if (index + 3 <= optionCount) { // +3 because C=3, D=4, E=5
            container.style.display = 'block';
            input.setAttribute('required', 'required');
        } else {
            container.style.display = 'none';
            input.removeAttribute('required');
            input.value = ''; // Clear hidden inputs
        }
    });

    // Update correct answer options
    correctAnswerSelect.innerHTML = '';
    const letters = ['A', 'B', 'C', 'D', 'E'];
    for (let i = 0; i < optionCount; i++) {
        const option = document.createElement('option');
        option.value = letters[i];
        option.textContent = letters[i];
        correctAnswerSelect.appendChild(option);
    }

    // Set A and B as always required
    document.getElementById('edit_option_a').setAttribute('required', 'required');
    document.getElementById('edit_option_b').setAttribute('required', 'required');
}

// Essay Answer Functions
function generateEssayAnswer(questionId) {
    const btn = document.querySelector(`button[onclick="generateEssayAnswer(${questionId})"]`);
    const originalText = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    // First check if table exists
    checkEssayTable().then(() => {
        // Table exists, proceed with generation
        generateAnswerRequest(questionId, btn, originalText);
    }).catch(error => {
        console.error('Table check failed:', error);
        showAlert('danger', 'Error: ' + error);
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function generateAnswerRequest(questionId, btn, originalText) {

    fetch('generate_essay_answer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `question_id=${questionId}&question_type=rpp_question`
    })
    .then(response => {
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response bukan JSON. Kemungkinan ada error PHP.');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            if (data.already_exists) {
                showEssayAnswer(data.answer);
            } else {
                showEssayAnswer(data.answer);
                // Update button to show answer is available
                btn.className = 'btn btn-success btn-sm';
                btn.setAttribute('onclick', `viewEssayAnswer(${questionId})`);
                btn.setAttribute('title', 'Lihat Jawaban');
                btn.innerHTML = '<i class="fas fa-eye"></i>';

                // Add badge to question header
                const questionHeader = btn.closest('.card-header');
                if (!questionHeader.querySelector('.badge.bg-success')) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-success ms-1';
                    badge.title = 'Memiliki jawaban yang diharapkan';
                    badge.innerHTML = '<i class="fas fa-check"></i> Jawaban Tersedia';
                    questionHeader.querySelector('div').appendChild(badge);
                }
            }

            // Show success message
            showAlert('success', data.message);
        } else {
            showAlert('danger', 'Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        let errorMessage = 'Terjadi kesalahan saat generate jawaban';

        if (error.message && error.message.includes('JSON')) {
            errorMessage = `
                <strong>Error:</strong> Response tidak valid dari server.<br>
                <small>Kemungkinan penyebab:</small>
                <ul class="mb-0 mt-1">
                    <li>Tidak ada soal essay di RPP ini</li>
                    <li>Ada error PHP di server</li>
                    <li>Konfigurasi database bermasalah</li>
                </ul>
            `;
        } else if (error.message) {
            errorMessage = 'Error: ' + error.message;
        }

        showAlert('danger', errorMessage);
    })
    .finally(() => {
        // Restore button if generation failed
        if (!btn.classList.contains('btn-success')) {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    });
}

function viewEssayAnswer(questionId) {
    fetch(`get_essay_answer.php?question_id=${questionId}&question_type=rpp_question`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.has_answer) {
            showEssayAnswer(data.answer);
        } else {
            showAlert('warning', 'Jawaban tidak ditemukan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Terjadi kesalahan saat mengambil jawaban');
    });
}

function showEssayAnswer(answer) {
    const modal = document.getElementById('essayAnswerModal');
    const content = document.getElementById('essayAnswerContent');
    const editBtn = document.getElementById('editAnswerBtn');
    const saveBtn = document.getElementById('saveAnswerBtn');

    // Parse JSON fields
    let scoringRubric = {};
    let generationMetadata = {};

    try {
        scoringRubric = typeof answer.scoring_rubric === 'string' ?
            JSON.parse(answer.scoring_rubric) : answer.scoring_rubric || {};
        generationMetadata = typeof answer.generation_metadata === 'string' ?
            JSON.parse(answer.generation_metadata) : answer.generation_metadata || {};
    } catch (e) {
        console.error('Error parsing JSON fields:', e);
    }

    // Format text with proper line breaks and structure
    const formatText = (text) => {
        if (!text) return 'Tidak ada data';
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^\s*[-•]\s*/gm, '• ')
            .replace(/^\s*\d+\.\s*/gm, (match) => `<strong>${match}</strong>`);
    };

    content.innerHTML = `
        <div class="mb-3">
            <label class="form-label"><strong>Jawaban yang Diharapkan:</strong></label>
            <div class="form-control essay-answer-display" style="min-height: 100px;" id="expectedAnswerDisplay">${formatText(answer.expected_answer)}</div>
            <textarea class="form-control" style="min-height: 100px; display: none;" id="expectedAnswerEdit">${answer.expected_answer}</textarea>
        </div>

        <div class="mb-3">
            <label class="form-label"><strong>Poin-poin Kunci:</strong></label>
            <div class="form-control essay-answer-display" style="min-height: 80px;" id="answerPointsDisplay">${formatText(answer.answer_points || 'Tidak ada poin khusus')}</div>
            <textarea class="form-control" style="min-height: 80px; display: none;" id="answerPointsEdit">${answer.answer_points || ''}</textarea>
        </div>

        <div class="mb-3">
            <label class="form-label"><strong>Rubrik Penilaian:</strong></label>
            <div id="scoringRubricDisplay">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-success mb-2">
                            <div class="card-body py-2">
                                <strong class="text-success">Sangat Baik (90-100):</strong><br>
                                <small>${scoringRubric.excellent || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                        <div class="card border-info mb-2">
                            <div class="card-body py-2">
                                <strong class="text-info">Baik (80-89):</strong><br>
                                <small>${scoringRubric.good || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-warning mb-2">
                            <div class="card-body py-2">
                                <strong class="text-warning">Cukup (70-79):</strong><br>
                                <small>${scoringRubric.fair || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                        <div class="card border-danger mb-2">
                            <div class="card-body py-2">
                                <strong class="text-danger">Kurang (60-69):</strong><br>
                                <small>${scoringRubric.poor || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Dibuat: ${new Date(answer.created_at).toLocaleString('id-ID')}
                ${generationMetadata.confidence_score ? ` | Confidence: ${Math.round(generationMetadata.confidence_score * 100)}%` : ''}
            </small>
        </div>
    `;

    // Store answer data for editing
    modal.setAttribute('data-answer-id', answer.id);

    // Show edit button
    editBtn.style.display = 'inline-block';
    saveBtn.style.display = 'none';

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

function generateAllEssayAnswers() {
    const btn = document.getElementById('bulkGenerateAnswersBtn');
    const originalText = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // First check if table exists
    checkEssayTable().then(() => {
        // Table exists, proceed with bulk generation
        generateBulkAnswersRequest(btn, originalText);
    }).catch(error => {
        console.error('Table check failed:', error);
        showAlert('danger', 'Error: ' + error);
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function generateBulkAnswersRequest(btn, originalText) {

    fetch('generate_bulk_essay_answers.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `source_type=rpp&source_id=<?= $rpp_id ?>`
    })
    .then(response => {
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response bukan JSON. Kemungkinan ada error PHP.');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            if (data.generated_count > 0) {
                // Refresh page to show updated buttons and badges
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        } else {
            showAlert('danger', 'Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        let errorMessage = 'Terjadi kesalahan saat generate jawaban';

        if (error.message && error.message.includes('JSON')) {
            errorMessage = `
                <strong>Error:</strong> Response tidak valid dari server.<br>
                <small>Kemungkinan penyebab:</small>
                <ul class="mb-0 mt-1">
                    <li>Tidak ada soal essay di RPP ini</li>
                    <li>Ada error PHP di server</li>
                    <li>Konfigurasi database bermasalah</li>
                </ul>
            `;
        } else if (error.message) {
            errorMessage = 'Error: ' + error.message;
        }

        showAlert('danger', errorMessage);
    })
    .finally(() => {
        // Restore button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Edit answer functionality
document.addEventListener('DOMContentLoaded', function() {
    const editBtn = document.getElementById('editAnswerBtn');
    const saveBtn = document.getElementById('saveAnswerBtn');

    if (editBtn) {
        editBtn.addEventListener('click', function() {
            // Switch to edit mode
            document.getElementById('expectedAnswerDisplay').style.display = 'none';
            document.getElementById('expectedAnswerEdit').style.display = 'block';
            document.getElementById('answerPointsDisplay').style.display = 'none';
            document.getElementById('answerPointsEdit').style.display = 'block';

            editBtn.style.display = 'none';
            saveBtn.style.display = 'inline-block';
        });
    }

    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            const modal = document.getElementById('essayAnswerModal');
            const answerId = modal.getAttribute('data-answer-id');
            const expectedAnswer = document.getElementById('expectedAnswerEdit').value;
            const answerPoints = document.getElementById('answerPointsEdit').value;

            if (!expectedAnswer.trim()) {
                showAlert('warning', 'Jawaban yang diharapkan tidak boleh kosong');
                return;
            }

            // Show loading
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
            saveBtn.disabled = true;

            fetch('update_essay_answer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `answer_id=${answerId}&expected_answer=${encodeURIComponent(expectedAnswer)}&answer_points=${encodeURIComponent(answerPoints)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);

                    // Update display with proper formatting
                    const formatText = (text) => {
                        if (!text) return 'Tidak ada data';
                        return text
                            .replace(/\n/g, '<br>')
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em>$1</em>')
                            .replace(/^\s*[-•]\s*/gm, '• ')
                            .replace(/^\s*\d+\.\s*/gm, (match) => `<strong>${match}</strong>`);
                    };

                    document.getElementById('expectedAnswerDisplay').innerHTML = formatText(expectedAnswer);
                    document.getElementById('answerPointsDisplay').innerHTML = formatText(answerPoints || 'Tidak ada poin khusus');

                    // Switch back to view mode
                    document.getElementById('expectedAnswerDisplay').style.display = 'block';
                    document.getElementById('expectedAnswerEdit').style.display = 'none';
                    document.getElementById('answerPointsDisplay').style.display = 'block';
                    document.getElementById('answerPointsEdit').style.display = 'none';

                    editBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'none';
                } else {
                    showAlert('danger', 'Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Terjadi kesalahan saat menyimpan jawaban');
            })
            .finally(() => {
                // Restore button
                saveBtn.innerHTML = 'Simpan Perubahan';
                saveBtn.disabled = false;
            });
        });
    }
});

// Helper function to check if essay_answers table exists
function checkEssayTable() {
    return fetch('check_essay_table.php', {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            if (data.action === 'incomplete') {
                throw new Error('Table essay_answers tidak lengkap. Silakan jalankan migration script.');
            } else {
                throw new Error(data.message);
            }
        }

        if (data.action === 'created') {
            showAlert('success', 'Table essay_answers berhasil dibuat otomatis.');
        }

        return true;
    })
    .catch(error => {
        console.error('Error checking table:', error);
        throw error.message || 'Gagal mengecek table database';
    });
}

// Helper function to show alerts
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert at the top of card-body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }
    }, 5000);
}
</script>

<?php
function getDifficultyColor($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'secondary';
        case 'hots_easy': return 'info';
        case 'hots_medium': return 'warning';
        case 'hots_hard': return 'danger';
        default: return 'secondary';
    }
}

function getDifficultyLabel($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'Regular (C1-C3)';
        case 'hots_easy': return 'HOTS Mudah (C4)';
        case 'hots_medium': return 'HOTS Sedang (C4-C5)';
        case 'hots_hard': return 'HOTS Tinggi (C5-C6)';
        default: return 'Regular (C1-C3)';
    }
}
?>

<?php require_once '../template/footer.php'; ?>
