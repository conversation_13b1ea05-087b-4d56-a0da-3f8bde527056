<?php
// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require_once '../vendor/autoload.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Absensi.php';
require_once '../models/TahunAjaran.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/Siswa.php';
session_start();

use Dompdf\Dompdf;
use Dompdf\Options;

try {
    if(!isset($_GET['kelas_id']) || !isset($_GET['mapel_id']) || !isset($_GET['tahun_ajaran'])) {
        die("Parameter tidak lengkap.");
    }

    $kelas_id = $_GET['kelas_id'];
    $mapel_id = $_GET['mapel_id'];
    $tahun_ajaran_id = $_GET['tahun_ajaran'];

    // Initialize models
    $kelas = new Kelas();
    $mapel = new MataPelajaran();
    $absensi = new Absensi();
    $tahunAjaran = new TahunAjaran();
    $jadwal = new JadwalPelajaran();
    $user = new User();
    $siswa = new Siswa();

    // Check if user has access to this subject
    if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
        $user = new User();
        $guru_id = $user->getGuruId($_SESSION['user_id']);
        
        if ($guru_id) {
            $jadwal = new JadwalPelajaran();
            $query = "SELECT COUNT(*) as total FROM jadwal_pelajaran WHERE mapel_id = :mapel_id AND guru_id = :guru_id";
            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->bindParam(":mapel_id", $mapel_id);
            $stmt->bindParam(":guru_id", $guru_id);
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($row['total'] == 0) {
                die("Anda tidak memiliki akses ke mata pelajaran ini.");
            }
        }
    }

    // Get data
    $report_data = $absensi->getReportBySemester($kelas_id, $mapel_id, $tahun_ajaran_id);
    $siswa_list = $siswa->getByKelas($kelas_id);
    
    // Get class and subject info
    $kelas->id = $kelas_id;
    $kelas->getOne();
    
    $mapel->id = $mapel_id;
    $mapel->getOne();
    
    $ta_stmt = $tahunAjaran->getById($tahun_ajaran_id);
    $ta_data = $ta_stmt->fetch(PDO::FETCH_ASSOC);

    // Prepare attendance data
    $attendance_data = [];
    if ($report_data) {
        while ($row = $report_data->fetch(PDO::FETCH_ASSOC)) {
            $attendance_data[$row['nis']] = $row;
        }
    }

    // Start generating PDF
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);
    $options->set('defaultFont', 'DejaVu Sans');

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'landscape');

    // Generate HTML content
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Rekap Absensi - ' . $mapel->nama_mapel . '</title>
        <style>
            body {
                font-family: DejaVu Sans, sans-serif;
                font-size: 12px;
                margin: 15px;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            table.data {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 11px;
            }
            table.data th, 
            table.data td {
                border: 1px solid #000;
                padding: 5px;
            }
            table.data th {
                background-color: #E2EFDA;
                font-weight: bold;
                text-align: center;
            }
            table.data td {
                text-align: left;
            }
            td.center {
                text-align: center;
            }
            td.right {
                text-align: right;
            }
            .badge {
                padding: 2px 5px;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
                color: white;
            }
            .bg-success { background-color: #28a745; }
            .bg-warning { background-color: #ffc107; color: #000; }
            .bg-info { background-color: #17a2b8; }
            .bg-danger { background-color: #dc3545; }
            .text-success { color: #28a745; }
            .text-warning { color: #856404; }
            .text-danger { color: #dc3545; }
            .fw-bold { font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>Rekap Absensi Semester</h2>
            <p>
                Kelas: ' . $kelas->nama_kelas . '<br>
                Mata Pelajaran: ' . $mapel->nama_mapel . '<br>
                Tahun Ajaran: ' . $ta_data['tahun_ajaran'] . '
            </p>
        </div>

        <table class="data">
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIS</th>
                    <th>Nama Siswa</th>
                    <th>Kelas</th>
                    <th>Total Pertemuan</th>
                    <th>Hadir</th>
                    <th>Sakit</th>
                    <th>Izin</th>
                    <th>Alpha</th>
                    <th>Persentase Kehadiran</th>
                </tr>
            </thead>
            <tbody>';

    if ($siswa_list->rowCount() > 0) {
        $no = 1;
        while ($siswa = $siswa_list->fetch(PDO::FETCH_ASSOC)) {
            $attendance = isset($attendance_data[$siswa['nis']]) ? $attendance_data[$siswa['nis']] : [
                'total_pertemuan' => 0,
                'hadir' => 0,
                'sakit' => 0,
                'izin' => 0,
                'alpha' => 0
            ];
            
            $persentase = $attendance['total_pertemuan'] > 0 ? 
                ($attendance['hadir'] * 100.0) / $attendance['total_pertemuan'] : 0;
            
            $percent_class = '';
            if ($persentase >= 90) {
                $percent_class = 'text-success';
            } elseif ($persentase >= 75) {
                $percent_class = 'text-warning';
            } else {
                $percent_class = 'text-danger';
            }

            $html .= '
                <tr>
                    <td class="center">' . $no++ . '</td>
                    <td class="center">' . htmlspecialchars($siswa['nis']) . '</td>
                    <td>' . htmlspecialchars($siswa['nama_siswa']) . '</td>
                    <td class="center">' . htmlspecialchars($siswa['nama_kelas']) . '</td>
                    <td class="center">' . $attendance['total_pertemuan'] . '</td>
                    <td class="center"><span class="badge bg-success">' . $attendance['hadir'] . '</span></td>
                    <td class="center"><span class="badge bg-warning">' . $attendance['sakit'] . '</span></td>
                    <td class="center"><span class="badge bg-info">' . $attendance['izin'] . '</span></td>
                    <td class="center"><span class="badge bg-danger">' . $attendance['alpha'] . '</span></td>
                    <td class="center ' . $percent_class . ' fw-bold">' . number_format($persentase, 2) . '%</td>
                </tr>';
        }
    } else {
        $html .= '
            <tr>
                <td colspan="10" class="center">Tidak ada data siswa di kelas ini</td>
            </tr>';
    }

    $html .= '
            </tbody>
        </table>

        <div style="margin-top: 20px;">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%; vertical-align: top;">
                        <strong>Keterangan:</strong><br>
                        H = Hadir<br>
                        S = Sakit<br>
                        I = Izin<br>
                        A = Alpha
                    </td>
                    <td style="width: 50%; text-align: right;">
                        <p>Dicetak pada: ' . date('d/m/Y H:i:s') . '</p>
                    </td>
                </tr>
            </table>
        </div>
    </body>
    </html>';

    $dompdf->loadHtml($html);
    $dompdf->render();

    // Output the generated PDF
    $dompdf->stream(
        'Rekap_Absensi_' . $kelas->nama_kelas . '_' . $mapel->nama_mapel . '_' . $ta_data['tahun_ajaran'] . '.pdf',
        array('Attachment' => true)
    );

    exit(0);

} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file PDF. Silakan coba lagi. Error: " . $e->getMessage());
}
