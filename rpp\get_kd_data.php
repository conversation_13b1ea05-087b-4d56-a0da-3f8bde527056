<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/Guru.php';

header('Content-Type: application/json');

try {
    // Get teacher ID from logged in user
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        throw new Exception("Data guru tidak ditemukan");
    }

    // Validate required parameters
    if (!isset($_POST['mapel_id']) || !isset($_POST['kelas_id'])) {
        throw new Exception("Parameter mapel_id dan kelas_id diperlukan");
    }

    $mapel_id = $_POST['mapel_id'];
    $kelas_id = $_POST['kelas_id'];
    $semester = $_POST['semester'] ?? null;
    $tahun_ajaran = $_POST['tahun_ajaran'] ?? null;

    // Initialize KD model
    $kd = new KompetensiDasar();

    // Get KD options for tema/subtema and materi pokok dropdowns
    $kd_options = $kd->getKdOptionsForRpp($mapel_id, $kelas_id, $guru_id, $semester, $tahun_ajaran);

    if (empty($kd_options)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tidak ada data Kompetensi Dasar ditemukan untuk mata pelajaran dan kelas yang dipilih',
            'data' => [
                'tema_subtema_options' => [],
                'materi_pokok_options' => []
            ],
            'debug' => [
                'mapel_id' => $mapel_id,
                'kelas_id' => $kelas_id,
                'guru_id' => $guru_id,
                'semester' => $semester,
                'tahun_ajaran' => $tahun_ajaran
            ]
        ]);
        exit;
    }

    // Extract unique tema/subtema and materi pokok options
    $tema_subtema_options = [];
    $materi_pokok_options = [];

    foreach ($kd_options as $option) {
        // Collect tema/subtema options
        if (!empty($option['tema_subtema'])) {
            $tema_key = $option['tema_subtema'];
            if (!isset($tema_subtema_options[$tema_key])) {
                $tema_subtema_options[$tema_key] = [
                    'value' => $option['tema_subtema'],
                    'text' => $option['tema_subtema']
                ];
            }
        }

        // Collect materi pokok options
        if (!empty($option['materi_pokok'])) {
            $materi_key = $option['materi_pokok'];
            if (!isset($materi_pokok_options[$materi_key])) {
                $materi_pokok_options[$materi_key] = [
                    'value' => $option['materi_pokok'],
                    'text' => $option['materi_pokok']
                ];
            }
        }
    }

    echo json_encode([
        'success' => true,
        'message' => 'Data tema/subtema dan materi pokok berhasil dimuat',
        'data' => [
            'tema_subtema_options' => array_values($tema_subtema_options),
            'materi_pokok_options' => array_values($materi_pokok_options),
            'total_kd_records' => count($kd_options)
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
