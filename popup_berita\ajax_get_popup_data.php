<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $popupBerita = new PopupBerita();
    $popupData = $popupBerita->getPopupData();

    if ($popupData) {
        echo json_encode([
            'success' => true,
            'data' => $popupData
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Popup tidak aktif atau tidak ada data'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>: ' . $e->getMessage()
    ]);
}
?>
