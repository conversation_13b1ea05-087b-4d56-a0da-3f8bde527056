<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

$perpustakaan = new Perpustakaan();

// Ambil semua peminjaman yang masih berstatus dipinjam menggunakan method dari class Perpustakaan
$peminjaman = $perpustakaan->getAllPeminjaman();

// Proses pengembalian jika ada
if (isset($_POST['action'])) {
    $id = $_POST['id_peminjaman'];
    try {
        // Ambil data peminjaman
        $pinjam = $perpustakaan->getPeminjamanById($id);
        if (!$pinjam) {
            throw new Exception("Data peminjaman tidak ditemukan.");
        }

        if ($pinjam['status'] == 'kembali') {
            throw new Exception("Buku sudah dikembalikan sebelumnya.");
        }

        // Ambil data buku
        $buku = $perpustakaan->getBukuById($pinjam['id_buku']);
        if (!$buku) {
            throw new Exception("Data buku tidak ditemukan.");
        }

        if ($_POST['action'] == 'return') {
            $jumlah_kembali = (int)$_POST['jumlah_kembali'];
            
            if ($jumlah_kembali <= 0 || $jumlah_kembali > $pinjam['jumlah_buku']) {
                throw new Exception("Jumlah pengembalian tidak valid.");
            }

            // Update jumlah buku yang masih dipinjam
            $sisa_pinjam = $pinjam['jumlah_buku'] - $jumlah_kembali;
            
            if ($sisa_pinjam > 0) {
                // Update jumlah buku yang dipinjam
                if (!$perpustakaan->updateJumlahPeminjaman($id, $sisa_pinjam)) {
                    throw new Exception("Gagal mengupdate jumlah peminjaman.");
                }
            } else {
                // Jika semua buku dikembalikan, update status menjadi 'kembali'
                if (!$perpustakaan->updateStatusPeminjaman($id, 'kembali')) {
                    throw new Exception("Gagal mengupdate status peminjaman.");
                }
            }

            // Catat riwayat pengembalian
            if (!$perpustakaan->tambahRiwayatPengembalian($id, $jumlah_kembali)) {
                throw new Exception("Gagal mencatat riwayat pengembalian.");
            }

            // Update stok buku - tambahkan kembali sejumlah buku yang dikembalikan
            $update_stok = [
                'judul_buku' => $buku['judul_buku'],
                'id_kategori' => $buku['id_kategori'],
                'pengarang' => $buku['pengarang'],
                'penerbit' => $buku['penerbit'],
                'tahun_terbit' => $buku['tahun_terbit'],
                'isbn' => $buku['isbn'],
                'jumlah_buku' => $buku['jumlah_buku'] + $jumlah_kembali,
                'lokasi' => $buku['lokasi']
            ];

            if (!$perpustakaan->editBuku($buku['id_buku'], $update_stok)) {
                throw new Exception("Gagal mengupdate stok buku.");
            }

            $_SESSION['success'] = "Buku berhasil dikembalikan";
        } elseif ($_POST['action'] == 'extend') {
            $tanggal_kembali_baru = $_POST['tanggal_kembali_baru'];
            
            if (strtotime($tanggal_kembali_baru) <= strtotime($pinjam['tanggal_kembali'])) {
                throw new Exception("Tanggal perpanjangan harus lebih besar dari tanggal kembali saat ini.");
            }

            if (!$perpustakaan->perpanjangPeminjaman($id, $tanggal_kembali_baru)) {
                throw new Exception("Gagal memperpanjang peminjaman.");
            }

            $_SESSION['success'] = "Peminjaman berhasil diperpanjang";
        }

        header("Location: pengembalian.php");
        exit();
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
        header("Location: pengembalian.php");
        exit();
    }
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Pengembalian Buku</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Pengembalian</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Daftar Peminjaman</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="tablePengembalian" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Peminjam</th>
                                    <th>Buku</th>
                                    <th>Jumlah</th>
                                    <th>Tanggal Pinjam</th>
                                    <th>Tanggal Kembali</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                foreach ($peminjaman as $p) :
                                ?>
                                    <tr>
                                        <td><?= $no++; ?></td>
                                        <td><?= htmlspecialchars($p['nama_peminjam']); ?></td>
                                        <td><?= htmlspecialchars($p['judul_buku']); ?></td>
                                        <td><?= $p['jumlah_buku']; ?></td>
                                        <td><?= date('d/m/Y', strtotime($p['tanggal_pinjam'])); ?></td>
                                        <td><?= date('d/m/Y', strtotime($p['tanggal_kembali'])); ?></td>
                                        <td>
                                            <?php if ($p['status'] == 'dipinjam') : ?>
                                                <span class="badge bg-warning">Dipinjam</span>
                                            <?php else : ?>
                                                <span class="badge bg-success">Dikembalikan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($p['status'] == 'dipinjam') : ?>
                                                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#modalReturn<?= $p['id_peminjaman']; ?>">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                                <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#modalExtend<?= $p['id_peminjaman']; ?>">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>

                                    <!-- Modal Pengembalian -->
                                    <div class="modal fade" id="modalReturn<?= $p['id_peminjaman']; ?>" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Pengembalian Buku</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <form action="" method="POST">
                                                    <div class="modal-body">
                                                        <input type="hidden" name="id_peminjaman" value="<?= $p['id_peminjaman']; ?>">
                                                        <input type="hidden" name="action" value="return">
                                                        <div class="mb-3">
                                                            <label for="jumlah_kembali" class="form-label">Jumlah Buku yang Dikembalikan</label>
                                                            <input type="number" class="form-control" id="jumlah_kembali" name="jumlah_kembali" 
                                                                   min="1" max="<?= $p['jumlah_buku']; ?>" value="<?= $p['jumlah_buku']; ?>" required>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                        <button type="submit" class="btn btn-primary">Kembalikan</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Perpanjangan -->
                                    <div class="modal fade" id="modalExtend<?= $p['id_peminjaman']; ?>" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Perpanjang Peminjaman</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <form action="" method="POST">
                                                    <div class="modal-body">
                                                        <input type="hidden" name="id_peminjaman" value="<?= $p['id_peminjaman']; ?>">
                                                        <input type="hidden" name="action" value="extend">
                                                        <div class="mb-3">
                                                            <label for="tanggal_kembali_baru" class="form-label">Tanggal Kembali Baru</label>
                                                            <input type="date" class="form-control" id="tanggal_kembali_baru" name="tanggal_kembali_baru" 
                                                                   min="<?= date('Y-m-d', strtotime('+1 day', strtotime($p['tanggal_kembali']))); ?>" required>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                        <button type="submit" class="btn btn-primary">Perpanjang</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tablePengembalian').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data peminjaman"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[4, "desc"]], // Urutkan berdasarkan tanggal pinjam
        "columnDefs": [
            {"orderable": false, "targets": 7} // Kolom aksi tidak bisa diurutkan
        ]
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
