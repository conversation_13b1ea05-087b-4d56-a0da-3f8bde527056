<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/Guru.php';

header('Content-Type: application/json');

try {
    // Get teacher ID from logged in user
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        throw new Exception("Data guru tidak ditemukan");
    }

    // Validate required parameters
    if (!isset($_POST['mapel_id']) || !isset($_POST['kelas_id'])) {
        throw new Exception("Parameter mapel_id dan kelas_id diperlukan");
    }

    $mapel_id = $_POST['mapel_id'];
    $kelas_id = $_POST['kelas_id'];
    $tema_subtema = $_POST['tema_subtema'] ?? null;
    $materi_pokok = $_POST['materi_pokok'] ?? null;
    $semester = $_POST['semester'] ?? null;
    $tahun_ajaran = $_POST['tahun_ajaran'] ?? null;

    // Initialize KD model
    $kd = new KompetensiDasar();

    // Get KD data based on tema/subtema and materi pokok
    $kd_records = $kd->getKdByTemaMateri($mapel_id, $kelas_id, $guru_id, $tema_subtema, $materi_pokok, $semester, $tahun_ajaran);

    if (empty($kd_records)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tidak ada Kompetensi Dasar ditemukan untuk tema/subtema dan materi pokok yang dipilih',
            'data' => null
        ]);
        exit;
    }

    // Format kompetensi dasar text
    $formatted_kd = '';
    foreach ($kd_records as $record) {
        $formatted_kd .= $record['kode_kd'] . ' ' . $record['deskripsi_kd'] . "\n";
    }

    echo json_encode([
        'success' => true,
        'message' => count($kd_records) . ' Kompetensi Dasar ditemukan dan berhasil dimuat',
        'data' => [
            'formatted_kd' => trim($formatted_kd),
            'kd_records' => $kd_records,
            'count' => count($kd_records)
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => null
    ]);
}
?>
