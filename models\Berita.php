<?php
if (!defined('ABSEN_PATH')) {
    define('ABSEN_PATH', dirname(dirname(__FILE__)));
}
require_once ABSEN_PATH . '/config/database.php';
require_once ABSEN_PATH . '/vendor/ezyang/htmlpurifier/library/HTMLPurifier.auto.php';

class Berita {
    private $conn;
    private $table_name = "berita";

    public $id;
    public $judul;
    public $isi;
    public $thumbnail;
    public $created_by;
    public $created_at;
    public $updated_at;
    public $nama_pembuat;

    public function __construct() {
        $db = new database();
        $this->conn = $db->getConnection();
    }

    public function getAll($limit = null) {
        $query = "SELECT b.*, u.nama_lengkap as nama_pembuat 
                FROM " . $this->table_name . " b
                LEFT JOIN users u ON b.created_by = u.id
                ORDER BY COALESCE(b.last_comment_at, b.created_at) DESC";
        
        if ($limit) {
            $query .= " LIMIT :limit";
        }

        $stmt = $this->conn->prepare($query);
        
        if ($limit) {
            $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getOne() {
        $query = "SELECT b.*, u.nama_lengkap as nama_pembuat 
                FROM " . $this->table_name . " b
                LEFT JOIN users u ON b.created_by = u.id
                WHERE b.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->judul = $row['judul'];
            $this->isi = $row['isi']; 
            $this->thumbnail = $row['thumbnail'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            $this->nama_pembuat = $row['nama_pembuat'];
            return true;
        }
        return false;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                (judul, isi, thumbnail, created_by)
                VALUES
                (:judul, :isi, :thumbnail, :created_by)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->isi = $this->sanitizeHtml($this->isi);
        $this->thumbnail = htmlspecialchars(strip_tags($this->thumbnail));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));

        // Bind parameters
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":isi", $this->isi);
        $stmt->bindParam(":thumbnail", $this->thumbnail);
        $stmt->bindParam(":created_by", $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET judul = :judul,
                    isi = :isi,
                    thumbnail = :thumbnail
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->isi = $this->sanitizeHtml($this->isi);
        $this->thumbnail = htmlspecialchars(strip_tags($this->thumbnail));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":isi", $this->isi);
        $stmt->bindParam(":thumbnail", $this->thumbnail);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getCount($where = "", $params = []) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " b " . $where;
        $stmt = $this->conn->prepare($query);
        
        // Bind additional parameters if any
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAllPaginated($offset, $limit, $where = "", $params = []) {
        $query = "SELECT b.*, u.nama_lengkap as nama_pembuat 
                FROM " . $this->table_name . " b
                LEFT JOIN users u ON b.created_by = u.id
                " . $where . "
                ORDER BY COALESCE(b.last_comment_at, b.created_at) DESC
                LIMIT :offset, :limit";

        $stmt = $this->conn->prepare($query);
        
        // Bind additional parameters if any
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt;
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        
        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function isNew() {
        $three_days_ago = date('Y-m-d H:i:s', strtotime('-3 days'));
        return $this->created_at >= $three_days_ago;
    }

    private function sanitizeHtml($html) {
        // Use a more permissive approach for FontAwesome and Bootstrap classes
        // while still maintaining security

        // First, let's use a custom approach that preserves FontAwesome classes
        $allowedTags = array(
            'p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'blockquote', 'a', 'img', 'i', 'span', 'div'
        );

        $allowedAttributes = array(
            'a' => array('href', 'class'),
            'img' => array('src', 'alt', 'class'),
            'i' => array('class'),
            'span' => array('class'),
            'div' => array('class'),
            'li' => array('class'),
            'ul' => array('class'),
            'ol' => array('class'),
            'p' => array('class')
        );

        // Use strip_tags with allowed tags, then manually preserve class attributes
        $allowedTagsString = '<' . implode('><', $allowedTags) . '>';
        $cleanHtml = strip_tags($html, $allowedTagsString);

        // Additional security: remove potentially dangerous attributes but keep class
        $cleanHtml = preg_replace('/\s(on\w+|javascript:|vbscript:|data:)[\s\S]*?(?=\s|>|$)/i', '', $cleanHtml);

        return $cleanHtml;
    }
}
