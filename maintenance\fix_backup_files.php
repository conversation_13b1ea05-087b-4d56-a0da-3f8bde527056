<?php
/**
 * Fix Existing Backup Files
 * This script fixes existing backup files that have view handling issues
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';

// Only admin can access this tool
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

class BackupFixer {
    private $backup_dir;
    private $fixed_count = 0;
    private $errors = [];
    
    public function __construct() {
        $this->backup_dir = __DIR__ . '/../database/backups/';
    }
    
    public function fixAllBackupFiles() {
        $backup_files = glob($this->backup_dir . '*.sql');
        
        foreach ($backup_files as $file_path) {
            $this->fixBackupFile($file_path);
        }
        
        return [
            'fixed_count' => $this->fixed_count,
            'errors' => $this->errors
        ];
    }
    
    public function fixBackupFile($file_path) {
        $filename = basename($file_path);
        
        if (!file_exists($file_path) || !is_readable($file_path)) {
            $this->errors[] = "Cannot read file: {$filename}";
            return false;
        }
        
        $content = file_get_contents($file_path);
        if ($content === false) {
            $this->errors[] = "Failed to read content: {$filename}";
            return false;
        }
        
        $original_content = $content;
        $changes_made = false;
        
        // Fix 1: Replace DROP TABLE with DROP VIEW for views
        $view_names = ['v_siswa_all_periods', 'v_siswa_current'];
        foreach ($view_names as $view_name) {
            $pattern = "/DROP TABLE IF EXISTS `{$view_name}`;/";
            $replacement = "DROP VIEW IF EXISTS `{$view_name}`;";
            
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, $replacement, $content);
                $changes_made = true;
            }
        }
        
        // Fix 2: Remove INSERT statements for views
        foreach ($view_names as $view_name) {
            $pattern = "/INSERT INTO `{$view_name}` VALUES \([^;]+\);\n?/";
            
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, "", $content);
                $changes_made = true;
            }
        }
        
        // Fix 3: Ensure proper foreign key settings
        if (!preg_match('/SET FOREIGN_KEY_CHECKS\s*=\s*0/', $content)) {
            $header_pattern = "/(-- PHP MySQL Backup\n-- Generated: [^\n]+\n\n)/";
            if (preg_match($header_pattern, $content)) {
                $content = preg_replace($header_pattern, "$1SET FOREIGN_KEY_CHECKS=0;\n\n", $content);
                $changes_made = true;
            }
        }
        
        if (!preg_match('/SET FOREIGN_KEY_CHECKS\s*=\s*1/', $content)) {
            $content .= "\nSET FOREIGN_KEY_CHECKS=1;\n";
            $changes_made = true;
        }
        
        // Save the fixed file if changes were made
        if ($changes_made) {
            // Create backup of original file
            $backup_original = $file_path . '.original.' . date('Y-m-d_H-i-s');
            if (!copy($file_path, $backup_original)) {
                $this->errors[] = "Failed to create backup of original file: {$filename}";
                return false;
            }
            
            // Write the fixed content
            if (file_put_contents($file_path, $content) === false) {
                $this->errors[] = "Failed to write fixed content: {$filename}";
                return false;
            }
            
            $this->fixed_count++;
            return true;
        }
        
        return false; // No changes needed
    }
    
    public function getFixedCount() {
        return $this->fixed_count;
    }
    
    public function getErrors() {
        return $this->errors;
    }
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_all'])) {
        $fixer = new BackupFixer();
        $result = $fixer->fixAllBackupFiles();
        
        if (empty($result['errors'])) {
            $message = "Proses perbaikan selesai! {$result['fixed_count']} file backup telah diperbaiki.";
        } else {
            $error = "Beberapa error terjadi:\n" . implode("\n", $result['errors']);
            if ($result['fixed_count'] > 0) {
                $message = "{$result['fixed_count']} file berhasil diperbaiki, namun ada beberapa error.";
            }
        }
    } elseif (isset($_POST['fix_single'])) {
        $filename = basename($_POST['fix_single']);
        $file_path = __DIR__ . '/../database/backups/' . $filename;
        
        $fixer = new BackupFixer();
        if ($fixer->fixBackupFile($file_path)) {
            $message = "File {$filename} berhasil diperbaiki!";
        } else {
            $errors = $fixer->getErrors();
            if (empty($errors)) {
                $message = "File {$filename} sudah dalam kondisi baik, tidak perlu perbaikan.";
            } else {
                $error = "Gagal memperbaiki file {$filename}: " . implode(", ", $errors);
            }
        }
    }
}

require_once __DIR__ . '/../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Perbaiki File Backup</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Peringatan</h6>
                        <p>Tool ini akan memperbaiki file backup yang memiliki masalah dengan view handling. 
                        File asli akan di-backup dengan ekstensi .original sebelum diperbaiki.</p>
                        <p><strong>Perbaikan yang dilakukan:</strong></p>
                        <ul>
                            <li>Mengubah "DROP TABLE IF EXISTS" menjadi "DROP VIEW IF EXISTS" untuk views</li>
                            <li>Menghapus INSERT statements untuk views</li>
                            <li>Menambahkan pengaturan FOREIGN_KEY_CHECKS yang proper</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <form method="post" onsubmit="return confirm('Apakah Anda yakin ingin memperbaiki semua file backup? File asli akan di-backup terlebih dahulu.')">
                            <button type="submit" name="fix_all" class="btn btn-warning">
                                <i class="fas fa-tools"></i> Perbaiki Semua File Backup
                            </button>
                        </form>
                    </div>

                    <h6>File Backup yang Tersedia</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>File Backup</th>
                                    <th>Ukuran</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $backup_dir = __DIR__ . '/../database/backups/';
                                $backup_files = glob($backup_dir . '*.sql');
                                
                                // Filter out .original files
                                $backup_files = array_filter($backup_files, function($file) {
                                    return !preg_match('/\.original\.\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/', $file);
                                });
                                
                                if (!empty($backup_files)) {
                                    rsort($backup_files); // Sort from newest
                                    foreach ($backup_files as $file) {
                                        $filename = basename($file);
                                        $size = filesize($file);
                                        $created = date("d-m-Y H:i:s", filemtime($file));
                                        
                                        echo "<tr>";
                                        echo "<td>{$filename}</td>";
                                        echo "<td>" . number_format($size / 1024, 2) . " KB</td>";
                                        echo "<td>{$created}</td>";
                                        echo "<td>";
                                        
                                        // Single file fix button
                                        echo "<form method='post' style='display: inline;' ";
                                        echo "onsubmit='return confirm(\"Perbaiki file {$filename}? File asli akan di-backup terlebih dahulu.\")'>";
                                        echo "<input type='hidden' name='fix_single' value='" . htmlspecialchars($filename, ENT_QUOTES) . "'>";
                                        echo "<button type='submit' class='btn btn-sm btn-warning me-2'>";
                                        echo "<i class='fas fa-tools'></i> Perbaiki</button>";
                                        echo "</form>";
                                        
                                        // Validate button (link to validation tool)
                                        echo "<a href='validate_backup.php' class='btn btn-sm btn-info'>";
                                        echo "<i class='fas fa-check-circle'></i> Validasi</a>";
                                        
                                        echo "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='4' class='text-center'>Tidak ada file backup tersedia.</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <h6>Informasi Tambahan</h6>
                        <div class="alert alert-info">
                            <ul class="mb-0">
                                <li><strong>File .original:</strong> File backup asli sebelum diperbaiki</li>
                                <li><strong>Validasi:</strong> Gunakan tool validasi untuk memeriksa file sebelum restore</li>
                                <li><strong>Restore:</strong> Setelah diperbaiki, file dapat di-restore dengan aman</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
