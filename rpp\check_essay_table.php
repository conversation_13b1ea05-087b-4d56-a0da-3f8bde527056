<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if essay_answers table exists
    $check_table = $conn->prepare("SHOW TABLES LIKE 'essay_answers'");
    $check_table->execute();
    
    if ($check_table->rowCount() == 0) {
        // Table doesn't exist, create it
        $create_table_sql = "
        CREATE TABLE `essay_answers` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `question_id` int(11) NOT NULL,
          `question_type` enum('rpp_question','multi_rpp_question') NOT NULL DEFAULT 'rpp_question',
          `expected_answer` text NOT NULL,
          `answer_points` text DEFAULT NULL,
          `scoring_rubric` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
          `generation_metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_question_answer` (`question_id`, `question_type`),
          KEY `question_id` (`question_id`),
          KEY `question_type` (`question_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $conn->exec($create_table_sql);
        
        echo json_encode([
            'success' => true,
            'message' => 'Table essay_answers berhasil dibuat',
            'action' => 'created'
        ]);
    } else {
        // Table exists, check structure
        $describe = $conn->prepare("DESCRIBE essay_answers");
        $describe->execute();
        $columns = $describe->fetchAll(PDO::FETCH_ASSOC);
        
        $column_names = array_column($columns, 'Field');
        $required_columns = ['id', 'question_id', 'question_type', 'expected_answer', 'answer_points', 'scoring_rubric', 'generation_metadata', 'created_at', 'updated_at'];
        
        $missing_columns = array_diff($required_columns, $column_names);
        
        if (empty($missing_columns)) {
            echo json_encode([
                'success' => true,
                'message' => 'Table essay_answers sudah ada dan struktur lengkap',
                'action' => 'exists',
                'columns' => $column_names
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Table essay_answers ada tapi struktur tidak lengkap',
                'action' => 'incomplete',
                'missing_columns' => $missing_columns,
                'existing_columns' => $column_names
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'action' => 'error'
    ]);
}
?>
