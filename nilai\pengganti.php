<?php

require_once '../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Nilai.php';
require_once '../models/NilaiPengganti.php';
require_once '../models/TugasTambahan.php';
require_once '../models/Siswa.php';
require_once '../models/User.php';
require_once '../models/JadwalPelajaran.php';
require_once '../template/header.php';

if(!isset($_GET['nilai_id']) || !isset($_GET['siswa_id']) || !isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    header("Location: index.php");
    exit();
}

$nilai_id = $_GET['nilai_id'];
$siswa_id = $_GET['siswa_id'];
$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Check if user has access to this subject
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if ($guru_id) {
        $jadwal = new JadwalPelajaran();
        $has_access = $jadwal->checkGuruMapelAccess($guru_id, $mapel_id);

        if (!$has_access) {
            header("Location: ../403.php");
            exit();
        }
    } else {
        header("Location: ../403.php");
        exit();
    }
}

// Get nilai details
$nilai = new Nilai();
$nilai->id = $nilai_id;
if (!$nilai->getOne() || $nilai->siswa_id != $siswa_id || $nilai->mapel_id != $mapel_id) {
    $_SESSION['error'] = "Data nilai tidak ditemukan!";
    header("Location: view.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
    exit();
}

// Get student details
$siswa = new Siswa();
$siswa->id = $siswa_id;
$siswa->getOne();

// Get subject details
$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

// Get available additional assignments
$nilaiPengganti = new NilaiPengganti();
$available_tugas = $nilaiPengganti->getAvailableTugasTambahan($siswa_id, $mapel_id, $semester, $tahun_ajaran);

// Get current replacements
$current_replacements = $nilaiPengganti->getByNilaiId($nilai_id);
$replacements = [];
while ($row = $current_replacements->fetch(PDO::FETCH_ASSOC)) {
    $replacements[$row['jenis_nilai']] = $row;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $success = true;
    $error_message = '';

    // Handle tugas replacement
    if (isset($_POST['tugas_action'])) {
        if ($_POST['tugas_action'] == 'replace') {
            // Check if using average or single replacement
            if (isset($_POST['tugas_replacement_type']) && $_POST['tugas_replacement_type'] == 'average') {
                // Check if there are enough tugas tambahan
                $avg_result = $nilaiPengganti->calculateAverageNilai(
                    $siswa_id,
                    $mapel_id,
                    $semester,
                    $tahun_ajaran
                );

                if ($avg_result['jumlah_tugas'] < 2) {
                    $success = false;
                    $error_message = "Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata!";
                } else {
                    // Use average of all tugas tambahan
                    if (!$nilaiPengganti->createAverageReplacement(
                        $nilai_id,
                        'nilai_tugas',
                        $siswa_id,
                        $mapel_id,
                        $semester,
                        $tahun_ajaran
                    )) {
                        $success = false;
                        $error_message = "Gagal mengganti nilai tugas dengan nilai rata-rata!";
                    }
                }
            } elseif (isset($_POST['tugas_tambahan_id']) && !empty($_POST['tugas_tambahan_id'])) {
                // Use single tugas tambahan
                $nilaiPengganti = new NilaiPengganti();
                $nilaiPengganti->nilai_id = $nilai_id;
                $nilaiPengganti->tugas_tambahan_siswa_id = $_POST['tugas_tambahan_id'];
                $nilaiPengganti->jenis_nilai = 'nilai_tugas';
                $nilaiPengganti->is_average = 0;

                if (!$nilaiPengganti->create()) {
                    $success = false;
                    $error_message = "Gagal mengganti nilai tugas! ID tugas tambahan: " . $_POST['tugas_tambahan_id'];
                }
            } else {
                $success = false;
                $error_message = "Pilih tugas tambahan atau gunakan nilai rata-rata!";
            }
        } elseif ($_POST['tugas_action'] == 'remove') {
            if (!$nilaiPengganti->delete($nilai_id, 'nilai_tugas')) {
                $success = false;
                $error_message = "Gagal menghapus penggantian nilai tugas!";
            }
        }
    }

    // Handle UTS replacement
    if (isset($_POST['uts_action'])) {
        if ($_POST['uts_action'] == 'replace') {
            // Check if using average or single replacement
            if (isset($_POST['uts_replacement_type']) && $_POST['uts_replacement_type'] == 'average') {
                // Check if there are enough tugas tambahan
                $avg_result = $nilaiPengganti->calculateAverageNilai(
                    $siswa_id,
                    $mapel_id,
                    $semester,
                    $tahun_ajaran
                );

                if ($avg_result['jumlah_tugas'] < 2) {
                    $success = false;
                    $error_message = "Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata!";
                } else {
                    // Use average of all tugas tambahan
                    if (!$nilaiPengganti->createAverageReplacement(
                        $nilai_id,
                        'nilai_uts',
                        $siswa_id,
                        $mapel_id,
                        $semester,
                        $tahun_ajaran
                    )) {
                        $success = false;
                        $error_message = "Gagal mengganti nilai UTS dengan nilai rata-rata!";
                    }
                }
            } elseif (isset($_POST['uts_tambahan_id']) && !empty($_POST['uts_tambahan_id'])) {
                // Use single tugas tambahan
                $nilaiPengganti = new NilaiPengganti();
                $nilaiPengganti->nilai_id = $nilai_id;
                $nilaiPengganti->tugas_tambahan_siswa_id = $_POST['uts_tambahan_id'];
                $nilaiPengganti->jenis_nilai = 'nilai_uts';
                $nilaiPengganti->is_average = 0;

                if (!$nilaiPengganti->create()) {
                    $success = false;
                    $error_message = "Gagal mengganti nilai UTS! ID tugas tambahan: " . $_POST['uts_tambahan_id'];
                }
            } else {
                $success = false;
                $error_message = "Pilih tugas tambahan atau gunakan nilai rata-rata!";
            }
        } elseif ($_POST['uts_action'] == 'remove') {
            if (!$nilaiPengganti->delete($nilai_id, 'nilai_uts')) {
                $success = false;
                $error_message = "Gagal menghapus penggantian nilai UTS!";
            }
        }
    }

    // Handle UAS replacement
    if (isset($_POST['uas_action'])) {
        if ($_POST['uas_action'] == 'replace') {
            // Check if using average or single replacement
            if (isset($_POST['uas_replacement_type']) && $_POST['uas_replacement_type'] == 'average') {
                // Check if there are enough tugas tambahan
                $avg_result = $nilaiPengganti->calculateAverageNilai(
                    $siswa_id,
                    $mapel_id,
                    $semester,
                    $tahun_ajaran
                );

                if ($avg_result['jumlah_tugas'] < 2) {
                    $success = false;
                    $error_message = "Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata!";
                } else {
                    // Use average of all tugas tambahan
                    if (!$nilaiPengganti->createAverageReplacement(
                        $nilai_id,
                        'nilai_uas',
                        $siswa_id,
                        $mapel_id,
                        $semester,
                        $tahun_ajaran
                    )) {
                        $success = false;
                        $error_message = "Gagal mengganti nilai UAS dengan nilai rata-rata!";
                    }
                }
            } elseif (isset($_POST['uas_tambahan_id']) && !empty($_POST['uas_tambahan_id'])) {
                // Use single tugas tambahan
                $nilaiPengganti = new NilaiPengganti();
                $nilaiPengganti->nilai_id = $nilai_id;
                $nilaiPengganti->tugas_tambahan_siswa_id = $_POST['uas_tambahan_id'];
                $nilaiPengganti->jenis_nilai = 'nilai_uas';
                $nilaiPengganti->is_average = 0;

                if (!$nilaiPengganti->create()) {
                    $success = false;
                    $error_message = "Gagal mengganti nilai UAS! ID tugas tambahan: " . $_POST['uas_tambahan_id'];
                }
            } else {
                $success = false;
                $error_message = "Pilih tugas tambahan atau gunakan nilai rata-rata!";
            }
        } elseif ($_POST['uas_action'] == 'remove') {
            if (!$nilaiPengganti->delete($nilai_id, 'nilai_uas')) {
                $success = false;
                $error_message = "Gagal menghapus penggantian nilai UAS!";
            }
        }
    }

    // Handle Absen replacement
    if (isset($_POST['absen_action'])) {
        if ($_POST['absen_action'] == 'replace') {
            // Check if using average or single replacement
            if (isset($_POST['absen_replacement_type']) && $_POST['absen_replacement_type'] == 'average') {
                // Check if there are enough tugas tambahan
                $avg_result = $nilaiPengganti->calculateAverageNilai(
                    $siswa_id,
                    $mapel_id,
                    $semester,
                    $tahun_ajaran
                );

                if ($avg_result['jumlah_tugas'] < 2) {
                    $success = false;
                    $error_message = "Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata!";
                } else {
                    // Use average of all tugas tambahan
                    if (!$nilaiPengganti->createAverageReplacement(
                        $nilai_id,
                        'nilai_absen',
                        $siswa_id,
                        $mapel_id,
                        $semester,
                        $tahun_ajaran
                    )) {
                        $success = false;
                        $error_message = "Gagal mengganti nilai absen dengan nilai rata-rata!";
                    }
                }
            } elseif (isset($_POST['absen_tambahan_id']) && !empty($_POST['absen_tambahan_id'])) {
                // Use single tugas tambahan
                $nilaiPengganti = new NilaiPengganti();
                $nilaiPengganti->nilai_id = $nilai_id;
                $nilaiPengganti->tugas_tambahan_siswa_id = $_POST['absen_tambahan_id'];
                $nilaiPengganti->jenis_nilai = 'nilai_absen';
                $nilaiPengganti->is_average = 0;

                if (!$nilaiPengganti->create()) {
                    $success = false;
                    $error_message = "Gagal mengganti nilai absen! ID tugas tambahan: " . $_POST['absen_tambahan_id'];
                }
            } else {
                $success = false;
                $error_message = "Pilih tugas tambahan atau gunakan nilai rata-rata!";
            }
        } elseif ($_POST['absen_action'] == 'remove') {
            if (!$nilaiPengganti->delete($nilai_id, 'nilai_absen')) {
                $success = false;
                $error_message = "Gagal menghapus penggantian nilai absen!";
            }
        }
    }

    if ($success) {
        // Recalculate final grade
        $nilai->getOne(); // Refresh nilai data
        $nilai_aktual = $nilai->getNilaiAktual();
        $nilai_akhir = $nilai->hitungNilaiAkhir();

        // Update nilai_akhir in database
        $nilai->nilai_akhir = $nilai_akhir;

        if ($nilai->update()) {
            $_SESSION['success'] = "Penggantian nilai berhasil disimpan!";
        } else {
            $_SESSION['error'] = "Penggantian nilai berhasil tetapi gagal memperbarui nilai akhir!";
        }

        header("Location: pengganti.php?nilai_id=$nilai_id&siswa_id=$siswa_id&mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
        exit();
    } else {
        $_SESSION['error'] = $error_message;
    }
}

// Refresh data after form submission
$nilai->getOne();
$nilai_aktual = $nilai->getNilaiAktual();
$current_replacements = $nilaiPengganti->getByNilaiId($nilai_id);
$replacements = [];
while ($row = $current_replacements->fetch(PDO::FETCH_ASSOC)) {
    $replacements[$row['jenis_nilai']] = $row;
}
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Penggantian Nilai</h1>

    <?php if(isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Penggantian Nilai <?php echo $mapel->nama_mapel; ?></h6>
                    <a href="view.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5>Informasi Siswa</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Nama Siswa</th>
                                        <td><?php echo $siswa->nama_siswa; ?></td>
                                    </tr>
                                    <tr>
                                        <th>NIS</th>
                                        <td><?php echo $siswa->nis; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Mata Pelajaran</th>
                                        <td><?php echo $mapel->nama_mapel; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Semester</th>
                                        <td><?php echo $semester; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tahun Ajaran</th>
                                        <td><?php echo $tahun_ajaran; ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Nilai Tugas</th>
                                        <td>
                                            <?php echo $nilai->nilai_tugas; ?>
                                            <?php if (isset($replacements['nilai_tugas'])): ?>
                                                <span class="badge bg-success">Diganti: <?php echo $replacements['nilai_tugas']['nilai_pengganti']; ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Nilai UTS</th>
                                        <td>
                                            <?php echo $nilai->nilai_uts; ?>
                                            <?php if (isset($replacements['nilai_uts'])): ?>
                                                <span class="badge bg-success">Diganti: <?php echo $replacements['nilai_uts']['nilai_pengganti']; ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Nilai UAS</th>
                                        <td>
                                            <?php echo $nilai->nilai_uas; ?>
                                            <?php if (isset($replacements['nilai_uas'])): ?>
                                                <span class="badge bg-success">Diganti: <?php echo $replacements['nilai_uas']['nilai_pengganti']; ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Nilai Absen</th>
                                        <td>
                                            <?php echo $nilai->nilai_absen; ?>
                                            <?php if (isset($replacements['nilai_absen'])): ?>
                                                <span class="badge bg-success">Diganti: <?php echo $replacements['nilai_absen']['nilai_pengganti'] ?? ''; ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Nilai Akhir</th>
                                        <td>
                                            <strong><?php echo $nilai->nilai_akhir; ?></strong>
                                            <?php if ($nilai->nilai_akhir >= $mapel->kkm): ?>
                                                <span class="badge bg-success">Tuntas</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Belum Tuntas</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <?php if ($available_tugas->rowCount() > 0): ?>
                            <h5>Penggantian Nilai dengan Tugas Tambahan</h5>
                            <div class="row">
                                <!-- Nilai Tugas Replacement -->
                                <div class="col-md-3">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">Nilai Tugas</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (isset($replacements['nilai_tugas'])): ?>
                                                <?php if (isset($replacements['nilai_tugas']['is_average']) && $replacements['nilai_tugas']['is_average'] == 1): ?>
                                                    <p>Nilai tugas saat ini diganti dengan <strong>nilai rata-rata</strong> dari semua tugas tambahan:</p>
                                                    <?php
                                                    // Calculate average grade
                                                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                        $siswa_id,
                                                        $mapel_id,
                                                        $semester,
                                                        $tahun_ajaran
                                                    );
                                                    ?>
                                                    <p>Nilai Rata-rata: <strong><?php echo $avg_result['rata_nilai']; ?></strong> (dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan)</p>
                                                <?php else: ?>
                                                    <p>Nilai tugas saat ini diganti dengan nilai dari tugas tambahan:</p>
                                                    <p><strong><?php echo $replacements['nilai_tugas']['judul_tugas'] ?? ''; ?></strong></p>
                                                    <p>Nilai: <strong><?php echo $replacements['nilai_tugas']['nilai_pengganti'] ?? ''; ?></strong></p>
                                                <?php endif; ?>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="tugas_action" value="remove">
                                                    <button type="submit" class="btn btn-danger btn-sm">Hapus Penggantian</button>
                                                </form>
                                            <?php else: ?>
                                                <?php
                                                // Calculate average grade
                                                $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                    $siswa_id,
                                                    $mapel_id,
                                                    $semester,
                                                    $tahun_ajaran
                                                );
                                                ?>

                                                <form method="POST" action="">
                                                    <div class="form-group mb-3">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="tugas_replacement_type" id="tugas_single" value="single" checked>
                                                            <label class="form-check-label" for="tugas_single">
                                                                Ganti dengan satu tugas tambahan
                                                            </label>
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="radio" name="tugas_replacement_type" id="tugas_average" value="average" <?php echo $avg_result['jumlah_tugas'] < 2 ? 'disabled' : ''; ?>>
                                                            <label class="form-check-label" for="tugas_average">
                                                                Ganti dengan nilai rata-rata (<?php echo $avg_result['rata_nilai']; ?>) dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan
                                                            </label>
                                                            <?php if ($avg_result['jumlah_tugas'] < 2): ?>
                                                                <div class="text-muted small">Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata</div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <div id="tugas_single_options">
                                                            <label for="tugas_tambahan_id">Pilih Tugas Tambahan:</label>
                                                            <select class="form-select" id="tugas_tambahan_id" name="tugas_tambahan_id">
                                                                <option value="">-- Pilih Tugas Tambahan --</option>
                                                                <?php
                                                                $available_tugas->execute(); // Reset the cursor
                                                                while ($row = $available_tugas->fetch(PDO::FETCH_ASSOC)):
                                                                ?>
                                                                    <option value="<?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>">
                                                                        <?php echo htmlspecialchars($row['judul']); ?> (Nilai: <?php echo htmlspecialchars($row['nilai']); ?>, ID: <?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>)
                                                                    </option>
                                                                <?php endwhile; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="tugas_action" value="replace">
                                                    <button type="submit" class="btn btn-primary btn-sm">Ganti Nilai Tugas</button>
                                                </form>

                                                <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const tugasSingleRadio = document.getElementById('tugas_single');
                                                    const tugasAverageRadio = document.getElementById('tugas_average');
                                                    const tugasSingleOptions = document.getElementById('tugas_single_options');

                                                    function toggleTugasOptions() {
                                                        if (tugasSingleRadio.checked) {
                                                            tugasSingleOptions.style.display = 'block';
                                                        } else {
                                                            tugasSingleOptions.style.display = 'none';
                                                        }
                                                    }

                                                    tugasSingleRadio.addEventListener('change', toggleTugasOptions);
                                                    tugasAverageRadio.addEventListener('change', toggleTugasOptions);

                                                    // Initial state
                                                    toggleTugasOptions();
                                                });
                                                </script>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Nilai UTS Replacement -->
                                <div class="col-md-3">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">Nilai UTS</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (isset($replacements['nilai_uts'])): ?>
                                                <?php if (isset($replacements['nilai_uts']['is_average']) && $replacements['nilai_uts']['is_average'] == 1): ?>
                                                    <p>Nilai UTS saat ini diganti dengan <strong>nilai rata-rata</strong> dari semua tugas tambahan:</p>
                                                    <?php
                                                    // Calculate average grade
                                                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                        $siswa_id,
                                                        $mapel_id,
                                                        $semester,
                                                        $tahun_ajaran
                                                    );
                                                    ?>
                                                    <p>Nilai Rata-rata: <strong><?php echo $avg_result['rata_nilai']; ?></strong> (dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan)</p>
                                                <?php else: ?>
                                                    <p>Nilai UTS saat ini diganti dengan nilai dari tugas tambahan:</p>
                                                    <p><strong><?php echo $replacements['nilai_uts']['judul_tugas'] ?? ''; ?></strong></p>
                                                    <p>Nilai: <strong><?php echo $replacements['nilai_uts']['nilai_pengganti'] ?? ''; ?></strong></p>
                                                <?php endif; ?>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="uts_action" value="remove">
                                                    <button type="submit" class="btn btn-danger btn-sm">Hapus Penggantian</button>
                                                </form>
                                            <?php else: ?>
                                                <?php
                                                // Calculate average grade
                                                $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                    $siswa_id,
                                                    $mapel_id,
                                                    $semester,
                                                    $tahun_ajaran
                                                );
                                                ?>

                                                <form method="POST" action="">
                                                    <div class="form-group mb-3">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="uts_replacement_type" id="uts_single" value="single" checked>
                                                            <label class="form-check-label" for="uts_single">
                                                                Ganti dengan satu tugas tambahan
                                                            </label>
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="radio" name="uts_replacement_type" id="uts_average" value="average" <?php echo $avg_result['jumlah_tugas'] < 2 ? 'disabled' : ''; ?>>
                                                            <label class="form-check-label" for="uts_average">
                                                                Ganti dengan nilai rata-rata (<?php echo $avg_result['rata_nilai']; ?>) dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan
                                                            </label>
                                                            <?php if ($avg_result['jumlah_tugas'] < 2): ?>
                                                                <div class="text-muted small">Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata</div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <div id="uts_single_options">
                                                            <label for="uts_tambahan_id">Pilih Tugas Tambahan:</label>
                                                            <select class="form-select" id="uts_tambahan_id" name="uts_tambahan_id">
                                                                <option value="">-- Pilih Tugas Tambahan --</option>
                                                                <?php
                                                                $available_tugas->execute(); // Reset the cursor
                                                                while ($row = $available_tugas->fetch(PDO::FETCH_ASSOC)):
                                                                ?>
                                                                    <option value="<?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>">
                                                                        <?php echo htmlspecialchars($row['judul']); ?> (Nilai: <?php echo htmlspecialchars($row['nilai']); ?>, ID: <?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>)
                                                                    </option>
                                                                <?php endwhile; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="uts_action" value="replace">
                                                    <button type="submit" class="btn btn-primary btn-sm">Ganti Nilai UTS</button>
                                                </form>

                                                <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const utsSingleRadio = document.getElementById('uts_single');
                                                    const utsAverageRadio = document.getElementById('uts_average');
                                                    const utsSingleOptions = document.getElementById('uts_single_options');

                                                    function toggleUtsOptions() {
                                                        if (utsSingleRadio.checked) {
                                                            utsSingleOptions.style.display = 'block';
                                                        } else {
                                                            utsSingleOptions.style.display = 'none';
                                                        }
                                                    }

                                                    utsSingleRadio.addEventListener('change', toggleUtsOptions);
                                                    utsAverageRadio.addEventListener('change', toggleUtsOptions);

                                                    // Initial state
                                                    toggleUtsOptions();
                                                });
                                                </script>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Nilai UAS Replacement -->
                                <div class="col-md-3">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">Nilai UAS</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (isset($replacements['nilai_uas'])): ?>
                                                <?php if (isset($replacements['nilai_uas']['is_average']) && $replacements['nilai_uas']['is_average'] == 1): ?>
                                                    <p>Nilai UAS saat ini diganti dengan <strong>nilai rata-rata</strong> dari semua tugas tambahan:</p>
                                                    <?php
                                                    // Calculate average grade
                                                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                        $siswa_id,
                                                        $mapel_id,
                                                        $semester,
                                                        $tahun_ajaran
                                                    );
                                                    ?>
                                                    <p>Nilai Rata-rata: <strong><?php echo $avg_result['rata_nilai']; ?></strong> (dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan)</p>
                                                <?php else: ?>
                                                    <p>Nilai UAS saat ini diganti dengan nilai dari tugas tambahan:</p>
                                                    <p><strong><?php echo $replacements['nilai_uas']['judul_tugas'] ?? ''; ?></strong></p>
                                                    <p>Nilai: <strong><?php echo $replacements['nilai_uas']['nilai_pengganti'] ?? ''; ?></strong></p>
                                                <?php endif; ?>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="uas_action" value="remove">
                                                    <button type="submit" class="btn btn-danger btn-sm">Hapus Penggantian</button>
                                                </form>
                                            <?php else: ?>
                                                <?php
                                                // Calculate average grade
                                                $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                    $siswa_id,
                                                    $mapel_id,
                                                    $semester,
                                                    $tahun_ajaran
                                                );
                                                ?>

                                                <form method="POST" action="">
                                                    <div class="form-group mb-3">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="uas_replacement_type" id="uas_single" value="single" checked>
                                                            <label class="form-check-label" for="uas_single">
                                                                Ganti dengan satu tugas tambahan
                                                            </label>
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="radio" name="uas_replacement_type" id="uas_average" value="average" <?php echo $avg_result['jumlah_tugas'] < 2 ? 'disabled' : ''; ?>>
                                                            <label class="form-check-label" for="uas_average">
                                                                Ganti dengan nilai rata-rata (<?php echo $avg_result['rata_nilai']; ?>) dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan
                                                            </label>
                                                            <?php if ($avg_result['jumlah_tugas'] < 2): ?>
                                                                <div class="text-muted small">Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata</div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <div id="uas_single_options">
                                                            <label for="uas_tambahan_id">Pilih Tugas Tambahan:</label>
                                                            <select class="form-select" id="uas_tambahan_id" name="uas_tambahan_id">
                                                                <option value="">-- Pilih Tugas Tambahan --</option>
                                                                <?php
                                                                $available_tugas->execute(); // Reset the cursor
                                                                while ($row = $available_tugas->fetch(PDO::FETCH_ASSOC)):
                                                                ?>
                                                                    <option value="<?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>">
                                                                        <?php echo htmlspecialchars($row['judul']); ?> (Nilai: <?php echo htmlspecialchars($row['nilai']); ?>, ID: <?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>)
                                                                    </option>
                                                                <?php endwhile; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="uas_action" value="replace">
                                                    <button type="submit" class="btn btn-primary btn-sm">Ganti Nilai UAS</button>
                                                </form>

                                                <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const uasSingleRadio = document.getElementById('uas_single');
                                                    const uasAverageRadio = document.getElementById('uas_average');
                                                    const uasSingleOptions = document.getElementById('uas_single_options');

                                                    function toggleUasOptions() {
                                                        if (uasSingleRadio.checked) {
                                                            uasSingleOptions.style.display = 'block';
                                                        } else {
                                                            uasSingleOptions.style.display = 'none';
                                                        }
                                                    }

                                                    uasSingleRadio.addEventListener('change', toggleUasOptions);
                                                    uasAverageRadio.addEventListener('change', toggleUasOptions);

                                                    // Initial state
                                                    toggleUasOptions();
                                                });
                                                </script>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Nilai Absen Replacement -->
                                <div class="col-md-3">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">Nilai Absen</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (isset($replacements['nilai_absen'])): ?>
                                                <?php if (isset($replacements['nilai_absen']['is_average']) && $replacements['nilai_absen']['is_average'] == 1): ?>
                                                    <p>Nilai absen saat ini diganti dengan <strong>nilai rata-rata</strong> dari semua tugas tambahan:</p>
                                                    <?php
                                                    // Calculate average grade
                                                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                        $siswa_id,
                                                        $mapel_id,
                                                        $semester,
                                                        $tahun_ajaran
                                                    );
                                                    ?>
                                                    <p>Nilai Rata-rata: <strong><?php echo $avg_result['rata_nilai']; ?></strong> (dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan)</p>
                                                <?php else: ?>
                                                    <p>Nilai absen saat ini diganti dengan nilai dari tugas tambahan:</p>
                                                    <p><strong><?php echo $replacements['nilai_absen']['judul_tugas'] ?? ''; ?></strong></p>
                                                    <p>Nilai: <strong><?php echo $replacements['nilai_absen']['nilai_pengganti'] ?? ''; ?></strong></p>
                                                <?php endif; ?>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="absen_action" value="remove">
                                                    <button type="submit" class="btn btn-danger btn-sm">Hapus Penggantian</button>
                                                </form>
                                            <?php else: ?>
                                                <?php
                                                // Calculate average grade
                                                $avg_result = $nilaiPengganti->calculateAverageNilai(
                                                    $siswa_id,
                                                    $mapel_id,
                                                    $semester,
                                                    $tahun_ajaran
                                                );
                                                ?>

                                                <form method="POST" action="">
                                                    <div class="form-group mb-3">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="absen_replacement_type" id="absen_single" value="single" checked>
                                                            <label class="form-check-label" for="absen_single">
                                                                Ganti dengan satu tugas tambahan
                                                            </label>
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="radio" name="absen_replacement_type" id="absen_average" value="average" <?php echo $avg_result['jumlah_tugas'] < 2 ? 'disabled' : ''; ?>>
                                                            <label class="form-check-label" for="absen_average">
                                                                Ganti dengan nilai rata-rata (<?php echo $avg_result['rata_nilai']; ?>) dari <?php echo $avg_result['jumlah_tugas']; ?> tugas tambahan
                                                            </label>
                                                            <?php if ($avg_result['jumlah_tugas'] < 2): ?>
                                                                <div class="text-muted small">Minimal harus ada 2 tugas tambahan untuk menggunakan nilai rata-rata</div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <div id="absen_single_options">
                                                            <label for="absen_tambahan_id">Pilih Tugas Tambahan:</label>
                                                            <select class="form-select" id="absen_tambahan_id" name="absen_tambahan_id">
                                                                <option value="">-- Pilih Tugas Tambahan --</option>
                                                                <?php
                                                                $available_tugas->execute(); // Reset the cursor
                                                                while ($row = $available_tugas->fetch(PDO::FETCH_ASSOC)):
                                                                ?>
                                                                    <option value="<?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>">
                                                                        <?php echo htmlspecialchars($row['judul']); ?> (Nilai: <?php echo htmlspecialchars($row['nilai']); ?>, ID: <?php echo htmlspecialchars($row['tugas_tambahan_siswa_id']); ?>)
                                                                    </option>
                                                                <?php endwhile; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="absen_action" value="replace">
                                                    <button type="submit" class="btn btn-primary btn-sm">Ganti Nilai Absen</button>
                                                </form>

                                                <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const absenSingleRadio = document.getElementById('absen_single');
                                                    const absenAverageRadio = document.getElementById('absen_average');
                                                    const absenSingleOptions = document.getElementById('absen_single_options');

                                                    function toggleAbsenOptions() {
                                                        if (absenSingleRadio.checked) {
                                                            absenSingleOptions.style.display = 'block';
                                                        } else {
                                                            absenSingleOptions.style.display = 'none';
                                                        }
                                                    }

                                                    absenSingleRadio.addEventListener('change', toggleAbsenOptions);
                                                    absenAverageRadio.addEventListener('change', toggleAbsenOptions);

                                                    // Initial state
                                                    toggleAbsenOptions();
                                                });
                                                </script>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p>Tidak ada tugas tambahan yang tersedia untuk siswa ini. Silakan tambahkan tugas tambahan terlebih dahulu.</p>
                            <a href="../tugas_tambahan/index.php?semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-primary btn-sm mt-2">
                                <i class="fas fa-plus"></i> Tambah Tugas Tambahan
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
