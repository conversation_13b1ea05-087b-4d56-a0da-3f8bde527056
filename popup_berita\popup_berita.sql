-- =====================================================
-- Popup Berita Module Database Migration
-- =====================================================
-- This file contains the database structure for the 
-- News Popup notification system
-- =====================================================

-- Table for popup berita settings
DROP TABLE IF EXISTS `popup_berita_settings`;
CREATE TABLE `popup_berita_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `title` varchar(255) NOT NULL DEFAULT 'Berita Terbaru',
  `show_excerpt` tinyint(1) NOT NULL DEFAULT 1,
  `excerpt_length` int(11) NOT NULL DEFAULT 150,
  `auto_show` tinyint(1) NOT NULL DEFAULT 1,
  `show_dont_show_again` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `popup_berita_settings_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for popup berita items (selected news articles)
DROP TABLE IF EXISTS `popup_berita_items`;
CREATE TABLE `popup_berita_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `berita_id` int(11) NOT NULL,
  `display_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_berita` (`berita_id`),
  KEY `berita_id` (`berita_id`),
  CONSTRAINT `popup_berita_items_ibfk_1` FOREIGN KEY (`berita_id`) REFERENCES `berita` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default settings (optional)
INSERT INTO `popup_berita_settings` 
(`is_active`, `title`, `show_excerpt`, `excerpt_length`, `auto_show`, `show_dont_show_again`, `created_by`) 
VALUES 
(0, 'Berita Terbaru', 1, 150, 1, 1, 1);

-- =====================================================
-- End of Migration
-- =====================================================
