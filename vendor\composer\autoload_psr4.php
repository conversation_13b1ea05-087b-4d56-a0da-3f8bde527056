<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'Svg\\' => array($vendorDir . '/phenx/php-svg-lib/src/Svg'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'PhpOffice\\PhpWord\\' => array($vendorDir . '/phpoffice/phpword/src/PhpWord'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'PhpOffice\\Math\\' => array($vendorDir . '/phpoffice/math/src/Math'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'FontLib\\' => array($vendorDir . '/phenx/php-font-lib/src/FontLib'),
    'Dompdf\\' => array($vendorDir . '/dompdf/dompdf/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
);
