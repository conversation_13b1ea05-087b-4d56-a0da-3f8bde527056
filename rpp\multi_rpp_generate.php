<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

$rpp = new Rpp();
$rppQuestion = new RppQuestion();
$multiRppExam = new MultiRppExam();

// Ambil daftar RPP milik guru yang login
$rpp_list = $rpp->getAllByGuru($guru_id);

// Ambil daftar ujian multi-RPP yang sudah ada
$existing_exams = $multiRppExam->getAllByGuru($guru_id);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-layer-group"></i> Generate Soal Multi-RPP (Multi-Chapter)
            </h5>
            <div>
                <button type="button" class="btn btn-outline-info me-2" data-bs-toggle="modal" data-bs-target="#helpModal">
                    <i class="fas fa-question-circle"></i> Bantuan
                </button>
                <a href="generate_questions.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-file-alt"></i> Single RPP
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar RPP
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Main Content -->

                    <?php if ($rpp_list && $rpp_list->rowCount() >= 2): ?>
                        <form id="multiRppForm" action="multi_rpp_configure.php" method="POST">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-check-square"></i> Pilih RPP/Chapter untuk Ujian</h6>
                                </div>
                                <div class="card-body">
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                            <label class="form-check-label fw-bold" for="selectAll">
                                                Pilih Semua RPP
                                            </label>
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th width="50">
                                                        <i class="fas fa-check"></i>
                                                    </th>
                                                    <th>Mata Pelajaran</th>
                                                    <th>Kelas</th>
                                                    <th>Tema/Chapter</th>
                                                    <th>Materi Pokok</th>
                                                    <th>Semester</th>
                                                    <th>Soal Tersedia</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php 
                                                $rpp_list->execute(); // Reset cursor
                                                while ($row = $rpp_list->fetch(PDO::FETCH_ASSOC)): 
                                                    $question_count = $rppQuestion->getCountByRppId($row['id']);
                                                ?>
                                                    <tr>
                                                        <td>
                                                            <div class="form-check">
                                                                <input class="form-check-input rpp-checkbox" type="checkbox" 
                                                                       name="selected_rpps[]" value="<?= $row['id'] ?>" 
                                                                       id="rpp_<?= $row['id'] ?>"
                                                                       data-mapel="<?= htmlspecialchars($row['nama_mapel']) ?>"
                                                                       data-kelas="<?= htmlspecialchars($row['nama_kelas']) ?>"
                                                                       data-tema="<?= htmlspecialchars($row['tema_subtema']) ?>"
                                                                       data-questions="<?= $question_count ?>">
                                                            </div>
                                                        </td>
                                                        <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                                        <td><?= htmlspecialchars($row['nama_kelas']) ?></td>
                                                        <td>
                                                            <strong><?= htmlspecialchars($row['tema_subtema']) ?></strong>
                                                        </td>
                                                        <td><?= htmlspecialchars($row['materi_pokok']) ?></td>
                                                        <td><?= htmlspecialchars($row['semester']) ?></td>
                                                        <td>
                                                            <span class="badge bg-info"><?= $question_count ?> soal</span>
                                                            <?php if ($question_count > 0): ?>
                                                                <a href="questions_list.php?rpp_id=<?= $row['id'] ?>" 
                                                                   class="btn btn-sm btn-outline-primary ms-1" target="_blank">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="mt-3">
                                        <div class="alert alert-warning" id="selectionWarning" style="display: none;">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>Perhatian:</strong> Pilih minimal 2 RPP untuk membuat ujian multi-chapter.
                                        </div>
                                        
                                        <div class="alert alert-success" id="selectionInfo" style="display: none;">
                                            <i class="fas fa-check-circle"></i>
                                            <span id="selectionText"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-cog"></i> Informasi Ujian</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="exam_title" class="form-label">Judul Ujian</label>
                                                <input type="text" class="form-control" id="exam_title" name="exam_title" 
                                                       placeholder="Contoh: UAS Semester 1 - Komputer dan Jaringan Dasar" required>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="exam_type" class="form-label">Jenis Ujian</label>
                                                <select class="form-select" id="exam_type" name="exam_type" required>
                                                    <option value="">Pilih Jenis Ujian</option>
                                                    <option value="UAS">UAS (Ujian Akhir Semester)</option>
                                                    <option value="UTS">UTS (Ujian Tengah Semester)</option>
                                                    <option value="Ulangan Harian">Ulangan Harian</option>
                                                    <option value="Quiz">Quiz</option>
                                                    <option value="Ujian Praktik">Ujian Praktik</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="semester" class="form-label">Semester</label>
                                                <select class="form-select" id="semester" name="semester" required>
                                                    <option value="">Pilih Semester</option>
                                                    <option value="1">Semester 1</option>
                                                    <option value="2">Semester 2</option>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                                <input type="text" class="form-control" id="tahun_ajaran" name="tahun_ajaran" 
                                                       placeholder="2024/2025" value="<?= date('Y') ?>/<?= date('Y') + 1 ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="exam_duration" class="form-label">Durasi Ujian (menit)</label>
                                                <input type="number" class="form-control" id="exam_duration" name="exam_duration" 
                                                       min="30" max="180" value="90" required>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="total_score" class="form-label">Total Skor</label>
                                                <input type="number" class="form-control" id="total_score" name="total_score" 
                                                       min="50" max="200" value="100" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="additional_notes" class="form-label">Catatan Tambahan (Opsional)</label>
                                        <textarea class="form-control" id="additional_notes" name="additional_notes" rows="3" 
                                                  placeholder="Catatan khusus untuk ujian ini..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 text-end">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                    <i class="fas fa-arrow-right"></i> Lanjut ke Konfigurasi Soal
                                </button>
                            </div>
                        </form>
                    <?php elseif ($rpp_list && $rpp_list->rowCount() == 1): ?>
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> RPP Tidak Mencukupi</h5>
                            <p>Anda hanya memiliki 1 RPP. Untuk membuat ujian multi-chapter, minimal diperlukan 2 RPP.</p>
                            <p class="mb-0">
                                Silakan <a href="create.php" class="alert-link">buat RPP baru</a> atau gunakan 
                                <a href="generate_questions.php" class="alert-link">generate soal single RPP</a>.
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> Belum Ada RPP</h5>
                            <p class="mb-0">Anda belum memiliki RPP. Silakan <a href="create.php" class="alert-link">buat RPP baru</a> terlebih dahulu sebelum dapat generate soal multi-chapter.</p>
                        </div>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">
                    <i class="fas fa-question-circle text-info"></i> Bantuan Multi-RPP Generation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Tentang Multi-RPP</h6>
                            </div>
                            <div class="card-body">
                                <p class="small">
                                    Fitur ini memungkinkan Anda membuat soal ujian komprehensif yang menggabungkan materi dari beberapa RPP/chapter sekaligus.
                                    Ideal untuk UAS, UTS, atau ujian akhir semester yang mencakup multiple topik pembelajaran.
                                </p>

                                <h6 class="mt-3">Keunggulan Multi-Chapter Exam:</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success"></i> Ujian komprehensif multi-topik</li>
                                    <li><i class="fas fa-check text-success"></i> Distribusi soal proporsional</li>
                                    <li><i class="fas fa-check text-success"></i> Integrasi antar chapter</li>
                                    <li><i class="fas fa-check text-success"></i> Kisi-kisi ujian otomatis</li>
                                    <li><i class="fas fa-check text-success"></i> Mapping Bloom's Taxonomy</li>
                                    <li><i class="fas fa-check text-success"></i> Export PDF/Word</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-cog"></i> Cara Penggunaan</h6>
                            </div>
                            <div class="card-body">
                                <ol class="small">
                                    <li><strong>Pilih RPP:</strong> Pilih minimal 2 RPP untuk membuat ujian multi-chapter</li>
                                    <li><strong>Isi Informasi:</strong> Lengkapi informasi ujian (judul, jenis, semester, dll)</li>
                                    <li><strong>Konfigurasi:</strong> Atur distribusi soal dan tingkat kesulitan</li>
                                    <li><strong>Generate:</strong> Sistem akan membuat soal secara otomatis</li>
                                    <li><strong>Review:</strong> Periksa dan edit soal jika diperlukan</li>
                                    <li><strong>Export:</strong> Download dalam format PDF atau Word</li>
                                </ol>

                                <h6 class="mt-3">Jenis Soal Tersedia:</h6>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-primary"></i> Pilihan Ganda (A-E)</li>
                                    <li><i class="fas fa-check text-primary"></i> Essay</li>
                                    <li><i class="fas fa-check text-primary"></i> Regular & HOTS</li>
                                    <li><i class="fas fa-check text-primary"></i> Level Kognitif C1-C6</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-history"></i> Ujian Multi-RPP Tersimpan</h6>
                            </div>
                            <div class="card-body">
                                <?php if ($existing_exams && $existing_exams->rowCount() > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Judul Ujian</th>
                                                    <th>Jenis</th>
                                                    <th>Semester</th>
                                                    <th>Tanggal</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $existing_exams->execute(); // Reset cursor
                                                while ($exam = $existing_exams->fetch(PDO::FETCH_ASSOC)):
                                                ?>
                                                    <tr>
                                                        <td><?= htmlspecialchars($exam['exam_title']) ?></td>
                                                        <td><?= htmlspecialchars($exam['exam_type']) ?></td>
                                                        <td><?= htmlspecialchars($exam['semester']) ?></td>
                                                        <td><?= date('d/m/Y', strtotime($exam['created_at'])) ?></td>
                                                        <td>
                                                            <a href="multi_rpp_configure.php?exam_id=<?= $exam['id'] ?>"
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i> Lihat
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted small mb-0">Belum ada ujian multi-RPP yang tersimpan.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rppCheckboxes = document.querySelectorAll('.rpp-checkbox');
    const submitBtn = document.getElementById('submitBtn');
    const selectionWarning = document.getElementById('selectionWarning');
    const selectionInfo = document.getElementById('selectionInfo');
    const selectionText = document.getElementById('selectionText');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        rppCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectionStatus();
    });

    // Individual checkbox functionality
    rppCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateSelectionStatus();
        });
    });

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.rpp-checkbox:checked').length;
        const totalCount = rppCheckboxes.length;
        
        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateSelectionStatus() {
        const checkedBoxes = document.querySelectorAll('.rpp-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count < 2) {
            selectionWarning.style.display = 'block';
            selectionInfo.style.display = 'none';
            submitBtn.disabled = true;
        } else {
            selectionWarning.style.display = 'none';
            selectionInfo.style.display = 'block';
            submitBtn.disabled = false;
            
            // Build selection info text
            const subjects = [...new Set(Array.from(checkedBoxes).map(cb => cb.dataset.mapel))];
            const classes = [...new Set(Array.from(checkedBoxes).map(cb => cb.dataset.kelas))];
            
            selectionText.innerHTML = `
                <strong>${count} RPP dipilih</strong><br>
                Mata Pelajaran: ${subjects.join(', ')}<br>
                Kelas: ${classes.join(', ')}
            `;
        }
    }

    // Form validation
    document.getElementById('multiRppForm').addEventListener('submit', function(e) {
        const checkedCount = document.querySelectorAll('.rpp-checkbox:checked').length;
        if (checkedCount < 2) {
            e.preventDefault();
            alert('Silakan pilih minimal 2 RPP untuk membuat ujian multi-chapter.');
            return false;
        }
    });

    // Initialize
    updateSelectionStatus();
});
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.card-title {
    color: #495057;
}

.badge {
    font-size: 0.8rem;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>

<?php require_once '../template/footer.php'; ?>
