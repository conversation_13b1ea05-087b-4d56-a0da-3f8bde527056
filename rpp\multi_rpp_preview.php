<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/MultiRppExam.php';
require_once '../models/GeminiApi.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Validasi input
if (!isset($_POST['selected_rpps']) || !isset($_POST['exam_title'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Ambil data dari form
$selected_rpp_ids = $_POST['selected_rpps'];
$exam_data = [
    'exam_title' => $_POST['exam_title'],
    'exam_type' => $_POST['exam_type'],
    'semester' => $_POST['semester'],
    'tahun_ajaran' => $_POST['tahun_ajaran'],
    'exam_duration' => $_POST['exam_duration'],
    'total_score' => $_POST['total_score'],
    'additional_notes' => $_POST['additional_notes'] ?? ''
];

$config = [
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count'],
    'distribution_strategy' => $_POST['distribution_strategy'] ?? 'proportional'
];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data RPP yang dipilih
$rpp = new Rpp();
$selected_rpps = [];
foreach ($selected_rpp_ids as $rpp_id) {
    $rpp_data = $rpp->getOne($rpp_id);
    
    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
        header("Location: multi_rpp_generate.php");
        exit();
    }
    
    $selected_rpps[] = $rpp_data;
}

// Generate questions using Gemini API
$generated_questions = [];
$error_message = '';
$loading = true;

try {
    $geminiApi = new GeminiApi();
    $generated_questions = $geminiApi->generateMultiRppQuestions($selected_rpps, $config);
    $loading = false;
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $loading = false;
}
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-eye"></i> Preview Soal Multi-RPP
            </h5>
            <div>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if ($loading): ?>
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="mt-3">Generating Questions...</h5>
                    <p class="text-muted">AI sedang membuat soal dari <?= count($selected_rpps) ?> RPP yang dipilih. Mohon tunggu...</p>
                </div>
            <?php elseif ($error_message): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Error Generate Questions</h5>
                    <p class="mb-0"><?= htmlspecialchars($error_message) ?></p>
                    <hr>
                    <a href="multi_rpp_configure.php" class="btn btn-outline-danger">Coba Lagi</a>
                </div>
            <?php else: ?>
                <!-- Exam Summary -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Ringkasan Ujian</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td width="120"><strong>Judul:</strong></td>
                                                <td><?= htmlspecialchars($exam_data['exam_title']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Jenis:</strong></td>
                                                <td><?= htmlspecialchars($exam_data['exam_type']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Durasi:</strong></td>
                                                <td><?= htmlspecialchars($exam_data['exam_duration']) ?> menit</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td width="120"><strong>Total Soal:</strong></td>
                                                <td><?= count($generated_questions) ?> soal</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Chapter:</strong></td>
                                                <td><?= count($selected_rpps) ?> RPP</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Skor:</strong></td>
                                                <td><?= htmlspecialchars($exam_data['total_score']) ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Distribusi Soal</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $stats = [
                                    'multiple_choice' => 0,
                                    'essay' => 0,
                                    'by_chapter' => [],
                                    'by_difficulty' => ['regular' => 0, 'hots_easy' => 0, 'hots_medium' => 0, 'hots_hard' => 0]
                                ];
                                
                                foreach ($generated_questions as $question) {
                                    $stats[$question['question_type']]++;
                                    $stats['by_difficulty'][$question['difficulty_level']]++;
                                    
                                    $chapter = $question['source_chapter'] ?? 1;
                                    if (!isset($stats['by_chapter'][$chapter])) {
                                        $stats['by_chapter'][$chapter] = 0;
                                    }
                                    $stats['by_chapter'][$chapter]++;
                                }
                                ?>
                                
                                <div class="small">
                                    <div class="d-flex justify-content-between">
                                        <span>Pilihan Ganda:</span>
                                        <span><strong><?= $stats['multiple_choice'] ?></strong></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Essay:</span>
                                        <span><strong><?= $stats['essay'] ?></strong></span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Regular:</span>
                                        <span><?= $stats['by_difficulty']['regular'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS:</span>
                                        <span><?= $stats['by_difficulty']['hots_easy'] + $stats['by_difficulty']['hots_medium'] + $stats['by_difficulty']['hots_hard'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Questions Preview -->
                <form id="saveForm" action="multi_rpp_save.php" method="POST">
                    <!-- Hidden fields -->
                    <?php foreach ($selected_rpp_ids as $rpp_id): ?>
                        <input type="hidden" name="selected_rpps[]" value="<?= $rpp_id ?>">
                    <?php endforeach; ?>
                    
                    <?php foreach ($exam_data as $key => $value): ?>
                        <input type="hidden" name="exam_data[<?= $key ?>]" value="<?= htmlspecialchars($value) ?>">
                    <?php endforeach; ?>
                    
                    <?php foreach ($config as $key => $value): ?>
                        <input type="hidden" name="config[<?= $key ?>]" value="<?= htmlspecialchars($value) ?>">
                    <?php endforeach; ?>

                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-list"></i> Daftar Soal Generated (<?= count($generated_questions) ?> soal)
                            </h6>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" id="selectAll" checked>
                                    <label class="form-check-label" for="selectAll">
                                        Pilih Semua
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($generated_questions)): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Tidak ada soal yang berhasil di-generate. Silakan coba lagi dengan konfigurasi yang berbeda.
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($generated_questions as $index => $question): ?>
                                        <div class="col-12 mb-4">
                                            <div class="card border">
                                                <div class="card-header">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-8">
                                                            <div class="form-check">
                                                                <input class="form-check-input question-checkbox" type="checkbox" 
                                                                       name="selected_questions[]" value="<?= $index ?>" 
                                                                       id="question_<?= $index ?>" checked>
                                                                <label class="form-check-label fw-bold" for="question_<?= $index ?>">
                                                                    Soal <?= $index + 1 ?> - Chapter <?= $question['source_chapter'] ?>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4 text-end">
                                                            <span class="badge bg-primary"><?= ucfirst(str_replace('_', ' ', $question['question_type'])) ?></span>
                                                            <span class="badge bg-secondary"><?= ucfirst(str_replace('_', ' ', $question['difficulty_level'])) ?></span>
                                                            <span class="badge bg-info"><?= $question['cognitive_level'] ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <strong>Pertanyaan:</strong>
                                                        <p class="mt-2"><?= nl2br(htmlspecialchars($question['question_text'])) ?></p>
                                                    </div>
                                                    
                                                    <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                                                        <div class="mb-3">
                                                            <strong>Pilihan Jawaban:</strong>
                                                            <div class="mt-2">
                                                                <?php foreach ($question['options'] as $option): ?>
                                                                    <div class="mb-1">
                                                                        <?php if ($option === $question['correct_answer'] || 
                                                                                  (is_string($option) && substr($option, 0, 1) === $question['correct_answer'])): ?>
                                                                            <span class="text-success fw-bold"><?= htmlspecialchars($option) ?> ✓</span>
                                                                        <?php else: ?>
                                                                            <?= htmlspecialchars($option) ?>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!empty($question['explanation'])): ?>
                                                        <div class="mb-3">
                                                            <strong>Penjelasan:</strong>
                                                            <p class="mt-2 text-muted small"><?= nl2br(htmlspecialchars($question['explanation'])) ?></p>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="small text-muted">
                                                        <strong>Source:</strong> 
                                                        <?php
                                                        $source_rpp = null;
                                                        foreach ($selected_rpps as $rpp) {
                                                            if ($rpp['id'] == $question['source_rpp_id']) {
                                                                $source_rpp = $rpp;
                                                                break;
                                                            }
                                                        }
                                                        ?>
                                                        <?= $source_rpp ? htmlspecialchars($source_rpp['tema_subtema']) : 'Chapter ' . $question['source_chapter'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Hidden input for question data -->
                                        <input type="hidden" name="questions[<?= $index ?>][source_rpp_id]" value="<?= $question['source_rpp_id'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][source_chapter]" value="<?= $question['source_chapter'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][question_text]" value="<?= htmlspecialchars($question['question_text']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][question_type]" value="<?= $question['question_type'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][options]" value="<?= htmlspecialchars(json_encode($question['options'])) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][correct_answer]" value="<?= htmlspecialchars($question['correct_answer']) ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][difficulty_level]" value="<?= $question['difficulty_level'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][cognitive_level]" value="<?= $question['cognitive_level'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][category]" value="<?= $question['category'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][source_type]" value="<?= $question['source_type'] ?>">
                                        <input type="hidden" name="questions[<?= $index ?>][explanation]" value="<?= htmlspecialchars($question['explanation']) ?>">
                                    <?php endforeach; ?>
                                </div>
                                
                                <div class="mt-4 text-center">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Pilih soal yang ingin disimpan, kemudian klik tombol "Simpan Soal Terpilih" di bawah.
                                    </div>
                                    
                                    <button type="submit" class="btn btn-success btn-lg" id="saveBtn">
                                        <i class="fas fa-save"></i> Simpan Soal Terpilih
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    const saveBtn = document.getElementById('saveBtn');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSaveButton();
        });
    }

    // Individual checkbox functionality
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateSaveButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
        const totalCount = questionCheckboxes.length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    }

    function updateSaveButton() {
        const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
        if (saveBtn) {
            saveBtn.disabled = checkedCount === 0;
            saveBtn.innerHTML = checkedCount > 0 
                ? `<i class="fas fa-save"></i> Simpan ${checkedCount} Soal Terpilih`
                : '<i class="fas fa-save"></i> Simpan Soal Terpilih';
        }
    }

    // Form validation
    if (document.getElementById('saveForm')) {
        document.getElementById('saveForm').addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
            if (checkedCount === 0) {
                e.preventDefault();
                alert('Silakan pilih minimal satu soal untuk disimpan.');
                return false;
            }
            
            // Show loading
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
                saveBtn.disabled = true;
            }
        });
    }

    // Initialize
    updateSaveButton();
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.badge {
    font-size: 0.75rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.question-checkbox:checked + label {
    color: #0d6efd;
}

#saveBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}
</style>

<?php require_once '../template/footer.php'; ?>
