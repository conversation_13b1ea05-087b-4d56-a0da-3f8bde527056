<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Nilai.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../template/header.php';

if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    header("Location: index.php");
    exit();
}

$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Check if user has access to this subject
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if ($guru_id) {
        $jadwal = new JadwalPelajaran();
        $query = "SELECT COUNT(*) as total FROM jadwal_pelajaran WHERE mapel_id = :mapel_id AND guru_id = :guru_id";
        $database = new Database();
        $conn = $database->getConnection();
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row['total'] == 0) {
            $_SESSION['error'] = "Anda tidak memiliki akses ke mata pelajaran ini.";
            header("Location: index.php");
            exit();
        }
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan untuk akun ini.";
        header("Location: index.php");
        exit();
    }
}

$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

$nilai = new Nilai();
$result_nilai = $nilai->getNilaiMapel($mapel_id, $semester, $tahun_ajaran);

// Get error messages if any
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Nilai <?php echo $mapel->nama_mapel; ?></h5>
                    <small class="text-muted">Semester <?php echo $semester; ?> - Tahun Ajaran <?php echo $tahun_ajaran; ?></small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">KKM: <?php echo $mapel->kkm; ?></span>
                    <a href="export.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>"
                       class="btn btn-success btn-sm">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                    <a href="export_pdf.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>"
                       class="btn btn-danger btn-sm ms-2">
                        <i class="fas fa-file-pdf"></i> Export PDF
                    </a>
                    <?php if(isset($_GET['kelas_id'])): ?>
                        <a href="export.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>&kelas_id=<?php echo $_GET['kelas_id']; ?>"
                           class="btn btn-success btn-sm ms-2">
                            <i class="fas fa-file-excel"></i> Export Excel Kelas Ini
                        </a>
                        <a href="export_pdf.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>&kelas_id=<?php echo $_GET['kelas_id']; ?>"
                           class="btn btn-danger btn-sm ms-2">
                            <i class="fas fa-file-pdf"></i> Export PDF Kelas Ini
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($result_nilai === false): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        Error executing query
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php elseif ($result_nilai->rowCount() === 0): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        Tidak ada data nilai untuk mata pelajaran ini pada semester dan tahun ajaran yang dipilih.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableNilai">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NIS</th>
                                <th>Nama Siswa</th>
                                <th>Rata-rata Tugas</th>
                                <th>UTS</th>
                                <th>UAS</th>
                                <th>Absensi</th>
                                <th>Rumus</th>
                                <th>Nilai Akhir</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php
                        $no = 1;
                        while($row = $result_nilai->fetch(PDO::FETCH_ASSOC)):
                            $nilai_akhir = $row['nilai_akhir'] ?? 0;
                            $status = $nilai_akhir >= $mapel->kkm ? 'Tuntas' : 'Belum Tuntas';
                            $color_class = $nilai_akhir >= $mapel->kkm ? 'text-success' : 'text-danger';
                        ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $row['nis']; ?></td>
                                <td><?php echo $row['nama_siswa']; ?></td>
                                <td>
                                    <?php echo $row['rata_tugas'] ? number_format($row['rata_tugas'], 2) : '-'; ?>
                                    <?php if (!empty($row['nilai_tugas_pengganti'])): ?>
                                        <?php if (!empty($row['is_tugas_average'])): ?>
                                            <span class="badge bg-info" title="Diganti dengan nilai rata-rata dari <?php echo $row['jumlah_tugas_tambahan']; ?> tugas tambahan">
                                                <?php echo $row['nilai_tugas_rata_rata']; ?> (Rata-rata)
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success" title="Diganti dengan nilai tugas tambahan: <?php echo $row['judul_tugas_pengganti']; ?>">
                                                <?php echo $row['nilai_tugas_pengganti']; ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $row['nilai_uts'] ?? '-'; ?>
                                    <?php if (!empty($row['nilai_uts_pengganti'])): ?>
                                        <?php if (!empty($row['is_uts_average'])): ?>
                                            <span class="badge bg-info" title="Diganti dengan nilai rata-rata dari <?php echo $row['jumlah_tugas_tambahan']; ?> tugas tambahan">
                                                <?php echo $row['nilai_tugas_rata_rata']; ?> (Rata-rata)
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success" title="Diganti dengan nilai tugas tambahan: <?php echo $row['judul_uts_pengganti']; ?>">
                                                <?php echo $row['nilai_uts_pengganti']; ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $row['nilai_uas'] ?? '-'; ?>
                                    <?php if (!empty($row['nilai_uas_pengganti'])): ?>
                                        <?php if (!empty($row['is_uas_average'])): ?>
                                            <span class="badge bg-info" title="Diganti dengan nilai rata-rata dari <?php echo $row['jumlah_tugas_tambahan']; ?> tugas tambahan">
                                                <?php echo $row['nilai_tugas_rata_rata']; ?> (Rata-rata)
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success" title="Diganti dengan nilai tugas tambahan: <?php echo $row['judul_uas_pengganti']; ?>">
                                                <?php echo $row['nilai_uas_pengganti']; ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $row['nilai_absen'] ?? '-'; ?>
                                    <?php if (!empty($row['nilai_absen_pengganti'])): ?>
                                        <?php if (!empty($row['is_absen_average'])): ?>
                                            <span class="badge bg-info" title="Diganti dengan nilai rata-rata dari <?php echo $row['jumlah_tugas_tambahan']; ?> tugas tambahan">
                                                <?php echo $row['nilai_tugas_rata_rata']; ?> (Rata-rata)
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success" title="Diganti dengan nilai tugas tambahan: <?php echo $row['judul_absen_pengganti']; ?>">
                                                <?php echo $row['nilai_absen_pengganti']; ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $row['rumus_nilai'] ?? '-'; ?></td>
                                <td class="<?php echo $color_class; ?> fw-bold"><?php echo $nilai_akhir ?: '-'; ?></td>
                                <td class="<?php echo $color_class; ?>"><?php echo $status; ?></td>
                                <td>
                                    <a href="pengganti.php?nilai_id=<?php echo $row['id']; ?>&siswa_id=<?php echo $row['siswa_id']; ?>&mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>"
                                       class="btn btn-sm btn-primary" title="Kelola Penggantian Nilai">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <div class="text-end mt-3">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <a href="input.php?mapel_id=<?php echo $mapel_id; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>"
                       class="btn btn-primary ms-2">
                        <i class="fas fa-edit"></i> Edit Nilai
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableNilai').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
