<?php
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'Nama Kelas');
$sheet->setCellValue('B1', 'Wali Kelas');
$sheet->setCellValue('C1', 'Tahun Ajaran');
$sheet->setCellValue('D1', 'Tingkat');
$sheet->setCellValue('E1', 'Jurusan');

// Example data
$sheet->setCellValue('A2', 'X IPA 1');
$sheet->setCellValue('B2', 'Nama Wali Kelas');
$sheet->setCellValue('C2', '2024/2025');
$sheet->setCellValue('D2', 'X');
$sheet->setCellValue('E2', 'IPA');

// Auto-size columns
foreach(range('A','E') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Style the header row
$sheet->getStyle('A1:E1')->getFont()->setBold(true);
$sheet->getStyle('A1:E1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Create the Excel file
$writer = new Xlsx($spreadsheet);
$writer->save('template_kelas.xlsx');

echo "Template created successfully!";
?>
