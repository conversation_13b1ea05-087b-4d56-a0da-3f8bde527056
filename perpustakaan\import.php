<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$perpustakaan = new Perpustakaan();
$kategori = $perpustakaan->getAllKategori();

// Jika request untuk download template
if (isset($_GET['download_template'])) {
    // Buat spreadsheet baru
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set header
    $sheet->setCellValue('A1', 'TEMPLATE IMPORT BUKU');
    $sheet->mergeCells('A1:H1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    
    // Set header kolom
    $sheet->setCellValue('A3', 'Judul Buku*');
    $sheet->setCellValue('B3', 'ID Kategori*');
    $sheet->setCellValue('C3', 'Pengarang*');
    $sheet->setCellValue('D3', 'Penerbit*');
    $sheet->setCellValue('E3', 'Tahun Terbit*');
    $sheet->setCellValue('F3', 'ISBN');
    $sheet->setCellValue('G3', 'Jumlah Buku*');
    $sheet->setCellValue('H3', 'Lokasi');
    
    // Contoh data
    $sheet->setCellValue('A4', 'Contoh: Matematika Dasar');
    $sheet->setCellValue('B4', '1');
    $sheet->setCellValue('C4', 'John Doe');
    $sheet->setCellValue('D4', 'Penerbit ABC');
    $sheet->setCellValue('E4', '2023');
    $sheet->setCellValue('F4', '************-93-9');
    $sheet->setCellValue('G4', '5');
    $sheet->setCellValue('H4', 'RAK-A');
    
    // Tambahkan daftar kategori di sheet kedua
    $sheet2 = $spreadsheet->createSheet();
    $sheet2->setTitle('Daftar Kategori');
    $sheet2->setCellValue('A1', 'ID Kategori');
    $sheet2->setCellValue('B1', 'Nama Kategori');
    
    $row = 2;
    foreach ($kategori as $k) {
        $sheet2->setCellValue('A' . $row, $k['id_kategori']);
        $sheet2->setCellValue('B' . $row, $k['nama_kategori']);
        $row++;
    }
    
    // Auto size kolom
    foreach (range('A', 'H') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    $sheet2->getColumnDimension('A')->setAutoSize(true);
    $sheet2->getColumnDimension('B')->setAutoSize(true);
    
    // Style header
    $sheet->getStyle('A3:H3')->getFont()->setBold(true);
    $sheet->getStyle('A3:H3')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');
    $sheet2->getStyle('A1:B1')->getFont()->setBold(true);
    $sheet2->getStyle('A1:B1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');
    
    // Set nama file
    $filename = 'Template_Import_Buku_' . date('Y-m-d') . '.xlsx';
    
    // Header untuk download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Export ke Excel
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
}

// Proses import file
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file_excel'])) {
    try {
        $file = $_FILES['file_excel']['tmp_name'];
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();
        
        $sukses = 0;
        $gagal = 0;
        $error_messages = [];
        
        // Mulai dari baris 4 (setelah header)
        for ($row = 4; $row <= $highestRow; $row++) {
            $data = [
                'judul_buku' => $worksheet->getCellByColumnAndRow(1, $row)->getValue(),
                'id_kategori' => $worksheet->getCellByColumnAndRow(2, $row)->getValue(),
                'pengarang' => $worksheet->getCellByColumnAndRow(3, $row)->getValue(),
                'penerbit' => $worksheet->getCellByColumnAndRow(4, $row)->getValue(),
                'tahun_terbit' => $worksheet->getCellByColumnAndRow(5, $row)->getValue(),
                'isbn' => $worksheet->getCellByColumnAndRow(6, $row)->getValue(),
                'jumlah_buku' => $worksheet->getCellByColumnAndRow(7, $row)->getValue(),
                'lokasi' => $worksheet->getCellByColumnAndRow(8, $row)->getValue()
            ];
            
            // Skip jika baris kosong
            if (empty($data['judul_buku'])) continue;
            
            // Validasi data wajib
            if (empty($data['judul_buku']) || empty($data['id_kategori']) || 
                empty($data['pengarang']) || empty($data['penerbit']) || 
                empty($data['tahun_terbit']) || empty($data['jumlah_buku'])) {
                $gagal++;
                $error_messages[] = "Baris $row: Data wajib tidak lengkap";
                continue;
            }
            
            try {
                if ($perpustakaan->tambahBuku($data)) {
                    $sukses++;
                } else {
                    $gagal++;
                    $error_messages[] = "Baris $row: Gagal menyimpan ke database";
                }
            } catch (Exception $e) {
                $gagal++;
                $error_messages[] = "Baris $row: " . $e->getMessage();
            }
        }
        
        $_SESSION['success'] = "Import selesai. Berhasil: $sukses, Gagal: $gagal";
        if (!empty($error_messages)) {
            $_SESSION['error'] = implode("<br>", $error_messages);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "Gagal memproses file: " . $e->getMessage();
    }
    
    header("Location: import.php");
    exit();
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Import Data Buku</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Import Buku</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Form Import Data</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($_SESSION['success'])): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <?= $_SESSION['success'] ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                <?php unset($_SESSION['success']); ?>
                            <?php endif; ?>

                            <?php if (isset($_SESSION['error'])): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <?= $_SESSION['error'] ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                <?php unset($_SESSION['error']); ?>
                            <?php endif; ?>

                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> Petunjuk Import:</h5>
                                <ol>
                                    <li>Download template Excel yang sudah disediakan</li>
                                    <li>Isi data sesuai dengan format yang ada</li>
                                    <li>Kolom yang bertanda * wajib diisi</li>
                                    <li>Lihat sheet "Daftar Kategori" untuk ID Kategori yang tersedia</li>
                                    <li>Upload file yang sudah diisi</li>
                                </ol>
                            </div>

                            <a href="template_buku.xlsx" class="btn btn-success mb-3">
                                <i class="fas fa-download"></i> Download Template Excel
                            </a>

                            <form action="" method="POST" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label for="file_excel">File Excel</label>
                                    <input type="file" class="form-control" id="file_excel" name="file_excel" accept=".xlsx,.xls" required>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload"></i> Import Data
                                    </button>
                                    <a href="index.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Kembali
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Daftar Kategori</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID Kategori</th>
                                            <th>Nama Kategori</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($kategori as $k): ?>
                                        <tr>
                                            <td><?= $k['id_kategori']; ?></td>
                                            <td><?= htmlspecialchars($k['nama_kategori']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>

<script>
$(function() {
    $('table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "pageLength": 5,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>
