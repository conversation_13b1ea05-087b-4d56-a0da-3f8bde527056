<?php
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/PeriodeAktif.php';

if (!isset($_GET['kelas_id'])) {
    echo '<div class="text-center p-3">Si<PERSON>an pilih kelas terlebih dahulu</div>';
    exit;
}

$kelas_id = $_GET['kelas_id'];
$tahun_ajaran = $_GET['tahun_ajaran'] ?? null;
$semester = $_GET['semester'] ?? null;

$siswa = new Siswa();
$siswaPeriode = new SiswaPeriode();

// If academic period is specified, use period-based retrieval
if ($tahun_ajaran && $semester) {
    $result = $siswaPeriode->getSiswaByPeriode($tahun_ajaran, $semester, $kelas_id);
} else {
    // Fallback to current period or default method
    $periode = new PeriodeAktif();
    if ($periode->getActive()) {
        $result = $siswaPeriode->getSiswaByPeriode($periode->tahun_ajaran, $periode->semester, $kelas_id);
    } else {
        $result = $siswa->getByKelas($kelas_id);
    }
}

if (!$result || $result->rowCount() == 0) {
    echo '<div class="text-center p-3">Tidak ada data siswa untuk kelas ini pada periode yang dipilih</div>';
    exit;
}
?>

<div class="mb-3">
    <div class="btn-group">
        <button type="button" class="btn btn-success btn-sm quick-select" data-status="hadir" title="Set semua siswa hadir">
            <i class="fas fa-check"></i> Set Semua Hadir
        </button>
        <button type="button" class="btn btn-warning btn-sm quick-select" data-status="sakit" title="Set semua siswa sakit">
            <i class="fas fa-procedures"></i> Set Semua Sakit
        </button>
        <button type="button" class="btn btn-info btn-sm quick-select" data-status="izin" title="Set semua siswa izin">
            <i class="fas fa-envelope"></i> Set Semua Izin
        </button>
        <button type="button" class="btn btn-danger btn-sm quick-select" data-status="alpha" title="Set semua siswa alpha">
            <i class="fas fa-times"></i> Set Semua Alpha
        </button>
    </div>
    <small class="text-muted d-block mt-1">Klik tombol di atas untuk mengatur status kehadiran semua siswa sekaligus</small>
</div>

<table class="table table-striped">
    <thead>
        <tr>
            <th width="5%">No</th>
            <th width="15%">NIS</th>
            <th>Nama Siswa</th>
            <th width="20%">Status Kehadiran</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $no = 1;
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            // Handle different column names from different queries
            $student_id = isset($row['siswa_id']) ? $row['siswa_id'] : $row['id'];
        ?>
            <tr>
                <td><?php echo $no++; ?></td>
                <td><?php echo htmlspecialchars($row['nis']); ?></td>
                <td><?php echo htmlspecialchars($row['nama_siswa']); ?></td>
                <td>
                    <select name="status[<?php echo $student_id; ?>]" class="form-select form-select-sm status-select">
                        <option value="">Pilih Status</option>
                        <option value="hadir">Hadir</option>
                        <option value="sakit">Sakit</option>
                        <option value="izin">Izin</option>
                        <option value="alpha">Alpha</option>
                    </select>
                </td>
            </tr>
        <?php
        }
        ?>
    </tbody>
</table>
