<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/SiswaPeriode.php';
require_once __DIR__ . '/PeriodeAktif.php';

class Siswa {
    private $conn;
    private $table_name = "siswa";

    public $id;
    public $nis;
    public $nama_siswa;
    public $jenis_kelamin;
    public $kelas_id;
    public $nama_kelas;
    public $alamat;
    public $no_telp;
    public $tahun_ajaran_current;
    public $semester_current;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get total count of students
    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAll() {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                ORDER BY CAST(s.nis AS UNSIGNED) ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get students by academic period (new method)
     */
    public function getByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->getSiswaByPeriode($tahun_ajaran, $semester, $kelas_id);
    }

    /**
     * Get current students (uses current period)
     */
    public function getCurrentStudents($kelas_id = null) {
        $periode = new PeriodeAktif();
        if ($periode->getActive()) {
            return $this->getByPeriode($periode->tahun_ajaran, $periode->semester, $kelas_id);
        }

        // Fallback to original method if no active period
        return $kelas_id ? $this->getByKelas($kelas_id) : $this->getAll();
    }

    public function getByKelas($kelas_id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.kelas_id = :kelas_id
                ORDER BY nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    public function getSiswaById($id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByKelasOld($kelas_id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.kelas_id = :kelas_id
                ORDER BY CAST(s.nis AS UNSIGNED) ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        try {
            $this->conn->beginTransaction();

            // Get current active period - with fallback
            $current_tahun_ajaran = date('Y') . '/' . (date('Y') + 1);
            $current_semester = '1';

            try {
                $periode = new PeriodeAktif();
                $periode->getActive();
                if ($periode->tahun_ajaran) {
                    $current_tahun_ajaran = $periode->tahun_ajaran;
                }
                if ($periode->semester) {
                    $current_semester = $periode->semester;
                }
            } catch (Exception $e) {
                // Use default values if periode aktif fails
                error_log("PeriodeAktif error, using defaults: " . $e->getMessage());
            }

            // Insert into siswa table with current period reference
            $query = "INSERT INTO " . $this->table_name . "
                    (nis, nama_siswa, jenis_kelamin, kelas_id, alamat, no_telp, tahun_ajaran_current, semester_current)
                    VALUES (:nis, :nama_siswa, :jenis_kelamin, :kelas_id, :alamat, :no_telp, :tahun_ajaran_current, :semester_current)";

            $stmt = $this->conn->prepare($query);

            // Sanitize input
            $this->nis = htmlspecialchars(strip_tags($this->nis));
            $this->nama_siswa = htmlspecialchars(strip_tags($this->nama_siswa));
            $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
            $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
            $this->alamat = htmlspecialchars(strip_tags($this->alamat));
            $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));

            // Bind parameters
            $stmt->bindParam(":nis", $this->nis);
            $stmt->bindParam(":nama_siswa", $this->nama_siswa);
            $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
            $stmt->bindParam(":kelas_id", $this->kelas_id);
            $stmt->bindParam(":alamat", $this->alamat);
            $stmt->bindParam(":no_telp", $this->no_telp);
            $stmt->bindParam(":tahun_ajaran_current", $current_tahun_ajaran);
            $stmt->bindParam(":semester_current", $current_semester);

            if (!$stmt->execute()) {
                $errorInfo = $stmt->errorInfo();
                throw new Exception("Failed to create student record: " . $errorInfo[2]);
            }

            // Get the new student ID
            $siswa_id = $this->conn->lastInsertId();

            // Create period entry - with fallback
            try {
                $siswaPeriode = new SiswaPeriode();
                if (!$siswaPeriode->addSiswaToNewPeriode($siswa_id, $this->kelas_id, $current_tahun_ajaran, $current_semester)) {
                    throw new Exception("Failed to create student period record");
                }
            } catch (Exception $e) {
                // If period creation fails, log it but don't fail the whole transaction
                error_log("Period creation failed, student created without period: " . $e->getMessage());
                // Student is still created, just without period entry
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            // Log error for debugging
            error_log("Siswa create error: " . $e->getMessage());
            return false;
        }
    }

    public function getOne() {
        $data = $this->getSiswaById($this->id);
        if ($data) {
            $this->nis = $data['nis'];
            $this->nama_siswa = $data['nama_siswa'];
            $this->jenis_kelamin = $data['jenis_kelamin'];
            $this->kelas_id = $data['kelas_id'];
            $this->alamat = $data['alamat'];
            $this->no_telp = $data['no_telp'];
            $this->nama_kelas = $data['nama_kelas'];
            $this->tahun_ajaran_current = $data['tahun_ajaran_current'] ?? null;
            $this->semester_current = $data['semester_current'] ?? null;
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET nis = :nis,
                    nama_siswa = :nama_siswa,
                    jenis_kelamin = :jenis_kelamin,
                    kelas_id = :kelas_id,
                    alamat = :alamat,
                    no_telp = :no_telp
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nis = htmlspecialchars(strip_tags($this->nis));
        $this->nama_siswa = htmlspecialchars(strip_tags($this->nama_siswa));
        $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->alamat = htmlspecialchars(strip_tags($this->alamat));
        $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":nis", $this->nis);
        $stmt->bindParam(":nama_siswa", $this->nama_siswa);
        $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":alamat", $this->alamat);
        $stmt->bindParam(":no_telp", $this->no_telp);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getByNIS($nis) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.nis = :nis";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nis", $nis);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get student by NIS for a specific academic period
     */
    public function getByNISAndPeriode($nis, $tahun_ajaran, $semester) {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->getSiswaByNISAndPeriode($nis, $tahun_ajaran, $semester);
    }

    /**
     * Get all academic periods for a student
     */
    public function getStudentPeriodes($siswa_id) {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->getPeriodesBySiswa($siswa_id);
    }

    /**
     * Add student to new academic period
     */
    public function addToNewPeriode($siswa_id, $kelas_id, $tahun_ajaran, $semester) {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->addSiswaToNewPeriode($siswa_id, $kelas_id, $tahun_ajaran, $semester);
    }

    /**
     * Get available academic periods
     */
    public function getAvailablePeriodes() {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->getAvailablePeriodes();
    }

    /**
     * Get count of students by period
     */
    public function getCountByPeriode($tahun_ajaran, $semester, $kelas_id = null) {
        $siswaPeriode = new SiswaPeriode();
        return $siswaPeriode->getCountByPeriode($tahun_ajaran, $semester, $kelas_id);
    }

    public function getByNama($nama) {
        $search_term = "%$nama%";
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.nama_siswa LIKE :nama
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nama", $search_term);
        $stmt->execute();

        return $stmt;
    }

    public function importFromArray($data) {
        try {
            $this->conn->beginTransaction();

            // Get current active period
            $periode = new PeriodeAktif();
            $periode->getActive();
            $current_tahun_ajaran = $periode->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
            $current_semester = $periode->semester ?: '1';

            $query = "INSERT INTO " . $this->table_name . "
                    (nis, nama_siswa, jenis_kelamin, kelas_id, alamat, no_telp, tahun_ajaran_current, semester_current)
                    VALUES (:nis, :nama_siswa, :jenis_kelamin, :kelas_id, :alamat, :no_telp, :tahun_ajaran_current, :semester_current)";

            $stmt = $this->conn->prepare($query);
            $success = true;

            foreach($data as $row) {
                // Sanitize input
                $nis = htmlspecialchars(strip_tags($row['nis']));
                $nama_siswa = htmlspecialchars(strip_tags($row['nama_siswa']));
                $jenis_kelamin = htmlspecialchars(strip_tags($row['jenis_kelamin']));
                $kelas_id = htmlspecialchars(strip_tags($row['kelas_id']));
                $alamat = htmlspecialchars(strip_tags($row['alamat']));
                $no_telp = htmlspecialchars(strip_tags($row['no_telp']));

                // Bind parameters
                $stmt->bindParam(":nis", $nis);
                $stmt->bindParam(":nama_siswa", $nama_siswa);
                $stmt->bindParam(":jenis_kelamin", $jenis_kelamin);
                $stmt->bindParam(":kelas_id", $kelas_id);
                $stmt->bindParam(":alamat", $alamat);
                $stmt->bindParam(":no_telp", $no_telp);
                $stmt->bindParam(":tahun_ajaran_current", $current_tahun_ajaran);
                $stmt->bindParam(":semester_current", $current_semester);

                if(!$stmt->execute()) {
                    $success = false;
                    break;
                }

                // Get the new student ID and create period entry
                $siswa_id = $this->conn->lastInsertId();

                // Create period entry
                $siswaPeriode = new SiswaPeriode();
                if (!$siswaPeriode->addSiswaToNewPeriode($siswa_id, $kelas_id, $current_tahun_ajaran, $current_semester)) {
                    $success = false;
                    break;
                }
            }

            if ($success) {
                $this->conn->commit();
            } else {
                $this->conn->rollback();
            }

            return $success;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    /**
     * Update current period reference for a student
     */
    public function updateCurrentPeriod($siswa_id, $tahun_ajaran, $semester) {
        $query = "UPDATE " . $this->table_name . "
                 SET tahun_ajaran_current = :tahun_ajaran,
                     semester_current = :semester
                 WHERE id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":semester", $semester);

        return $stmt->execute();
    }

    /**
     * Create student without automatic period creation (for historical registration)
     */
    public function createWithoutPeriod() {
        try {
            // Insert into siswa table without period reference initially
            $query = "INSERT INTO " . $this->table_name . "
                    (nis, nama_siswa, jenis_kelamin, kelas_id, alamat, no_telp)
                    VALUES (:nis, :nama_siswa, :jenis_kelamin, :kelas_id, :alamat, :no_telp)";

            $stmt = $this->conn->prepare($query);

            // Sanitize input
            $this->nis = htmlspecialchars(strip_tags($this->nis));
            $this->nama_siswa = htmlspecialchars(strip_tags($this->nama_siswa));
            $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
            $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
            $this->alamat = htmlspecialchars(strip_tags($this->alamat));
            $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));

            // Bind parameters
            $stmt->bindParam(":nis", $this->nis);
            $stmt->bindParam(":nama_siswa", $this->nama_siswa);
            $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
            $stmt->bindParam(":kelas_id", $this->kelas_id);
            $stmt->bindParam(":alamat", $this->alamat);
            $stmt->bindParam(":no_telp", $this->no_telp);

            if (!$stmt->execute()) {
                $errorInfo = $stmt->errorInfo();
                throw new Exception("Failed to create student record: " . $errorInfo[2]);
            }

            return true;
        } catch (Exception $e) {
            error_log("Siswa createWithoutPeriod error: " . $e->getMessage());
            return false;
        }
    }
}
