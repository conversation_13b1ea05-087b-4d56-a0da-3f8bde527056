<?php
/**
 * Server-Compatible Database Restore
 * Alternative restore script that works on shared hosting without exec()
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../template/header.php';

// Hanya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

/**
 * Execute SQL file using PDO (server-compatible alternative to exec())
 */
function executeSqlFile($pdo, $sql_content) {
    try {
        // Set longer execution time for large restores
        @set_time_limit(300);
        @ini_set('memory_limit', '512M');

        // Set SQL mode to handle data truncation more gracefully
        try {
            $pdo->exec("SET sql_mode = ''");
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            error_log("SQL mode set to permissive for data truncation handling");
        } catch (Exception $mode_error) {
            error_log("Warning: Could not set SQL mode: " . $mode_error->getMessage());
        }
        
        // Split SQL content into individual statements
        $statements = [];
        $current_statement = '';
        $lines = explode("\n", $sql_content);
        $delimiter = ';';
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines and comments
            if (empty($line) || strpos($line, '--') === 0 || strpos($line, '/*') === 0) {
                continue;
            }
            
            // Handle DELIMITER changes (for stored procedures)
            if (preg_match('/^DELIMITER\s+(.+)$/i', $line, $matches)) {
                $delimiter = trim($matches[1]);
                continue;
            }
            
            $current_statement .= $line . "\n";
            
            // Check if statement ends with current delimiter
            if (substr(rtrim($line), -strlen($delimiter)) === $delimiter) {
                // Remove the delimiter and add to statements
                $stmt = rtrim(substr($current_statement, 0, -strlen($delimiter)));
                if (!empty(trim($stmt))) {
                    $statements[] = trim($stmt);
                }
                $current_statement = '';
            }
        }
        
        // Add any remaining statement
        if (!empty(trim($current_statement))) {
            $statements[] = trim($current_statement);
        }
        
        // Execute statements in batches
        $executed_count = 0;
        $errors = [];
        $batch_size = 50; // Process in smaller batches for server compatibility
        
        // Process statements in batches
        $total_statements = count($statements);
        for ($i = 0; $i < $total_statements; $i += $batch_size) {
            $batch = array_slice($statements, $i, $batch_size);
            
            try {
                // Start transaction only if not already in one
                $transaction_started = false;
                if (!$pdo->inTransaction()) {
                    $pdo->beginTransaction();
                    $transaction_started = true;
                }

                foreach ($batch as $statement) {
                    if (empty($statement)) continue;

                    try {
                        $pdo->exec($statement);
                        $executed_count++;

                        // Check if statement ended the transaction (DDL statements do this)
                        if ($transaction_started && !$pdo->inTransaction()) {
                            // Transaction was auto-committed, start a new one for remaining statements
                            $pdo->beginTransaction();
                        }

                    } catch (PDOException $e) {
                        $error_msg = $e->getMessage();
                        $errors[] = "Statement {$executed_count}: " . $error_msg;

                        // Handle different types of errors
                        $non_critical_errors = [
                            'already exists',
                            'Duplicate entry',
                            'Data truncated',
                            'Incorrect string value',
                            'Out of range value'
                        ];

                        $is_critical = true;
                        foreach ($non_critical_errors as $pattern) {
                            if (strpos($error_msg, $pattern) !== false) {
                                $is_critical = false;
                                break;
                            }
                        }

                        if ($is_critical) {
                            throw $e; // Re-throw critical errors
                        } else {
                            error_log("Non-critical error (continuing): " . $error_msg);
                        }
                    }
                }

                // Commit only if we have an active transaction
                if ($pdo->inTransaction()) {
                    $pdo->commit();
                }
                
                // Small delay between batches to prevent server timeout
                if ($i + $batch_size < $total_statements) {
                    usleep(100000); // 0.1 second delay
                }
                
            } catch (Exception $batch_error) {
                if ($pdo->inTransaction()) {
                    $pdo->rollback();
                }
                return [
                    'success' => false,
                    'error' => "Batch error at statement {$executed_count}: " . $batch_error->getMessage(),
                    'executed_count' => $executed_count
                ];
            }
        }
        
        // Restore SQL mode and foreign key checks
        try {
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            $pdo->exec("SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");
            error_log("SQL mode and foreign key checks restored");
        } catch (Exception $restore_error) {
            error_log("Warning: Could not restore SQL mode: " . $restore_error->getMessage());
        }

        $result = [
            'success' => true,
            'executed_count' => $executed_count,
            'total_statements' => $total_statements
        ];

        if (!empty($errors)) {
            $result['warnings'] = $errors;
        }

        return $result;
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'executed_count' => $executed_count ?? 0
        ];
    }
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle file upload
    if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === 0) {
        $file_tmp = $_FILES['backup_file']['tmp_name'];
        $file_name = $_FILES['backup_file']['name'];
        
        // Validasi file extension
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if ($file_ext === 'sql') {
            $backup_dir = __DIR__ . '/../database/backups/';
            $target_path = $backup_dir . $file_name;

            // Buat direktori jika belum ada
            if (!file_exists($backup_dir)) {
                mkdir($backup_dir, 0777, true);
            }

            // Cek jika file sudah ada
            if (file_exists($target_path)) {
                $error = "File dengan nama yang sama sudah ada. Silakan gunakan nama file yang berbeda.";
            } else {
                // Pindahkan file
                if (move_uploaded_file($file_tmp, $target_path)) {
                    $_SESSION['success'] = "File backup berhasil diunggah: {$file_name}";
                } else {
                    $error = "Gagal mengunggah file backup.";
                }
            }
        } else {
            $error = "Tipe file tidak valid. Silakan unggah file .sql";
        }
    }
    // Handle restore from existing backup
    elseif (isset($_POST['restore_file'])) {
        $backup_dir = __DIR__ . '/../database/backups/';
        $file_name = basename($_POST['restore_file']);
        $file_path = $backup_dir . $file_name;

        // Validasi file exists dan path
        if (file_exists($file_path) && is_file($file_path)) {
            // Validasi file extension
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            if ($file_ext === 'sql') {
                try {
                    // Get database connection
                    $database = new Database();
                    $pdo = $database->getConnection();

                    // Pre-restoration cleanup to prevent conflicts
                    try {
                        // Check if migration procedures exist, if not create them
                        $migration_file = __DIR__ . '/../database/migration_fix_restore_conflicts.sql';
                        if (file_exists($migration_file)) {
                            $migration_sql = file_get_contents($migration_file);
                            executeSqlFile($pdo, $migration_sql);
                            error_log("Migration procedures created successfully");
                        }
                        
                        // Run pre-restoration cleanup
                        $pdo->exec("CALL PreRestorationCleanup()");
                        error_log("Pre-restoration cleanup completed");
                        
                    } catch (Exception $cleanup_error) {
                        error_log("Pre-restoration cleanup warning: " . $cleanup_error->getMessage());
                        // Continue with restoration even if cleanup fails
                    }

                    // Read and execute SQL file using PDO
                    $sql_content = file_get_contents($file_path);
                    if ($sql_content === false) {
                        throw new Exception("Gagal membaca file backup");
                    }
                    
                    error_log("Starting server-compatible restore for file: {$file_name}");
                    
                    // Execute the restore using our custom function
                    $result = executeSqlFile($pdo, $sql_content);
                    
                    if ($result['success']) {
                        // Post-restoration validation
                        try {
                            $validation_result = $pdo->query("CALL PostRestorationValidation()")->fetch(PDO::FETCH_ASSOC);
                            error_log("Post-restoration validation: " . json_encode($validation_result));
                            
                            $success_msg = "Database berhasil dipulihkan dari {$file_name}! ";
                            $success_msg .= "Dieksekusi: {$result['executed_count']}/{$result['total_statements']} statement. ";
                            
                            if (isset($validation_result['total_tables'])) {
                                $success_msg .= "Validasi: {$validation_result['total_tables']} tabel, ";
                                $success_msg .= "{$validation_result['total_views']} view. ";
                                $success_msg .= "Status: {$validation_result['validation_result']}";
                            }
                            
                            if (!empty($result['warnings'])) {
                                $success_msg .= " (dengan " . count($result['warnings']) . " warning)";
                            }
                            
                            $_SESSION['success'] = $success_msg;
                            
                        } catch (Exception $validation_error) {
                            error_log("Post-restoration validation warning: " . $validation_error->getMessage());
                            $_SESSION['success'] = "Database berhasil dipulihkan dari {$file_name}! " .
                                                 "Dieksekusi: {$result['executed_count']}/{$result['total_statements']} statement.";
                        }
                    } else {
                        $error_msg = $result['error'];
                        error_log("Database restore error: " . $error_msg);
                        $_SESSION['error'] = "Gagal memulihkan database. Error: " . $error_msg;
                    }
                    
                } catch (Exception $restore_error) {
                    error_log("Server restore error: " . $restore_error->getMessage());
                    $_SESSION['error'] = "Gagal memulihkan database. Error: " . $restore_error->getMessage();
                }
            } else {
                $_SESSION['error'] = "Format nama file backup tidak valid: {$file_name}";
            }
        } else {
            $_SESSION['error'] = "File backup tidak ditemukan atau path tidak valid: {$file_path}";
        }
        header("Location: restore_server_compatible.php");
        exit();
    } else {
        $error = "Silakan pilih file backup untuk diunggah.";
    }
}

// Check for session messages
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    unset($_SESSION['success']);
}
if (isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Pulihkan Database (Server Compatible)</h5>
                    <div>
                        <a href="restore.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-desktop"></i> Versi Desktop
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-server"></i> Versi Server Compatible</h6>
                        <p>Versi ini dirancang khusus untuk server hosting yang menonaktifkan fungsi <code>exec()</code>. 
                        Menggunakan PDO untuk menjalankan SQL secara langsung dengan fitur:</p>
                        <ul class="mb-0">
                            <li>Pemrosesan batch untuk mencegah timeout</li>
                            <li>Error handling yang lebih baik</li>
                            <li>Kompatibilitas dengan shared hosting</li>
                            <li>Pre-restoration cleanup otomatis</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Peringatan: Memulihkan database akan menimpa semua data yang ada saat ini.
                        Pastikan Anda telah membuat backup terlebih dahulu.
                    </div>

                    <div class="mb-4">
                        <h6>Unggah File Backup</h6>
                        <p class="text-muted">Pilih file backup (.sql) untuk diunggah ke sistem.</p>
                        <form method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <input type="file" class="form-control" name="backup_file" accept=".sql" required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Unggah File
                            </button>
                        </form>
                    </div>

                    <h6 class="mt-4">Backup yang Tersedia</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>File Backup</th>
                                    <th>Ukuran</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $backup_dir = __DIR__ . '/../database/backups/';
                                $backup_files = glob($backup_dir . '*.sql');
                                
                                if (!empty($backup_files)) {
                                    rsort($backup_files); // Urutkan dari yang terbaru
                                    foreach ($backup_files as $file) {
                                        $filename = basename($file);
                                        $size = filesize($file);
                                        $created = date("d-m-Y H:i:s", filemtime($file));
                                        
                                        echo "<tr>";
                                        echo "<td>{$filename}</td>";
                                        echo "<td>" . number_format($size / 1024, 2) . " KB</td>";
                                        echo "<td>{$created}</td>";
                                        echo "<td>";
                                        
                                        // Tombol Restore
                                        echo "<form method='post' style='display: inline;' onsubmit='return confirm(\"Peringatan: Ini akan menimpa database saat ini dengan pemrosesan batch. Proses mungkin memakan waktu lebih lama. Apakah Anda yakin ingin melanjutkan?\")'>";
                                        echo "<input type='hidden' name='restore_file' value='" . htmlspecialchars($filename, ENT_QUOTES) . "'>";
                                        echo "<button type='submit' class='btn btn-sm btn-warning me-2'>";
                                        echo "<i class='fas fa-undo'></i> Pulihkan</button>";
                                        echo "</form>";

                                        // Tombol Download
                                        echo "<a href='/absen/database/backups/{$filename}' class='btn btn-sm btn-primary me-2' download>";
                                        echo "<i class='fas fa-download'></i> Unduh</a>";
                                        
                                        echo "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='4' class='text-center'>Tidak ada file backup tersedia.</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
