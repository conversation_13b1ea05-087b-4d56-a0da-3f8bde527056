<?php
/**
 * <PERSON><PERSON><PERSON> to fix backup files that contain incorrect INSERT statements for views
 * This script removes INSERT statements for views from backup files
 */

// Only require database config if running from web interface
if (php_sapi_name() !== 'cli') {
    require_once '../config/database.php';
}

function fixBackupFile($backup_file_path) {
    if (!file_exists($backup_file_path)) {
        throw new Exception("Backup file tidak ditemukan: $backup_file_path");
    }
    
    // Read the backup file
    $content = file_get_contents($backup_file_path);
    if ($content === false) {
        throw new Exception("Gagal membaca file backup");
    }
    
    // Create a temporary file for the fixed content
    $temp_file = $backup_file_path . '.temp';
    $fp = fopen($temp_file, 'w');
    if (!$fp) {
        throw new Exception("Gagal membuat file temporary");
    }
    
    // Split content into lines
    $lines = explode("\n", $content);
    $skip_insert = false;
    $current_table = '';
    
    // List of known views that should not have INSERT statements
    $views = ['v_siswa_all_periods', 'v_siswa_current'];
    
    foreach ($lines as $line) {
        $line_trimmed = trim($line);
        
        // Check if this is a DROP statement for a view
        if (preg_match('/^DROP TABLE IF EXISTS `(v_[^`]+)`/', $line_trimmed, $matches)) {
            $current_table = $matches[1];
            if (in_array($current_table, $views)) {
                // Change DROP TABLE to DROP VIEW for views
                $line = str_replace('DROP TABLE IF EXISTS', 'DROP VIEW IF EXISTS', $line);
            }
        }
        
        // Check if this is a CREATE statement for a view
        if (preg_match('/^CREATE ALGORITHM=/', $line_trimmed)) {
            $skip_insert = true; // We're in a view definition, skip any INSERT statements that follow
        }
        
        // Check if this is an INSERT statement for a view
        if (preg_match('/^INSERT INTO `(v_[^`]+)`/', $line_trimmed, $matches)) {
            $table_name = $matches[1];
            if (in_array($table_name, $views)) {
                // Skip this INSERT statement for views
                continue;
            }
        }
        
        // Check if we're starting a new table/view section
        if (preg_match('/^DROP (TABLE|VIEW) IF EXISTS `([^`]+)`/', $line_trimmed, $matches)) {
            $current_table = $matches[2];
            $skip_insert = in_array($current_table, $views);
        }
        
        // Write the line to the temp file
        fwrite($fp, $line . "\n");
    }
    
    fclose($fp);
    
    // Replace the original file with the fixed one
    if (!rename($temp_file, $backup_file_path)) {
        unlink($temp_file);
        throw new Exception("Gagal mengganti file backup dengan versi yang diperbaiki");
    }
    
    return true;
}

// Check if this is called via command line or web
if (php_sapi_name() === 'cli') {
    // Command line usage
    if ($argc < 2) {
        echo "Usage: php fix_backup.php <backup_file_path>\n";
        exit(1);
    }
    
    $backup_file = $argv[1];
    
    try {
        fixBackupFile($backup_file);
        echo "Backup file berhasil diperbaiki: $backup_file\n";
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
} else {
    // Web interface
    $message = '';
    $error = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_backup'])) {
        $backup_file = $_POST['backup_file'];
        
        try {
            fixBackupFile($backup_file);
            $message = "Backup file berhasil diperbaiki: " . basename($backup_file);
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    }
    
    // Get list of backup files
    $backup_dir = __DIR__ . '/../database/backups/';
    $backup_files = [];
    
    if (is_dir($backup_dir)) {
        $files = scandir($backup_dir);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
                $backup_files[] = $backup_dir . $file;
            }
        }
    }
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Fix Backup Files</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h2>Fix Backup Files</h2>
            <p>Tool ini memperbaiki file backup yang mengandung INSERT statements untuk views.</p>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="backup_file" class="form-label">Pilih Backup File:</label>
                    <select name="backup_file" id="backup_file" class="form-select" required>
                        <option value="">-- Pilih File Backup --</option>
                        <?php foreach ($backup_files as $file): ?>
                            <option value="<?php echo htmlspecialchars($file); ?>">
                                <?php echo htmlspecialchars(basename($file)); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="fix_backup" class="btn btn-primary">Fix Backup File</button>
                <a href="backup.php" class="btn btn-secondary">Kembali ke Backup</a>
            </form>
        </div>
    </body>
    </html>
    <?php
}
?>
