<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

$rpp = new Rpp();
$rppQuestion = new RppQuestion();

// Ambil daftar RPP milik guru yang login
$rpp_list = $rpp->getAllByGuru($guru_id);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Generate Soal dari RPP</h5>
            <div>
                <a href="multi_rpp_generate.php" class="btn btn-primary me-2">
                    <i class="fas fa-layer-group"></i> Multi-RPP Exam
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar RPP
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Info Bar -->
            <div class="alert alert-info mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2"><i class="fas fa-magic"></i> Generate Soal dari RPP</h6>
                        <p class="mb-0">Pilih RPP untuk membuat soal dengan AI atau manual.
                        <button type="button" class="btn btn-link p-0 text-decoration-none" data-bs-toggle="modal" data-bs-target="#featuresModal">
                            <i class="fas fa-info-circle"></i> Lihat fitur lengkap
                        </button>
                        </p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#guideModal">
                            <i class="fas fa-question-circle"></i> Panduan
                        </button>
                        <a href="multi_rpp_generate.php" class="btn btn-success btn-sm">
                            <i class="fas fa-layer-group"></i> Multi-RPP
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">

                    <?php if ($rpp_list && $rpp_list->rowCount() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Mata Pelajaran</th>
                                        <th>Kelas</th>
                                        <th>Materi Pokok</th>
                                        <th>Semester</th>
                                        <th>Tahun Ajaran</th>
                                        <th>Soal Tersimpan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($row = $rpp_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <?php 
                                        $question_count = $rppQuestion->getCountByRppId($row['id']);
                                        ?>
                                        <tr>
                                            <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                            <td><?= htmlspecialchars($row['nama_kelas']) ?></td>
                                            <td><?= htmlspecialchars($row['materi_pokok']) ?></td>
                                            <td><?= htmlspecialchars($row['semester']) ?></td>
                                            <td><?= htmlspecialchars($row['tahun_ajaran']) ?></td>
                                            <td>
                                                <span class="badge bg-info"><?= $question_count ?> soal</span>
                                                <?php if ($question_count > 0): ?>
                                                    <a href="questions_list.php?rpp_id=<?= $row['id'] ?>" class="btn btn-sm btn-outline-primary ms-1">
                                                        <i class="fas fa-eye"></i> Lihat
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="configure_generation.php?rpp_id=<?= $row['id'] ?>" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-magic"></i> Generate AI
                                                    </a>
                                                    <a href="manual_questions.php?rpp_id=<?= $row['id'] ?>" class="btn btn-success btn-sm">
                                                        <i class="fas fa-plus"></i> Manual
                                                    </a>
                                                    <a href="view.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" title="Lihat RPP">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> Belum Ada RPP</h5>
                            <p class="mb-0">Anda belum memiliki RPP. Silakan <a href="create.php" class="alert-link">buat RPP baru</a> terlebih dahulu sebelum dapat generate soal.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Modal -->
<div class="modal fade" id="featuresModal" tabindex="-1" aria-labelledby="featuresModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="featuresModalLabel">
                    <i class="fas fa-star"></i> Fitur Generate Soal
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-robot"></i> Generate Soal AI:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Soal Pilihan Ganda (A-B, A-C, A-D, A-E)</li>
                            <li><i class="fas fa-check text-success"></i> Soal Essay</li>
                            <li><i class="fas fa-check text-success"></i> Tingkat Kesulitan Regular</li>
                            <li><i class="fas fa-check text-success"></i> Tingkat HOTS (Mudah, Sedang, Tinggi)</li>
                            <li><i class="fas fa-check text-success"></i> Maksimal 10 soal per generate</li>
                            <li><i class="fas fa-check text-success"></i> Kunci jawaban otomatis</li>
                            <li><i class="fas fa-check text-success"></i> Preview sebelum menyimpan</li>
                        </ul>

                        <h6 class="text-warning mt-3"><i class="fas fa-edit"></i> Soal Manual:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-warning"></i> Tambah soal manual dengan form</li>
                            <li><i class="fas fa-check text-warning"></i> Bulk creation (multiple soal)</li>
                            <li><i class="fas fa-check text-warning"></i> Analisis tingkat kesulitan AI</li>
                            <li><i class="fas fa-check text-warning"></i> Saran perbaikan dari AI</li>
                            <li><i class="fas fa-check text-warning"></i> Upgrade soal ke HOTS</li>
                            <li><i class="fas fa-check text-warning"></i> Buat soal lebih unik</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary"><i class="fas fa-layer-group"></i> Multi-RPP Exam <span class="badge bg-success">NEW</span>:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-primary"></i> Ujian komprehensif multi-chapter</li>
                            <li><i class="fas fa-check text-primary"></i> Distribusi soal proporsional</li>
                            <li><i class="fas fa-check text-primary"></i> Integrasi antar chapter</li>
                            <li><i class="fas fa-check text-primary"></i> Kisi-kisi ujian otomatis</li>
                            <li><i class="fas fa-check text-primary"></i> Mapping Bloom's Taxonomy</li>
                            <li><i class="fas fa-check text-primary"></i> Export PDF/Word</li>
                            <li><i class="fas fa-check text-primary"></i> Maksimal 30 soal per ujian</li>
                        </ul>

                        <div class="alert alert-info small mt-3">
                            <i class="fas fa-info-circle"></i>
                            <strong>Catatan:</strong> Pastikan API key Gemini sudah dikonfigurasi untuk menggunakan fitur AI.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="multi_rpp_generate.php" class="btn btn-primary">
                    <i class="fas fa-layer-group"></i> Coba Multi-RPP
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Guide Modal -->
<div class="modal fade" id="guideModal" tabindex="-1" aria-labelledby="guideModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="guideModalLabel">
                    <i class="fas fa-question-circle"></i> Panduan Penggunaan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-robot"></i> Generate AI:</h6>
                        <ol class="small">
                            <li>Pilih RPP yang akan digunakan</li>
                            <li>Klik tombol "Generate AI"</li>
                            <li>Atur konfigurasi soal (jenis, jumlah, tingkat kesulitan)</li>
                            <li>Preview hasil generate</li>
                            <li>Pilih soal yang akan disimpan</li>
                        </ol>

                        <h6 class="text-warning mt-3"><i class="fas fa-edit"></i> Soal Manual:</h6>
                        <ol class="small">
                            <li>Pilih RPP yang akan digunakan</li>
                            <li>Klik tombol "Manual"</li>
                            <li>Isi form soal (bisa multiple)</li>
                            <li>Gunakan analisis & perbaikan AI</li>
                            <li>Simpan semua soal</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary"><i class="fas fa-layer-group"></i> Multi-RPP Exam:</h6>
                        <ol class="small">
                            <li>Klik tombol "Multi-RPP"</li>
                            <li>Pilih 2 atau lebih RPP</li>
                            <li>Atur konfigurasi ujian</li>
                            <li>Atur distribusi soal per chapter</li>
                            <li>Generate soal komprehensif</li>
                            <li>Generate kisi-kisi ujian</li>
                            <li>Export ke PDF/Word</li>
                        </ol>

                        <div class="alert alert-warning small mt-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Tips:</strong> Gunakan Multi-RPP untuk ujian tengah semester atau ujian akhir yang mencakup beberapa chapter.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="multi_rpp_generate.php" class="btn btn-success">
                    <i class="fas fa-layer-group"></i> Multi-RPP Exam
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}

.card-title {
    color: #495057;
}

.alert-link {
    font-weight: 600;
}

.badge {
    font-size: 0.8rem;
}
</style>

<?php require_once '../template/footer.php'; ?>
