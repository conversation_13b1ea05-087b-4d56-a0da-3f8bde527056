# Sistem Informasi Absensi dan Manajemen Akademik

Aplikasi berbasis web untuk manajemen absensi dan akademik sekolah yang memudahkan pengelolaan data siswa, guru, dan kegiatan pembelajaran.

## Fitur Utama

### 1. <PERSON><PERSON><PERSON><PERSON> Pengguna
- Sistem multi-user (<PERSON><PERSON>, <PERSON>, dan <PERSON>)
- <PERSON><PERSON><PERSON><PERSON> akun dan profil pengguna
- Sistem autentikasi dan autorisasi

### 2. Manajemen Akademik
- Pengelolaan data kelas
- Pengelolaan mata pelajaran
- Manajemen jadwal pelajaran
- Pengelolaan tahun ajaran
- Sistem periode aktif pembelajaran

### 3. Absensi
- Pencatatan kehadiran siswa
- Rekap absensi per kelas
- Laporan absensi detail
- Ekspor data absensi

### 4. Penilaian
- Input nilai siswa
- Rekap nilai per mata pelajaran
- <PERSON><PERSON><PERSON> nilai siswa
- Manajemen tugas

### 5. Data Master
- Manajemen data guru
- Manajemen data siswa
- Manajemen data alumni
- Pengelolaan data kelas
- Pengelolaan mata pelajaran

### 6. <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON> absensi
- Laporan nilai
- Rekap nilai per periode
- Ekspor data dalam berbagai format

### 7. Dashboard
- Dashboard khusus guru dengan jadwal mengajar
- Dashboard admin dengan statistik sistem
- Aksi cepat untuk fungsi-fungsi umum
- Tampilan jadwal pelajaran hari ini

## Struktur Folder

- `/absensi` - Modul pengelolaan absensi
- `/akun` - Manajemen akun pengguna
- `/alumni` - Pengelolaan data alumni
- `/api` - API endpoints
- `/assets` - File statis (CSS, JS, Images)
- `/config` - Konfigurasi sistem
- `/database` - Skema dan migrasi database
- `/guru` - Modul pengelolaan guru
- `/jadwal` - Manajemen jadwal pelajaran
- `/kelas` - Pengelolaan data kelas
- `/laporan` - Generasi laporan
- `/mapel` - Manajemen mata pelajaran
- `/models` - Model database
- `/nilai` - Modul penilaian
- `/siswa` - Pengelolaan data siswa
- `/template` - Template tampilan
- `/tugas` - Manajemen tugas

## Teknologi yang Digunakan

- PHP
- MySQL/MariaDB
- HTML5
- CSS3
- JavaScript
- Composer untuk manajemen dependensi

## Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL/MariaDB
- Web Server (Apache/Nginx)
- Composer

## Instalasi

1. Clone repositori ini ke direktori web server Anda
2. Jalankan `composer install` untuk menginstal dependensi
3. Import skema database dari folder `/database`
4. Sesuaikan konfigurasi database di folder `/config`
5. Akses aplikasi melalui web browser

## Pengembangan

Aplikasi ini dikembangkan dengan memperhatikan:
- Keamanan sistem
- Kemudahan penggunaan
- Responsivitas tampilan
- Skalabilitas sistem

## Pemeliharaan

Folder `/maintenance` tersedia untuk keperluan pemeliharaan sistem, termasuk:
- Backup database
- Log sistem
- Pembaruan sistem
