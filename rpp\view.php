<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';

// Cek jika id tidak ada
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "ID RPP tidak ditemukan.";
    header("Location: index.php");
    exit();
}

$id = $_GET['id'];
$rpp = new Rpp();
$rppKegiatan = new RppKegiatan();

// Ambil data RPP
$rpp_data = $rpp->getOne($id);
if (!$rpp_data) {
    $_SESSION['error'] = "RPP tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Ambil data kegiatan pembelajaran
$stmt_kegiatan = $rppKegiatan->getByRppId($id);
$kegiatan = [
    'pendahuluan' => [],
    'inti' => [],
    'penutup' => []
];

while ($row = $stmt_kegiatan->fetch(PDO::FETCH_ASSOC)) {
    $kegiatan[$row['jenis_kegiatan']][] = $row;
}

// Ambil data tambahan
$mapel = new MataPelajaran();
$kelas = new Kelas();
$guru = new Guru();

// Set ID dan ambil data
$mapel->id = $rpp_data['mapel_id'];
$mapel_data = $mapel->getOne();

$kelas_data = $kelas->getById($rpp_data['kelas_id']);

$guru->id = $rpp_data['guru_id'];
$guru_data = $guru->getOneNew();

// Cek jika data tidak ditemukan
if (!$mapel_data || !$kelas_data || !$guru_data) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: index.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>Detail RPP</h2>
        <div>
            <a href="index.php" class="btn btn-secondary">Kembali</a>
            <a href="edit.php?id=<?= $id ?>" class="btn btn-warning">Edit</a>
            <a href="export_pdf.php?id=<?= $id ?>" class="btn btn-primary" target="_blank">Export PDF</a>
            <a href="export_word.php?id=<?= $id ?>" class="btn btn-success" target="_blank">Export Word</a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="section">
                <h3 class="text-center mb-4">RENCANA PELAKSANAAN PEMBELAJARAN (RPP)</h3>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Nama Sekolah:</strong> <?= htmlspecialchars($rpp_data['nama_sekolah']) ?></p>
                        <p><strong>Mata Pelajaran:</strong> <?= htmlspecialchars($mapel_data['nama_mapel']) ?></p>
                        <p><strong>Kelas:</strong> <?= htmlspecialchars($kelas_data['nama_kelas']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Semester:</strong> <?= htmlspecialchars($rpp_data['semester']) ?></p>
                        <p><strong>Tahun Ajaran:</strong> <?= htmlspecialchars($rpp_data['tahun_ajaran']) ?></p>
                        <p><strong>Alokasi Waktu:</strong> <?= htmlspecialchars($rpp_data['alokasi_waktu']) ?></p>
                        <p><strong>Guru:</strong> <?= htmlspecialchars($guru_data['nama_lengkap']) ?></p>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">A. Tujuan Pembelajaran</div>
                <?= nl2br(htmlspecialchars($rpp_data['tujuan_pembelajaran'])) ?>
            </div>

            <div class="section">
                <div class="section-title">B. Kompetensi Dasar</div>
                <?= nl2br(htmlspecialchars($rpp_data['kompetensi_dasar'])) ?>
            </div>

            <div class="section">
                <div class="section-title">C. Indikator Pencapaian Kompetensi</div>
                <?= nl2br(htmlspecialchars($rpp_data['indikator_pencapaian'])) ?>
            </div>

            <div class="section">
                <div class="section-title">D. Materi Pembelajaran</div>
                <?= nl2br(htmlspecialchars($rpp_data['materi_pembelajaran'])) ?>
            </div>

            <div class="section">
                <div class="section-title">E. Kegiatan Pembelajaran</div>
                
                <div class="mb-3">
                    <strong>1. Pendahuluan</strong>
                    <?php if (isset($kegiatan['pendahuluan'])): ?>
                        <?php foreach ($kegiatan['pendahuluan'] as $item): ?>
                            <div class="kegiatan-item"><?= nl2br(htmlspecialchars($item['deskripsi'])) ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <strong>2. Kegiatan Inti</strong>
                    <?php if (isset($kegiatan['inti'])): ?>
                        <?php foreach ($kegiatan['inti'] as $item): ?>
                            <div class="kegiatan-item"><?= nl2br(htmlspecialchars($item['deskripsi'])) ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <strong>3. Penutup</strong>
                    <?php if (isset($kegiatan['penutup'])): ?>
                        <?php foreach ($kegiatan['penutup'] as $item): ?>
                            <div class="kegiatan-item"><?= nl2br(htmlspecialchars($item['deskripsi'])) ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="section">
                <div class="section-title">E. Metode Pembelajaran</div>
                <?= nl2br(htmlspecialchars($rpp_data['metode_pembelajaran'])) ?>
            </div>

            <div class="section">
                <div class="section-title">F. Media Pembelajaran</div>
                <?= nl2br(htmlspecialchars($rpp_data['media_pembelajaran'])) ?>
            </div>

            <div class="section">
                <div class="section-title">G. Sumber Belajar</div>
                <?= nl2br(htmlspecialchars($rpp_data['sumber_belajar'])) ?>
            </div>

            <div class="section">
                <div class="section-title">H. Kegiatan Pembelajaran</div>

                <?php if (!empty($kegiatan['pendahuluan']) || !empty($kegiatan['inti']) || !empty($kegiatan['penutup'])): ?>
                    <?php
                    $max_kegiatan = max(count($kegiatan['pendahuluan']), count($kegiatan['inti']), count($kegiatan['penutup']));
                    for ($i = 0; $i < $max_kegiatan; $i++):
                    ?>
                        <div class="kegiatan-section mb-4">
                            <h5 class="kegiatan-title">Kegiatan <?= $i + 1 ?></h5>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="kegiatan-item">
                                        <h6 class="kegiatan-subtitle">Pendahuluan</h6>
                                        <div class="kegiatan-content">
                                            <?= isset($kegiatan['pendahuluan'][$i]) ? nl2br(htmlspecialchars($kegiatan['pendahuluan'][$i]['deskripsi'])) : '<em class="text-muted">Tidak ada data</em>' ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="kegiatan-item">
                                        <h6 class="kegiatan-subtitle">Kegiatan Inti</h6>
                                        <div class="kegiatan-content">
                                            <?= isset($kegiatan['inti'][$i]) ? nl2br(htmlspecialchars($kegiatan['inti'][$i]['deskripsi'])) : '<em class="text-muted">Tidak ada data</em>' ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="kegiatan-item">
                                        <h6 class="kegiatan-subtitle">Penutup</h6>
                                        <div class="kegiatan-content">
                                            <?= isset($kegiatan['penutup'][$i]) ? nl2br(htmlspecialchars($kegiatan['penutup'][$i]['deskripsi'])) : '<em class="text-muted">Tidak ada data</em>' ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                <?php else: ?>
                    <p class="text-muted"><em>Belum ada kegiatan pembelajaran yang ditambahkan.</em></p>
                <?php endif; ?>
            </div>

            <div class="section">
                <div class="section-title">I. Penilaian</div>
                <?= nl2br(htmlspecialchars($rpp_data['penilaian'])) ?>
            </div>
        </div>
    </div>
</div>

<style>
.section {
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.section:last-child {
    border-bottom: none;
}

.section-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
}

.kegiatan-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.kegiatan-title {
    color: #007bff;
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
}

.kegiatan-item {
    background-color: white;
    border-radius: 6px;
    padding: 1rem;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.kegiatan-subtitle {
    color: #495057;
    font-weight: bold;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.kegiatan-content {
    color: #6c757d;
    line-height: 1.6;
}

.card-body {
    padding: 2rem;
}

h3 {
    color: #495057;
    font-weight: bold;
}

p strong {
    color: #495057;
}
</style>

<?php require_once '../template/footer.php'; ?>