<?php
session_start();
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized'
    ]);
    exit();
}

require_once '../models/LikeBerita.php';
require_once '../models/LikeKomentar.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? '';
    $id = $_POST['id'] ?? 0;
    $user_id = $_POST['user_id'] ?? 0;
    $is_dislike = $_POST['is_dislike'] ?? 0;

    if ($type && $id && $user_id) {
        if ($type === 'berita') {
            $like = new LikeBerita();
            $like->berita_id = $id;
            $like->user_id = $user_id;
            $like->is_dislike = $is_dislike;
            
            if ($like->toggleLike()) {
                $counts = $like->getLikeCount($id);
                $user_status = $like->getUserLikeStatus($id, $user_id);
                
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'like_count' => $counts['like_count'] ?? 0,
                        'dislike_count' => $counts['dislike_count'] ?? 0,
                        'user_status' => $user_status
                    ]
                ]);
                exit();
            }
        } else if ($type === 'komentar') {
            $like = new LikeKomentar();
            $like->komentar_id = $id;
            $like->user_id = $user_id;
            $like->is_dislike = $is_dislike;
            
            if ($like->toggleLike()) {
                $counts = $like->getLikeCount($id);
                $user_status = $like->getUserLikeStatus($id, $user_id);
                
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'like_count' => $counts['like_count'] ?? 0,
                        'dislike_count' => $counts['dislike_count'] ?? 0,
                        'user_status' => $user_status
                    ]
                ]);
                exit();
            }
        }
    }
}

header('Content-Type: application/json');
echo json_encode([
    'status' => 'error',
    'message' => 'Terjadi kesalahan saat memproses like/dislike'
]);