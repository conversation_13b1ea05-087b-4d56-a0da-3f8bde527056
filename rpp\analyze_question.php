<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/GeminiApi.php';
require_once '../models/Guru.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Validate input
if (!isset($_POST['question_text']) || !isset($_POST['question_type']) || !isset($_POST['rpp_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data tidak lengkap']);
    exit();
}

$question_text = trim($_POST['question_text']);
$question_type = $_POST['question_type'];
$rpp_id = $_POST['rpp_id'];

// Validate question text
if (empty($question_text)) {
    echo json_encode(['success' => false, 'message' => 'Teks soal tidak boleh kosong']);
    exit();
}

// Validate question type
if (!in_array($question_type, ['multiple_choice', 'essay'])) {
    echo json_encode(['success' => false, 'message' => 'Jenis soal tidak valid']);
    exit();
}

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
        exit();
    }

    // Validate RPP ownership
    $rpp = new Rpp();
    $stmt = $rpp->getOne($rpp_id);
    $rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        echo json_encode(['success' => false, 'message' => 'RPP tidak ditemukan atau bukan milik Anda']);
        exit();
    }

    // Build RPP context for better analysis
    $rpp_context = "Mata Pelajaran: " . $rpp_data['nama_mapel'] . "\n";
    $rpp_context .= "Kelas: " . $rpp_data['nama_kelas'] . "\n";
    $rpp_context .= "Materi Pokok: " . $rpp_data['materi_pokok'] . "\n";
    $rpp_context .= "Tujuan Pembelajaran: " . $rpp_data['tujuan_pembelajaran'] . "\n";
    $rpp_context .= "Kompetensi Dasar: " . $rpp_data['kompetensi_dasar'];

    // Analyze question using Gemini API
    $geminiApi = new GeminiApi();
    $analysis = $geminiApi->analyzeQuestionDifficulty($question_text, $question_type, $rpp_context);

    echo json_encode([
        'success' => true,
        'analysis' => $analysis,
        'message' => 'Analisis berhasil'
    ]);

} catch (Exception $e) {
    error_log("Question Analysis Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
