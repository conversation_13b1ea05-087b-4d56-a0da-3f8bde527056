<?php
/**
 * Simple CLI Migration Runner
 * Runs the simple migration to create essential stored procedures
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../config/database.php';

echo "=== Simple Database Migration Runner ===\n";
echo "Creating stored procedures for restore conflict handling...\n\n";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    // Enable buffered queries to avoid issues
    $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
    
    echo "Connected to database successfully.\n";
    
    // Create the procedures manually to avoid DELIMITER issues
    echo "Creating SafeDropViews procedure...\n";
    $pdo->exec("DROP PROCEDURE IF EXISTS SafeDropViews");
    $pdo->exec("
        CREATE PROCEDURE SafeDropViews()
        BEGIN
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
            
            DROP VIEW IF EXISTS v_siswa_all_periods;
            DROP VIEW IF EXISTS v_siswa_current;
            DROP VIEW IF EXISTS v_absensi_summary;
            DROP VIEW IF EXISTS v_nilai_summary;
            DROP VIEW IF EXISTS v_jadwal_detail;
            
        END
    ");
    
    echo "Creating PreRestorationCleanup procedure...\n";
    $pdo->exec("DROP PROCEDURE IF EXISTS PreRestorationCleanup");
    $pdo->exec("
        CREATE PROCEDURE PreRestorationCleanup()
        BEGIN
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
            
            CALL SafeDropViews();
            
        END
    ");
    
    echo "Creating PostRestorationValidation procedure...\n";
    $pdo->exec("DROP PROCEDURE IF EXISTS PostRestorationValidation");
    $pdo->exec("
        CREATE PROCEDURE PostRestorationValidation()
        BEGIN
            DECLARE view_count INT DEFAULT 0;
            DECLARE table_count INT DEFAULT 0;
            
            SELECT COUNT(*) INTO view_count 
            FROM information_schema.VIEWS 
            WHERE TABLE_SCHEMA = DATABASE();
            
            SELECT COUNT(*) INTO table_count 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE';
            
            SELECT 
                'Post-restoration validation completed' as status,
                table_count as total_tables,
                view_count as total_views,
                CASE 
                    WHEN view_count >= 2 AND table_count >= 10 THEN 'PASSED'
                    ELSE 'WARNING: Some objects may be missing'
                END as validation_result;
                
        END
    ");
    
    // Test the created procedures
    echo "Verifying created procedures...\n";
    $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()");
    $procedures = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_procedures = ['SafeDropViews', 'PreRestorationCleanup', 'PostRestorationValidation'];
    $existing_procedures = [];
    
    foreach ($procedures as $proc) {
        if (in_array($proc['Name'], $required_procedures)) {
            $existing_procedures[] = $proc['Name'];
        }
    }
    
    echo "\nMigration Results:\n";
    echo "- Total procedures in database: " . count($procedures) . "\n";
    echo "- Required procedures found: " . count($existing_procedures) . "/" . count($required_procedures) . "\n";
    
    if (count($existing_procedures) === count($required_procedures)) {
        echo "✓ Migration completed successfully!\n";
        echo "✓ All required procedures are now available.\n";
        
        // Test the procedures
        echo "\nTesting procedures...\n";
        try {
            $pdo->exec("CALL PreRestorationCleanup()");
            echo "✓ PreRestorationCleanup works\n";
            
            $stmt = $pdo->query("CALL PostRestorationValidation()");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✓ PostRestorationValidation works\n";
            echo "  - Tables: " . $result['total_tables'] . "\n";
            echo "  - Views: " . $result['total_views'] . "\n";
            echo "  - Status: " . $result['validation_result'] . "\n";
            
        } catch (Exception $test_error) {
            echo "⚠ Procedures created but testing failed: " . $test_error->getMessage() . "\n";
        }
        
    } else {
        echo "⚠ Migration completed with warnings.\n";
        $missing = array_diff($required_procedures, $existing_procedures);
        echo "Missing procedures: " . implode(', ', $missing) . "\n";
    }
    
    echo "\nProcedures available:\n";
    foreach ($existing_procedures as $proc) {
        echo "- {$proc}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Migration Complete ===\n";
echo "The database is now ready for safe backup restoration!\n";
?>
