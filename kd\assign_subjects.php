<?php
/**
 * Subject Assignment Helper for KD Module
 * This script helps administrators assign subjects to teachers
 */

require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess(); // Only admin can access this
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$success_msg = "";
$error_msg = "";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $guru_id = $_POST['guru_id'];
    $mapel_ids = $_POST['mapel_ids'] ?? [];
    
    if (!empty($guru_id) && !empty($mapel_ids)) {
        try {
            $conn->beginTransaction();
            
            // First, remove existing assignments for this teacher
            $delete_query = "DELETE FROM mapel_guru WHERE guru_id = :guru_id";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bindParam(':guru_id', $guru_id);
            $delete_stmt->execute();
            
            // Then add new assignments
            $insert_query = "INSERT INTO mapel_guru (guru_id, mapel_id) VALUES (:guru_id, :mapel_id)";
            $insert_stmt = $conn->prepare($insert_query);
            
            foreach ($mapel_ids as $mapel_id) {
                $insert_stmt->bindParam(':guru_id', $guru_id);
                $insert_stmt->bindParam(':mapel_id', $mapel_id);
                $insert_stmt->execute();
            }
            
            $conn->commit();
            $success_msg = "Penugasan mata pelajaran berhasil disimpan!";
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_msg = "Error: " . $e->getMessage();
        }
    } else {
        $error_msg = "Silakan pilih guru dan minimal satu mata pelajaran.";
    }
}

// Get all teachers
$teachers_query = "SELECT id, nama_lengkap, nip FROM guru ORDER BY nama_lengkap";
$teachers_stmt = $conn->prepare($teachers_query);
$teachers_stmt->execute();
$teachers = $teachers_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all subjects
$subjects_query = "SELECT id, nama_mapel, kode_mapel FROM mata_pelajaran ORDER BY nama_mapel";
$subjects_stmt = $conn->prepare($subjects_query);
$subjects_stmt->execute();
$subjects = $subjects_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get current assignments
$assignments_query = "SELECT mg.*, g.nama_lengkap, m.nama_mapel 
                     FROM mapel_guru mg 
                     LEFT JOIN guru g ON mg.guru_id = g.id 
                     LEFT JOIN mata_pelajaran m ON mg.mapel_id = m.id 
                     ORDER BY g.nama_lengkap, m.nama_mapel";
$assignments_stmt = $conn->prepare($assignments_query);
$assignments_stmt->execute();
$assignments = $assignments_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Assign Subjects to Teachers</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h2><i class="fas fa-chalkboard-teacher"></i> Assign Subjects to Teachers</h2>
                <p class="text-muted">This tool helps assign subjects to teachers for the KD module.</p>
                
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?= $success_msg ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?= $error_msg ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Assignment Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> Add New Assignment</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="guru_id" class="form-label">Select Teacher</label>
                                    <select class="form-select" id="guru_id" name="guru_id" required>
                                        <option value="">Choose Teacher...</option>
                                        <?php foreach ($teachers as $teacher): ?>
                                            <option value="<?= $teacher['id'] ?>">
                                                <?= htmlspecialchars($teacher['nama_lengkap']) ?> (<?= htmlspecialchars($teacher['nip']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Select Subjects</label>
                                    <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px;">
                                        <?php foreach ($subjects as $subject): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="mapel_ids[]" value="<?= $subject['id'] ?>" 
                                                       id="mapel_<?= $subject['id'] ?>">
                                                <label class="form-check-label" for="mapel_<?= $subject['id'] ?>">
                                                    <?= htmlspecialchars($subject['nama_mapel']) ?> (<?= htmlspecialchars($subject['kode_mapel']) ?>)
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Current Assignments -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Current Assignments</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($assignments)): ?>
                            <p class="text-muted">No subject assignments found.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Teacher</th>
                                            <th>Subject</th>
                                            <th>Assigned Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($assignments as $assignment): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($assignment['nama_lengkap']) ?></td>
                                                <td><?= htmlspecialchars($assignment['nama_mapel']) ?></td>
                                                <td><?= date('d/m/Y H:i', strtotime($assignment['created_at'])) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="../" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="debug_data.php" class="btn btn-info">
                        <i class="fas fa-bug"></i> Debug Data
                    </a>
                    <a href="index.php" class="btn btn-success">
                        <i class="fas fa-graduation-cap"></i> KD Module
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-select subjects when teacher changes (show current assignments)
        document.getElementById('guru_id').addEventListener('change', function() {
            const guruId = this.value;
            if (guruId) {
                // You could add AJAX here to load current assignments for the selected teacher
                // For now, we'll just clear all checkboxes
                document.querySelectorAll('input[name="mapel_ids[]"]').forEach(cb => cb.checked = false);
            }
        });
    </script>
</body>
</html>
