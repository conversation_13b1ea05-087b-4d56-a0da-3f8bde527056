<?php
if (!defined('ABSEN_PATH')) {
    define('ABSEN_PATH', dirname(dirname(__FILE__)));
}
require_once ABSEN_PATH . '/config/database.php';

class PopupBerita {
    private $conn;
    private $settings_table = "popup_berita_settings";
    private $items_table = "popup_berita_items";

    public $id;
    public $is_active;
    public $title;
    public $show_excerpt;
    public $excerpt_length;
    public $auto_show;
    public $show_dont_show_again;
    public $created_by;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $db = new Database();
        $this->conn = $db->getConnection();
    }

    // Settings methods
    public function getSettings() {
        $query = "SELECT * FROM " . $this->settings_table . " ORDER BY id DESC LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->id = $row['id'];
            $this->is_active = $row['is_active'];
            $this->title = $row['title'];
            $this->show_excerpt = $row['show_excerpt'];
            $this->excerpt_length = $row['excerpt_length'];
            $this->auto_show = $row['auto_show'];
            $this->show_dont_show_again = $row['show_dont_show_again'];
            $this->created_by = $row['created_by'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        return false;
    }

    public function createSettings() {
        $query = "INSERT INTO " . $this->settings_table . "
                (is_active, title, show_excerpt, excerpt_length, auto_show, show_dont_show_again, created_by)
                VALUES
                (:is_active, :title, :show_excerpt, :excerpt_length, :auto_show, :show_dont_show_again, :created_by)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->is_active = htmlspecialchars(strip_tags($this->is_active));
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->show_excerpt = htmlspecialchars(strip_tags($this->show_excerpt));
        $this->excerpt_length = htmlspecialchars(strip_tags($this->excerpt_length));
        $this->auto_show = htmlspecialchars(strip_tags($this->auto_show));
        $this->show_dont_show_again = htmlspecialchars(strip_tags($this->show_dont_show_again));
        $this->created_by = htmlspecialchars(strip_tags($this->created_by));

        // Bind parameters
        $stmt->bindParam(":is_active", $this->is_active);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":show_excerpt", $this->show_excerpt);
        $stmt->bindParam(":excerpt_length", $this->excerpt_length);
        $stmt->bindParam(":auto_show", $this->auto_show);
        $stmt->bindParam(":show_dont_show_again", $this->show_dont_show_again);
        $stmt->bindParam(":created_by", $this->created_by);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function updateSettings() {
        $query = "UPDATE " . $this->settings_table . "
                SET is_active = :is_active,
                    title = :title,
                    show_excerpt = :show_excerpt,
                    excerpt_length = :excerpt_length,
                    auto_show = :auto_show,
                    show_dont_show_again = :show_dont_show_again
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->is_active = htmlspecialchars(strip_tags($this->is_active));
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->show_excerpt = htmlspecialchars(strip_tags($this->show_excerpt));
        $this->excerpt_length = htmlspecialchars(strip_tags($this->excerpt_length));
        $this->auto_show = htmlspecialchars(strip_tags($this->auto_show));
        $this->show_dont_show_again = htmlspecialchars(strip_tags($this->show_dont_show_again));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":is_active", $this->is_active);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":show_excerpt", $this->show_excerpt);
        $stmt->bindParam(":excerpt_length", $this->excerpt_length);
        $stmt->bindParam(":auto_show", $this->auto_show);
        $stmt->bindParam(":show_dont_show_again", $this->show_dont_show_again);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Items methods
    public function getSelectedBerita() {
        $query = "SELECT pbi.*, b.judul, b.isi, b.created_at as berita_created_at, u.nama_lengkap as nama_pembuat
                FROM " . $this->items_table . " pbi
                LEFT JOIN berita b ON pbi.berita_id = b.id
                LEFT JOIN users u ON b.created_by = u.id
                WHERE pbi.is_active = 1
                ORDER BY pbi.display_order ASC, pbi.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllBeritaForSelection() {
        $query = "SELECT b.*, u.nama_lengkap as nama_pembuat,
                CASE WHEN pbi.berita_id IS NOT NULL THEN 1 ELSE 0 END as is_selected
                FROM berita b
                LEFT JOIN users u ON b.created_by = u.id
                LEFT JOIN " . $this->items_table . " pbi ON b.id = pbi.berita_id
                ORDER BY b.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function addBeritaToPopup($berita_id, $display_order = 0) {
        $query = "INSERT INTO " . $this->items_table . "
                (berita_id, display_order, is_active)
                VALUES
                (:berita_id, :display_order, 1)
                ON DUPLICATE KEY UPDATE
                is_active = 1, display_order = :display_order";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->bindParam(":display_order", $display_order);

        return $stmt->execute();
    }

    public function removeBeritaFromPopup($berita_id) {
        $query = "DELETE FROM " . $this->items_table . " WHERE berita_id = :berita_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);

        return $stmt->execute();
    }

    public function updateBeritaOrder($berita_id, $display_order) {
        $query = "UPDATE " . $this->items_table . "
                SET display_order = :display_order
                WHERE berita_id = :berita_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":berita_id", $berita_id);
        $stmt->bindParam(":display_order", $display_order);

        return $stmt->execute();
    }

    public function clearAllItems() {
        $query = "DELETE FROM " . $this->items_table;
        $stmt = $this->conn->prepare($query);
        return $stmt->execute();
    }

    // Utility methods
    public function isPopupActive() {
        if ($this->getSettings()) {
            return $this->is_active == 1;
        }
        return false;
    }

    public function getPopupData() {
        if (!$this->isPopupActive()) {
            return false;
        }

        $settings = [
            'title' => $this->title,
            'show_excerpt' => $this->show_excerpt,
            'excerpt_length' => $this->excerpt_length,
            'auto_show' => $this->auto_show,
            'show_dont_show_again' => $this->show_dont_show_again
        ];

        $berita_result = $this->getSelectedBerita();
        $berita_items = [];
        
        while ($row = $berita_result->fetch(PDO::FETCH_ASSOC)) {
            $excerpt = '';
            if ($settings['show_excerpt'] && !empty($row['isi'])) {
                $excerpt = substr(strip_tags($row['isi']), 0, $settings['excerpt_length']);
                if (strlen(strip_tags($row['isi'])) > $settings['excerpt_length']) {
                    $excerpt .= '...';
                }
            }
            
            $berita_items[] = [
                'id' => $row['berita_id'],
                'judul' => $row['judul'],
                'isi' => $row['isi'],
                'excerpt' => $excerpt,
                'nama_pembuat' => $row['nama_pembuat'],
                'created_at' => $row['berita_created_at']
            ];
        }

        return [
            'settings' => $settings,
            'berita' => $berita_items
        ];
    }

    public function truncateText($text, $length = 150) {
        $text = strip_tags($text);
        if (strlen($text) <= $length) {
            return $text;
        }
        return substr($text, 0, $length) . '...';
    }
}
?>
