<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';
require_once '../models/PeriodeAktif.php';

$siswa = new Siswa();
$kelas = new Kelas();
$tahunAjaran = new TahunAjaran();
$periodeAktif = new PeriodeAktif();

// Get active period
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1);
$current_semester = $periodeAktif->semester ?: '1';

// Get filter parameters - Default to period-based view
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $current_tahun_ajaran;
$semester = isset($_GET['semester']) ? $_GET['semester'] : $current_semester;
$view_mode = isset($_GET['view_mode']) ? $_GET['view_mode'] : 'period'; // Default changed to 'period'

// Get class list for filter
$kelas_list = $kelas->getAll();

// Get available academic years
$tahun_ajaran_list = $tahunAjaran->getAllAsArray();

// Get students based on filter and view mode - Default to period-based
if ($view_mode === 'current') {
    // Use current students (legacy mode)
    $result = $kelas_id ? $siswa->getByKelas($kelas_id) : $siswa->getAll();
} else {
    // Use period-based filtering (default)
    $result = $siswa->getByPeriode($tahun_ajaran, $semester, $kelas_id);
}

// Handle success message
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Data Siswa</h5>
                <div>
                    <a href="manage_periods.php" class="btn btn-info mr-2">
                        <i class="fas fa-calendar-alt"></i> Kelola Periode
                    </a>
                    <a href="bulk_period_assignment.php" class="btn btn-warning mr-2">
                        <i class="fas fa-users-cog"></i> Kelola Massal
                    </a>
                    <a href="import.php" class="btn btn-success mr-2">
                        <i class="fas fa-file-excel"></i> Import Excel
                    </a>
                    <a href="create_historical.php" class="btn btn-secondary mr-2">
                        <i class="fas fa-history"></i> Daftar Historis
                    </a>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Siswa
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Filter Form -->
                <form method="get" class="mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Filter Data Siswa</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="view_mode" class="form-label">Mode Tampilan</label>
                                    <select name="view_mode" id="view_mode" class="form-select" onchange="togglePeriodFilters()">
                                        <option value="period" <?php echo $view_mode == 'period' ? 'selected' : ''; ?>>Berdasarkan Periode Akademik</option>
                                        <option value="current" <?php echo $view_mode == 'current' ? 'selected' : ''; ?>>Siswa Aktif Saat Ini (Legacy)</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="kelas_id" class="form-label">Filter Kelas</label>
                                    <select name="kelas_id" id="kelas_id" class="form-select">
                                        <option value="">Semua Kelas</option>
                                        <?php
                                        $kelas_list->execute(); // Reset the statement
                                        while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                            <option value="<?php echo $row['id']; ?>" <?php echo $kelas_id == $row['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 period-filter" style="<?php echo $view_mode == 'current' ? 'display: none;' : ''; ?>">
                                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                                        <?php foreach ($tahun_ajaran_list as $ta): ?>
                                            <option value="<?php echo $ta['tahun_ajaran']; ?>" <?php echo $tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : ''; ?>>
                                                <?php echo $ta['tahun_ajaran']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 period-filter" style="<?php echo $view_mode == 'current' ? 'display: none;' : ''; ?>">
                                    <label for="semester" class="form-label">Semester</label>
                                    <select name="semester" id="semester" class="form-select">
                                        <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                        <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                        <a href="index.php" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php if ($view_mode === 'period'): ?>
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Menampilkan data siswa untuk <strong><?php echo $tahun_ajaran; ?> - Semester <?php echo $semester; ?></strong>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableSiswa">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NIS</th>
                                <th>Nama Siswa</th>
                                <th>Jenis Kelamin</th>
                                <th>Kelas</th>
                                <?php if ($view_mode === 'period'): ?>
                                    <th>Periode</th>
                                    <th>Status</th>
                                <?php endif; ?>
                                <th>No. Telp</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $hasData = false;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                                $hasData = true;

                                // Validate and sanitize data
                                $student_id = $row['siswa_id'] ?? $row['id'] ?? '';
                                $nis = $row['nis'] ?? '';
                                $nama_siswa = $row['nama_siswa'] ?? '';
                                $jenis_kelamin = $row['jenis_kelamin'] ?? '';
                                $nama_kelas = $row['nama_kelas'] ?? '';
                                $no_telp = $row['no_telp'] ?? '';

                                // Skip if essential data is missing
                                if (empty($student_id) || empty($nama_siswa)) {
                                    continue;
                                }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($nis); ?></td>
                                <td><?php echo htmlspecialchars($nama_siswa); ?></td>
                                <td><?php echo $jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan'; ?></td>
                                <td><?php echo htmlspecialchars($nama_kelas); ?></td>
                                <?php if ($view_mode === 'period'):
                                    $tahun_ajaran = $row['tahun_ajaran'] ?? '';
                                    $semester = $row['semester'] ?? '';
                                    $status = $row['status'] ?? 'aktif';
                                ?>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($tahun_ajaran); ?> - Sem <?php echo htmlspecialchars($semester); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch($status) {
                                            case 'aktif':
                                                $status_class = 'badge bg-success';
                                                $status_text = 'Aktif';
                                                break;
                                            case 'lulus':
                                                $status_class = 'badge bg-primary';
                                                $status_text = 'Lulus';
                                                break;
                                            case 'pindah':
                                                $status_class = 'badge bg-warning';
                                                $status_text = 'Pindah';
                                                break;
                                            case 'keluar':
                                                $status_class = 'badge bg-danger';
                                                $status_text = 'Keluar';
                                                break;
                                            default:
                                                $status_class = 'badge bg-secondary';
                                                $status_text = ucfirst($status);
                                        }
                                        ?>
                                        <span class="<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                <?php endif; ?>
                                <td><?php echo htmlspecialchars($no_telp); ?></td>
                                <td>
                                    <a href="edit.php?id=<?php echo htmlspecialchars($student_id); ?>" class="btn btn-sm btn-warning" title="Edit Data">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="academic_history.php?id=<?php echo htmlspecialchars($student_id); ?>" class="btn btn-sm btn-info" title="Riwayat Akademik">
                                        <i class="fas fa-history"></i>
                                    </a>
                                    <?php if ($view_mode === 'period'): ?>
                                        <a href="view_periods.php?id=<?php echo htmlspecialchars($student_id); ?>" class="btn btn-sm btn-secondary" title="Detail Periode">
                                            <i class="fas fa-calendar-alt"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="delete.php?id=<?php echo htmlspecialchars($student_id); ?>"
                                       class="btn btn-sm btn-danger"
                                       onclick="return confirmDelete()" title="Hapus Data">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                    <?php if (!$hasData): ?>
                        <p class="text-center mt-3">Tidak ada data siswa<?php echo $kelas_id ? ' untuk kelas yang dipilih' : ''; ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var actionColumnIndex = <?php echo $view_mode === 'period' ? '8' : '6'; ?>; // Adjust based on view mode

    $('#tableSiswa').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data siswa",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[2, "asc"]], // Urutkan berdasarkan nama siswa
        "columnDefs": [
            {"orderable": false, "targets": actionColumnIndex}, // Kolom aksi tidak bisa diurutkan
            {"width": "120px", "targets": actionColumnIndex} // Atur lebar kolom aksi
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});

function togglePeriodFilters() {
    var viewMode = document.getElementById('view_mode').value;
    var periodFilters = document.querySelectorAll('.period-filter');

    if (viewMode === 'period') {
        periodFilters.forEach(function(element) {
            element.style.display = 'block';
        });
    } else {
        periodFilters.forEach(function(element) {
            element.style.display = 'none';
        });
    }
}

function confirmDelete() {
    return confirm("Apakah Anda yakin ingin menghapus data ini?");
}</script>
</script>

<?php
require_once '../template/footer.php';
?>
