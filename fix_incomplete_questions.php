<?php
require_once 'config/database.php';

// <PERSON><PERSON><PERSON> to identify and optionally remove incomplete multiple choice questions
echo "<h2>Fix Incomplete Multiple Choice Questions</h2>\n";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Find incomplete multiple choice questions
    $stmt = $conn->query("
        SELECT id, question_text, created_at, rpp_id
        FROM rpp_questions
        WHERE question_type = 'multiple_choice'
        AND (options IS NULL OR options = '')
        ORDER BY created_at DESC
    ");
    
    $incomplete_questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($incomplete_questions)) {
        echo "<p style='color: green;'>✅ No incomplete multiple choice questions found!</p>\n";
        exit;
    }
    
    echo "<h3>Found " . count($incomplete_questions) . " Incomplete Multiple Choice Questions</h3>\n";
    echo "<p>These questions are missing their options and appear to be from incomplete AI responses:</p>\n";
    
    foreach ($incomplete_questions as $question) {
        echo "<div style='border: 1px solid #dc3545; padding: 10px; margin: 10px 0; background-color: #f8d7da;'>\n";
        echo "<p><strong>ID:</strong> " . $question['id'] . "</p>\n";
        echo "<p><strong>Created:</strong> " . $question['created_at'] . "</p>\n";
        echo "<p><strong>Question:</strong> " . htmlspecialchars($question['question_text']) . "</p>\n";
        echo "</div>\n";
    }
    
    // Check if this is a GET request with action parameter
    if (isset($_GET['action']) && $_GET['action'] === 'delete') {
        echo "<h3>Deleting Incomplete Questions...</h3>\n";

        $question_ids = array_column($incomplete_questions, 'id');
        $placeholders = str_repeat('?,', count($question_ids) - 1) . '?';

        $stmt = $conn->prepare("DELETE FROM rpp_questions WHERE id IN ($placeholders)");
        $result = $stmt->execute($question_ids);

        if ($result) {
            echo "<p style='color: green;'>✅ Successfully deleted " . count($question_ids) . " incomplete questions.</p>\n";
            echo "<p>You can now regenerate questions for the affected RPP.</p>\n";

            // Show which RPPs were affected
            $affected_rpps = array_unique(array_column($incomplete_questions, 'rpp_id'));
            if (!empty($affected_rpps)) {
                echo "<h4>Affected RPPs (you may want to regenerate questions for these):</h4>\n";
                foreach ($affected_rpps as $rpp_id) {
                    $stmt = $conn->prepare("
                        SELECT r.*, mp.nama_mapel, k.nama_kelas
                        FROM rpp r
                        LEFT JOIN mata_pelajaran mp ON r.mapel_id = mp.id
                        LEFT JOIN kelas k ON r.kelas_id = k.id
                        WHERE r.id = ?
                    ");
                    $stmt->execute([$rpp_id]);
                    $rpp = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($rpp) {
                        echo "<p>• <strong>RPP ID $rpp_id:</strong> " . htmlspecialchars($rpp['nama_mapel']) . " - " . htmlspecialchars($rpp['nama_kelas']) . " (" . htmlspecialchars($rpp['materi_pokok']) . ")</p>\n";
                    }
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to delete questions.</p>\n";
        }
    } else {
        echo "<h3>Recommended Actions</h3>\n";
        echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>\n";
        echo "<p><strong>Option 1: Delete Incomplete Questions</strong></p>\n";
        echo "<p>Remove these incomplete questions so you can regenerate proper ones:</p>\n";
        echo "<p><a href='?action=delete' class='btn btn-danger' onclick='return confirm(\"Are you sure you want to delete " . count($incomplete_questions) . " incomplete questions?\")'>Delete Incomplete Questions</a></p>\n";
        echo "</div>\n";
        
        echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<p><strong>Option 2: Regenerate Questions</strong></p>\n";
        echo "<p>After deleting the incomplete questions, go to the RPP and generate new questions with proper configuration:</p>\n";
        echo "<ol>\n";
        echo "<li>Make sure to set <strong>multiple_choice_count > 0</strong></li>\n";
        echo "<li>The system now has improved validation to prevent incomplete questions</li>\n";
        echo "<li>If you encounter incomplete questions again, it indicates an AI response issue</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<p><strong>What Was Fixed</strong></p>\n";
        echo "<ul>\n";
        echo "<li>✅ Added validation to detect incomplete AI responses</li>\n";
        echo "<li>✅ Added user warnings for essay-only configurations</li>\n";
        echo "<li>✅ Improved error handling for malformed AI responses</li>\n";
        echo "<li>✅ The system will now skip incomplete questions during generation</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
}
?>

<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px 2px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    color: white;
}
</style>
