on:
  workflow_call:
    inputs:
      releaseName:
        required: true
        type: string
      stable:
        required: false
        type: boolean
        default: false

name: "Release"

permissions:
  contents: read

jobs:
  create:
    name: Create Release

    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit

      - name: Create prerelease
        if: ${{ !inputs.stable }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release create \
            --repo ${{ github.repository }} \
            --title ${{ inputs.releaseName }} \
            --prerelease \
            --generate-notes \
            ${{ inputs.releaseName }}

      - name: Create release
        if: ${{ inputs.stable }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release create \
            --repo ${{ github.repository }} \
            --title ${{ inputs.releaseName }} \
            --generate-notes \
            ${{ inputs.releaseName }}

  upload_release:
    name: "Upload"

    needs: ["create"]

    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: write
      attestations: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit

      - uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
      - uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: docs
          path: docs
      - run: |
          tar -czvf docs.tar.gz docs
      - name: "Attest Documentation"
        id: attestation
        uses: actions/attest-build-provenance@1c608d11d69870c2092266b3f9a6f3abbf17002c # v1.4.3
        with:
          subject-path: "docs.tar.gz"
      - name: Copy Attestation
        run: cp "$ATTESTATION" docs.tar.gz.sigstore
        env:
          ATTESTATION: "${{ steps.attestation.outputs.bundle-path }}"
      - name: Upload
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          gh release upload --clobber "${{ github.ref_name }}" \
            docs.tar.gz docs.tar.gz.sigstore
