on:
  workflow_call: {}

name: "Documentation"

permissions:
  contents: read

jobs:
  generate:
    name: "Generate"

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@91182cccc01eb5e619899d80e4e971d6181294a7 # v2.10.1
        with:
          egress-policy: audit

      - name: Checkout Code
        uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
      - name: SetUp PHP
        id: setup-php
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231 # v2
        with:
          php-version: "8.3"
          tools: phive
      - name: <PERSON><PERSON>
        uses: actions/cache@3624ceb22c1c5a301c8db4169662070a689d9ea8 # v4.1.1
        id: cache
        with:
          path: ~/.phive
          key: tools-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-${{ hashFiles('**/phars.xml') }}
          restore-keys: |
            tools-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-
            tools-${{ steps.setup-php.outputs.php-version }}-
            tools-
      - name: Install Tools
        run: composer run install:tools
      - name: Generate Docs
        run: composer run docs:generate
      - uses: actions/upload-artifact@604373da6381bf24206979c74d06a550515601b9 # v4.4.1
        with:
          name: docs
          path: docs
      - name: Package for GitHub Pages
        uses: actions/upload-pages-artifact@56afc609e74202658d3ffba0e8f6dda462b719fa # v3.0.1
        with:
          path: docs

