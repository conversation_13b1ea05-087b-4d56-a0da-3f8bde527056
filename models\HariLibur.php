<?php
require_once __DIR__ . '/../config/database.php';

class HariLibur {
    private $conn;
    public $id;
    public $tanggal;
    public $keterangan;
    public $tahun_ajaran;
    public $semester;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function isHoliday($date) {
        $query = "SELECT * FROM hari_libur WHERE tanggal = :tanggal";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tanggal', $date);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    public function getAll($tahun_ajaran = null, $semester = null) {
        $query = "SELECT * FROM hari_libur";

        $conditions = [];
        if ($tahun_ajaran) {
            $conditions[] = "tahun_ajaran = :tahun_ajaran";
        }
        if ($semester) {
            $conditions[] = "semester = :semester";
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " ORDER BY tanggal ASC";

        $stmt = $this->conn->prepare($query);

        if ($tahun_ajaran) {
            $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        }
        if ($semester) {
            $stmt->bindParam(':semester', $semester);
        }

        $stmt->execute();
        return $stmt;
    }

    public function create() {
        // Get active period if not set
        if (!$this->tahun_ajaran || !$this->semester) {
            require_once __DIR__ . '/PeriodeAktif.php';
            $periode = new PeriodeAktif();
            if ($periode->getActive()) {
                $this->tahun_ajaran = $periode->tahun_ajaran;
                $this->semester = $periode->semester;
            }
        }

        $query = "INSERT INTO hari_libur (tanggal, keterangan, tahun_ajaran, semester) VALUES (:tanggal, :keterangan, :tahun_ajaran, :semester)";
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':tanggal', $this->tanggal);
        $stmt->bindParam(':keterangan', $this->keterangan);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        $stmt->bindParam(':semester', $this->semester);

        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM hari_libur WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }

    public function getByDate($date) {
        $query = "SELECT * FROM hari_libur WHERE tanggal = :tanggal";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tanggal', $date);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function update() {
        $query = "UPDATE hari_libur SET tanggal = :tanggal, keterangan = :keterangan, tahun_ajaran = :tahun_ajaran, semester = :semester WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':tanggal', $this->tanggal);
        $stmt->bindParam(':keterangan', $this->keterangan);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        $stmt->bindParam(':semester', $this->semester);

        return $stmt->execute();
    }

    public function getById($id) {
        $query = "SELECT * FROM hari_libur WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByPeriode($tahun_ajaran, $semester) {
        $query = "SELECT * FROM hari_libur WHERE tahun_ajaran = :tahun_ajaran AND semester = :semester ORDER BY tanggal ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();

        return $stmt;
    }

    public function isHolidayInPeriode($date, $tahun_ajaran, $semester) {
        $query = "SELECT * FROM hari_libur WHERE tanggal = :tanggal AND tahun_ajaran = :tahun_ajaran AND semester = :semester";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tanggal', $date);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    public function getCurrentPeriodeHolidays() {
        require_once __DIR__ . '/PeriodeAktif.php';
        $periode = new PeriodeAktif();
        if ($periode->getActive()) {
            return $this->getByPeriode($periode->tahun_ajaran, $periode->semester);
        }
        return null;
    }
}