<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../config/database.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../template/header.php';

$periode = new PeriodeAktif();
$tahun_ajaran = new TahunAjaran();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['set_active'])) {
        $periode->tahun_ajaran_id = $_POST['tahun_ajaran_id'];
        $periode->semester = $_POST['semester'];
        
        if($periode->setActive()) {
            $_SESSION['success'] = "Periode aktif berhasil diubah!";
            header("Location: index.php");
            exit();
        } else {
            $_SESSION['error'] = "Gagal mengubah periode aktif!";
        }
    }
}

// Get active period
$active_period = new PeriodeAktif();
$active_period->getActive();

// Get all tahun ajaran
$tahun_ajaran_list = $tahun_ajaran->getAll();

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Manajemen Periode Aktif</h5>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Set Periode Aktif</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="row g-3">
                                    <div class="col-md-8">
                                        <label for="tahun_ajaran_id" class="form-label">Tahun Ajaran</label>
                                        <select class="form-select" id="tahun_ajaran_id" name="tahun_ajaran_id" required>
                                            <?php while($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                                <option value="<?php echo $row['id']; ?>" 
                                                        <?php echo ($active_period->tahun_ajaran_id == $row['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $row['tahun_ajaran']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="semester" class="form-label">Semester</label>
                                        <select class="form-select" id="semester" name="semester" required>
                                            <option value="1" <?php echo ($active_period->semester == '1') ? 'selected' : ''; ?>>1</option>
                                            <option value="2" <?php echo ($active_period->semester == '2') ? 'selected' : ''; ?>>2</option>
                                        </select>
                                    </div>

                                    <div class="col-12">
                                        <button type="submit" name="set_active" class="btn btn-primary">
                                            <i class="fas fa-check"></i> Set Aktif
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Periode Aktif Saat Ini</h5>
                            </div>
                            <div class="card-body">
                                <?php if($active_period->id): ?>
                                    <table class="table">
                                        <tr>
                                            <th>Tahun Ajaran</th>
                                            <td><?php echo $active_period->tahun_ajaran; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Semester</th>
                                            <td><?php echo $active_period->semester; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Periode</th>
                                            <td>
                                                <?php 
                                                echo $active_period->formatDate($active_period->tanggal_mulai) . ' s/d ' . 
                                                     $active_period->formatDate($active_period->tanggal_selesai); 
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Status</th>
                                            <td><span class="badge bg-success">Aktif</span></td>
                                        </tr>
                                    </table>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        Belum ada periode aktif yang diset.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Riwayat Periode Aktif</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="tablePeriode">
                                        <thead>
                                            <tr>
                                                <th>No</th>
                                                <th>Tahun Ajaran</th>
                                                <th>Semester</th>
                                                <th>Periode</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $no = 1;
                                            $history = $periode->getAll();
                                            while($row = $history->fetch(PDO::FETCH_ASSOC)):
                                            ?>
                                            <tr>
                                                <td><?php echo $no++; ?></td>
                                                <td><?php echo $row['tahun_ajaran']; ?></td>
                                                <td><?php echo $row['semester']; ?></td>
                                                <td>
                                                    <?php 
                                                    echo $periode->formatDate($row['tanggal_mulai']) . ' s/d ' . 
                                                         $periode->formatDate($row['tanggal_selesai']); 
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if($row['is_active']): ?>
                                                        <span class="badge bg-success">Aktif</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Tidak Aktif</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tablePeriode').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
