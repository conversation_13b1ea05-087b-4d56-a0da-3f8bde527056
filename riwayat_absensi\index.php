<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/DetailJadwalJam.php';

// Check if user is a teacher
if ($_SESSION['role'] !== 'guru') {
    echo "<script>alert('Aks<PERSON> ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

$user = new User();
$jadwal = new JadwalPelajaran();
$absensi = new Absensi();
$detailJadwalJam = new DetailJadwalJam();

// Get guru_id
$guru_id = $user->getGuruId($_SESSION['user_id']);

// Get active period
$active_period = new PeriodeAktif();
$active_period->getActive();
$current_tahun_ajaran = $active_period->tahun_ajaran ?? null;
$current_semester = $active_period->semester ?? null;

// Get teacher's schedule for current period
$jadwal_mengajar = $jadwal->getByGuru($guru_id, $current_semester, $current_tahun_ajaran);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Riwayat Absensi</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Jadwal Mengajar</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="jadwalTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Hari</th>
                            <th>Mata Pelajaran</th>
                            <th>Kelas</th>
                            <th>Jam Pelajaran</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        while ($row = $jadwal_mengajar->fetch(PDO::FETCH_ASSOC)): 
                            // Get detail jam for this jadwal
                            $detail_jam = $detailJadwalJam->getByJadwalId($row['id']);
                            $jam_detail = [];
                            $current_group = [];
                            $prev_jam = null;
                            
                            while ($jam = $detail_jam->fetch(PDO::FETCH_ASSOC)) {
                                if ($prev_jam) {
                                    $prev_end = strtotime($prev_jam['jam_selesai']);
                                    $current_start = strtotime($jam['jam_mulai']);
                                    $time_diff = $current_start - $prev_end;
                                    
                                    // If consecutive time and jam_ke
                                    if ($time_diff <= 300 && $jam['jam_ke'] == $prev_jam['jam_ke'] + 1) { // 5 minutes tolerance
                                        $current_group[] = $jam;
                                    } else {
                                        // Process previous group
                                        if (!empty($current_group)) {
                                            $first = reset($current_group);
                                            $last = end($current_group);
                                            if (count($current_group) > 1) {
                                                $jam_detail[] = "Jam ke-{$first['jam_ke']}-{$last['jam_ke']} (" . 
                                                    date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                                                    date('H:i', strtotime($last['jam_selesai'])) . ")";
                                            } else {
                                                $jam_detail[] = "Jam ke-{$first['jam_ke']} (" . 
                                                    date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                                                    date('H:i', strtotime($first['jam_selesai'])) . ")";
                                            }
                                        }
                                        $current_group = [$jam];
                                    }
                                } else {
                                    $current_group[] = $jam;
                                }
                                $prev_jam = $jam;
                            }
                            
                            // Process the last group
                            if (!empty($current_group)) {
                                $first = reset($current_group);
                                $last = end($current_group);
                                if (count($current_group) > 1) {
                                    $jam_detail[] = "Jam ke-{$first['jam_ke']}-{$last['jam_ke']} (" . 
                                        date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                                        date('H:i', strtotime($last['jam_selesai'])) . ")";
                                } else {
                                    $jam_detail[] = "Jam ke-{$first['jam_ke']} (" . 
                                        date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                                        date('H:i', strtotime($first['jam_selesai'])) . ")";
                                }
                            }
                        ?>
                            <tr>
                                <td></td>
                                <td><?= htmlspecialchars($row['hari']) ?></td>
                                <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                <td><?= htmlspecialchars($row['nama_kelas']) ?></td>
                                <td>
                                    <?php if (!empty($jam_detail)): ?>
                                        <?= implode('<br>', $jam_detail) ?>
                                    <?php else: ?>
                                        <?= date('H:i', strtotime($row['jam_mulai'])) ?> - 
                                        <?= date('H:i', strtotime($row['jam_selesai'])) ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="view.php?jadwal_id=<?= $row['id'] ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-calendar-alt"></i> Lihat Riwayat
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#jadwalTable').DataTable({
        "order": [[1, "asc"]], // Sort by day column ascending
        "columnDefs": [
            {
                "targets": 1,
                "type": "day-order" // Custom type for day ordering
            },
            {
                "searchable": false,
                "orderable": false,
                "targets": 0
            }
        ],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    }).on('order.dt search.dt', function () {
        let i = 1;
        
        $(this).find('td:first-child').each(function () {
            $(this).html(i++);
        });
    }).draw();

    // Custom sorting for days of week
    $.fn.dataTable.ext.type.order['day-order-pre'] = function (d) {
        switch (d.toLowerCase()) {
            case 'senin': return 1;
            case 'selasa': return 2;
            case 'rabu': return 3;
            case 'kamis': return 4;
            case 'jumat': return 5;
            case 'sabtu': return 6;
            case 'minggu': return 7;
            default: return 8;
        }
    };
});
</script>

<?php require_once '../template/footer.php'; ?>