<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Load vendor libraries for proper document generation
require_once '../vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\SimpleType\Jc;

// Cek parameter
if (!isset($_GET['rpp_id']) || !isset($_GET['format'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];
$format = $_GET['format']; // 'pdf' or 'docx'
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all'; // 'all', 'multiple_choice', 'essay'

// Validasi format
if (!in_array($format, ['pdf', 'docx'])) {
    $_SESSION['error'] = "Format export tidak valid.";
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Ambil daftar soal dengan filter
$rppQuestion = new RppQuestion();
$questions_list = $rppQuestion->getByRppIdWithFilter($rpp_id, $filter);

if (!$questions_list || $questions_list->rowCount() === 0) {
    $_SESSION['error'] = "Tidak ada soal untuk diekspor.";
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}

// Generate export berdasarkan format
try {
    if ($format === 'pdf') {
        generatePdfExport($rpp_data, $questions_list, $filter);
    } elseif ($format === 'docx') {
        generateDocxExport($rpp_data, $questions_list, $filter);
    }
} catch (Exception $e) {
    $_SESSION['error'] = "Gagal mengekspor: " . $e->getMessage();
    header("Location: questions_list.php?rpp_id=" . $rpp_id);
    exit();
}

function generatePdfExport($rpp_data, $questions_list, $filter) {
    try {
        // Prevent any unwanted output
        ob_clean();

        // Initialize DomPDF with options
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);
        $options->set('defaultFont', 'DejaVu Sans');

        $dompdf = new Dompdf($options);
        $dompdf->setPaper('A4', 'portrait');

        // Generate HTML content for PDF
        $html = generatePdfHtml($rpp_data, $questions_list, $filter);

        // Load HTML to DomPDF
        $dompdf->loadHtml($html);

        // Render the PDF
        $dompdf->render();

        // Generate filename
        $filename = generateFilename($rpp_data, $filter, 'pdf');

        // Output the generated PDF
        $dompdf->stream($filename, [
            'Attachment' => true
        ]);

        exit(0);

    } catch (Exception $e) {
        error_log('PDF Export Error: ' . $e->getMessage());
        $_SESSION['error'] = "Maaf, terjadi kesalahan saat mengekspor file PDF: " . $e->getMessage();
        header("Location: questions_list.php?rpp_id=" . $rpp_data['id']);
        exit();
    }
}

function generateDocxExport($rpp_data, $questions_list, $filter) {
    try {
        // Create new Word Document
        $phpWord = new PhpWord();

        // Set default font
        $phpWord->setDefaultFontName('Times New Roman');
        $phpWord->setDefaultFontSize(12);

        // Add section
        $section = $phpWord->addSection();

        // Generate Word content
        generateWordContent($section, $rpp_data, $questions_list, $filter);

        // Generate filename
        $filename = generateFilename($rpp_data, $filter, 'docx');

        // Set headers for download
        header("Content-Description: File Transfer");
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        // Save and output
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save('php://output');
        exit();

    } catch (Exception $e) {
        error_log('Word Export Error: ' . $e->getMessage());
        $_SESSION['error'] = "Maaf, terjadi kesalahan saat mengekspor file Word: " . $e->getMessage();
        header("Location: questions_list.php?rpp_id=" . $rpp_data['id']);
        exit();
    }
}

function generatePdfHtml($rpp_data, $questions_list, $filter) {
    $filterLabel = getFilterLabel($filter);

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Bank Soal RPP</title>
        <style>
            body {
                font-family: "DejaVu Sans", Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
                font-size: 12pt;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }
            .header h1 {
                font-size: 18pt;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .header h2 {
                font-size: 16pt;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .header h3 {
                font-size: 14pt;
                font-weight: bold;
            }
            .rpp-info {
                background-color: #f8f9fa;
                padding: 15px;
                border: 1px solid #ddd;
                margin-bottom: 20px;
            }
            .rpp-info h3 {
                font-size: 14pt;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .question {
                margin-bottom: 25px;
                page-break-inside: avoid;
            }
            .question-header {
                font-weight: bold;
                font-size: 12pt;
                margin-bottom: 10px;
                color: #333;
            }
            .question-text {
                margin-bottom: 15px;
                font-size: 11pt;
            }
            .options {
                margin-left: 20px;
            }
            .option {
                margin-bottom: 5px;
            }
            .correct-answer {
                font-weight: bold;
            }
            .answer-key {
                margin-top: 10px;
                font-weight: bold;
                color: #0066cc;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>SIHADIR - Sistem Informasi Kehadiran Siswa</h1>
            <h2>BANK SOAL RPP</h2>
            <h3>' . htmlspecialchars($rpp_data['nama_mapel']) . '</h3>
        </div>

        <div class="rpp-info">
            <h3>Informasi RPP</h3>
            <p><strong>Mata Pelajaran:</strong> ' . htmlspecialchars($rpp_data['nama_mapel']) . '</p>
            <p><strong>Kelas:</strong> ' . htmlspecialchars($rpp_data['nama_kelas']) . '</p>
            <p><strong>Materi Pokok:</strong> ' . htmlspecialchars($rpp_data['materi_pokok']) . '</p>
            <p><strong>Semester:</strong> ' . htmlspecialchars($rpp_data['semester']) . '</p>
            <p><strong>Tahun Ajaran:</strong> ' . htmlspecialchars($rpp_data['tahun_ajaran']) . '</p>
            <p><strong>Jenis Soal:</strong> ' . $filterLabel . '</p>
            <p><strong>Tanggal Export:</strong> ' . date('d/m/Y H:i') . '</p>
        </div>';

    $question_number = 1;
    while ($question = $questions_list->fetch(PDO::FETCH_ASSOC)) {
        $typeLabel = $question['question_type'] === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay';
        $difficultyLabel = getDifficultyLabel($question['difficulty_level']);

        $html .= '
        <div class="question">
            <div class="question-header">
                Soal ' . $question_number . ' (' . $typeLabel . ' - ' . $difficultyLabel . ')
            </div>
            <div class="question-text">' . nl2br(htmlspecialchars($question['question_text'])) . '</div>';

        if ($question['question_type'] === 'multiple_choice' && $question['options']) {
            $options = json_decode($question['options'], true);
            if ($options) {
                $html .= '<div class="options">';
                foreach ($options as $option) {
                    $isCorrect = (substr($option, 0, 1) === $question['correct_answer']);
                    $class = $isCorrect ? 'option correct-answer' : 'option';
                    $html .= '<div class="' . $class . '">' . htmlspecialchars($option) . '</div>';
                }
                $html .= '</div>';
                $html .= '<div class="answer-key">Kunci Jawaban: ' . htmlspecialchars($question['correct_answer']) . '</div>';
            }
        }

        $html .= '</div>';
        $question_number++;
    }

    $html .= '</body></html>';
    return $html;
}

function generateWordContent($section, $rpp_data, $questions_list, $filter) {
    $filterLabel = getFilterLabel($filter);

    // Add header
    $section->addText('SIHADIR - Sistem Informasi Kehadiran Siswa', ['bold' => true, 'size' => 16], ['alignment' => Jc::CENTER]);
    $section->addText('BANK SOAL RPP', ['bold' => true, 'size' => 18], ['alignment' => Jc::CENTER]);
    $section->addText(htmlspecialchars($rpp_data['nama_mapel']), ['bold' => true, 'size' => 14], ['alignment' => Jc::CENTER]);
    $section->addTextBreak(2);

    // Add RPP information table
    $table = $section->addTable(['borderSize' => 6, 'borderColor' => '000000']);

    // Header row
    $table->addRow();
    $table->addCell(4000)->addText('INFORMASI RPP', ['bold' => true, 'size' => 12]);
    $table->addCell(4000)->addText('', ['bold' => true, 'size' => 12]);

    // Information rows
    $infoData = [
        ['Mata Pelajaran', htmlspecialchars($rpp_data['nama_mapel'])],
        ['Kelas', htmlspecialchars($rpp_data['nama_kelas'])],
        ['Materi Pokok', htmlspecialchars($rpp_data['materi_pokok'])],
        ['Semester', htmlspecialchars($rpp_data['semester'])],
        ['Tahun Ajaran', htmlspecialchars($rpp_data['tahun_ajaran'])],
        ['Jenis Soal', $filterLabel],
        ['Tanggal Export', date('d/m/Y H:i')]
    ];

    foreach ($infoData as $info) {
        $table->addRow();
        $table->addCell(2000)->addText($info[0], ['bold' => true]);
        $table->addCell(6000)->addText($info[1]);
    }

    $section->addTextBreak(2);

    // Add questions
    $question_number = 1;
    while ($question = $questions_list->fetch(PDO::FETCH_ASSOC)) {
        $typeLabel = $question['question_type'] === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay';
        $difficultyLabel = getDifficultyLabel($question['difficulty_level']);

        // Question header
        $section->addText('Soal ' . $question_number . ' (' . $typeLabel . ' - ' . $difficultyLabel . ')',
                         ['bold' => true, 'size' => 12]);
        $section->addTextBreak();

        // Question text
        $section->addText(htmlspecialchars($question['question_text']), ['size' => 11]);
        $section->addTextBreak();

        // Options for multiple choice
        if ($question['question_type'] === 'multiple_choice' && $question['options']) {
            $options = json_decode($question['options'], true);
            if ($options) {
                foreach ($options as $option) {
                    $isCorrect = (substr($option, 0, 1) === $question['correct_answer']);
                    $fontStyle = $isCorrect ? ['bold' => true, 'size' => 11] : ['size' => 11];
                    $section->addText('   ' . htmlspecialchars($option), $fontStyle);
                    $section->addTextBreak();
                }
                $section->addText('Kunci Jawaban: ' . htmlspecialchars($question['correct_answer']),
                                 ['bold' => true, 'size' => 11]);
            }
        }

        $section->addTextBreak(2);
        $question_number++;
    }
}

function generateFilename($rpp_data, $filter, $extension) {
    $filterSuffix = '';
    switch ($filter) {
        case 'multiple_choice':
            $filterSuffix = '_PilihanGanda';
            break;
        case 'essay':
            $filterSuffix = '_Essay';
            break;
        default:
            $filterSuffix = '_SemuaSoal';
    }
    
    $filename = 'BankSoal_' . 
                preg_replace('/[^A-Za-z0-9_-]/', '', $rpp_data['nama_mapel']) . '_' .
                preg_replace('/[^A-Za-z0-9_-]/', '', $rpp_data['nama_kelas']) . 
                $filterSuffix . '_' . 
                date('Y-m-d') . '.' . $extension;
    
    return $filename;
}

function getFilterLabel($filter) {
    switch ($filter) {
        case 'multiple_choice': return 'Pilihan Ganda';
        case 'essay': return 'Essay';
        default: return 'Semua Soal';
    }
}

function getDifficultyLabel($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'Regular';
        case 'hots_easy': return 'HOTS Mudah';
        case 'hots_medium': return 'HOTS Sedang';
        case 'hots_hard': return 'HOTS Tinggi';
        default: return 'Regular';
    }
}

function getDifficultyColor($difficulty) {
    switch ($difficulty) {
        case 'regular': return 'secondary';
        case 'hots_easy': return 'info';
        case 'hots_medium': return 'warning';
        case 'hots_hard': return 'danger';
        default: return 'secondary';
    }
}
?>
