# Modul Profil Sekolah

Modul ini digunakan untuk mengelola informasi profil sekolah, termasuk:

## Informasi Umum Sekolah
- Nama <PERSON>
- NPSN (Nomor Pokok Sekolah Nasional)
- Status Sekolah (Negeri / Swasta)
- <PERSON><PERSON><PERSON> (SD/SMP/SMA/SMK/dll)

## Alamat <PERSON>g<PERSON>p
- Jalan
- Kelurahan / Desa
- Kecamatan
- Kabupaten / Kota
- Provinsi
- Kode Pos

## Kontak
- Nomor Telepon
- Email Sekolah
- Website Resmi

## Identitas Kepemimpinan
- Nama Kepala Sekolah
- NIP Kepala Sekolah

## Fitur Tambahan
- Logo Sekolah
- Visi dan Misi

## Penggunaan
1. Aks<PERSON> modul ini melalui menu "Profil Sekolah" di sidebar admin
2. Untuk pertama kali, jalankan `initialize.php` untuk membuat tabel dan data awal
3. <PERSON><PERSON><PERSON> ha<PERSON><PERSON> edit untuk mengubah informasi profil sekolah

## Catatan
- <PERSON>ya admin yang dapat mengakses dan mengedit profil sekolah
- Informasi profil sekolah dapat digunakan di berbagai bagian aplikasi seperti laporan, surat, dan dokumen resmi
