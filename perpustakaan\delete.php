<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

// Cek jika id tidak ada
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "ID buku tidak ditemukan.";
    header("Location: index.php");
    exit();
}

$id = $_GET['id'];
$perpustakaan = new Perpustakaan();

try {
    // Cek apakah buku masih dipinjam
    $query = "SELECT COUNT(*) as total FROM peminjaman WHERE id_buku = :id AND status = 'dipinjam'";
    $stmt = $perpustakaan->db->prepare($query);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['total'] > 0) {
        throw new Exception("Buku tidak dapat dihapus karena sedang dipinjam.");
    }

    if ($perpustakaan->hapusBuku($id)) {
        $_SESSION['success'] = "Buku berhasil dihapus";
    } else {
        throw new Exception("Gagal menghapus buku");
    }
} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
}

header("Location: index.php");
exit();
