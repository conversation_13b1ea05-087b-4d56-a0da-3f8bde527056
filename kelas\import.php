<?php
require_once '../template/header.php';
require_once '../models/Kelas.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';
require '../vendor/autoload.php'; // Make sure to install PhpSpreadsheet via composer

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $file = $_FILES['file']['tmp_name'];
        $reader = new Xlsx();
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];

        $tingkatModel = new Tingkat();
        $jurusanModel = new Jurusan();

        // Start from row 2 (after header)
        foreach ($worksheet->getRowIterator(2) as $row) {
            $rowData = [];
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }

            // Skip empty rows
            if (empty($cells[0]) && empty($cells[1]) && empty($cells[2])) {
                continue;
            }

            // Get tingkat_id based on nama_tingkat
            $tingkat = $tingkatModel->getByNamaTingkat($cells[3]);
            $tingkat_id = $tingkat ? $tingkat['id'] : null;

            // Get jurusan_id based on nama_jurusan
            $jurusan = $jurusanModel->getByNamaJurusan($cells[4]);
            $jurusan_id = $jurusan ? $jurusan['id'] : null;

            $rowData = [
                'nama_kelas' => $cells[0],
                'wali_kelas' => $cells[1],
                'tahun_ajaran' => $cells[2],
                'tingkat_id' => $tingkat_id,
                'jurusan_id' => $jurusan_id
            ];
            
            $data[] = $rowData;
        }

        $kelas = new Kelas();
        if ($kelas->importFromArray($data)) {
            header("Location: index.php?success=1");
            exit;
        } else {
            $error = "Gagal mengimpor data";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Import Data Kelas dari Excel</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">File Excel</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        <div class="form-text">
                            Format file: .xlsx<br>
                            Kolom yang dibutuhkan:
                            <ul>
                                <li>Nama Kelas (kolom A)</li>
                                <li>Wali Kelas (kolom B)</li>
                                <li>Tahun Ajaran (kolom C)</li>
                                <li>Tingkat (kolom D)</li>
                                <li>Jurusan (kolom E)</li>
                            </ul>
                            <a href="template_kelas.xlsx" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
