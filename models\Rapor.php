<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/Nilai.php';
require_once __DIR__ . '/Absensi.php';
require_once __DIR__ . '/NilaiTugas.php';
require_once __DIR__ . '/MataPelajaran.php';
require_once __DIR__ . '/Siswa.php';
require_once __DIR__ . '/Kelas.php';
require_once __DIR__ . '/SiswaPeriode.php';
require_once __DIR__ . '/PeriodeAktif.php';
require_once __DIR__ . '/ProfilSekolah.php';
require_once __DIR__ . '/Tingkat.php';

class Rapor {
    private $conn;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get all subjects and grades for a student in a specific semester and academic year
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Array of subjects with grades
     */
    public function getNilaiRapor($siswa_id, $semester, $tahun_ajaran) {
        // Get student's class information first
        $query = "SELECT
                    s.id as siswa_id,
                    k.id as kelas_id,
                    k.tingkat_id,
                    k.jurusan_id
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        $student_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$student_info) {
            return [];
        }

        // Now get subjects and grades filtered by student's tingkat and jurusan
        $query = "SELECT
                    mp.id as mapel_id,
                    mp.nama_mapel,
                    mp.kkm,
                    n.nilai_tugas,
                    n.nilai_uts,
                    n.nilai_uas,
                    n.nilai_absen,
                    n.nilai_akhir,
                    n.rumus_nilai
                FROM mata_pelajaran mp
                JOIN mapel_jurusan mj ON mp.id = mj.mapel_id
                LEFT JOIN nilai n ON mp.id = n.mapel_id AND n.siswa_id = :siswa_id
                    AND n.semester = :semester AND n.tahun_ajaran = :tahun_ajaran
                WHERE mp.tingkat_id = :tingkat_id
                AND mj.jurusan_id = :jurusan_id
                ORDER BY mp.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':tingkat_id', $student_info['tingkat_id']);
        $stmt->bindParam(':jurusan_id', $student_info['jurusan_id']);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get attendance summary for a student in a specific semester and academic year
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Attendance summary
     */
    public function getRekapAbsensi($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT
                    COUNT(CASE WHEN da.status = 'Hadir' THEN 1 END) as hadir,
                    COUNT(CASE WHEN da.status = 'Sakit' THEN 1 END) as sakit,
                    COUNT(CASE WHEN da.status = 'Izin' THEN 1 END) as izin,
                    COUNT(CASE WHEN da.status = 'Alpha' THEN 1 END) as alpha,
                    COUNT(da.id) as total_pertemuan
                FROM detail_absensi da
                JOIN absensi a ON da.absensi_id = a.id
                WHERE da.siswa_id = :siswa_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Calculate average grade for a student in a specific semester and academic year
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return float Average grade
     */
    public function getRataRataNilai($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT AVG(nilai_akhir) as rata_rata
                FROM nilai
                WHERE siswa_id = :siswa_id
                AND semester = :semester
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['rata_rata'] ? round($result['rata_rata'], 2) : 0;
    }

    /**
     * Get class rank for a student based on average grades
     *
     * @param int $siswa_id Student ID
     * @param int $kelas_id Class ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Rank information
     */
    public function getPeringkatKelas($siswa_id, $kelas_id, $semester, $tahun_ajaran) {
        // First get all students in the class with their average grades
        $query = "SELECT
                    s.id as siswa_id,
                    s.nama_siswa,
                    AVG(n.nilai_akhir) as rata_rata
                FROM siswa s
                LEFT JOIN nilai n ON s.id = n.siswa_id
                    AND n.semester = :semester
                    AND n.tahun_ajaran = :tahun_ajaran
                WHERE s.kelas_id = :kelas_id
                GROUP BY s.id, s.nama_siswa
                ORDER BY rata_rata DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $rankings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $total_siswa = count($rankings);

        // Find the student's rank
        $rank = 0;
        foreach ($rankings as $index => $student) {
            if ($student['siswa_id'] == $siswa_id) {
                $rank = $index + 1;
                break;
            }
        }

        return [
            'peringkat' => $rank,
            'total_siswa' => $total_siswa
        ];
    }

    /**
     * Get student's extracurricular activities
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Extracurricular activities
     */
    public function getEkstrakurikuler($siswa_id, $semester, $tahun_ajaran) {
        // Check if ekstrakurikuler table exists
        $query = "SHOW TABLES LIKE 'ekstrakurikuler'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, return empty array
            return [];
        }

        // Table exists, get data
        $query = "SELECT e.nama_ekstrakurikuler, ne.nilai, ne.keterangan
                FROM nilai_ekstrakurikuler ne
                JOIN ekstrakurikuler e ON ne.ekstrakurikuler_id = e.id
                WHERE ne.siswa_id = :siswa_id
                AND ne.semester = :semester
                AND ne.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get student's behavior assessment
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Behavior assessment
     */
    public function getPenilaianSikap($siswa_id, $semester, $tahun_ajaran) {
        // Check if penilaian_sikap table exists
        $query = "SHOW TABLES LIKE 'penilaian_sikap'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, return default values
            return [
                'sikap_spiritual' => 'B',
                'sikap_sosial' => 'B',
                'catatan' => ''
            ];
        }

        // Table exists, get data
        $query = "SELECT sikap_spiritual, sikap_sosial, catatan
                FROM penilaian_sikap
                WHERE siswa_id = :siswa_id
                AND semester = :semester
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) {
            // No data found, return default values
            return [
                'sikap_spiritual' => 'B',
                'sikap_sosial' => 'B',
                'catatan' => ''
            ];
        }

        return $result;
    }

    /**
     * Get student's achievement
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Student's achievement
     */
    public function getPrestasi($siswa_id, $semester, $tahun_ajaran) {
        // Check if prestasi table exists
        $query = "SHOW TABLES LIKE 'prestasi'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, return empty array
            return [];
        }

        // Table exists, get data
        $query = "SELECT jenis_prestasi, keterangan
                FROM prestasi
                WHERE siswa_id = :siswa_id
                AND semester = :semester
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get student's attendance summary
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Attendance summary
     */
    public function getKehadiran($siswa_id, $semester, $tahun_ajaran) {
        $absensi = new Absensi();
        $rekap = $absensi->getRekapBySiswa($siswa_id, $semester, $tahun_ajaran);

        if (!$rekap) {
            return [
                'sakit' => 0,
                'izin' => 0,
                'alpha' => 0,
                'per_mapel' => true // Flag to indicate this is per subject
            ];
        }

        return [
            'sakit' => $rekap['total_sakit'],
            'izin' => $rekap['total_izin'],
            'alpha' => $rekap['total_alpha'],
            'per_mapel' => true // Flag to indicate this is per subject
        ];
    }

    /**
     * Get student's attendance summary per subject
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Attendance summary per subject
     */
    public function getKehadiranPerMapel($siswa_id, $semester, $tahun_ajaran) {
        // Get student's class information first
        $query = "SELECT
                    s.id as siswa_id,
                    k.id as kelas_id,
                    k.tingkat_id,
                    k.jurusan_id
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();
        $student_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$student_info) {
            return [];
        }

        // Get subjects for this student's tingkat and jurusan
        $query = "SELECT
                    mp.id as mapel_id,
                    mp.nama_mapel
                FROM mata_pelajaran mp
                JOIN mapel_jurusan mj ON mp.id = mj.mapel_id
                WHERE mp.tingkat_id = :tingkat_id
                AND mj.jurusan_id = :jurusan_id
                ORDER BY mp.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tingkat_id', $student_info['tingkat_id']);
        $stmt->bindParam(':jurusan_id', $student_info['jurusan_id']);
        $stmt->execute();

        $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Now get attendance data for these subjects
        $absensi = new Absensi();
        $rekap = $absensi->getRekapBySiswaPerMapel($siswa_id, $semester, $tahun_ajaran);

        if (!$rekap || $rekap->rowCount() == 0) {
            // Return subjects with zero attendance
            $result = [];
            foreach ($subjects as $subject) {
                $result[] = [
                    'mapel_id' => $subject['mapel_id'],
                    'nama_mapel' => $subject['nama_mapel'],
                    'hadir' => 0,
                    'sakit' => 0,
                    'izin' => 0,
                    'alpha' => 0,
                    'total_pertemuan' => 0
                ];
            }
            return $result;
        }

        // Create a lookup array for attendance data
        $attendance_data = [];
        while ($row = $rekap->fetch(PDO::FETCH_ASSOC)) {
            $attendance_data[$row['mapel_id']] = [
                'mapel_id' => $row['mapel_id'],
                'nama_mapel' => $row['nama_mapel'],
                'hadir' => $row['total_hadir'],
                'sakit' => $row['total_sakit'],
                'izin' => $row['total_izin'],
                'alpha' => $row['total_alpha'],
                'total_pertemuan' => $row['total_pertemuan']
            ];
        }

        // Combine subject list with attendance data
        $result = [];
        foreach ($subjects as $subject) {
            if (isset($attendance_data[$subject['mapel_id']])) {
                $result[] = $attendance_data[$subject['mapel_id']];
            } else {
                $result[] = [
                    'mapel_id' => $subject['mapel_id'],
                    'nama_mapel' => $subject['nama_mapel'],
                    'hadir' => 0,
                    'sakit' => 0,
                    'izin' => 0,
                    'alpha' => 0,
                    'total_pertemuan' => 0
                ];
            }
        }

        return $result;
    }

    /**
     * Get teacher's notes for student
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return string Teacher's notes
     */
    public function getCatatanWaliKelas($siswa_id, $semester, $tahun_ajaran) {
        // Check if catatan_wali_kelas table exists
        $query = "SHOW TABLES LIKE 'catatan_wali_kelas'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, return empty string
            return '';
        }

        // Table exists, get data
        $query = "SELECT catatan
                FROM catatan_wali_kelas
                WHERE siswa_id = :siswa_id
                AND semester = :semester
                AND tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['catatan'] : '';
    }

    /**
     * Check if student is in final grade (grade 12)
     *
     * @param int $siswa_id Student ID
     * @return bool True if student is in grade 12
     */
    public function isStudentInFinalGrade($siswa_id) {
        $query = "SELECT t.nama_tingkat
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                JOIN tingkat t ON k.tingkat_id = t.id
                WHERE s.id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            $tingkat = strtolower($result['nama_tingkat']);
            // Check for various formats of grade 12 naming
            return (strpos($tingkat, '12') !== false ||
                    strpos($tingkat, 'xii') !== false ||
                    strpos($tingkat, 'kelas 12') !== false ||
                    strpos($tingkat, 'grade 12') !== false);
        }

        return false;
    }

    /**
     * Get promotion status for student
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return string Promotion status
     */
    public function getStatusKenaikanKelas($siswa_id, $semester, $tahun_ajaran) {
        // Only applicable for semester 2
        if ($semester != '2') {
            return '';
        }

        // Check if student is in final grade (grade 12)
        if ($this->isStudentInFinalGrade($siswa_id)) {
            return ''; // No promotion decision for final grade students
        }

        // Check if kenaikan_kelas table exists
        $query = "SHOW TABLES LIKE 'kenaikan_kelas'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, determine based on grades, attendance, and behavior
            $nilai = new Nilai();
            $mapel = new MataPelajaran();
            $absensi = new Absensi();

            // Get all subjects for the student
            $siswa = new Siswa();
            $siswa->id = $siswa_id;
            $siswa->getOne();

            // Get tingkat_id and jurusan_id from kelas
            $kelas = new Kelas();
            $kelas->id = $siswa->kelas_id;
            $kelas_data = $kelas->getById($siswa->kelas_id);

            if (!$kelas_data) {
                return 'Naik Kelas'; // Default if class data not found
            }

            $tingkat_id = $kelas_data['tingkat_id'];
            $jurusan_id = $kelas_data['jurusan_id'];

            // 1. Check academic grades
            $mapel_list = $mapel->getByTingkatAndJurusan($tingkat_id, $jurusan_id);
            $failed_subjects = 0;
            $total_subjects = 0;
            $critical_subjects = 0; // Mata pelajaran inti yang gagal

            // Get list of critical subjects (mata pelajaran inti)
            $critical_subject_list = $this->getCriticalSubjects($tingkat_id, $jurusan_id);

            while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
                $total_subjects++;
                $nilai_data = $nilai->getNilaiSiswa($siswa_id, $row['id'], $semester, $tahun_ajaran);

                if (isset($nilai_data['nilai_akhir']) && $nilai_data['nilai_akhir'] < $row['kkm']) {
                    $failed_subjects++;

                    // Check if this is a critical subject
                    if (in_array($row['id'], $critical_subject_list)) {
                        $critical_subjects++;
                    }
                }
            }

            // 2. Check attendance
            $kehadiran = $absensi->getRekapBySiswa($siswa_id, $semester, $tahun_ajaran);
            $total_alpha = isset($kehadiran['total_alpha']) ? $kehadiran['total_alpha'] : 0;

            // 3. Check behavior (if available)
            $sikap = $this->getPenilaianSikap($siswa_id, $semester, $tahun_ajaran);
            $sikap_spiritual = $sikap['sikap_spiritual'] ?? 'B';
            $sikap_sosial = $sikap['sikap_sosial'] ?? 'B';

            // Decision logic:
            // 1. Jika siswa gagal lebih dari 3 mata pelajaran, tinggal kelas
            // 2. Jika siswa gagal mata pelajaran inti, tinggal kelas
            // 3. Jika siswa alpha lebih dari 10% dari total pertemuan, tinggal kelas
            // 4. Jika sikap spiritual atau sosial mendapat nilai D, tinggal kelas

            $total_pertemuan = isset($kehadiran['total_pertemuan']) ? $kehadiran['total_pertemuan'] : 0;
            $alpha_percentage = ($total_pertemuan > 0) ? ($total_alpha / $total_pertemuan) * 100 : 0;

            if ($failed_subjects > 3) {
                return 'Tinggal Kelas (Gagal ' . $failed_subjects . ' mata pelajaran)';
            }

            if ($critical_subjects > 0) {
                return 'Tinggal Kelas (Gagal mata pelajaran inti)';
            }

            if ($alpha_percentage > 10) {
                return 'Tinggal Kelas (Kehadiran kurang)';
            }

            if ($sikap_spiritual == 'D' || $sikap_sosial == 'D') {
                return 'Tinggal Kelas (Sikap kurang)';
            }

            return 'Naik Kelas';
        }

        // Table exists, check if columns exist
        $query = "SHOW COLUMNS FROM kenaikan_kelas LIKE 'alasan'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $hasAlasanColumn = ($stmt->rowCount() > 0);

        $query = "SHOW COLUMNS FROM kenaikan_kelas LIKE 'alasan_lain'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $hasAlasanLainColumn = ($stmt->rowCount() > 0);

        // Prepare query based on available columns
        if ($hasAlasanColumn && $hasAlasanLainColumn) {
            $query = "SELECT status, alasan, alasan_lain
                    FROM kenaikan_kelas
                    WHERE siswa_id = :siswa_id
                    AND tahun_ajaran = :tahun_ajaran";
        } else {
            $query = "SELECT status
                    FROM kenaikan_kelas
                    WHERE siswa_id = :siswa_id
                    AND tahun_ajaran = :tahun_ajaran";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) {
            return 'Naik Kelas';
        }

        $status = $result['status'];

        // If status is 'Tinggal Kelas' or 'Naik Kelas Bersyarat', add reason if available
        if (($status === 'Tinggal Kelas' || $status === 'Naik Kelas Bersyarat') && $hasAlasanColumn && $hasAlasanLainColumn) {
            $alasan = $result['alasan'];
            $alasan_lain = $result['alasan_lain'];

            if (!empty($alasan)) {
                // Check if alasan is JSON
                $alasan_array = json_decode($alasan, true);
                if (is_array($alasan_array)) {
                    $status .= ' (' . implode(', ', $alasan_array) . ')';
                } else {
                    $status .= ' (' . $alasan . ')';
                }
            }

            if (!empty($alasan_lain)) {
                if (strpos($status, '(') !== false) {
                    $status = str_replace(')', ', ' . $alasan_lain . ')', $status);
                } else {
                    $status .= ' (' . $alasan_lain . ')';
                }
            }
        }

        return $status;
    }

    /**
     * Get list of critical subjects (mata pelajaran inti)
     *
     * @param int $tingkat_id Grade level ID
     * @param int $jurusan_id Major ID
     * @return array List of critical subject IDs
     */
    private function getCriticalSubjects($tingkat_id, $jurusan_id) {
        // Check if critical_subjects table exists
        $query = "SHOW TABLES LIKE 'critical_subjects'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Table exists, get data
            $query = "SELECT mapel_id
                    FROM critical_subjects
                    WHERE tingkat_id = :tingkat_id
                    AND jurusan_id = :jurusan_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':tingkat_id', $tingkat_id);
            $stmt->bindParam(':jurusan_id', $jurusan_id);
            $stmt->execute();

            $result = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $result[] = $row['mapel_id'];
            }

            return $result;
        }

        // Table doesn't exist, use default critical subjects
        // Get subjects with 'inti' in the name or other criteria
        $query = "SELECT id
                FROM mata_pelajaran
                WHERE tingkat_id = :tingkat_id
                AND (nama_mapel LIKE '%inti%'
                     OR nama_mapel LIKE '%matematika%'
                     OR nama_mapel LIKE '%bahasa indonesia%'
                     OR nama_mapel LIKE '%bahasa inggris%'
                     OR nama_mapel LIKE '%ipa%'
                     OR nama_mapel LIKE '%ips%'
                     OR nama_mapel LIKE '%produktif%')";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tingkat_id', $tingkat_id);
        $stmt->execute();

        $result = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $result[] = $row['id'];
        }

        return $result;
    }

    /**
     * Get complete K13 report card data for a student
     *
     * @param int $siswa_id Student ID
     * @param string $semester Semester (1 or 2)
     * @param string $tahun_ajaran Academic year
     * @return array Complete report card data
     */
    public function getCompleteRaporData($siswa_id, $semester, $tahun_ajaran) {
        // Get student information
        $siswa = new Siswa();
        $siswa->id = $siswa_id;
        $siswa->getOne();

        // Get class information
        $kelas = new Kelas();
        $kelas_data = $kelas->getById($siswa->kelas_id);

        // Get school information (if available)
        $school_info = $this->getSchoolInfo();

        return [
            'siswa' => [
                'id' => $siswa->id,
                'nis' => $siswa->nis,
                'nama_siswa' => $siswa->nama_siswa,
                'jenis_kelamin' => $siswa->jenis_kelamin,
                'alamat' => $siswa->alamat,
                'no_telp' => $siswa->no_telp
            ],
            'kelas' => $kelas_data,
            'school' => $school_info,
            'periode' => [
                'semester' => $semester,
                'tahun_ajaran' => $tahun_ajaran
            ],
            'nilai_akademik' => $this->getNilaiRapor($siswa_id, $semester, $tahun_ajaran),
            'penilaian_sikap' => $this->getPenilaianSikap($siswa_id, $semester, $tahun_ajaran),
            'kehadiran' => $this->getKehadiran($siswa_id, $semester, $tahun_ajaran),
            'kehadiran_per_mapel' => $this->getKehadiranPerMapel($siswa_id, $semester, $tahun_ajaran),
            'ekstrakurikuler' => $this->getEkstrakurikuler($siswa_id, $semester, $tahun_ajaran),
            'prestasi' => $this->getPrestasi($siswa_id, $semester, $tahun_ajaran),
            'catatan_wali_kelas' => $this->getCatatanWaliKelas($siswa_id, $semester, $tahun_ajaran),
            'status_kenaikan' => $this->getStatusKenaikanKelas($siswa_id, $semester, $tahun_ajaran)
        ];
    }

    /**
     * Get school information from existing school profile
     *
     * @return array School information
     */
    public function getSchoolInfo() {
        // Use existing ProfilSekolah model
        $profilSekolah = new ProfilSekolah();

        if ($profilSekolah->get()) {
            // Format address from multiple fields
            $alamat_lengkap = trim($profilSekolah->alamat_jalan);
            if (!empty($profilSekolah->desa_kelurahan)) {
                $alamat_lengkap .= ', ' . $profilSekolah->desa_kelurahan;
            }
            if (!empty($profilSekolah->kecamatan)) {
                $alamat_lengkap .= ', Kec. ' . $profilSekolah->kecamatan;
            }
            if (!empty($profilSekolah->kabupaten_kota)) {
                $alamat_lengkap .= ', ' . $profilSekolah->kabupaten_kota;
            }
            if (!empty($profilSekolah->provinsi)) {
                $alamat_lengkap .= ', ' . $profilSekolah->provinsi;
            }
            if (!empty($profilSekolah->kode_pos)) {
                $alamat_lengkap .= ' ' . $profilSekolah->kode_pos;
            }

            // Use actual database values (even if they are placeholder values)
            return [
                'nama_sekolah' => $profilSekolah->nama_sekolah,
                'alamat_sekolah' => $alamat_lengkap,
                'kota' => $profilSekolah->kabupaten_kota,
                'provinsi' => $profilSekolah->provinsi,
                'kode_pos' => $profilSekolah->kode_pos,
                'telepon' => $profilSekolah->no_telepon,
                'email' => $profilSekolah->email,
                'website' => $profilSekolah->website,
                'kepala_sekolah' => $profilSekolah->nama_kepala_sekolah,
                'nip_kepala_sekolah' => $profilSekolah->nip_kepala_sekolah,
                'npsn' => $profilSekolah->npsn,
                'status_sekolah' => $profilSekolah->status_sekolah,
                'jenjang_pendidikan' => $profilSekolah->jenjang_pendidikan,
                'visi' => $profilSekolah->visi,
                'misi' => $profilSekolah->misi
            ];
        }

        // Fallback to default values only if no school profile found at all
        return [
            'nama_sekolah' => 'SMA Negeri 1',
            'alamat_sekolah' => 'Jl. Pendidikan No. 1',
            'kota' => 'Jakarta',
            'provinsi' => 'DKI Jakarta',
            'kode_pos' => '12345',
            'telepon' => '021-1234567',
            'email' => '<EMAIL>',
            'website' => 'www.sekolah.sch.id',
            'kepala_sekolah' => 'Kepala Sekolah',
            'nip_kepala_sekolah' => '196501011990031001',
            'npsn' => '',
            'status_sekolah' => 'Negeri',
            'jenjang_pendidikan' => 'SMA',
            'visi' => '',
            'misi' => ''
        ];
    }

    /**
     * Get grade description based on K13 standards
     *
     * @param float|null $nilai Grade value
     * @param float $kkm Minimum passing grade
     * @return array Grade description with predicate and description
     */
    public function getGradeDescription($nilai, $kkm) {
        // Handle missing or null grades
        if ($nilai === null || $nilai === '' || $nilai === 0) {
            return [
                'predikat' => '-',
                'deskripsi' => '-'
            ];
        }

        // Convert to float for comparison
        $nilai = (float) $nilai;

        if ($nilai >= 90) {
            return [
                'predikat' => 'A',
                'deskripsi' => 'Sangat Baik'
            ];
        } elseif ($nilai >= 80) {
            return [
                'predikat' => 'B',
                'deskripsi' => 'Baik'
            ];
        } elseif ($nilai >= $kkm) {
            return [
                'predikat' => 'C',
                'deskripsi' => 'Cukup'
            ];
        } else {
            return [
                'predikat' => 'D',
                'deskripsi' => 'Kurang'
            ];
        }
    }

    /**
     * Get character assessment description
     *
     * @param string $grade Character grade (A, B, C, D)
     * @return string Description
     */
    public function getCharacterDescription($grade) {
        switch ($grade) {
            case 'A':
                return 'Sangat Baik';
            case 'B':
                return 'Baik';
            case 'C':
                return 'Cukup';
            case 'D':
                return 'Kurang';
            default:
                return 'Baik';
        }
    }
}
