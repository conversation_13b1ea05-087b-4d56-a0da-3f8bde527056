<?php
/**
 * CLI Migration Runner
 * Command line version to run migration without web authentication
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../config/database.php';

echo "=== Database Migration Runner ===\n";
echo "Running migration to fix restore conflicts...\n\n";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Connected to database successfully.\n";
    
    // Read the migration file
    $migration_file = __DIR__ . '/migration_fix_restore_conflicts.sql';
    
    if (!file_exists($migration_file)) {
        throw new Exception("Migration file not found: {$migration_file}");
    }
    
    echo "Reading migration file...\n";
    $migration_sql = file_get_contents($migration_file);
    if ($migration_sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    // Execute the migration
    echo "Executing migration...\n";

    // Handle DELIMITER statements by splitting and processing
    $statements = [];
    $current_statement = '';
    $lines = explode("\n", $migration_sql);
    $delimiter = ';';

    foreach ($lines as $line) {
        $line = trim($line);

        // Skip empty lines and comments
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }

        // Handle DELIMITER changes
        if (preg_match('/^DELIMITER\s+(.+)$/', $line, $matches)) {
            $delimiter = trim($matches[1]);
            continue;
        }

        $current_statement .= $line . "\n";

        // Check if statement ends with current delimiter
        if (substr(rtrim($line), -strlen($delimiter)) === $delimiter) {
            // Remove the delimiter and add to statements
            $stmt = rtrim(substr($current_statement, 0, -strlen($delimiter)));
            if (!empty(trim($stmt))) {
                $statements[] = $stmt;
            }
            $current_statement = '';
        }
    }

    // Execute each statement
    $executed_count = 0;
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                $executed_count++;
                echo "✓ Executed statement " . ($executed_count) . "\n";
            } catch (PDOException $e) {
                echo "⚠ Warning on statement " . ($executed_count + 1) . ": " . $e->getMessage() . "\n";
            }
        }
    }

    echo "Total statements executed: {$executed_count}\n";
    
    // Test the created procedures
    echo "Verifying created procedures...\n";
    $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()")->fetchAll(PDO::FETCH_ASSOC);
    
    $required_procedures = ['SafeDropViews', 'SafeDropTables', 'PreRestorationCleanup', 'PostRestorationValidation'];
    $existing_procedures = [];
    
    foreach ($procedures as $proc) {
        if (in_array($proc['Name'], $required_procedures)) {
            $existing_procedures[] = $proc['Name'];
        }
    }
    
    echo "\nMigration Results:\n";
    echo "- Total procedures in database: " . count($procedures) . "\n";
    echo "- Required procedures found: " . count($existing_procedures) . "/" . count($required_procedures) . "\n";
    
    if (count($existing_procedures) === count($required_procedures)) {
        echo "✓ Migration completed successfully!\n";
        echo "✓ All required procedures are now available.\n";
    } else {
        echo "⚠ Migration completed with warnings.\n";
        echo "Missing procedures: " . implode(', ', array_diff($required_procedures, $existing_procedures)) . "\n";
    }
    
    echo "\nProcedures created:\n";
    foreach ($existing_procedures as $proc) {
        echo "- {$proc}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Migration Complete ===\n";
?>
