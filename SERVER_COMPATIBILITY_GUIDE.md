# Server Compatibility Guide - Database Restore

## <PERSON><PERSON><PERSON> di Server Hosting

Jika Anda mengalami error berikut di server hosting:

```
Fatal error: Uncaught Error: Call to undefined function exec() in /www/wwwroot/domain.com/absen/maintenance/restore.php:113
```

Ini terjadi karena fungsi `exec()` dinonaktifkan di sebagian besar shared hosting untuk alasan keamanan.

## Solusi: Server Compatible Restore

### 1. Gunakan Versi Server Compatible

Akses: `maintenance/restore_server_compatible.php`

**Fitur:**
- ✅ Tidak menggunakan fungsi `exec()`
- ✅ Menggunakan PDO untuk menjalankan SQL secara langsung
- ✅ Pemrosesan batch untuk mencegah timeout
- ✅ Error handling yang lebih baik
- ✅ Kompatibel dengan shared hosting

### 2. Perbedaan dengan Versi Desktop

| Fitur | Desktop Version | Server Compatible |
|-------|----------------|-------------------|
| Method | Command line MySQL | PDO langsung |
| Fungsi exec() | Diperlukan | Tidak diperlukan |
| Pemrosesan | Sekali jalan | Batch processing |
| Timeout handling | Bergantung server | Built-in delay |
| Error recovery | Terbatas | Advanced |

### 3. Cara Menggunakan

1. **Upload file backup** seperti biasa
2. **Pilih file backup** yang ingin di-restore
3. **Klik "Pulihkan"** - sistem akan:
   - Menjalankan pre-restoration cleanup
   - Memproses SQL dalam batch kecil
   - Memberikan delay antar batch
   - Melakukan post-restoration validation

### 4. Monitoring Progress

Server compatible version akan:
- Log progress setiap 100 statement
- Memberikan informasi detail tentang jumlah statement yang dieksekusi
- Menampilkan warning jika ada statement yang gagal (non-critical)
- Melakukan rollback otomatis jika ada error critical

### 5. Troubleshooting

**Jika masih timeout:**
1. Pecah file backup menjadi bagian yang lebih kecil
2. Restore secara bertahap
3. Tingkatkan memory limit di hosting (jika memungkinkan)

**Jika ada error permission:**
1. Pastikan folder `database/backups/` writable
2. Cek permission file backup (644 atau 755)

**Jika proses terhenti:**
1. Cek error log server
2. Gunakan file backup yang lebih kecil
3. Hubungi provider hosting untuk limit yang lebih tinggi

### 6. Optimasi untuk Server

**Untuk file backup besar:**
```php
// Dalam restore_server_compatible.php, Anda bisa adjust:
$batch_size = 25; // Kurangi jika masih timeout
usleep(200000);   // Tambah delay jika perlu (0.2 detik)
```

**Memory management:**
```php
@ini_set('memory_limit', '256M'); // Sesuaikan dengan limit hosting
@set_time_limit(600);             // 10 menit max execution
```

### 7. Verifikasi Hasil

Setelah restore berhasil, sistem akan menampilkan:
- Jumlah statement yang dieksekusi
- Jumlah tabel dan view yang berhasil dibuat
- Status validasi database
- Warning jika ada (biasanya tidak critical)

### 8. Best Practices untuk Server

1. **Selalu backup database** sebelum restore
2. **Gunakan file backup terbaru** yang sudah diperbaiki
3. **Validasi file backup** sebelum restore
4. **Monitor resource usage** selama proses
5. **Lakukan restore saat traffic rendah**

### 9. Alternatif Manual (Jika Semua Gagal)

Jika server compatible version masih bermasalah:

1. **Download file backup** ke komputer lokal
2. **Restore di localhost** menggunakan phpMyAdmin atau MySQL Workbench
3. **Export hasil restore** dalam format yang lebih kecil
4. **Upload kembali** ke server dalam bagian-bagian

### 10. Dukungan Hosting Provider

Beberapa hosting provider yang mendukung:
- ✅ Shared hosting dengan PDO enabled
- ✅ VPS/Dedicated server
- ✅ Cloud hosting (AWS, Google Cloud, dll)
- ❌ Hosting dengan PDO disabled (sangat jarang)

## Kesimpulan

Server compatible version menyelesaikan masalah `exec()` yang dinonaktifkan di hosting dengan:

1. **Menggunakan PDO** sebagai pengganti command line
2. **Batch processing** untuk mencegah timeout
3. **Error handling** yang lebih robust
4. **Monitoring progress** yang lebih baik

Dengan solusi ini, Anda dapat melakukan restore database di server hosting tanpa masalah compatibility.
