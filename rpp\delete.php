<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';

// Cek role
if($_SESSION['role'] != 'guru') {
    header("Location: /absen/");
    exit();
}

if (isset($_GET['id'])) {
    $id = $_GET['id'];

    $rpp = new Rpp();
    $rppKegiatan = new RppKegiatan();

    // Create separate database connection for transaction handling
    $database = new Database();
    $conn = $database->getConnection();

    // Start transaction
    $conn->beginTransaction();

    try {
        // Delete kegiatan first (child records)
        $rppKegiatan->deleteByRppId($id);

        // Then delete RPP
        if ($rpp->delete($id)) {
            $conn->commit();
            header("Location: index.php");
            exit();
        } else {
            throw new Exception("Gagal menghapus RPP");
        }
    } catch (Exception $e) {
        $conn->rollBack();
        die("Error: " . $e->getMessage());
    }
}

header("Location: index.php");
exit();
?>