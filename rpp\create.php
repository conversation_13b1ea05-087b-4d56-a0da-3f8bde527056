<?php
require_once '../template/header.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/Kelas.php';
require_once '../models/TahunAjaran.php';
require_once '../models/GeminiApi.php';

// Cek role
if($_SESSION['role'] != 'guru') {
    header("Location: /absen/");
    exit();
}

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

$mapel = new MataPelajaran();
$kelas = new Kelas();
$tahunAjaran = new TahunAjaran();

// Ambil mata pelajaran yang diampu oleh guru yang login
$mapel_list = $mapel->getMapelByGuruId($guru_id);
$kelas_list = $kelas->getAll();
$tahun_ajaran_list = $tahunAjaran->getAll();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $rpp = new Rpp();
    $rppKegiatan = new RppKegiatan();
    
    // Set properties
    $rpp->mapel_id = $_POST['mapel_id'];
    $rpp->guru_id = $guru_id;

    // Handle kelas_id array - convert to comma-separated string or take first value
    if (is_array($_POST['kelas_id'])) {
        // If multiple classes selected, take the first one for now
        // In future, you might want to create separate RPP for each class
        $rpp->kelas_id = $_POST['kelas_id'][0];
    } else {
        $rpp->kelas_id = $_POST['kelas_id'];
    }

    $rpp->semester = $_POST['semester'];
    $rpp->tahun_ajaran = $_POST['tahun_ajaran'];
    $rpp->nama_sekolah = $_POST['nama_sekolah'];
    $rpp->tema_subtema = $_POST['tema_subtema'];
    $rpp->materi_pokok = $_POST['materi_pokok'];
    $rpp->alokasi_waktu = $_POST['alokasi_waktu'];
    $rpp->tujuan_pembelajaran = $_POST['tujuan_pembelajaran'];
    $rpp->kompetensi_dasar = $_POST['kompetensi_dasar'];
    $rpp->indikator_pencapaian = $_POST['indikator_pencapaian'];
    $rpp->materi_pembelajaran = $_POST['materi_pembelajaran'];
    $rpp->metode_pembelajaran = $_POST['metode_pembelajaran'];
    $rpp->media_pembelajaran = $_POST['media_pembelajaran'];
    $rpp->sumber_belajar = $_POST['sumber_belajar'];
    $rpp->penilaian = $_POST['penilaian'];

    if ($rpp_id = $rpp->create()) {
        // Save kegiatan pembelajaran
        foreach ($_POST['pendahuluan'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'pendahuluan';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }

        foreach ($_POST['kegiatan_inti'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'inti';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }

        foreach ($_POST['penutup'] as $index => $deskripsi) {
            if (!empty($deskripsi)) {
                $rppKegiatan->rpp_id = $rpp_id;
                $rppKegiatan->jenis_kegiatan = 'penutup';
                $rppKegiatan->deskripsi = $deskripsi;
                $rppKegiatan->urutan = $index + 1;
                $rppKegiatan->create();
            }
        }
        
        header("Location: index.php");
        exit();
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Tambah RPP</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .ai-assistant-card {
            border: 2px solid #007bff;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
        }

        .ai-assistant-card:hover {
            box-shadow: 0 6px 12px rgba(0, 123, 255, 0.15);
        }

        .ai-loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .generated-field {
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }

        .generated-field:focus {
            background-color: #ffffff;
            border-left-color: #007bff;
        }

        .btn-ai-generate {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
        }

        .btn-ai-generate:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
            transform: translateY(-2px);
        }

        .btn-ai-generate:disabled {
            background: #6c757d;
            box-shadow: none;
            transform: none;
        }

        .ai-info-alert {
            border-left: 4px solid #17a2b8;
            background-color: #d1ecf1;
        }

        .btn-remove-kegiatan {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .kegiatan-item {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Tambah RPP</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['error'])) : ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= $_SESSION['error'] ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php unset($_SESSION['error']); ?>
                    <?php endif; ?>
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="kelas_id" class="form-label">Kelas</label>
                            <select class="form-select" id="kelas_id" name="kelas_id" required>
                                <option value="">Pilih Kelas</option>
                                <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?= $row['id'] ?>"><?= htmlspecialchars($row['nama_kelas']) ?></option>
                                <?php endwhile; ?>
                            </select>
                            <small class="text-muted">Pilih kelas terlebih dahulu untuk melihat mata pelajaran yang tersedia</small>
                        </div>

                        <div class="mb-3">
                            <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                            <select class="form-select" id="mapel_id" name="mapel_id" required>
                                <option value="">Pilih kelas terlebih dahulu</option>
                            </select>
                            <small class="text-muted">Mata pelajaran akan muncul setelah memilih kelas</small>
                        </div>

                        <div class="mb-3">
                            <label for="semester" class="form-label">Semester</label>
                            <select class="form-select" id="semester" name="semester" required>
                                <option value="">Pilih Semester</option>
                                <option value="1">Semester 1</option>
                                <option value="2">Semester 2</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                            <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                <option value="">Pilih Tahun Ajaran</option>
                                <?php while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?= $row['tahun_ajaran'] ?>"><?= $row['tahun_ajaran'] ?></option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="nama_sekolah" class="form-label">Nama Sekolah</label>
                            <input type="text" class="form-control" id="nama_sekolah" name="nama_sekolah" required>
                        </div>

                        <!-- Tema/Subtema Selection -->
                        <div class="mb-3">
                            <label for="tema_subtema" class="form-label">
                                Tema/Subtema
                                <span class="badge bg-info ms-2" id="tema-kd-indicator" style="display: none;">
                                    <i class="fas fa-link"></i> Dari KD
                                </span>
                            </label>
                            <select class="form-select" id="tema_subtema" name="tema_subtema" required>
                                <option value="">Pilih mata pelajaran dan kelas terlebih dahulu</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Pilih tema/subtema berdasarkan data KD yang tersedia
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="materi_pokok" class="form-label">
                                Materi Pokok
                                <span class="badge bg-info ms-2" id="materi-kd-indicator" style="display: none;">
                                    <i class="fas fa-link"></i> Dari KD
                                </span>
                            </label>
                            <select class="form-select" id="materi_pokok" name="materi_pokok" required>
                                <option value="">Pilih tema/subtema terlebih dahulu</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Pilih materi pokok berdasarkan tema/subtema yang dipilih
                            </small>
                        </div>

                        <!-- AI Assistant Section -->
                        <div class="mb-4">
                            <div class="card ai-assistant-card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-robot"></i> Bantuan AI - Generate RPP Otomatis
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert ai-info-alert mb-3">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Cara Menggunakan:</strong> Isi field "Tema/Subtema" dan "Materi Pokok" di atas, lalu klik tombol "Isi RPP dengan AI" untuk mengisi otomatis field-field RPP lainnya.
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <button type="button" class="btn btn-success btn-lg btn-ai-generate" id="generateRppBtn" onclick="generateRppContent()">
                                            <i class="fas fa-magic"></i> Isi RPP dengan AI
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="clearFormBtn" onclick="clearGeneratedContent()">
                                            <i class="fas fa-eraser"></i> Bersihkan Form
                                        </button>
                                    </div>

                                    <div id="aiLoadingIndicator" class="text-center mt-3" style="display: none;">
                                        <div class="spinner-border text-primary ai-loading-spinner" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">AI sedang mengisi RPP... Mohon tunggu sebentar.</p>
                                    </div>

                                    <div id="aiResultMessage" class="mt-3" style="display: none;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="alokasi_waktu" class="form-label">Alokasi Waktu</label>
                            <input type="text" class="form-control" id="alokasi_waktu" name="alokasi_waktu" required
                            placeholder="3 x 45 menit">
                        </div>

                        <div class="mb-3">
                            <label for="tujuan_pembelajaran" class="form-label">Tujuan Pembelajaran</label>
                            <textarea class="form-control" id="tujuan_pembelajaran" name="tujuan_pembelajaran" rows="3" required
                            placeholder="1. Siswa dapat menjelaskan pengertian K3LH dengan benar
2. Siswa dapat mengidentifikasi potensi bahaya di lingkungan kerja
3. Siswa dapat menerapkan prosedur K3LH saat praktikum
4. Siswa dapat menggunakan alat pelindung diri sesuai standar"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="kompetensi_dasar" class="form-label">
                                Kompetensi Dasar
                                <span class="badge bg-info ms-2" id="kd-auto-indicator" style="display: none;">
                                    <i class="fas fa-magic"></i> Auto-populated
                                </span>
                            </label>
                            <textarea class="form-control" id="kompetensi_dasar" name="kompetensi_dasar" rows="3" required
                            placeholder="3.1 Menerapkan K3LH disesuaikan dengan lingkungan kerja
4.1 Melaksanakan K3LH dilingkungan kerja"></textarea>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Kompetensi Dasar akan otomatis terisi berdasarkan mata pelajaran dan kelas yang dipilih.
                                Anda tetap dapat mengedit sesuai kebutuhan.
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="indikator_pencapaian" class="form-label">Indikator Pencapaian</label>
                            <textarea class="form-control" id="indikator_pencapaian" name="indikator_pencapaian" rows="3" required
                            placeholder="3.1.1 Menjelaskan pengertian K3LH dengan tepat
3.1.2 Mengidentifikasi potensi bahaya di lingkungan kerja
4.1.1 Mendemonstrasikan penggunaan alat pelindung diri
4.1.2 Menerapkan prosedur K3LH dalam praktik"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="materi_pembelajaran" class="form-label">Materi Pembelajaran</label>
                            <textarea class="form-control" id="materi_pembelajaran" name="materi_pembelajaran" rows="3" required
                            placeholder="1. Pengertian K3LH
2. Tujuan dan manfaat K3LH
3. Prosedur K3LH di laboratorium komputer
4. Potensi bahaya dan pencegahannya
5. Penggunaan alat pelindung diri"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="metode_pembelajaran" class="form-label">Metode Pembelajaran</label>
                            <textarea class="form-control" id="metode_pembelajaran" name="metode_pembelajaran" rows="3" required
                            placeholder="1. Ceramah interaktif
2. Demonstrasi
3. Praktik langsung
4. Diskusi kelompok
5. Problem based learning"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="media_pembelajaran" class="form-label">Media Pembelajaran</label>
                            <textarea class="form-control" id="media_pembelajaran" name="media_pembelajaran" rows="3" required
                            placeholder="1. LCD Proyektor
2. Komputer/Laptop
3. Video tutorial K3LH
4. Alat pelindung diri
5. Poster K3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="sumber_belajar" class="form-label">Sumber Belajar</label>
                            <textarea class="form-control" id="sumber_belajar" name="sumber_belajar" rows="3" required
                            placeholder="1. Modul Komputer dan Jaringan Dasar
2. Buku K3LH untuk SMK
3. SOP Laboratorium Komputer
4. Video pembelajaran K3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="penilaian" class="form-label">Penilaian</label>
                            <textarea class="form-control" id="penilaian" name="penilaian" rows="3" required
                            placeholder="1. Sikap: Observasi menggunakan lembar pengamatan
2. Pengetahuan: Tes tertulis pilihan ganda dan essay
3. Keterampilan: Praktik penerapan K3LH"></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kegiatan Pembelajaran</label>
                            <div id="kegiatan-container">
                                <div class="kegiatan-item mb-3" data-kegiatan="1">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">Kegiatan 1</h5>

                                            <div class="mb-3">
                                                <label class="form-label">Pendahuluan</label>
                                                <textarea class="form-control" name="pendahuluan[]" rows="3" required
                                                placeholder="1. Guru membuka pelajaran dan mengecek kehadiran
2. Guru menyampaikan tujuan pembelajaran
3. Guru memberikan apersepsi tentang pentingnya K3"></textarea>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Kegiatan Inti</label>
                                                <textarea class="form-control" name="kegiatan_inti[]" rows="3" required
                                                placeholder="1. Guru menjelaskan konsep dasar K3LH
2. Siswa mengamati video tentang K3 di lab komputer
3. Siswa berdiskusi tentang potensi bahaya
4. Guru mendemonstrasikan penggunaan APD
5. Siswa mempraktikkan penggunaan APD"></textarea>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Penutup</label>
                                                <textarea class="form-control" name="penutup[]" rows="3" required
                                                placeholder="1. Siswa menyimpulkan pembelajaran
2. Guru memberikan post-test
3. Guru menyampaikan materi pertemuan selanjutnya"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-secondary" onclick="tambahKegiatan()">
                                    <i class="fas fa-plus"></i> Tambah Kegiatan
                                </button>
                            </div>
                        </div>

                        <hr>
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan RPP
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        // Inisialisasi Select2 untuk multiple select
        $('#kelas_id').select2({
            placeholder: 'Pilih Kelas',
            allowClear: true
        });

        // Event handler ketika kelas dipilih - load mata pelajaran options
        $('#kelas_id').change(function() {
            var kelas_id = $(this).val();
            console.log('Kelas selected:', kelas_id);

            const mapelSelect = document.getElementById('mapel_id');

            // Clear existing options
            mapelSelect.innerHTML = '<option value="">Pilih mata pelajaran</option>';

            if(kelas_id) {
                // Show loading state
                mapelSelect.innerHTML = '<option value="">Loading mata pelajaran...</option>';

                // Use fetch API like in KD module
                fetch('get_mapel_by_kelas.php?kelas_id=' + kelas_id)
                    .then(response => {
                        console.log('Response status:', response.status);
                        if (!response.ok) {
                            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Fetch Response:', data);

                        mapelSelect.innerHTML = '<option value="">Pilih Mata Pelajaran</option>';

                        if (data.length === 0) {
                            mapelSelect.innerHTML = '<option value="">Tidak ada mata pelajaran tersedia untuk kelas ini</option>';
                        } else {
                            data.forEach(mapel => {
                                const option = document.createElement('option');
                                option.value = mapel.id;
                                option.textContent = mapel.nama_mapel;
                                mapelSelect.appendChild(option);
                            });
                        }

                        // Trigger change event for dependent dropdowns
                        $(mapelSelect).trigger('change');
                    })
                    .catch(error => {
                        console.error('Fetch Error:', error);
                        mapelSelect.innerHTML = '<option value="">Error loading mata pelajaran: ' + error.message + '</option>';
                    });
            } else {
                mapelSelect.innerHTML = '<option value="">Pilih kelas terlebih dahulu</option>';
                // Clear KD field when kelas is cleared
                clearKdField();
                clearTemaMateriOptions();
                // Trigger change event
                $(mapelSelect).trigger('change');
            }
        });

        // Event handler ketika mata pelajaran dipilih - load tema/subtema options
        $('#mapel_id').change(function() {
            var mapel_id = $(this).val();
            var kelas_id = $('#kelas_id').val();

            console.log('Mapel selection changed:', {mapel_id: mapel_id, kelas_id: kelas_id});

            if (mapel_id && kelas_id) {
                loadTemaMateriOptions(mapel_id, kelas_id);
                loadKompetensiDasar(mapel_id, kelas_id); // Keep existing KD auto-population
            } else {
                clearKdField();
                clearTemaMateriOptions();
            }
        });

        // Event handler for tema/subtema selection
        $('#tema_subtema').change(function() {
            var mapel_id = $('#mapel_id').val();
            var kelas_id = $('#kelas_id').val();
            var tema_subtema = $(this).val();

            if (mapel_id && kelas_id && tema_subtema) {
                loadMateriPokokOptions(mapel_id, kelas_id, tema_subtema);
            } else {
                clearMateriPokokOptions();
            }

            // Update kompetensi dasar when tema changes
            updateKompetensiDasarFromTemaMateri();
        });

        // Event handler for materi pokok selection
        $('#materi_pokok').change(function() {
            // Update kompetensi dasar when materi pokok changes
            updateKompetensiDasarFromTemaMateri();
        });
    });

    let kegiatanCounter = 1;

    // Function to load Kompetensi Dasar based on subject and class
    function loadKompetensiDasar(mapel_id, kelas_id) {
        // Show loading indicator
        const kdField = document.getElementById('kompetensi_dasar');
        const kdIndicator = document.getElementById('kd-auto-indicator');

        kdField.value = 'Memuat Kompetensi Dasar...';
        kdField.disabled = true;

        // Get current semester and tahun_ajaran from form if available
        const semester = document.getElementById('semester') ? document.getElementById('semester').value : '';
        const tahun_ajaran = document.getElementById('tahun_ajaran') ? document.getElementById('tahun_ajaran').value : '';

        // Debug: Log the data being sent
        const requestData = {
            mapel_id: mapel_id,
            kelas_id: kelas_id,
            semester: semester,
            tahun_ajaran: tahun_ajaran
        };
        console.log('KD Request data:', requestData);

        $.ajax({
            url: 'get_kompetensi_dasar_clean.php', // Using clean version with array handling
            type: 'POST',
            data: requestData,
            success: function(response) {
                try {
                    // Check if response is already an object (jQuery auto-parsed) or a string
                    const data = typeof response === 'string' ? JSON.parse(response) : response;

                    console.log('KD Response received:', data); // Debug log

                    if (data.success) {
                        if (data.data && data.data.formatted_kd) {
                            kdField.value = data.data.formatted_kd;
                            kdIndicator.style.display = 'inline-block';

                            // Show success message
                            showKdMessage('success', `${data.data.count} Kompetensi Dasar berhasil dimuat`);
                        } else {
                            kdField.value = '';
                            kdIndicator.style.display = 'none';
                            showKdMessage('info', data.message || 'Tidak ada data KD ditemukan');
                        }
                    } else {
                        kdField.value = '';
                        kdIndicator.style.display = 'none';
                        showKdMessage('warning', data.message || 'Gagal memuat data KD');

                        // Log debug info if available
                        if (data.debug) {
                            console.log('KD Debug info:', data.debug);
                        }
                    }
                } catch (e) {
                    kdField.value = '';
                    kdIndicator.style.display = 'none';
                    console.error('KD JSON Parse Error:', e);
                    console.error('Raw response:', response);
                    console.error('Response type:', typeof response);
                    showKdMessage('error', 'Terjadi kesalahan saat memuat data KD: ' + e.message);
                }

                kdField.disabled = false;
            },
            error: function(xhr, status, error) {
                kdField.value = '';
                kdField.disabled = false;
                kdIndicator.style.display = 'none';

                let errorMessage = 'Gagal menghubungi server';
                let debugInfo = '';

                if (xhr.status === 0) {
                    errorMessage = 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
                    debugInfo = 'Network error or CORS issue';
                } else if (xhr.status === 404) {
                    errorMessage = 'Endpoint tidak ditemukan (404). Periksa path file.';
                    debugInfo = 'File get_kompetensi_dasar.php not found';
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error (500). Periksa log server untuk detail.';
                    debugInfo = 'Internal server error';
                } else {
                    errorMessage = `Error ${xhr.status}: ${error}`;
                    debugInfo = `Status: ${status}, Error: ${error}`;
                }

                console.error('KD AJAX Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error,
                    debugInfo: debugInfo
                });

                showKdMessage('error', errorMessage + ' (Debug: ' + debugInfo + ')');
            }
        });
    }

    // Function to clear KD field
    function clearKdField() {
        const kdField = document.getElementById('kompetensi_dasar');
        const kdIndicator = document.getElementById('kd-auto-indicator');

        kdField.value = '';
        kdIndicator.style.display = 'none';
    }

    // Function to show KD-related messages
    function showKdMessage(type, message) {
        // Create or update message element
        let messageElement = document.getElementById('kd-message');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'kd-message';
            messageElement.className = 'alert alert-dismissible fade show mt-2';

            // Insert after KD field
            const kdField = document.getElementById('kompetensi_dasar');
            kdField.parentNode.insertBefore(messageElement, kdField.nextSibling);
        }

        // Update message
        messageElement.className = `alert alert-${type} alert-dismissible fade show mt-2`;
        messageElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto-hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                if (messageElement && messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 3000);
        }
    }

    // Function to load tema/subtema and materi pokok options
    function loadTemaMateriOptions(mapel_id, kelas_id) {
        const temaSelect = document.getElementById('tema_subtema');
        const materiSelect = document.getElementById('materi_pokok');
        const temaIndicator = document.getElementById('tema-kd-indicator');
        const materiIndicator = document.getElementById('materi-kd-indicator');

        // Show loading state
        temaSelect.innerHTML = '<option value="">Memuat data tema/subtema...</option>';
        temaSelect.disabled = true;
        materiSelect.innerHTML = '<option value="">Pilih tema/subtema terlebih dahulu</option>';
        materiSelect.disabled = true;

        // Get current semester and tahun_ajaran from form if available
        const semester = document.getElementById('semester') ? document.getElementById('semester').value : '';
        const tahun_ajaran = document.getElementById('tahun_ajaran') ? document.getElementById('tahun_ajaran').value : '';

        const requestData = {
            mapel_id: mapel_id,
            kelas_id: kelas_id,
            semester: semester,
            tahun_ajaran: tahun_ajaran
        };

        console.log('Loading Tema/Materi options:', requestData);

        $.ajax({
            url: 'get_kd_data.php',
            type: 'POST',
            data: requestData,
            success: function(response) {
                try {
                    const data = typeof response === 'string' ? JSON.parse(response) : response;
                    console.log('Tema/Materi Options Response:', data);

                    // Reset tema/subtema dropdown
                    temaSelect.innerHTML = '<option value="">Pilih tema/subtema</option>';

                    if (data.success && data.data) {
                        // Populate tema/subtema options
                        if (data.data.tema_subtema_options && data.data.tema_subtema_options.length > 0) {
                            data.data.tema_subtema_options.forEach(tema => {
                                const option = document.createElement('option');
                                option.value = tema.value;
                                option.textContent = tema.text;
                                temaSelect.appendChild(option);
                            });
                            temaIndicator.style.display = 'inline-block';
                        }

                        // Store materi pokok options for later use
                        window.materiPokokOptions = data.data.materi_pokok_options || [];

                        showTemaMateriMessage('success', `${data.data.tema_subtema_options.length} tema/subtema tersedia`);
                    } else {
                        temaIndicator.style.display = 'none';
                        materiIndicator.style.display = 'none';
                        showTemaMateriMessage('info', data.message || 'Tidak ada data tema/materi tersedia');
                    }
                } catch (e) {
                    console.error('Tema/Materi Options JSON Parse Error:', e);
                    temaSelect.innerHTML = '<option value="">Error memuat data</option>';
                    showTemaMateriMessage('error', 'Terjadi kesalahan saat memuat data tema/materi');
                }

                temaSelect.disabled = false;
            },
            error: function(xhr, status, error) {
                console.error('Tema/Materi Options AJAX Error:', {status: xhr.status, error: error});
                temaSelect.innerHTML = '<option value="">Error memuat data</option>';
                temaSelect.disabled = false;
                showTemaMateriMessage('error', 'Gagal menghubungi server untuk memuat data tema/materi');
            }
        });
    }

    // Function to load materi pokok options based on selected tema/subtema
    function loadMateriPokokOptions(mapel_id, kelas_id, tema_subtema) {
        const materiSelect = document.getElementById('materi_pokok');
        const materiIndicator = document.getElementById('materi-kd-indicator');

        // Reset materi pokok dropdown
        materiSelect.innerHTML = '<option value="">Pilih materi pokok</option>';
        materiSelect.disabled = false;

        // Filter materi pokok options based on selected tema
        if (window.materiPokokOptions && window.materiPokokOptions.length > 0) {
            window.materiPokokOptions.forEach(materi => {
                const option = document.createElement('option');
                option.value = materi.value;
                option.textContent = materi.text;
                materiSelect.appendChild(option);
            });
            materiIndicator.style.display = 'inline-block';
        }
    }

    // Function to update kompetensi dasar based on tema/subtema and materi pokok selection
    function updateKompetensiDasarFromTemaMateri() {
        const mapel_id = document.getElementById('mapel_id').value;
        const kelas_id = document.getElementById('kelas_id').value;
        const tema_subtema = document.getElementById('tema_subtema').value;
        const materi_pokok = document.getElementById('materi_pokok').value;
        const kdField = document.getElementById('kompetensi_dasar');
        const kdIndicator = document.getElementById('kd-auto-indicator');

        // Only proceed if we have all required data
        if (!mapel_id || !kelas_id || !tema_subtema || !materi_pokok) {
            return;
        }

        // Show loading state
        kdField.value = 'Memuat Kompetensi Dasar berdasarkan tema dan materi...';
        kdField.disabled = true;

        // Get current semester and tahun_ajaran from form if available
        const semester = document.getElementById('semester') ? document.getElementById('semester').value : '';
        const tahun_ajaran = document.getElementById('tahun_ajaran') ? document.getElementById('tahun_ajaran').value : '';

        const requestData = {
            mapel_id: mapel_id,
            kelas_id: kelas_id,
            tema_subtema: tema_subtema,
            materi_pokok: materi_pokok,
            semester: semester,
            tahun_ajaran: tahun_ajaran
        };

        console.log('Updating KD from tema/materi:', requestData);

        $.ajax({
            url: 'get_kd_details.php',
            type: 'POST',
            data: requestData,
            success: function(response) {
                try {
                    const data = typeof response === 'string' ? JSON.parse(response) : response;
                    console.log('KD Update Response:', data);

                    if (data.success && data.data && data.data.formatted_kd) {
                        kdField.value = data.data.formatted_kd;
                        kdIndicator.style.display = 'inline-block';
                        showKdMessage('success', `${data.data.count} Kompetensi Dasar berhasil dimuat berdasarkan tema dan materi`);
                    } else {
                        kdField.value = '';
                        kdIndicator.style.display = 'none';
                        showKdMessage('info', data.message || 'Tidak ada KD ditemukan untuk tema dan materi yang dipilih');
                    }
                } catch (e) {
                    console.error('KD Update JSON Parse Error:', e);
                    kdField.value = '';
                    kdIndicator.style.display = 'none';
                    showKdMessage('error', 'Terjadi kesalahan saat memuat KD');
                }

                kdField.disabled = false;
            },
            error: function(xhr, status, error) {
                console.error('KD Update AJAX Error:', {status: xhr.status, error: error});
                kdField.value = '';
                kdField.disabled = false;
                kdIndicator.style.display = 'none';
                showKdMessage('error', 'Gagal menghubungi server untuk memuat KD');
            }
        });
    }

    // Function to load specific KD details (kept for backward compatibility)
    function loadKdDetails(kd_id) {
        const temaField = document.getElementById('tema_subtema');
        const materiField = document.getElementById('materi_pokok');
        const temaIndicator = document.getElementById('tema-auto-indicator');
        const materiIndicator = document.getElementById('materi-auto-indicator');

        console.log('Loading KD details for ID:', kd_id);

        $.ajax({
            url: 'get_kd_details.php',
            type: 'POST',
            data: { kd_id: kd_id },
            success: function(response) {
                try {
                    const data = typeof response === 'string' ? JSON.parse(response) : response;
                    console.log('KD Details Response:', data);

                    if (data.success && data.data) {
                        // Auto-fill tema/subtema if available
                        if (data.data.tema_subtema) {
                            temaField.value = data.data.tema_subtema;
                            temaIndicator.style.display = 'inline-block';
                        }

                        // Auto-fill materi pokok if available
                        if (data.data.materi_pokok) {
                            materiField.value = data.data.materi_pokok;
                            materiIndicator.style.display = 'inline-block';
                        }

                        showKdSelectionMessage('success', 'Data KD berhasil dimuat dan field terisi otomatis');
                    } else {
                        showKdSelectionMessage('warning', data.message || 'Gagal memuat detail KD');
                    }
                } catch (e) {
                    console.error('KD Details JSON Parse Error:', e);
                    showKdSelectionMessage('error', 'Terjadi kesalahan saat memuat detail KD');
                }
            },
            error: function(xhr, status, error) {
                console.error('KD Details AJAX Error:', {status: xhr.status, error: error});
                showKdSelectionMessage('error', 'Gagal menghubungi server untuk memuat detail KD');
            }
        });
    }

    // Function to clear tema/materi options
    function clearTemaMateriOptions() {
        const temaSelect = document.getElementById('tema_subtema');
        const materiSelect = document.getElementById('materi_pokok');
        const temaIndicator = document.getElementById('tema-kd-indicator');
        const materiIndicator = document.getElementById('materi-kd-indicator');

        temaSelect.innerHTML = '<option value="">Pilih mata pelajaran dan kelas terlebih dahulu</option>';
        materiSelect.innerHTML = '<option value="">Pilih tema/subtema terlebih dahulu</option>';
        temaIndicator.style.display = 'none';
        materiIndicator.style.display = 'none';

        // Clear stored options
        window.materiPokokOptions = [];
    }

    // Function to clear materi pokok options
    function clearMateriPokokOptions() {
        const materiSelect = document.getElementById('materi_pokok');
        const materiIndicator = document.getElementById('materi-kd-indicator');

        materiSelect.innerHTML = '<option value="">Pilih tema/subtema terlebih dahulu</option>';
        materiIndicator.style.display = 'none';
    }

    // Function to show KD selection messages
    function showKdSelectionMessage(type, message) {
        // Create or update message element
        let messageElement = document.getElementById('kd-selection-message');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'kd-selection-message';
            messageElement.className = 'alert alert-dismissible fade show mt-2';

            // Insert after KD selection field
            const kdField = document.getElementById('kd_selection');
            kdField.parentNode.insertBefore(messageElement, kdField.nextSibling);
        }

        // Update message
        messageElement.className = `alert alert-${type} alert-dismissible fade show mt-2`;
        messageElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto-hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                if (messageElement && messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 3000);
        }
    }

    // Function to show tema/materi selection messages
    function showTemaMateriMessage(type, message) {
        // Create or update message element
        let messageElement = document.getElementById('tema-materi-message');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'tema-materi-message';
            messageElement.className = 'alert alert-dismissible fade show mt-2';

            // Insert after tema/subtema field
            const temaField = document.getElementById('tema_subtema');
            temaField.parentNode.insertBefore(messageElement, temaField.nextSibling);
        }

        // Update message
        messageElement.className = `alert alert-${type} alert-dismissible fade show mt-2`;
        messageElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto-hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                if (messageElement && messageElement.parentNode) {
                    messageElement.remove();
                }
            }, 3000);
        }
    }

    function tambahKegiatan() {
        kegiatanCounter++;
        const container = document.getElementById('kegiatan-container');

        const kegiatanDiv = document.createElement('div');
        kegiatanDiv.className = 'kegiatan-item mb-3';
        kegiatanDiv.setAttribute('data-kegiatan', kegiatanCounter);
        kegiatanDiv.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Kegiatan ${kegiatanCounter}</h5>
                        <button type="button" class="btn btn-danger btn-sm btn-remove-kegiatan" onclick="hapusKegiatan(this)">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Pendahuluan</label>
                        <textarea class="form-control" name="pendahuluan[]" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Kegiatan Inti</label>
                        <textarea class="form-control" name="kegiatan_inti[]" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Penutup</label>
                        <textarea class="form-control" name="penutup[]" rows="3" required></textarea>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(kegiatanDiv);
    }

    function hapusKegiatan(button) {
        const kegiatanItem = button.closest('.kegiatan-item');
        const kegiatanNumber = parseInt(kegiatanItem.getAttribute('data-kegiatan'));

        // Don't allow deletion of the first kegiatan
        if (kegiatanNumber === 1) {
            alert('Kegiatan pertama tidak dapat dihapus.');
            return;
        }

        if (confirm('Apakah Anda yakin ingin menghapus kegiatan ini?')) {
            kegiatanItem.remove();
            updateKegiatanNumbers();
        }
    }

    function updateKegiatanNumbers() {
        const kegiatanItems = document.querySelectorAll('.kegiatan-item');
        kegiatanItems.forEach((item, index) => {
            const newNumber = index + 1;
            item.setAttribute('data-kegiatan', newNumber);
            const title = item.querySelector('.card-title');
            if (title) {
                title.textContent = `Kegiatan ${newNumber}`;
            }
        });
        kegiatanCounter = kegiatanItems.length;
    }

    // AI Assistant Functions
    function generateRppContent() {
        const temaSubtema = document.getElementById('tema_subtema').value.trim();
        const materiPokok = document.getElementById('materi_pokok').value.trim();

        if (!temaSubtema || !materiPokok) {
            showAiMessage('error', 'Silakan isi field "Tema/Subtema" dan "Materi Pokok" terlebih dahulu.');
            return;
        }

        // Show loading
        showAiLoading(true);
        document.getElementById('generateRppBtn').disabled = true;

        // Get additional context
        const mapelId = document.getElementById('mapel_id').value;
        const kelasSelect = document.getElementById('kelas_id');
        const kelasId = kelasSelect.value || (kelasSelect.selectedOptions.length > 0 ? kelasSelect.selectedOptions[0].value : '');
        const kompetensiDasar = document.getElementById('kompetensi_dasar').value.trim();

        // Count current kegiatan sections
        const kegiatanCount = document.querySelectorAll('.kegiatan-item').length;

        // Prepare form data
        const formData = new FormData();
        formData.append('tema_subtema', temaSubtema);
        formData.append('materi_pokok', materiPokok);
        formData.append('mapel_id', mapelId);
        formData.append('kelas_id', kelasId);
        formData.append('kompetensi_dasar', kompetensiDasar);
        formData.append('kegiatan_count', kegiatanCount);

        // Make AJAX request
        fetch('generate_rpp_content.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            showAiLoading(false);
            document.getElementById('generateRppBtn').disabled = false;

            // Validate response structure
            if (!data || typeof data !== 'object') {
                console.error('Invalid response structure:', data);
                showAiMessage('error', 'Respons server tidak valid. Silakan coba lagi.');
                return;
            }

            if (data.success) {
                // Validate data.data exists and is an object
                if (!data.data || typeof data.data !== 'object') {
                    console.error('Invalid data structure in response:', data);
                    showAiMessage('error', 'Struktur data respons tidak valid. Silakan coba lagi.');
                    return;
                }

                console.log('AI Response data:', data.data); // Debug log
                fillRppFields(data.data);
                showAiMessage('success', 'RPP berhasil di-generate! Anda dapat mengedit konten sesuai kebutuhan sebelum menyimpan.');
            } else {
                // Show the user-friendly error message from server
                showAiMessage('error', data.message || 'Terjadi kesalahan saat menghasilkan RPP.');

                // Log debug information if available
                if (data.debug_message) {
                    console.error('Debug Error:', data.debug_message);
                }
            }
        })
        .catch(error => {
            showAiLoading(false);
            document.getElementById('generateRppBtn').disabled = false;

            // Provide more specific error messages based on error type
            let errorMessage = 'Terjadi kesalahan koneksi. Silakan coba lagi.';

            if (error.message.includes('HTTP 429')) {
                errorMessage = 'Layanan AI sedang sibuk. Silakan tunggu beberapa menit dan coba lagi.';
            } else if (error.message.includes('HTTP 500') || error.message.includes('HTTP 502') || error.message.includes('HTTP 503')) {
                errorMessage = 'Server sedang mengalami gangguan. Silakan coba lagi dalam beberapa menit.';
            } else if (error.message.includes('HTTP 404')) {
                errorMessage = 'Layanan tidak ditemukan. Hubungi administrator sistem.';
            } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
            }

            showAiMessage('error', errorMessage);
            console.error('Network Error:', error);
        });
    }

    // Function to format AI-generated content with proper line breaks
    function formatAiContent(content) {
        // Type checking and validation
        if (content === null || content === undefined) {
            console.warn('formatAiContent: content is null or undefined');
            return '';
        }

        // Convert to string if it's not already a string
        if (typeof content !== 'string') {
            console.warn('formatAiContent: content is not a string, type:', typeof content, 'value:', content);
            // Try to convert to string
            try {
                content = String(content);
            } catch (e) {
                console.error('formatAiContent: Failed to convert content to string:', e);
                return '';
            }
        }

        // Check if content is empty after conversion
        if (!content || content.trim() === '') {
            return '';
        }

        // Convert literal \n to actual line breaks
        let formatted = content.replace(/\\n/g, '\n');

        // Handle numbered lists - ensure proper spacing between items
        // Pattern: number followed by dot and space, then content
        formatted = formatted.replace(/(\d+\.\s[^0-9\n]*?)(\d+\.)/g, '$1\n$2');

        // Handle lettered lists (a., b., c., etc.)
        formatted = formatted.replace(/([a-z]\.\s[^a-z\n]*?)([a-z]\.)/g, '$1\n$2');

        // Handle bullet points with dashes
        formatted = formatted.replace(/(-\s[^-\n]*?)(-\s)/g, '$1\n$2');

        // Clean up multiple consecutive newlines (max 2)
        formatted = formatted.replace(/\n{3,}/g, '\n\n');

        // Trim whitespace from start and end
        formatted = formatted.trim();

        return formatted;
    }

    function fillRppFields(data) {
        // Validate input data
        if (!data || typeof data !== 'object') {
            console.error('fillRppFields: Invalid data parameter:', data);
            showAiMessage('error', 'Data respons AI tidak valid. Silakan coba lagi.');
            return;
        }

        console.log('fillRppFields: Processing data:', data); // Debug log

        // Check if KD was auto-populated (has the indicator visible)
        const kdIndicator = document.getElementById('kd-auto-indicator');
        const kdAutoPopulated = kdIndicator && kdIndicator.style.display !== 'none';

        // Fill main RPP fields and add visual indicators
        const fieldsToFill = [
            'tujuan_pembelajaran', 'indikator_pencapaian',
            'materi_pembelajaran', 'metode_pembelajaran', 'media_pembelajaran',
            'sumber_belajar', 'penilaian'
        ];

        // Only include kompetensi_dasar if it wasn't auto-populated and is present in response
        if (!kdAutoPopulated && data['kompetensi_dasar']) {
            fieldsToFill.push('kompetensi_dasar');
        }

        fieldsToFill.forEach(fieldName => {
            if (data[fieldName] !== undefined && data[fieldName] !== null) {
                const element = document.getElementById(fieldName);
                if (element) {
                    try {
                        // Format the content with proper line breaks
                        const formattedContent = formatAiContent(data[fieldName]);
                        element.value = formattedContent;
                        element.classList.add('generated-field');

                        // Add a small indicator
                        addGeneratedIndicator(element);
                    } catch (error) {
                        console.error(`Error formatting content for field ${fieldName}:`, error);
                        console.error('Field data:', data[fieldName]);
                        // Set a fallback value
                        element.value = String(data[fieldName] || '');
                        element.classList.add('generated-field');
                        addGeneratedIndicator(element);
                    }
                }
            }
        });

        // Fill kegiatan pembelajaran for ALL activity fields (including dynamically added ones)
        const pendahuluanTextareas = document.querySelectorAll('textarea[name="pendahuluan[]"]');
        const kegiatanIntiTextareas = document.querySelectorAll('textarea[name="kegiatan_inti[]"]');
        const penutupTextareas = document.querySelectorAll('textarea[name="penutup[]"]');

        // Helper function to safely fill textarea with error handling
        function fillTextareaWithErrorHandling(textarea, content, activityType, index = null) {
            if (content !== undefined && content !== null) {
                try {
                    const formattedContent = formatAiContent(content);
                    textarea.value = formattedContent;
                    textarea.classList.add('generated-field');
                    addGeneratedIndicator(textarea);
                } catch (error) {
                    console.error(`Error formatting ${activityType} content${index !== null ? ` at index ${index}` : ''}:`, error);
                    console.error('Content data:', content);
                    // Set a fallback value
                    textarea.value = String(content || '');
                    textarea.classList.add('generated-field');
                    addGeneratedIndicator(textarea);
                }
            }
        }

        // Handle multiple activities (array format) or single activity (string format)
        if (Array.isArray(data.kegiatan_pendahuluan)) {
            // Multiple activities - fill each with unique content
            pendahuluanTextareas.forEach((textarea, index) => {
                if (data.kegiatan_pendahuluan[index]) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_pendahuluan[index], 'pendahuluan', index);
                }
            });

            kegiatanIntiTextareas.forEach((textarea, index) => {
                if (data.kegiatan_inti[index]) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_inti[index], 'kegiatan_inti', index);
                }
            });

            penutupTextareas.forEach((textarea, index) => {
                if (data.kegiatan_penutup[index]) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_penutup[index], 'penutup', index);
                }
            });
        } else {
            // Single activity - fill all fields with same content (backward compatibility)
            pendahuluanTextareas.forEach((textarea, index) => {
                if (data.kegiatan_pendahuluan) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_pendahuluan, 'pendahuluan');
                }
            });

            kegiatanIntiTextareas.forEach((textarea, index) => {
                if (data.kegiatan_inti) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_inti, 'kegiatan_inti');
                }
            });

            penutupTextareas.forEach((textarea, index) => {
                if (data.kegiatan_penutup) {
                    fillTextareaWithErrorHandling(textarea, data.kegiatan_penutup, 'penutup');
                }
            });
        }

        // Scroll to the first filled field for user to review
        document.getElementById('tujuan_pembelajaran').scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    function addGeneratedIndicator(element) {
        // Remove existing indicator if any
        const existingIndicator = element.parentNode.querySelector('.ai-generated-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Add new indicator
        const indicator = document.createElement('small');
        indicator.className = 'text-success ai-generated-indicator';
        indicator.innerHTML = '<i class="fas fa-robot"></i> Diisi oleh AI - Dapat diedit';
        indicator.style.display = 'block';
        indicator.style.marginTop = '4px';

        element.parentNode.appendChild(indicator);

        // Remove indicator when user starts editing
        element.addEventListener('input', function() {
            element.classList.remove('generated-field');
            const indicator = element.parentNode.querySelector('.ai-generated-indicator');
            if (indicator) {
                indicator.remove();
            }
        }, { once: true });
    }

    function clearGeneratedContent() {
        if (confirm('Apakah Anda yakin ingin menghapus semua konten yang telah diisi? Tindakan ini tidak dapat dibatalkan.')) {
            // Check if KD was auto-populated
            const kdIndicator = document.getElementById('kd-auto-indicator');
            const kdAutoPopulated = kdIndicator && kdIndicator.style.display !== 'none';

            // Clear main RPP fields (except tema_subtema, materi_pokok, and auto-populated KD)
            const fieldsToKeep = ['mapel_id', 'kelas_id', 'semester', 'tahun_ajaran', 'nama_sekolah', 'tema_subtema', 'materi_pokok', 'alokasi_waktu'];

            // If KD was auto-populated, preserve it
            if (kdAutoPopulated) {
                fieldsToKeep.push('kompetensi_dasar');
            }

            const allInputs = document.querySelectorAll('input, textarea, select');

            allInputs.forEach(input => {
                if (!fieldsToKeep.includes(input.name) && !input.name.includes('[]')) {
                    input.value = '';
                    input.classList.remove('generated-field');
                }
            });

            // Clear ALL kegiatan pembelajaran fields (including dynamically added ones)
            document.querySelectorAll('textarea[name="pendahuluan[]"], textarea[name="kegiatan_inti[]"], textarea[name="penutup[]"]').forEach(textarea => {
                textarea.value = '';
                textarea.classList.remove('generated-field');
            });

            // Remove all AI generated indicators
            document.querySelectorAll('.ai-generated-indicator').forEach(indicator => {
                indicator.remove();
            });

            const preservedMessage = kdAutoPopulated ?
                'Form telah dibersihkan. Field dasar (Tema/Subtema, Materi Pokok) dan Kompetensi Dasar yang auto-populated tetap dipertahankan.' :
                'Form telah dibersihkan. Field dasar (Tema/Subtema, Materi Pokok) tetap dipertahankan.';
            showAiMessage('info', preservedMessage);
        }
    }

    function showAiLoading(show) {
        const loadingIndicator = document.getElementById('aiLoadingIndicator');
        loadingIndicator.style.display = show ? 'block' : 'none';
    }

    function showAiMessage(type, message) {
        const messageDiv = document.getElementById('aiResultMessage');
        let alertClass = 'alert-info';
        let icon = 'fas fa-info-circle';

        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                alertClass = 'alert-danger';
                icon = 'fas fa-exclamation-triangle';
                break;
            case 'warning':
                alertClass = 'alert-warning';
                icon = 'fas fa-exclamation-circle';
                break;
        }

        messageDiv.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${icon}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        messageDiv.style.display = 'block';

        // Auto hide after 10 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                const alert = messageDiv.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 150);
                }
            }, 10000);
        }
    }
    </script>
</body>
</html>

<?php
require_once '../template/footer.php';
?>