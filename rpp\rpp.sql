CREATE TABLE `rpp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mapel_id` int(11) NOT NULL,
  `guru_id` int(11) NOT NULL,
  `kelas_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `nama_sekolah` varchar(100) NOT NULL,
  `tema_subtema` varchar(100) DEFAULT NULL,
  `materi_pokok` varchar(255) NOT NULL,
  `alokasi_waktu` varchar(50) NOT NULL,
  `tujuan_pembelajaran` text NOT NULL,
  `kompetensi_dasar` text NOT NULL,
  `indikator_pencapaian` text NOT NULL,
  `materi_pembelajaran` text NOT NULL,
  `metode_pembelajaran` text NOT NULL,
  `media_pembelajaran` text NOT NULL,
  `sumber_belajar` text NOT NULL,
  `penilaian` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `mapel_id` (`mapel_id`),
  KEY `guru_id` (`guru_id`),
  KEY `kelas_id` (`kelas_id`),
  CONSTRAINT `rpp_ibfk_1` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE,
  CONSTRAINT `rpp_ibfk_2` FOREIGN KEY (`guru_id`) REFERENCES `guru` (`id`) ON DELETE CASCADE,
  CONSTRAINT `rpp_ibfk_3` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;