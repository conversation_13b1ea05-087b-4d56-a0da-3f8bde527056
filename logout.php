<?php
session_start();
require_once 'models/User.php';

// Clear remember token from database if it exists
if (isset($_SESSION['user_id'])) {
    $user = new User();
    $user->id = $_SESSION['user_id'];
    $user->clearRememberToken();
}

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/'); // Set expiration to past time
    unset($_COOKIE['remember_token']);
}

// Destroy session
session_destroy();

// Redirect to login page
header("Location: login.php");
exit();
?>
