<?php
require_once __DIR__ . '/../config/database.php';

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $password;
    public $nama_lengkap;
    public $role;
    public $remember_token;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function login($username, $password) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE username = :username LIMIT 1";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(":username", $username);
        $stmt->execute();
        
        if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if(password_verify($password, $row['password'])) {
                $this->id = $row['id'];
                $this->username = $row['username'];
                $this->nama_lengkap = $row['nama_lengkap'];
                $this->role = $row['role'];
                $this->remember_token = $row['remember_token'];
                
                // Update last login time
                $update_query = "UPDATE " . $this->table_name . " SET last_login = CURRENT_TIMESTAMP WHERE id = :id";
                $update_stmt = $this->conn->prepare($update_query);
                $update_stmt->bindParam(":id", $this->id);
                $update_stmt->execute();
                
                return true;
            }
        }
        return false;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                (username, password, nama_lengkap, role) 
                VALUES (:username, :password, :nama_lengkap, :role)";

        $stmt = $this->conn->prepare($query);

        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->nama_lengkap = htmlspecialchars(strip_tags($this->nama_lengkap));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->password = password_hash($this->password, PASSWORD_DEFAULT);

        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":password", $this->password);
        $stmt->bindParam(":nama_lengkap", $this->nama_lengkap);
        $stmt->bindParam(":role", $this->role);

        return $stmt->execute();
    }

    public function getGuruId($user_id) {
        // First get the nama_lengkap from users table
        $query = "SELECT nama_lengkap FROM " . $this->table_name . " WHERE id = :user_id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // Then get guru_id using nama_lengkap
            $query = "SELECT id FROM guru WHERE nama_lengkap = :nama_lengkap AND status = 'aktif' LIMIT 1";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":nama_lengkap", $row['nama_lengkap']);
            $stmt->execute();
            
            if($guru = $stmt->fetch(PDO::FETCH_ASSOC)) {
                return $guru['id'];
            }
        }
        return null;
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
        
        if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->username = $row['username'];
            $this->password = $row['password'];
            $this->nama_lengkap = $row['nama_lengkap'];
            $this->role = $row['role'];
            $this->remember_token = $row['remember_token'];
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                SET nama_lengkap = :nama_lengkap,
                    username = :username,
                    role = :role 
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nama_lengkap = htmlspecialchars(strip_tags($this->nama_lengkap));
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->role = htmlspecialchars(strip_tags($this->role));

        // Bind parameters
        $stmt->bindParam(":nama_lengkap", $this->nama_lengkap);
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":id", $this->id);

        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function updatePassword() {
        $query = "UPDATE " . $this->table_name . " 
                SET password = :password 
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        $this->password = password_hash($this->password, PASSWORD_DEFAULT);
        $stmt->bindParam(":password", $this->password);
        $stmt->bindParam(":id", $this->id);

        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function updateUsername() {
        $query = "UPDATE " . $this->table_name . " 
                SET username = :username 
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->username = htmlspecialchars(strip_tags($this->username));

        // Bind parameters
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":id", $this->id);

        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function updateRememberToken($token) {
        $query = "UPDATE " . $this->table_name . " 
                SET remember_token = :token 
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->bindParam(":id", $this->id);
        
        return $stmt->execute();
    }

    public function getUserByRememberToken($token) {
        $query = "SELECT * FROM " . $this->table_name . " 
                WHERE remember_token = :token 
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function clearRememberToken() {
        $query = "UPDATE " . $this->table_name . " 
                SET remember_token = NULL 
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        
        return $stmt->execute();
    }

    public function getAllUsers() {
        $query = "SELECT u.*, 
                CASE 
                    WHEN u.role = 'guru' THEN g.nip 
                    ELSE NULL 
                END as nip,
                CASE 
                    WHEN u.role = 'guru' THEN g.status 
                    ELSE NULL 
                END as status
                FROM " . $this->table_name . " u 
                LEFT JOIN guru g ON u.nama_lengkap = g.nama_lengkap 
                ORDER BY u.role ASC, u.nama_lengkap ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }

    public function delete() {
        // First check if this is not the admin account
        if ($this->username === 'admin') {
            return false;
        }

        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Bind parameter
        $stmt->bindParam(":id", $this->id);
        
        try {
            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function usernameExists($username) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username LIMIT 1";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(":username", $username);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ? true : false;
    }
}
