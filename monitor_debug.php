<?php
echo "<h2>Debug Log Monitor</h2>\n";
echo "<p>This page will show the debug log contents. Refresh after generating questions.</p>\n";

// Clear previous log
if (isset($_GET['clear'])) {
    if (file_exists('debug_save_questions.log')) {
        unlink('debug_save_questions.log');
        echo "<p style='color: green;'>✅ Debug log cleared</p>\n";
    }
}

echo "<p><a href='?clear=1' style='color: red;'>Clear Debug Log</a></p>\n";

if (file_exists('debug_save_questions.log')) {
    $log_content = file_get_contents('debug_save_questions.log');
    if (!empty($log_content)) {
        echo "<h3>Debug Log Contents:</h3>\n";
        echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 500px; overflow-y: auto;'>";
        echo htmlspecialchars($log_content);
        echo "</pre>\n";
        
        echo "<p><strong>Log file size:</strong> " . filesize('debug_save_questions.log') . " bytes</p>\n";
        echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime('debug_save_questions.log')) . "</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ Debug log file exists but is empty</p>\n";
    }
} else {
    echo "<p style='color: gray;'>ℹ️ No debug log file found yet. Generate some questions first.</p>\n";
}

echo "<h3>Instructions:</h3>\n";
echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>\n";
echo "<ol>\n";
echo "<li>Go to <a href='configure_generation.php?rpp_id=2' target='_blank'>Generate Questions</a></li>\n";
echo "<li>Configure: 1 multiple choice, 0 essay</li>\n";
echo "<li>Generate questions</li>\n";
echo "<li>Select and save questions</li>\n";
echo "<li>Come back here and refresh to see the debug log</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<p><em>Auto-refresh in 10 seconds...</em></p>\n";
echo "<script>setTimeout(function(){ location.reload(); }, 10000);</script>\n";
?>
