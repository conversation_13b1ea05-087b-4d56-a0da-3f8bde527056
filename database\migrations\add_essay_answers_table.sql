-- Migration script to add essay_answers table
-- Run this script if you already have an existing database

-- Check if table exists and create if not
CREATE TABLE IF NOT EXISTS `essay_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `question_type` enum('rpp_question','multi_rpp_question') NOT NULL DEFAULT 'rpp_question',
  `expected_answer` text NOT NULL,
  `answer_points` text DEFAULT NULL,
  `scoring_rubric` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`scoring_rubric`)),
  `generation_metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`generation_metadata`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON><PERSON> KEY `unique_question_answer` (`question_id`, `question_type`),
  <PERSON><PERSON>Y `question_id` (`question_id`),
  KEY `question_type` (`question_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_essay_answers_question_type` ON `essay_answers` (`question_type`);
CREATE INDEX IF NOT EXISTS `idx_essay_answers_created_at` ON `essay_answers` (`created_at`);

-- Insert sample data (optional - remove if not needed)
-- This is just for testing purposes
/*
INSERT IGNORE INTO `essay_answers` (`question_id`, `question_type`, `expected_answer`, `answer_points`, `scoring_rubric`, `generation_metadata`) VALUES
(1, 'rpp_question', 'Contoh jawaban yang diharapkan untuk soal essay pertama.', 'Poin 1: Penjelasan konsep\nPoin 2: Contoh aplikasi\nPoin 3: Kesimpulan', '{"excellent": "Jawaban lengkap dengan penjelasan detail dan contoh yang tepat", "good": "Jawaban baik dengan penjelasan yang cukup", "fair": "Jawaban cukup namun kurang detail", "poor": "Jawaban kurang tepat atau tidak lengkap"}', '{"confidence_score": 0.95, "reasoning": "Generated based on learning objectives"}');
*/
