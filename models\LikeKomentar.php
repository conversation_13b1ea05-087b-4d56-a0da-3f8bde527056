<?php
if (!defined('ABSEN_PATH')) {
    define('ABSEN_PATH', dirname(dirname(__FILE__)));
}
require_once ABSEN_PATH . '/config/database.php';

class LikeKomentar {
    private $conn;
    private $table_name = "like_komentar";

    public $id;
    public $komentar_id;
    public $user_id;
    public $is_dislike;
    public $created_at;

    public function __construct() {
        $db = new database();
        $this->conn = $db->getConnection();
    }

    public function toggleLike() {
        // Cek apakah sudah ada like/dislike dari user ini
        $query = "SELECT id, is_dislike FROM " . $this->table_name . " 
                WHERE komentar_id = :komentar_id AND user_id = :user_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $this->komentar_id);
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->execute();

        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // Jika sudah ada dan statusnya sama, hapus like/dislike
            if ($row['is_dislike'] == $this->is_dislike) {
                $query = "DELETE FROM " . $this->table_name . " 
                        WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":id", $row['id']);
                return $stmt->execute();
            } else {
                // Jika sudah ada tapi statusnya berbeda, update status
                $query = "UPDATE " . $this->table_name . " 
                        SET is_dislike = :is_dislike 
                        WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":is_dislike", $this->is_dislike);
                $stmt->bindParam(":id", $row['id']);
                return $stmt->execute();
            }
        } else {
            // Jika belum ada, buat baru
            $query = "INSERT INTO " . $this->table_name . " 
                    (komentar_id, user_id, is_dislike) 
                    VALUES (:komentar_id, :user_id, :is_dislike)";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":komentar_id", $this->komentar_id);
            $stmt->bindParam(":user_id", $this->user_id);
            $stmt->bindParam(":is_dislike", $this->is_dislike);
            return $stmt->execute();
        }
    }

    public function getLikeCount($komentar_id) {
        $query = "SELECT 
                    SUM(CASE WHEN is_dislike = 0 THEN 1 ELSE 0 END) as like_count,
                    SUM(CASE WHEN is_dislike = 1 THEN 1 ELSE 0 END) as dislike_count
                FROM " . $this->table_name . " 
                WHERE komentar_id = :komentar_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $komentar_id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getUserLikeStatus($komentar_id, $user_id) {
        $query = "SELECT is_dislike 
                FROM " . $this->table_name . " 
                WHERE komentar_id = :komentar_id AND user_id = :user_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":komentar_id", $komentar_id);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            return $row['is_dislike'] ? 'dislike' : 'like';
        }
        return null;
    }
}