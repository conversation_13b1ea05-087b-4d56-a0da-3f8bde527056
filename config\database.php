<?php
class database {
    private $host = "localhost";
    private $db_name = "db_absensi";
    private $username = "root";
    private $password = "";
    private $kalender_api_key = "68e1bb03c174f233";
    private $tinymce_api_key = "l0oi6sreor4yotc9b3y7r042f2aqgt5o769v70qizl5azg7x";
    private $gemini_api_key ="AIzaSyBMBpIhaID40sjcQgQSEwlgjbw9bOGCWRU";
    public $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->exec("set names utf8");
        } catch(PDOException $e) {
            echo "Connection error: " . $e->getMessage();
        }

        return $this->conn;
    }

    // Getter methods for database credentials
    public function getHost() {
        return $this->host;
    }

    public function getDatabase() {
        return $this->db_name;
    }

    public function getUsername() {
        return $this->username;
    }

    public function getPassword() {
        return $this->password;
    }   

    public function getKalenderApiKey() {
        return $this->kalender_api_key;
    }

    public function getTinyMCEApiKey() {
        return $this->tinymce_api_key;
    }
    
    public function getGeminiApiKey() {
        return $this->gemini_api_key;
    }
}
