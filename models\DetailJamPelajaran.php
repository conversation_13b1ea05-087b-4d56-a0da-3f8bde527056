<?php
require_once __DIR__ . '/../config/database.php';

class DetailJamPelajaran {
    private $conn;
    public $id;
    public $config_id;
    public $jam_ke;
    public $jam_mulai;
    public $jam_selesai;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO detail_jam_pelajaran (config_id, jam_ke, jam_mulai, jam_selesai) 
                 VALUES (:config_id, :jam_ke, :jam_mulai, :jam_selesai)";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':config_id', $this->config_id);
        $stmt->bindParam(':jam_ke', $this->jam_ke);
        $stmt->bindParam(':jam_mulai', $this->jam_mulai);
        $stmt->bindParam(':jam_selesai', $this->jam_selesai);
        
        return $stmt->execute();
    }

    public function getByConfigId($config_id) {
        $query = "SELECT * FROM detail_jam_pelajaran WHERE config_id = :config_id ORDER BY jam_ke";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':config_id', $config_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function deleteByConfigId($config_id) {
        $query = "DELETE FROM detail_jam_pelajaran WHERE config_id = :config_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':config_id', $config_id);
        
        return $stmt->execute();
    }
}
