<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

use Dompdf\Dompdf;
use Dompdf\Options;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

// Validate parameters
if (!isset($_GET['blueprint_id']) || !isset($_GET['format'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: blueprint_list.php");
    exit();
}

$blueprint_id = $_GET['blueprint_id'];
$format = $_GET['format'];

if (!in_array($format, ['pdf', 'word'])) {
    $_SESSION['error'] = "Format export tidak valid.";
    header("Location: single_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
}

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Get blueprint data
$examBlueprint = new ExamBlueprint();
$blueprint_data = $examBlueprint->getOne($blueprint_id);

if (!$blueprint_data || $blueprint_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Kisi-kisi tidak ditemukan atau bukan milik Anda.";
    header("Location: blueprint_list.php");
    exit();
}

// Get related RPP data
$rpp_data = null;
if ($blueprint_data['rpp_id']) {
    $rpp = new Rpp();
    $rpp_data = $rpp->getOne($blueprint_data['rpp_id']);
}

// Decode JSON data
$generated_blueprint = json_decode($blueprint_data['blueprint_data'], true);

if (!$generated_blueprint) {
    $_SESSION['error'] = "Data kisi-kisi tidak valid.";
    header("Location: single_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
}

// Get application info
$app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
$app_version = "v2.19.0";

try {
    if ($format === 'pdf') {
        exportToPDF($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data);
    } else {
        exportToWord($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data);
    }
} catch (Exception $e) {
    $_SESSION['error'] = "Gagal export kisi-kisi: " . $e->getMessage();
    header("Location: single_blueprint_result.php?blueprint_id=" . $blueprint_id);
    exit();
}

function exportToPDF($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data) {
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    $options->set('isHtml5ParserEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = generateBlueprintHTML($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data);
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    $filename = 'Kisi-kisi_' . ($rpp_data ? $rpp_data['tema_subtema'] : 'RPP') . '_' . date('Y-m-d') . '.pdf';
    $filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $filename);

    $dompdf->stream($filename, array("Attachment" => true));
}

function exportToWord($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data) {
    $phpWord = new PhpWord();
    
    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator($app_name . ' ' . $app_version);
    $properties->setTitle('Kisi-kisi Ujian RPP');
    $properties->setDescription('Kisi-kisi ujian yang dihasilkan dari ' . $app_name);
    
    $section = $phpWord->addSection();
    
    // Title
    $titleStyle = array('name' => 'Arial', 'size' => 16, 'bold' => true);
    $section->addText('KISI-KISI UJIAN', $titleStyle, array('alignment' => 'center'));
    $section->addTextBreak();
    
    // Exam Information
    if (isset($generated_blueprint['exam_info'])) {
        $exam_info = $generated_blueprint['exam_info'];
        
        $tableStyle = array('borderSize' => 6, 'borderColor' => '000000', 'cellMargin' => 80);
        $table = $section->addTable($tableStyle);
        
        $table->addRow();
        $table->addCell(3000)->addText('Mata Pelajaran', array('bold' => true));
        $table->addCell(3000)->addText($exam_info['subject'] ?? ($rpp_data['nama_mapel'] ?? ''));
        $table->addCell(3000)->addText('Jenis Ujian', array('bold' => true));
        $table->addCell(3000)->addText($exam_info['type'] ?? '');
        
        $table->addRow();
        $table->addCell(3000)->addText('Kelas', array('bold' => true));
        $table->addCell(3000)->addText($rpp_data['nama_kelas'] ?? '');
        $table->addCell(3000)->addText('Durasi', array('bold' => true));
        $table->addCell(3000)->addText($exam_info['duration'] ?? '');
        
        $section->addTextBreak();
    }
    
    // Learning Objectives
    if (isset($generated_blueprint['learning_objectives']) && !empty($generated_blueprint['learning_objectives'])) {
        $section->addText('TUJUAN PEMBELAJARAN', array('bold' => true, 'size' => 12));
        $section->addTextBreak();
        
        foreach ($generated_blueprint['learning_objectives'] as $objective) {
            if (isset($objective['objectives']) && is_array($objective['objectives'])) {
                foreach ($objective['objectives'] as $obj) {
                    $section->addText('• ' . $obj);
                }
            }
        }
        $section->addTextBreak();
    }
    
    // Cognitive Mapping
    if (isset($generated_blueprint['cognitive_mapping'])) {
        $section->addText('PEMETAAN LEVEL KOGNITIF', array('bold' => true, 'size' => 12));
        $section->addTextBreak();
        
        $tableStyle = array('borderSize' => 6, 'borderColor' => '000000', 'cellMargin' => 80);
        $table = $section->addTable($tableStyle);
        
        $table->addRow();
        $table->addCell(4000)->addText('Level Kognitif', array('bold' => true));
        $table->addCell(2000)->addText('Jumlah Soal', array('bold' => true));
        $table->addCell(2000)->addText('Persentase', array('bold' => true));
        
        $cognitiveDescriptions = [
            'C1' => 'Mengingat (Remember)',
            'C2' => 'Memahami (Understand)',
            'C3' => 'Menerapkan (Apply)',
            'C4' => 'Menganalisis (Analyze)',
            'C5' => 'Mengevaluasi (Evaluate)',
            'C6' => 'Mencipta (Create)'
        ];
        
        $totalQuestions = array_sum($generated_blueprint['cognitive_mapping']);
        
        foreach ($generated_blueprint['cognitive_mapping'] as $level => $count) {
            if ($count > 0) {
                $percentage = $totalQuestions > 0 ? round(($count / $totalQuestions) * 100, 1) : 0;
                
                $table->addRow();
                $table->addCell(4000)->addText($level . ' - ' . ($cognitiveDescriptions[$level] ?? $level));
                $table->addCell(2000)->addText($count);
                $table->addCell(2000)->addText($percentage . '%');
            }
        }
        
        $section->addTextBreak();
    }

    // Blueprint Table
    if (isset($generated_blueprint['blueprint_table']) && !empty($generated_blueprint['blueprint_table'])) {
        $section->addText('TABEL KISI-KISI', array('bold' => true, 'size' => 12));
        $section->addTextBreak();

        $tableStyle = array('borderSize' => 6, 'borderColor' => '000000', 'cellMargin' => 80);
        $table = $section->addTable($tableStyle);

        // Table header
        $table->addRow();
        $table->addCell(2000)->addText('Chapter', array('bold' => true));
        $table->addCell(3000)->addText('Tujuan Pembelajaran', array('bold' => true));
        $table->addCell(2500)->addText('Indikator', array('bold' => true));
        $table->addCell(1500)->addText('Level Kognitif', array('bold' => true));
        $table->addCell(1500)->addText('No. Soal', array('bold' => true));
        $table->addCell(1000)->addText('Jumlah', array('bold' => true));

        // Table content
        foreach ($generated_blueprint['blueprint_table'] as $row) {
            $table->addRow();
            $table->addCell(2000)->addText($row['chapter'] ?? '');
            $table->addCell(3000)->addText($row['learning_objective'] ?? '');
            $table->addCell(2500)->addText($row['indicator'] ?? '');
            $table->addCell(1500)->addText($row['cognitive_level'] ?? '');
            $table->addCell(1500)->addText(
                is_array($row['question_numbers']) ? implode(', ', $row['question_numbers']) : ($row['question_numbers'] ?? '')
            );
            $table->addCell(1000)->addText($row['total_questions'] ?? 1);
        }

        $section->addTextBreak();
    }

    // Summary
    if (isset($generated_blueprint['summary'])) {
        $section->addText('RINGKASAN', array('bold' => true, 'size' => 12));
        $section->addTextBreak();
        
        $summary = $generated_blueprint['summary'];
        $section->addText('Total Soal: ' . ($summary['total_questions'] ?? 0));
        $section->addText('Soal Pilihan Ganda: ' . ($summary['multiple_choice'] ?? 0));
        $section->addText('Soal Essay: ' . ($summary['essay'] ?? 0));
        
        $section->addTextBreak();
    }
    
    // Footer
    $section->addText('Dibuat dengan ' . $app_name . ' ' . $app_version, 
                     array('size' => 8, 'italic' => true), 
                     array('alignment' => 'center'));
    
    $filename = 'Kisi-kisi_' . ($rpp_data ? $rpp_data['tema_subtema'] : 'RPP') . '_' . date('Y-m-d') . '.docx';
    $filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $filename);
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save('php://output');
}

function generateBlueprintHTML($blueprint_data, $generated_blueprint, $app_name, $app_version, $rpp_data) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Kisi-kisi Ujian RPP</title>
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; line-height: 1.4; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .subtitle { font-size: 14px; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #000; padding: 8px; text-align: left; }
            th { background-color: #f0f0f0; font-weight: bold; }
            .section-title { font-size: 14px; font-weight: bold; margin: 20px 0 10px 0; }
            .footer { text-align: center; margin-top: 30px; font-size: 10px; color: #666; }
            .info-table td { border: 1px solid #ccc; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="title">KISI-KISI UJIAN</div>
            <div class="subtitle">' . htmlspecialchars($blueprint_data['blueprint_title']) . '</div>
        </div>';
    
    // Exam Information
    if (isset($generated_blueprint['exam_info'])) {
        $exam_info = $generated_blueprint['exam_info'];
        $html .= '
        <table class="info-table">
            <tr>
                <td width="150"><strong>Mata Pelajaran</strong></td>
                <td>' . htmlspecialchars($exam_info['subject'] ?? ($rpp_data['nama_mapel'] ?? '')) . '</td>
                <td width="150"><strong>Jenis Ujian</strong></td>
                <td>' . htmlspecialchars($exam_info['type'] ?? '') . '</td>
            </tr>
            <tr>
                <td><strong>Kelas</strong></td>
                <td>' . htmlspecialchars($rpp_data['nama_kelas'] ?? '') . '</td>
                <td><strong>Durasi</strong></td>
                <td>' . htmlspecialchars($exam_info['duration'] ?? '') . '</td>
            </tr>
        </table>';
    }
    
    // Learning Objectives
    if (isset($generated_blueprint['learning_objectives']) && !empty($generated_blueprint['learning_objectives'])) {
        $html .= '<div class="section-title">TUJUAN PEMBELAJARAN</div>';
        $html .= '<table><thead><tr><th>Chapter</th><th>Tujuan Pembelajaran</th></tr></thead><tbody>';
        
        foreach ($generated_blueprint['learning_objectives'] as $objective) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($objective['chapter'] ?? 'Chapter 1') . '</td>';
            $html .= '<td>';
            if (isset($objective['objectives']) && is_array($objective['objectives'])) {
                $html .= '<ul>';
                foreach ($objective['objectives'] as $obj) {
                    $html .= '<li>' . htmlspecialchars($obj) . '</li>';
                }
                $html .= '</ul>';
            }
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody></table>';
    }
    
    // Cognitive Mapping
    if (isset($generated_blueprint['cognitive_mapping'])) {
        $html .= '<div class="section-title">PEMETAAN LEVEL KOGNITIF</div>';
        $html .= '<table><thead><tr><th>Level</th><th>Jumlah Soal</th><th>Persentase</th></tr></thead><tbody>';
        
        $cognitiveDescriptions = [
            'C1' => 'Mengingat (Remember)',
            'C2' => 'Memahami (Understand)',
            'C3' => 'Menerapkan (Apply)',
            'C4' => 'Menganalisis (Analyze)',
            'C5' => 'Mengevaluasi (Evaluate)',
            'C6' => 'Mencipta (Create)'
        ];
        
        $totalQuestions = array_sum($generated_blueprint['cognitive_mapping']);
        
        foreach ($generated_blueprint['cognitive_mapping'] as $level => $count) {
            if ($count > 0) {
                $percentage = $totalQuestions > 0 ? round(($count / $totalQuestions) * 100, 1) : 0;
                $html .= '<tr>';
                $html .= '<td><strong>' . $level . '</strong> - ' . ($cognitiveDescriptions[$level] ?? $level) . '</td>';
                $html .= '<td style="text-align: center;">' . $count . '</td>';
                $html .= '<td style="text-align: center;">' . $percentage . '%</td>';
                $html .= '</tr>';
            }
        }
        
        $html .= '</tbody></table>';
    }

    // Blueprint Table
    if (isset($generated_blueprint['blueprint_table']) && !empty($generated_blueprint['blueprint_table'])) {
        $html .= '<div class="section-title">TABEL KISI-KISI</div>';
        $html .= '<table><thead><tr>';
        $html .= '<th>Chapter</th>';
        $html .= '<th>Tujuan Pembelajaran</th>';
        $html .= '<th>Indikator</th>';
        $html .= '<th>Level Kognitif</th>';
        $html .= '<th>No. Soal</th>';
        $html .= '<th>Jumlah</th>';
        $html .= '</tr></thead><tbody>';

        foreach ($generated_blueprint['blueprint_table'] as $row) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($row['chapter'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['learning_objective'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['indicator'] ?? '') . '</td>';
            $html .= '<td style="text-align: center;">' . htmlspecialchars($row['cognitive_level'] ?? '') . '</td>';
            $html .= '<td style="text-align: center;">';
            if (is_array($row['question_numbers'])) {
                $html .= htmlspecialchars(implode(', ', $row['question_numbers']));
            } else {
                $html .= htmlspecialchars($row['question_numbers'] ?? '');
            }
            $html .= '</td>';
            $html .= '<td style="text-align: center;">' . htmlspecialchars($row['total_questions'] ?? 1) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';
    }

    // Summary
    if (isset($generated_blueprint['summary'])) {
        $summary = $generated_blueprint['summary'];
        $html .= '<div class="section-title">RINGKASAN</div>';
        $html .= '<table>';
        $html .= '<tr><td width="200"><strong>Total Soal</strong></td><td>' . ($summary['total_questions'] ?? 0) . '</td></tr>';
        $html .= '<tr><td><strong>Soal Pilihan Ganda</strong></td><td>' . ($summary['multiple_choice'] ?? 0) . '</td></tr>';
        $html .= '<tr><td><strong>Soal Essay</strong></td><td>' . ($summary['essay'] ?? 0) . '</td></tr>';
        $html .= '</table>';
    }
    
    $html .= '
        <div class="footer">
            Dibuat dengan ' . $app_name . ' ' . $app_version . ' pada ' . date('d/m/Y H:i') . '
        </div>
    </body>
    </html>';
    
    return $html;
}
?>
