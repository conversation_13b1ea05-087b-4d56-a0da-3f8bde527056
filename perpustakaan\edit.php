<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

$perpustakaan = new Perpustakaan();

// Cek jika id tidak ada
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "ID buku tidak ditemukan.";
    header("Location: view.php");
    exit();
}

$id = $_GET['id'];
$kategori = $perpustakaan->getAllKategori();

// Ambil data buku
$buku = $perpustakaan->getBukuById($id);
if (!$buku) {
    $_SESSION['error'] = "Buku tidak ditemukan.";
    header("Location: view.php");
    exit();
}

// Proses form jika ada POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'judul_buku' => $_POST['judul_buku'],
        'id_kategori' => $_POST['id_kategori'],
        'pengarang' => $_POST['pengarang'],
        'penerbit' => $_POST['penerbit'],
        'tahun_terbit' => $_POST['tahun_terbit'],
        'isbn' => $_POST['isbn'],
        'jumlah_buku' => $_POST['jumlah_buku'],
        'lokasi' => $_POST['lokasi']
    ];

    try {
        if ($perpustakaan->editBuku($id, $data)) {
            $_SESSION['success'] = "Buku berhasil diperbarui";
            header("Location: view.php");
            exit();
        } else {
            throw new Exception("Gagal memperbarui buku");
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Edit Buku</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Edit Buku</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Form Edit Buku</h3>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST">
                                <div class="form-group">
                                    <label for="judul_buku">Judul Buku</label>
                                    <input type="text" class="form-control" id="judul_buku" name="judul_buku" value="<?= htmlspecialchars($buku['judul_buku']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="id_kategori">Kategori</label>
                                    <select class="form-control" id="id_kategori" name="id_kategori" required>
                                        <option value="">Pilih Kategori</option>
                                        <?php foreach ($kategori as $k) : ?>
                                            <option value="<?= $k['id_kategori']; ?>" <?= ($k['id_kategori'] == $buku['id_kategori']) ? 'selected' : ''; ?>>
                                                <?= htmlspecialchars($k['nama_kategori']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="pengarang">Pengarang</label>
                                    <input type="text" class="form-control" id="pengarang" name="pengarang" value="<?= htmlspecialchars($buku['pengarang']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="penerbit">Penerbit</label>
                                    <input type="text" class="form-control" id="penerbit" name="penerbit" value="<?= htmlspecialchars($buku['penerbit']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="tahun_terbit">Tahun Terbit</label>
                                    <input type="number" class="form-control" id="tahun_terbit" name="tahun_terbit" min="1900" max="<?= date('Y'); ?>" value="<?= htmlspecialchars($buku['tahun_terbit']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="isbn">ISBN</label>
                                    <input type="text" class="form-control" id="isbn" name="isbn" value="<?= htmlspecialchars($buku['isbn']); ?>">
                                </div>
                                <div class="form-group">
                                    <label for="jumlah_buku">Jumlah Buku</label>
                                    <input type="number" class="form-control" id="jumlah_buku" name="jumlah_buku" min="0" value="<?= htmlspecialchars($buku['jumlah_buku']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="lokasi">Lokasi</label>
                                    <input type="text" class="form-control" id="lokasi" name="lokasi" value="<?= htmlspecialchars($buku['lokasi']); ?>">
                                </div>
                                <button type="submit" class="btn btn-primary">Simpan</button>
                                <a href="index.php" class="btn btn-secondary">Kembali</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
