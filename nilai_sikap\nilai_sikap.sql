DROP TABLE IF EXISTS `nilai_sikap`;
CREATE TABLE `nilai_sikap` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `kelas_id` int(11) NOT NULL,
  `mapel_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `ta<PERSON>_ajaran` varchar(9) NOT NULL,
  `nilai_spiritual` enum('A','B','C','D') NOT NULL DEFAULT 'B',
  `deskripsi_spiritual` text NOT NULL,
  `nilai_sosial` enum('A','B','C','D') NOT NULL DEFAULT 'B',
  `deskripsi_sosial` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `siswa_id` (`siswa_id`),
  KEY `kelas_id` (`kelas_id`),
  KEY `mapel_id` (`mapel_id`),
  CONSTRAINT `nilai_sikap_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE,
  CONSTRAINT `nilai_sikap_ibfk_2` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE,
  CONSTRAINT `nilai_sikap_ibfk_3` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
