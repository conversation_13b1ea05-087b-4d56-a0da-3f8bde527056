<?php
require_once '../template/header.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/User.php';
require_once '../models/DetailJadwalJam.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';

$jadwal = new JadwalPelajaran();
$kelas = new Kelas();
$user = new User();
$detailJadwalJam = new DetailJadwalJam();
$periodeAktif = new PeriodeAktif();
$tahunAjaran = new TahunAjaran();
$tingkat = new Tingkat();
$jurusan = new Jurusan();

// Get active period
$periodeAktif->getActive();

// Get guru_id for current user if role is guru
$guru_id = null;
if ($_SESSION['role'] === 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get filter parameters
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$selected_semester = isset($_GET['semester']) ? $_GET['semester'] : $periodeAktif->semester;
$selected_tahun = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periodeAktif->tahun_ajaran;

// Filter by class if selected and user is admin
if ($_SESSION['role'] === 'admin') {
    $result = $selected_kelas ? 
        $jadwal->getByKelas($selected_kelas, $selected_semester, $selected_tahun) : 
        $jadwal->getAll($selected_semester, $selected_tahun);
} else {
    // For guru, show their schedules with period filter
    if (isset($_GET['semester']) && isset($_GET['tahun_ajaran'])) {
        $result = $jadwal->getByGuruId($guru_id, $selected_semester, $selected_tahun);
    } else {
        $result = $jadwal->getByGuruId($guru_id, $periodeAktif->semester, $periodeAktif->tahun_ajaran);
    }
}

// Check if there's any data
$has_data = ($result && $result->rowCount() > 0);

// Get class list for filter (only for admin)
$kelas_list = $kelas->getAll();

// Get tahun ajaran list
$tahun_ajaran_list = $tahunAjaran->getAll();

// Handle success message
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Jadwal Pelajaran</h5>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                <div>
                    <a href="import.php" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Import Excel
                    </a>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Jadwal
                    </a>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Filter Form -->
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'guru'): ?>
                <form action="" method="get" class="mb-4">
                    <div class="row">
                        <?php if ($_SESSION['role'] === 'admin'): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="kelas_id" class="form-label">Kelas</label>
                                <select name="kelas_id" id="kelas_id" class="form-select">
                                    <option value="">Semua Kelas</option>
                                    <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['id']; ?>" 
                                                <?php echo $selected_kelas == $row['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="semester" class="form-label">Semester</label>
                                <select name="semester" id="semester" class="form-select">
                                    <option value="1" <?php echo $selected_semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                    <option value="2" <?php echo $selected_semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                                    <?php while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['tahun_ajaran']; ?>" 
                                                <?php echo $selected_tahun == $row['tahun_ajaran'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($row['tahun_ajaran']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block">Filter</button>
                            </div>
                        </div>
                    </div>
                </form>
                <?php endif; ?>

                <div class="table-responsive">
                    <?php if (!$has_data): ?>
                    <div class="alert alert-info">
                        <?php if ($_SESSION['role'] === 'guru'): ?>
                            Anda belum memiliki jadwal mengajar untuk periode ini.
                        <?php else: ?>
                            Tidak ada data jadwal yang tersedia.
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <table class="table table-striped table-hover" id="tableJadwal" <?php echo !$has_data ? 'style="display: none;"' : ''; ?>>
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th width="10%">Kelas</th>
                                <th width="10%">Tingkat</th>
                                <th width="12%">Jurusan</th>
                                <th width="20%">Mata Pelajaran</th>
                                <th width="8%">Hari</th>
                                <th width="20%">Jam</th>
                                <?php if ($_SESSION['role'] === 'admin'): ?>
                                <th width="15%">Guru Pengampu</th>
                                <th width="13%">Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            if ($has_data):
                            $no = 1;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)):
                                // Get tingkat
                                $tingkat_nama = '';
                                if (!empty($row['tingkat_id'])) {
                                    $tingkat->id = $row['tingkat_id'];
                                    if ($tingkat->getOne()) {
                                        $tingkat_nama = $tingkat->nama_tingkat;
                                    }
                                }

                                // Get jurusan
                                $jurusan_ids = $jadwal->getJurusanIds($row['id']);
                                $jurusan_names = [];
                                foreach ($jurusan_ids as $jid) {
                                    $jurusan->id = $jid;
                                    if ($jurusan->getOne()) {
                                        $jurusan_names[] = $jurusan->nama_jurusan;
                                    }
                                }
                                $jurusan_nama = implode(', ', $jurusan_names);

                                $details = $detailJadwalJam->getByJadwalId($row['id']);
                                $conflicts = $jadwal->getConflictingSchedules($row['id']);
                                $has_conflict = !empty($conflicts);
                                $jam_ranges = [];
                                $current_range = [];
                                $prev_jam = null;
                                
                                while ($detail = $details->fetch(PDO::FETCH_ASSOC)) {
                                    if ($prev_jam === null) {
                                        $current_range = [$detail];
                                    } else {
                                        $prev_detail = end($current_range);
                                        // Check if times are continuous
                                        if ($detail['jam_ke'] == $prev_jam + 1 && 
                                            $prev_detail['jam_selesai'] == $detail['jam_mulai']) {
                                            $current_range[] = $detail;
                                        } else {
                                            if (!empty($current_range)) {
                                                $jam_ranges[] = $current_range;
                                            }
                                            $current_range = [$detail];
                                        }
                                    }
                                    $prev_jam = $detail['jam_ke'];
                                }
                                if (!empty($current_range)) {
                                    $jam_ranges[] = $current_range;
                                }
                            ?>
                            <tr<?php echo $has_conflict ? ' class="table-warning"' : ''; ?>>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                <td><?php echo htmlspecialchars($tingkat_nama); ?></td>
                                <td><?php echo htmlspecialchars($jurusan_nama); ?></td>
                                <td>
                                    <?php 
                                    if ($has_conflict) {
                                        echo '<i class="fas fa-exclamation-triangle text-warning" title="Jadwal Bertabrakan"></i> ';
                                    }
                                    echo htmlspecialchars($row['nama_mapel']); 
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($row['hari']); ?></td>
                                <td>
                                    <?php
                                    $jam_display = [];
                                    foreach ($jam_ranges as $range) {
                                        $start = reset($range);
                                        $end = end($range);
                                        if (count($range) > 1) {
                                            $jam_display[] = "Jam ke-" . $start['jam_ke'] . "-" . $end['jam_ke'] . 
                                                            " (" . substr($start['jam_mulai'], 0, 5) . "-" . 
                                                            substr($end['jam_selesai'], 0, 5) . ")";
                                        } else {
                                            $jam_display[] = "Jam ke-" . $start['jam_ke'] . 
                                                            " (" . substr($start['jam_mulai'], 0, 5) . "-" . 
                                                            substr($start['jam_selesai'], 0, 5) . ")";
                                        }
                                    }
                                    echo implode("<br>", $jam_display);
                                    ?>
                                </td>
                                <?php if ($_SESSION['role'] === 'admin'): ?>
                                <td><?php echo htmlspecialchars($row['nama_guru'] ?? '-'); ?></td>
                                <td>
                                    <?php if ($has_conflict): ?>
                                    <button type="button" class="btn btn-warning btn-sm mb-1" 
                                            data-bs-toggle="popover" 
                                            data-bs-title="Detail Konflik Jadwal" 
                                            data-bs-html="true"
                                            data-bs-content="<?php 
                                                $conflict_details = [];
                                                foreach ($conflicts as $conflict) {
                                                    $conflict_details[] = "{$conflict['nama_mapel']} - {$conflict['nama_kelas']} (Jam ke-{$conflict['jam_ke']})";
                                                }
                                                echo htmlspecialchars(implode('<br>', $conflict_details));
                                            ?>">
                                        <i class="fas fa-exclamation-circle"></i> Lihat Konflik
                                    </button>
                                    <?php endif; ?>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete.php?id=<?php echo $row['id']; ?>" 
                                       class="btn btn-sm btn-danger" 
                                       onclick="return confirmDelete()">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                                <?php endif; ?>
                            </tr>
                            <?php
                            endwhile;
                            endif;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    <?php if ($has_data): ?>
    var table = $('#tableJadwal').DataTable({
        "pageLength": 25,
        "ordering": true,
        "searching": true,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
    <?php endif; ?>

    // Initialize popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl, {
        trigger: 'click',
        placement: 'left'
    }));

    // Close popover when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[data-bs-toggle="popover"]')) {
            popoverList.forEach(popover => {
                popover._element.click();
            });
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
