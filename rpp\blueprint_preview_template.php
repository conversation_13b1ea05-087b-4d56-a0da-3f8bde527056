<?php
// Blueprint Preview Template
// This file is included in blueprint generation pages to show preview

if (!isset($generated_blueprint)) {
    return;
}

$blueprint = $generated_blueprint;
?>

<div class="blueprint-preview">
    <!-- Exam Information -->
    <div class="row mb-4">
        <div class="col-md-12">
            <h5 class="text-center mb-3"><?= htmlspecialchars($blueprint['exam_info']['title'] ?? 'Kisi-kisi Ujian') ?></h5>
            
            <table class="table table-bordered">
                <tr>
                    <td width="150"><strong>Mata Pelajaran</strong></td>
                    <td><?= htmlspecialchars($blueprint['exam_info']['subject'] ?? '') ?></td>
                    <td width="150"><strong><PERSON><PERSON></strong></td>
                    <td><?= htmlspecialchars($blueprint['exam_info']['type'] ?? '') ?></td>
                </tr>
                <tr>
                    <td><strong>Durasi</strong></td>
                    <td><?= htmlspecialchars($blueprint['exam_info']['duration'] ?? '') ?></td>
                    <td><strong>Total Skor</strong></td>
                    <td><?= htmlspecialchars($blueprint['exam_info']['total_score'] ?? '') ?></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Learning Objectives -->
    <?php if (!empty($blueprint['learning_objectives'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-bullseye"></i> Tujuan Pembelajaran</h6>
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th>Chapter</th>
                            <th>Tujuan Pembelajaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($blueprint['learning_objectives'] as $objective): ?>
                            <tr>
                                <td><strong><?= htmlspecialchars($objective['chapter'] ?? '') ?></strong></td>
                                <td>
                                    <?php if (isset($objective['objectives']) && is_array($objective['objectives'])): ?>
                                        <ul class="mb-0">
                                            <?php foreach ($objective['objectives'] as $obj): ?>
                                                <li><?= htmlspecialchars($obj) ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    <!-- Question Distribution -->
    <?php if (!empty($blueprint['question_distribution'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-chart-pie"></i> Distribusi Soal</h6>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="small">Distribusi per Chapter:</h6>
                    <?php if (isset($blueprint['question_distribution']['by_chapter'])): ?>
                        <table class="table table-sm table-bordered">
                            <?php foreach ($blueprint['question_distribution']['by_chapter'] as $chapter => $count): ?>
                                <tr>
                                    <td><?= htmlspecialchars($chapter) ?></td>
                                    <td class="text-end"><span class="badge bg-info"><?= $count ?> soal</span></td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <h6 class="small">Distribusi per Jenis:</h6>
                    <?php if (isset($blueprint['question_distribution']['by_type'])): ?>
                        <table class="table table-sm table-bordered">
                            <?php foreach ($blueprint['question_distribution']['by_type'] as $type => $count): ?>
                                <tr>
                                    <td><?= ucfirst(str_replace('_', ' ', $type)) ?></td>
                                    <td class="text-end"><span class="badge bg-primary"><?= $count ?> soal</span></td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Cognitive Mapping -->
    <?php if (!empty($blueprint['cognitive_mapping'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-brain"></i> Pemetaan Level Kognitif (Bloom's Taxonomy)</h6>
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th>Level</th>
                            <th>Deskripsi</th>
                            <th>Jumlah Soal</th>
                            <th>Persentase</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $cognitive_descriptions = [
                            'C1' => 'Mengingat (Remember)',
                            'C2' => 'Memahami (Understand)', 
                            'C3' => 'Menerapkan (Apply)',
                            'C4' => 'Menganalisis (Analyze)',
                            'C5' => 'Mengevaluasi (Evaluate)',
                            'C6' => 'Mencipta (Create)'
                        ];
                        
                        $total_questions = array_sum($blueprint['cognitive_mapping']);
                        
                        foreach ($blueprint['cognitive_mapping'] as $level => $count): 
                            if ($count > 0):
                                $percentage = $total_questions > 0 ? round(($count / $total_questions) * 100, 1) : 0;
                        ?>
                            <tr>
                                <td><strong><?= $level ?></strong></td>
                                <td><?= $cognitive_descriptions[$level] ?? $level ?></td>
                                <td class="text-center"><?= $count ?></td>
                                <td class="text-center"><?= $percentage ?>%</td>
                            </tr>
                        <?php 
                            endif;
                        endforeach; 
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    <!-- Blueprint Table -->
    <?php if (!empty($blueprint['blueprint_table'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-table"></i> Tabel Kisi-kisi Ujian</h6>
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th>Chapter</th>
                            <th>Tujuan Pembelajaran</th>
                            <th>Indikator</th>
                            <th>Level Kognitif</th>
                            <th>No. Soal</th>
                            <th>Jumlah</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($blueprint['blueprint_table'] as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['chapter'] ?? '') ?></td>
                                <td><?= htmlspecialchars($row['learning_objective'] ?? '') ?></td>
                                <td><?= htmlspecialchars($row['indicator'] ?? '') ?></td>
                                <td class="text-center">
                                    <span class="badge bg-info"><?= htmlspecialchars($row['cognitive_level'] ?? '') ?></span>
                                </td>
                                <td class="text-center">
                                    <?php 
                                    if (isset($row['question_numbers']) && is_array($row['question_numbers'])):
                                        echo implode(', ', $row['question_numbers']);
                                    endif;
                                    ?>
                                </td>
                                <td class="text-center">
                                    <strong><?= $row['total_questions'] ?? 0 ?></strong>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    <!-- Summary -->
    <?php if (!empty($blueprint['summary'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-chart-bar"></i> Ringkasan</h6>
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title"><?= $blueprint['summary']['total_questions'] ?? 0 ?></h5>
                            <p class="card-text">Total Soal</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title"><?= $blueprint['summary']['total_chapters'] ?? 0 ?></h5>
                            <p class="card-text">Total Chapter</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">
                                <?= ($blueprint['summary']['multiple_choice'] ?? 0) ?> : <?= ($blueprint['summary']['essay'] ?? 0) ?>
                            </h5>
                            <p class="card-text">PG : Essay</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Chapters Information -->
    <?php if (!empty($blueprint['chapters'])): ?>
        <div class="mb-4">
            <h6><i class="fas fa-layer-group"></i> Informasi Chapter</h6>
            <div class="accordion" id="chaptersAccordion">
                <?php foreach ($blueprint['chapters'] as $index => $chapter): ?>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading<?= $index ?>">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse<?= $index ?>" aria-expanded="false" 
                                    aria-controls="collapse<?= $index ?>">
                                Chapter <?= $chapter['number'] ?? ($index + 1) ?>: <?= htmlspecialchars($chapter['title'] ?? '') ?>
                            </button>
                        </h2>
                        <div id="collapse<?= $index ?>" class="accordion-collapse collapse" 
                             aria-labelledby="heading<?= $index ?>" data-bs-parent="#chaptersAccordion">
                            <div class="accordion-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td width="150"><strong>Mata Pelajaran:</strong></td>
                                        <td><?= htmlspecialchars($chapter['subject'] ?? '') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Materi Pokok:</strong></td>
                                        <td><?= htmlspecialchars($chapter['material'] ?? '') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tujuan Pembelajaran:</strong></td>
                                        <td><?= nl2br(htmlspecialchars($chapter['learning_objectives'] ?? '')) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kompetensi Dasar:</strong></td>
                                        <td><?= nl2br(htmlspecialchars($chapter['basic_competency'] ?? '')) ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.blueprint-preview .table {
    font-size: 0.9rem;
}

.blueprint-preview .table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.blueprint-preview .badge {
    font-size: 0.75rem;
}

.blueprint-preview .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.blueprint-preview .accordion-button {
    font-size: 0.9rem;
    font-weight: 600;
}

.blueprint-preview .accordion-body {
    font-size: 0.85rem;
}

.blueprint-preview h6 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}
</style>
