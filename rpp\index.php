<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/Kelas.php';

// Dapatkan guru_id dari tabel guru berdasarkan user_id yang login
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

$rpp = new Rpp();
$result = $rpp->getAllByGuru($guru_id);

// Handle success message
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Daftar RPP</h5>
            <div>
                <a href="create.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buat RPP Baru
                </a>
                <a href="generate_questions.php" class="btn btn-success">
                    <i class="fas fa-magic"></i> Buat Soal AI
                </a>
                <a href="multi_rpp_generate.php" class="btn btn-info">
                    <i class="fas fa-layer-group"></i> Ujian Multi-RPP
                </a>
                <a href="multi_rpp_list.php" class="btn btn-outline-info">
                    <i class="fas fa-list-alt"></i> Kelola Multi-RPP
                </a>
                <a href="blueprint_list.php" class="btn btn-outline-secondary">
                    <i class="fas fa-file-alt"></i> Kisi-kisi Ujian
                </a>
                <a href="essay_answers_management.php" class="btn btn-outline-warning">
                    <i class="fas fa-clipboard-check"></i> Kelola Jawaban Essay
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="tableRpp">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Mata Pelajaran</th>
                            <th>Kelas</th>
                            <th>Semester</th>
                            <th>Tahun Ajaran</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        while ($row = $result->fetch(PDO::FETCH_ASSOC)): 
                            $mapel = new MataPelajaran();
                            $mapel->id = $row['mapel_id'];
                            $mapel_data = $mapel->getOne();

                            $kelas = new Kelas();
                            $kelas_data = $kelas->getById($row['kelas_id']);
                        ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= htmlspecialchars($mapel_data['nama_mapel']) ?></td>
                                <td><?= htmlspecialchars($kelas_data['nama_kelas']) ?></td>
                                <td><?= htmlspecialchars($row['semester']) ?></td>
                                <td><?= htmlspecialchars($row['tahun_ajaran']) ?></td>
                                <td>
                                    <a href="view.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" title="Lihat">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?= $row['id'] ?>" class="btn btn-warning btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete.php?id=<?= $row['id'] ?>" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus RPP ini?')" title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableRpp').DataTable({
        "pageLength": 10,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>