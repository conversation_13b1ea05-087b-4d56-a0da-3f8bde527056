<?php
/**
 * Fix Data Truncation Issues
 * Fixes data truncation errors during database restore
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';

// Only admin can access this tool
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

class DataTruncationFixer {
    private $pdo;
    private $fixes_applied = [];
    private $errors = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function analyzeBackupFile($file_path) {
        $issues = [];
        
        if (!file_exists($file_path)) {
            return ['error' => 'File tidak ditemukan'];
        }
        
        $content = file_get_contents($file_path);
        if ($content === false) {
            return ['error' => 'Gagal membaca file'];
        }
        
        // Analyze status column issues
        $status_issues = $this->analyzeStatusColumns($content);
        
        return [
            'status_issues' => $status_issues,
            'total_issues' => count($status_issues)
        ];
    }
    
    private function analyzeStatusColumns($content) {
        $issues = [];
        
        // Define expected ENUM values for status columns
        $status_enums = [
            'detail_absensi' => ['Hadir', 'Sakit', 'Izin', 'Alpha'],
            'guru' => ['aktif', 'nonaktif'],
            'siswa_periode' => ['aktif', 'lulus', 'pindah', 'keluar'],
            'classroom_migration_log' => ['pending', 'running', 'completed', 'failed']
        ];
        
        foreach ($status_enums as $table => $valid_values) {
            // Find INSERT statements for this table
            $pattern = "/INSERT INTO `{$table}`[^;]+;/";
            preg_match_all($pattern, $content, $matches);
            
            foreach ($matches[0] as $insert_statement) {
                // Extract values from INSERT statement
                if (preg_match("/VALUES\s*\(([^)]+)\)/", $insert_statement, $value_matches)) {
                    $values = $this->parseInsertValues($value_matches[1]);
                    
                    // Get table structure to find status column position
                    $status_position = $this->getStatusColumnPosition($table);
                    
                    if ($status_position !== false && isset($values[$status_position])) {
                        $status_value = trim($values[$status_position], "'\"");
                        
                        if (!in_array($status_value, $valid_values) && !empty($status_value)) {
                            $issues[] = [
                                'table' => $table,
                                'invalid_value' => $status_value,
                                'valid_values' => $valid_values,
                                'statement' => substr($insert_statement, 0, 100) . '...'
                            ];
                        }
                    }
                }
            }
        }
        
        return $issues;
    }
    
    private function parseInsertValues($values_string) {
        // Simple parser for INSERT values
        $values = [];
        $current_value = '';
        $in_quotes = false;
        $quote_char = '';
        
        for ($i = 0; $i < strlen($values_string); $i++) {
            $char = $values_string[$i];
            
            if (!$in_quotes && ($char === "'" || $char === '"')) {
                $in_quotes = true;
                $quote_char = $char;
                $current_value .= $char;
            } elseif ($in_quotes && $char === $quote_char) {
                $in_quotes = false;
                $current_value .= $char;
            } elseif (!$in_quotes && $char === ',') {
                $values[] = trim($current_value);
                $current_value = '';
            } else {
                $current_value .= $char;
            }
        }
        
        // Add the last value
        if (!empty(trim($current_value))) {
            $values[] = trim($current_value);
        }
        
        return $values;
    }
    
    private function getStatusColumnPosition($table) {
        try {
            $stmt = $this->pdo->query("DESCRIBE `{$table}`");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($columns as $index => $column) {
                if ($column['Field'] === 'status') {
                    return $index;
                }
            }
        } catch (Exception $e) {
            // Table might not exist yet
        }
        
        return false;
    }
    
    public function fixBackupFile($file_path) {
        if (!file_exists($file_path)) {
            return ['success' => false, 'error' => 'File tidak ditemukan'];
        }
        
        $content = file_get_contents($file_path);
        if ($content === false) {
            return ['success' => false, 'error' => 'Gagal membaca file'];
        }
        
        // Create backup of original file
        $backup_path = $file_path . '.before_fix.' . date('Y-m-d_H-i-s');
        if (!copy($file_path, $backup_path)) {
            return ['success' => false, 'error' => 'Gagal membuat backup file asli'];
        }
        
        $original_content = $content;
        $fixes_count = 0;
        
        // Fix status column issues
        $content = $this->fixStatusColumnIssues($content, $fixes_count);
        
        // Add SQL mode settings to handle data truncation more gracefully
        $sql_mode_fix = "-- Fix for data truncation issues\n";
        $sql_mode_fix .= "SET sql_mode = '';\n";
        $sql_mode_fix .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
        
        // Insert after the header
        if (preg_match('/(-- PHP MySQL Backup\n-- Generated: [^\n]+\n\n)/', $content, $matches)) {
            $content = str_replace($matches[1], $matches[1] . $sql_mode_fix, $content);
            $fixes_count++;
        } else {
            $content = $sql_mode_fix . $content;
            $fixes_count++;
        }
        
        // Add SQL mode restore at the end
        $content .= "\n-- Restore original SQL mode\n";
        $content .= "SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';\n";
        
        // Write the fixed content
        if (file_put_contents($file_path, $content) === false) {
            return ['success' => false, 'error' => 'Gagal menulis file yang diperbaiki'];
        }
        
        return [
            'success' => true,
            'fixes_applied' => $fixes_count,
            'backup_created' => $backup_path
        ];
    }
    
    private function fixStatusColumnIssues($content, &$fixes_count) {
        // Define status value mappings for common issues
        $status_mappings = [
            // Common invalid values and their corrections
            '' => 'aktif',
            'active' => 'aktif',
            'inactive' => 'nonaktif',
            'non-aktif' => 'nonaktif',
            'Aktif' => 'aktif',
            'Non-aktif' => 'nonaktif',
            'AKTIF' => 'aktif',
            'NONAKTIF' => 'nonaktif',
            'hadir' => 'Hadir',
            'sakit' => 'Sakit',
            'izin' => 'Izin',
            'alpha' => 'Alpha',
            'HADIR' => 'Hadir',
            'SAKIT' => 'Sakit',
            'IZIN' => 'Izin',
            'ALPHA' => 'Alpha'
        ];
        
        foreach ($status_mappings as $invalid => $valid) {
            $pattern = "/('|\"){$invalid}('|\")/";
            $replacement = "'{$valid}'";
            $new_content = preg_replace($pattern, $replacement, $content);
            
            if ($new_content !== $content) {
                $content = $new_content;
                $fixes_count++;
            }
        }
        
        return $content;
    }
    
    public function getFixesApplied() {
        return $this->fixes_applied;
    }
    
    public function getErrors() {
        return $this->errors;
    }
}

$message = '';
$error = '';
$analysis_result = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $database = new Database();
        $pdo = $database->getConnection();
        $fixer = new DataTruncationFixer($pdo);
        
        if (isset($_POST['analyze_file'])) {
            $file_name = basename($_POST['analyze_file']);
            $file_path = __DIR__ . '/../database/backups/' . $file_name;
            
            $analysis_result = $fixer->analyzeBackupFile($file_path);
            
            if (isset($analysis_result['error'])) {
                $error = "Gagal menganalisis file: " . $analysis_result['error'];
            } else {
                $message = "Analisis selesai. Ditemukan {$analysis_result['total_issues']} masalah data truncation.";
            }
            
        } elseif (isset($_POST['fix_file'])) {
            $file_name = basename($_POST['fix_file']);
            $file_path = __DIR__ . '/../database/backups/' . $file_name;
            
            $result = $fixer->fixBackupFile($file_path);
            
            if ($result['success']) {
                $message = "File berhasil diperbaiki! {$result['fixes_applied']} perbaikan diterapkan. ";
                $message .= "Backup asli disimpan di: " . basename($result['backup_created']);
            } else {
                $error = "Gagal memperbaiki file: " . $result['error'];
            }
        }
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

require_once __DIR__ . '/../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Perbaiki Data Truncation Error</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Tentang Data Truncation Error</h6>
                        <p>Error "Data truncated for column 'status'" terjadi ketika data dalam backup tidak sesuai dengan nilai ENUM yang diizinkan dalam struktur tabel. Tool ini akan:</p>
                        <ul class="mb-0">
                            <li>Menganalisis file backup untuk menemukan data yang bermasalah</li>
                            <li>Memperbaiki nilai status yang tidak valid</li>
                            <li>Menambahkan pengaturan SQL mode yang lebih permisif</li>
                            <li>Membuat backup file asli sebelum perbaikan</li>
                        </ul>
                    </div>

                    <?php if ($analysis_result && !isset($analysis_result['error'])): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Hasil Analisis</h6>
                            </div>
                            <div class="card-body">
                                <?php if ($analysis_result['total_issues'] > 0): ?>
                                    <div class="alert alert-warning">
                                        <strong>Ditemukan <?php echo $analysis_result['total_issues']; ?> masalah data truncation:</strong>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Tabel</th>
                                                    <th>Nilai Invalid</th>
                                                    <th>Nilai Valid</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($analysis_result['status_issues'] as $issue): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($issue['table']); ?></td>
                                                    <td><code><?php echo htmlspecialchars($issue['invalid_value']); ?></code></td>
                                                    <td><?php echo implode(', ', $issue['valid_values']); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> Tidak ditemukan masalah data truncation dalam file ini.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <h6>File Backup yang Tersedia</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>File Backup</th>
                                    <th>Ukuran</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $backup_dir = __DIR__ . '/../database/backups/';
                                $backup_files = glob($backup_dir . '*.sql');
                                
                                // Filter out backup files
                                $backup_files = array_filter($backup_files, function($file) {
                                    return !preg_match('/\.(original|before_fix)\.\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/', $file);
                                });
                                
                                if (!empty($backup_files)) {
                                    rsort($backup_files);
                                    foreach ($backup_files as $file) {
                                        $filename = basename($file);
                                        $size = filesize($file);
                                        $created = date("d-m-Y H:i:s", filemtime($file));
                                        
                                        echo "<tr>";
                                        echo "<td>{$filename}</td>";
                                        echo "<td>" . number_format($size / 1024, 2) . " KB</td>";
                                        echo "<td>{$created}</td>";
                                        echo "<td>";
                                        
                                        // Analyze button
                                        echo "<form method='post' style='display: inline;' class='me-2'>";
                                        echo "<input type='hidden' name='analyze_file' value='" . htmlspecialchars($filename, ENT_QUOTES) . "'>";
                                        echo "<button type='submit' class='btn btn-sm btn-info'>";
                                        echo "<i class='fas fa-search'></i> Analisis</button>";
                                        echo "</form>";
                                        
                                        // Fix button
                                        echo "<form method='post' style='display: inline;' ";
                                        echo "onsubmit='return confirm(\"Perbaiki file {$filename}? File asli akan di-backup terlebih dahulu.\")'>";
                                        echo "<input type='hidden' name='fix_file' value='" . htmlspecialchars($filename, ENT_QUOTES) . "'>";
                                        echo "<button type='submit' class='btn btn-sm btn-warning'>";
                                        echo "<i class='fas fa-tools'></i> Perbaiki</button>";
                                        echo "</form>";
                                        
                                        echo "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='4' class='text-center'>Tidak ada file backup tersedia.</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <h6>Informasi Perbaikan</h6>
                        <div class="alert alert-secondary">
                            <p><strong>Perbaikan yang dilakukan:</strong></p>
                            <ul>
                                <li><strong>SQL Mode:</strong> Mengatur mode yang lebih permisif untuk menangani data truncation</li>
                                <li><strong>Status Values:</strong> Memperbaiki nilai status yang tidak sesuai dengan ENUM</li>
                                <li><strong>Foreign Keys:</strong> Menonaktifkan sementara untuk mencegah konflik</li>
                                <li><strong>Backup:</strong> File asli disimpan dengan ekstensi .before_fix</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
