<?php
require_once __DIR__ . '/../middleware/auth.php';

require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Get current active period
$periodeAktif = new PeriodeAktif();
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?: 'Tidak ada periode aktif';
$current_semester = $periodeAktif->semester ?: '-';

// Get kelas data for dropdown
$kelas = new Kelas();
$kelas_list = $kelas->getAll();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $siswa = new Siswa();
    $siswa->nis = $_POST['nis'];
    $siswa->nama_siswa = $_POST['nama_siswa'];
    $siswa->jenis_kelamin = $_POST['jenis_kelamin'];
    $siswa->kelas_id = $_POST['kelas_id'];
    $siswa->alamat = $_POST['alamat'];
    $siswa->no_telp = $_POST['no_telp'];

    if ($siswa->create()) {
        $_SESSION['success'] = "Data siswa berhasil ditambahkan!";
        header('Location: index.php');
        exit;
    } else {
        $_SESSION['error'] = "Gagal menyimpan data siswa.";
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Tambah Data Siswa</h5>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
            <div class="card-body">
                <!-- Current Period Info -->
                <div class="alert alert-info mb-4">
                    <h6><i class="fas fa-calendar-alt"></i> Periode Akademik Aktif</h6>
                    <p class="mb-0">
                        <strong>Tahun Ajaran:</strong> <?= htmlspecialchars($current_tahun_ajaran) ?><br>
                        <strong>Semester:</strong> <?= htmlspecialchars($current_semester) ?><br>
                        <small class="text-muted">Siswa baru akan otomatis didaftarkan untuk periode ini.</small>
                    </p>
                </div>

                <?php if (isset($_SESSION['error'])) : ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        <hr>
                        <small>
                            <i class="fas fa-tools"></i>
                            Jika masalah terus terjadi, gunakan
                            <a href="debug_create.php" class="alert-link">Debug Tool</a>
                            untuk mendiagnosis masalah.
                        </small>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>
                <form method="POST" action="">
                    <div class="form-group mb-3">
                        <label for="nis">NIS <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nis" name="nis" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="nama_siswa">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama_siswa" name="nama_siswa" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="jenis_kelamin">Jenis Kelamin <span class="text-danger">*</span></label>
                        <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" required>
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="L">Laki-laki</option>
                            <option value="P">Perempuan</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="kelas_id">Kelas untuk Periode Aktif <span class="text-danger">*</span></label>
                        <select class="form-control" id="kelas_id" name="kelas_id" required>
                            <option value="">Pilih Kelas</option>
                            <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?= $row['id'] ?>"><?= htmlspecialchars($row['nama_kelas']) ?></option>
                            <?php endwhile; ?>
                        </select>
                        <small class="form-text text-muted">
                            Kelas ini akan digunakan untuk periode akademik aktif saat ini
                            (<?= htmlspecialchars($current_tahun_ajaran ?? 'Tidak ada periode aktif') ?> - Semester <?= htmlspecialchars($current_semester ?? '-') ?>)
                        </small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="alamat">Alamat</label>
                        <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                    </div>
                    <div class="form-group mb-3">
                        <label for="no_telp">No. Telepon</label>
                        <input type="text" class="form-control" id="no_telp" name="no_telp">
                    </div>
                    <hr>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
