<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Cek parameter rpp_id
if (!isset($_GET['rpp_id'])) {
    $_SESSION['error'] = "ID RPP tidak ditemukan.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_GET['rpp_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP
$rpp = new Rpp();
$rpp_data = $rpp->getOne($rpp_id);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Tambah Soal Manual</h5>
            <div>
                <a href="questions_list.php?rpp_id=<?= $rpp_id ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar Soal
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- RPP Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-alt"></i> Informasi RPP</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Mata Pelajaran:</strong> <?= htmlspecialchars($rpp_data['nama_mapel']) ?></p>
                            <p><strong>Kelas:</strong> <?= htmlspecialchars($rpp_data['nama_kelas']) ?></p>
                            <p><strong>Materi Pokok:</strong> <?= htmlspecialchars($rpp_data['materi_pokok']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Semester:</strong> <?= htmlspecialchars($rpp_data['semester']) ?></p>
                            <p><strong>Tahun Ajaran:</strong> <?= htmlspecialchars($rpp_data['tahun_ajaran']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Question Form -->
            <form id="manualQuestionForm" action="save_manual_questions.php" method="POST">
                <input type="hidden" name="rpp_id" value="<?= $rpp_id ?>">
                
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-plus"></i> Tambah Soal Manual</h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addQuestion()">
                                <i class="fas fa-plus"></i> Tambah Soal Baru
                            </button>
                            <span class="badge bg-info ms-2" id="questionCount">1 soal</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="questionsContainer">
                            <!-- Questions will be added here dynamically -->
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-info" onclick="analyzeAllQuestions()">
                                            <i class="fas fa-search"></i> Analisis Semua Soal
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="enhanceAllQuestions()">
                                            <i class="fas fa-magic"></i> Saran Perbaikan
                                        </button>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save"></i> Simpan Semua Soal
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Question Template (Hidden) -->
<div id="questionTemplate" style="display: none;">
    <div class="question-item card mb-3" data-question-index="">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Soal <span class="question-number"></span></h6>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-info analyze-btn" onclick="analyzeQuestion(this)">
                    <i class="fas fa-search"></i> Analisis
                </button>
                <button type="button" class="btn btn-sm btn-outline-warning enhance-btn" onclick="enhanceQuestion(this)">
                    <i class="fas fa-magic"></i> Perbaiki
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeQuestion(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Jenis Soal</label>
                        <select class="form-select question-type" name="questions[][question_type]" onchange="toggleOptions(this)">
                            <option value="multiple_choice">Pilihan Ganda</option>
                            <option value="essay">Essay</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Tingkat Kesulitan</label>
                        <select class="form-select difficulty-level" name="questions[][difficulty_level]">
                            <option value="regular">Regular (C1-C3: Mengingat, Memahami, Menerapkan)</option>
                            <option value="hots_easy">HOTS Mudah (C4: Menganalisis)</option>
                            <option value="hots_medium">HOTS Sedang (C4-C5: Menganalisis, Mengevaluasi)</option>
                            <option value="hots_hard">HOTS Tinggi (C5-C6: Mengevaluasi, Mencipta)</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Kategori</label>
                        <input type="text" class="form-control" name="questions[][category]" value="Manual" placeholder="Kategori soal">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Status Analisis</label>
                        <span class="badge bg-secondary analysis-status">Belum dianalisis</span>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Teks Soal</label>
                <textarea class="form-control question-text" name="questions[][question_text]" rows="4" placeholder="Masukkan teks soal..." required></textarea>
            </div>
            
            <!-- Multiple Choice Options (shown by default) -->
            <div class="options-container">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Opsi Jawaban</label>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Jumlah Opsi</label>
                        <select class="form-select option-count" onchange="updateQuestionOptions(this)">
                            <option value="2">2 Opsi (A-B)</option>
                            <option value="3">3 Opsi (A-C)</option>
                            <option value="4" selected>4 Opsi (A-D)</option>
                            <option value="5">5 Opsi (A-E)</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <div class="input-group">
                                <span class="input-group-text">A.</span>
                                <input type="text" class="form-control option-input" name="questions[][options][]" placeholder="Opsi A" required>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="input-group">
                                <span class="input-group-text">B.</span>
                                <input type="text" class="form-control option-input" name="questions[][options][]" placeholder="Opsi B" required>
                            </div>
                        </div>
                        <div class="mb-2 option-c-container">
                            <div class="input-group">
                                <span class="input-group-text">C.</span>
                                <input type="text" class="form-control option-input" name="questions[][options][]" placeholder="Opsi C">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2 option-d-container">
                            <div class="input-group">
                                <span class="input-group-text">D.</span>
                                <input type="text" class="form-control option-input" name="questions[][options][]" placeholder="Opsi D">
                            </div>
                        </div>
                        <div class="mb-2 option-e-container" style="display: none;">
                            <div class="input-group">
                                <span class="input-group-text">E.</span>
                                <input type="text" class="form-control option-input" name="questions[][options][]" placeholder="Opsi E">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Kunci Jawaban</label>
                    <select class="form-select correct-answer" name="questions[][correct_answer]">
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="D">D</option>
                    </select>
                </div>
            </div>
            
            <!-- Analysis Results (hidden by default) -->
            <div class="analysis-results" style="display: none;">
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-line"></i> Hasil Analisis AI</h6>
                    <div class="analysis-content"></div>
                </div>
            </div>
            
            <!-- Enhancement Suggestions (hidden by default) -->
            <div class="enhancement-suggestions" style="display: none;">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-lightbulb"></i> Saran Perbaikan AI</h6>
                    <div class="enhancement-content"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.question-item {
    transition: all 0.3s ease;
}

.question-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.analysis-status {
    font-size: 0.8rem;
}

.option-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.btn-group .btn {
    margin-right: 2px;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.analysis-results .alert {
    margin-bottom: 0;
}

.enhancement-suggestions .alert {
    margin-bottom: 0;
}

.card-header h6 {
    color: #495057;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 4px;
        border-radius: 0.375rem !important;
    }
}
</style>

<script>
let questionIndex = 0;

// Initialize first question on page load
document.addEventListener('DOMContentLoaded', function() {
    addQuestion();
});

function addQuestion() {
    const template = document.getElementById('questionTemplate');
    const container = document.getElementById('questionsContainer');
    const clone = template.cloneNode(true);
    
    clone.style.display = 'block';
    clone.id = '';
    clone.setAttribute('data-question-index', questionIndex);
    
    // Update question number
    clone.querySelector('.question-number').textContent = questionIndex + 1;
    
    // Update form field names with proper indexing
    const inputs = clone.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.name) {
            input.name = input.name.replace('[]', `[${questionIndex}]`);
        }
    });
    
    container.appendChild(clone);
    questionIndex++;
    updateQuestionCount();
}

function removeQuestion(button) {
    if (document.querySelectorAll('.question-item[data-question-index]').length <= 1) {
        alert('Minimal harus ada satu soal.');
        return;
    }
    
    const questionItem = button.closest('.question-item');
    questionItem.remove();
    updateQuestionNumbers();
    updateQuestionCount();
}

function updateQuestionNumbers() {
    const questions = document.querySelectorAll('.question-item[data-question-index]');
    questions.forEach((question, index) => {
        question.querySelector('.question-number').textContent = index + 1;
    });
}

function updateQuestionCount() {
    const count = document.querySelectorAll('.question-item[data-question-index]').length;
    document.getElementById('questionCount').textContent = count + ' soal';
}

function toggleOptions(select) {
    const questionItem = select.closest('.question-item');
    const optionsContainer = questionItem.querySelector('.options-container');

    if (select.value === 'essay') {
        optionsContainer.style.display = 'none';
        // Clear required attribute from option inputs
        optionsContainer.querySelectorAll('.option-input').forEach(input => {
            input.removeAttribute('required');
        });
    } else {
        optionsContainer.style.display = 'block';
        // Update option visibility based on current count
        const optionCountSelect = optionsContainer.querySelector('.option-count');
        updateQuestionOptions(optionCountSelect);
    }
}

function updateQuestionOptions(selectElement) {
    const questionItem = selectElement.closest('.question-item');
    const optionCount = parseInt(selectElement.value);

    // Get option containers
    const optionCContainer = questionItem.querySelector('.option-c-container');
    const optionDContainer = questionItem.querySelector('.option-d-container');
    const optionEContainer = questionItem.querySelector('.option-e-container');
    const correctAnswerSelect = questionItem.querySelector('.correct-answer');

    // Show/hide option containers based on count
    const containers = [
        { container: optionCContainer, minCount: 3 },
        { container: optionDContainer, minCount: 4 },
        { container: optionEContainer, minCount: 5 }
    ];

    containers.forEach(({ container, minCount }) => {
        const input = container.querySelector('.option-input');

        if (optionCount >= minCount) {
            container.style.display = 'block';
            input.setAttribute('required', 'required');
        } else {
            container.style.display = 'none';
            input.removeAttribute('required');
            input.value = ''; // Clear hidden inputs
        }
    });

    // Update correct answer options
    correctAnswerSelect.innerHTML = '';
    const letters = ['A', 'B', 'C', 'D', 'E'];
    for (let i = 0; i < optionCount; i++) {
        const option = document.createElement('option');
        option.value = letters[i];
        option.textContent = letters[i];
        correctAnswerSelect.appendChild(option);
    }

    // Ensure A and B are always required
    const optionA = questionItem.querySelector('input[placeholder="Opsi A"]');
    const optionB = questionItem.querySelector('input[placeholder="Opsi B"]');
    if (optionA) optionA.setAttribute('required', 'required');
    if (optionB) optionB.setAttribute('required', 'required');
}

function analyzeQuestion(button) {
    const questionItem = button.closest('.question-item');
    const questionText = questionItem.querySelector('.question-text').value.trim();
    const questionType = questionItem.querySelector('.question-type').value;

    if (!questionText) {
        alert('Silakan isi teks soal terlebih dahulu.');
        return;
    }

    // Validate verb appropriateness before sending to AI
    const verbValidation = validateQuestionVerbs(questionText, questionType);
    if (!verbValidation.isValid) {
        if (confirm(`PERINGATAN: ${verbValidation.message}\n\nApakah Anda tetap ingin melanjutkan analisis?`)) {
            // Continue with analysis despite warning
        } else {
            return;
        }
    }

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading-spinner"></span> Menganalisis...';
    button.disabled = true;

    // Prepare data
    const formData = new FormData();
    formData.append('question_text', questionText);
    formData.append('question_type', questionType);
    formData.append('rpp_id', '<?= $rpp_id ?>');

    fetch('analyze_question.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResults(questionItem, data.analysis);
            updateDifficultyLevel(questionItem, data.analysis.difficulty_level);
            updateAnalysisStatus(questionItem, 'Sudah dianalisis', 'success');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat menganalisis soal.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function enhanceQuestion(button) {
    const questionItem = button.closest('.question-item');
    const questionText = questionItem.querySelector('.question-text').value.trim();
    const questionType = questionItem.querySelector('.question-type').value;

    if (!questionText) {
        alert('Silakan isi teks soal terlebih dahulu.');
        return;
    }

    // Show enhancement options modal
    showEnhancementModal(questionItem, questionText, questionType);
}

function analyzeAllQuestions() {
    const questions = document.querySelectorAll('.question-item[data-question-index]');
    let completed = 0;

    questions.forEach((questionItem, index) => {
        const analyzeBtn = questionItem.querySelector('.analyze-btn');

        setTimeout(() => {
            analyzeQuestion(analyzeBtn);
        }, index * 2000); // Delay each request by 2 seconds
    });
}

function enhanceAllQuestions() {
    const questions = document.querySelectorAll('.question-item[data-question-index]');

    if (questions.length === 0) {
        alert('Tidak ada soal untuk diperbaiki.');
        return;
    }

    if (confirm('Apakah Anda ingin memberikan saran perbaikan untuk semua soal? Ini akan memakan waktu beberapa menit.')) {
        questions.forEach((questionItem, index) => {
            const enhanceBtn = questionItem.querySelector('.enhance-btn');

            setTimeout(() => {
                enhanceQuestion(enhanceBtn);
            }, index * 3000); // Delay each request by 3 seconds
        });
    }
}

function displayAnalysisResults(questionItem, analysis) {
    const resultsContainer = questionItem.querySelector('.analysis-results');
    const contentDiv = resultsContainer.querySelector('.analysis-content');

    const hotsLabel = analysis.is_hots ? 'Ya' : 'Tidak';
    const confidencePercent = Math.round(analysis.confidence_score * 100);

    contentDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <p><strong>Tingkat Kesulitan:</strong> <span class="badge bg-${getDifficultyColor(analysis.difficulty_level)}">${getDifficultyLabel(analysis.difficulty_level)}</span></p>
                <p><strong>Level Kognitif:</strong> ${analysis.cognitive_level}</p>
                <p><strong>HOTS:</strong> ${hotsLabel}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Confidence Score:</strong> ${confidencePercent}%</p>
                <p><strong>Keywords:</strong> ${analysis.keywords ? analysis.keywords.join(', ') : 'Tidak ada'}</p>
            </div>
        </div>
        <p><strong>Alasan:</strong> ${analysis.reasoning}</p>
    `;

    resultsContainer.style.display = 'block';
}

function updateDifficultyLevel(questionItem, difficultyLevel) {
    const difficultySelect = questionItem.querySelector('.difficulty-level');
    difficultySelect.value = difficultyLevel;
}

function updateAnalysisStatus(questionItem, status, type) {
    const statusBadge = questionItem.querySelector('.analysis-status');
    statusBadge.textContent = status;
    statusBadge.className = `badge bg-${type} analysis-status`;
}

function getDifficultyColor(difficulty) {
    switch (difficulty) {
        case 'regular': return 'secondary';
        case 'hots_easy': return 'info';
        case 'hots_medium': return 'warning';
        case 'hots_hard': return 'danger';
        default: return 'secondary';
    }
}

function getDifficultyLabel(difficulty) {
    switch (difficulty) {
        case 'regular': return 'Regular (C1-C3)';
        case 'hots_easy': return 'HOTS Mudah (C4)';
        case 'hots_medium': return 'HOTS Sedang (C4-C5)';
        case 'hots_hard': return 'HOTS Tinggi (C5-C6)';
        default: return 'Regular (C1-C3)';
    }
}

function showEnhancementModal(questionItem, questionText, questionType) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('enhancementModal');
    if (!modal) {
        modal = createEnhancementModal();
        document.body.appendChild(modal);
    }

    // Store reference to current question item
    modal.setAttribute('data-current-question', questionItem.getAttribute('data-question-index'));

    // Show modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

function createEnhancementModal() {
    const modalHtml = `
        <div class="modal fade" id="enhancementModal" tabindex="-1" aria-labelledby="enhancementModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="enhancementModalLabel">Pilih Jenis Perbaikan</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">Pilih jenis perbaikan yang ingin diterapkan pada soal:</p>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="card enhancement-option" onclick="selectEnhancement('upgrade_to_hots')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-arrow-up fa-2x text-primary mb-2"></i>
                                        <h6>Upgrade ke HOTS</h6>
                                        <p class="small text-muted">Tingkatkan soal menjadi Higher Order Thinking Skills</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card enhancement-option" onclick="selectEnhancement('make_unique')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                                        <h6>Buat Unik</h6>
                                        <p class="small text-muted">Buat soal sulit dicari di internet</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card enhancement-option" onclick="selectEnhancement('general_improvement')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-edit fa-2x text-success mb-2"></i>
                                        <h6>Perbaikan Umum</h6>
                                        <p class="small text-muted">Perbaiki struktur dan kejelasan soal</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-info" onclick="analyzeOnly()">
                                <i class="fas fa-search"></i> Hanya Analisis Saja
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    const modalElement = document.createElement('div');
    modalElement.innerHTML = modalHtml;
    return modalElement.firstElementChild;
}

function selectEnhancement(enhancementType) {
    const modal = document.getElementById('enhancementModal');
    const questionIndex = modal.getAttribute('data-current-question');
    const questionItem = document.querySelector(`[data-question-index="${questionIndex}"]`);

    if (!questionItem) return;

    const questionText = questionItem.querySelector('.question-text').value.trim();
    const questionType = questionItem.querySelector('.question-type').value;

    // Close modal
    const bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();

    // Show loading state
    const enhanceBtn = questionItem.querySelector('.enhance-btn');
    const originalText = enhanceBtn.innerHTML;
    enhanceBtn.innerHTML = '<span class="loading-spinner"></span> Memperbaiki...';
    enhanceBtn.disabled = true;

    // Prepare data
    const formData = new FormData();
    formData.append('question_text', questionText);
    formData.append('question_type', questionType);
    formData.append('enhancement_type', enhancementType);
    formData.append('rpp_id', '<?= $rpp_id ?>');

    fetch('enhance_question.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayEnhancementResults(questionItem, data.enhancement);
            updateAnalysisStatus(questionItem, 'Sudah diperbaiki', 'warning');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan saat memperbaiki soal.');
    })
    .finally(() => {
        enhanceBtn.innerHTML = originalText;
        enhanceBtn.disabled = false;
    });
}

function analyzeOnly() {
    const modal = document.getElementById('enhancementModal');
    const questionIndex = modal.getAttribute('data-current-question');
    const questionItem = document.querySelector(`[data-question-index="${questionIndex}"]`);

    if (!questionItem) return;

    // Close modal
    const bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();

    // Trigger analysis
    const analyzeBtn = questionItem.querySelector('.analyze-btn');
    analyzeQuestion(analyzeBtn);
}

function displayEnhancementResults(questionItem, enhancement) {
    const resultsContainer = questionItem.querySelector('.enhancement-suggestions');
    const contentDiv = resultsContainer.querySelector('.enhancement-content');

    const improvementsList = enhancement.improvements.map(imp => `<li>${imp}</li>`).join('');

    contentDiv.innerHTML = `
        <div class="mb-3">
            <h6>Soal yang Diperbaiki:</h6>
            <div class="p-3 bg-light border rounded">
                ${enhancement.enhanced_question}
            </div>
        </div>

        <div class="mb-3">
            <h6>Perbaikan yang Dilakukan:</h6>
            <ul>${improvementsList}</ul>
        </div>

        <div class="mb-3">
            <p><strong>Tingkat Kesulitan Baru:</strong>
                <span class="badge bg-${getDifficultyColor(enhancement.difficulty_level)}">
                    ${getDifficultyLabel(enhancement.difficulty_level)}
                </span>
            </p>
            <p><strong>Alasan:</strong> ${enhancement.reasoning}</p>
        </div>

        <div class="text-end">
            <button type="button" class="btn btn-success btn-sm" onclick="applyEnhancement(this)">
                <i class="fas fa-check"></i> Terapkan Perbaikan
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="dismissEnhancement(this)">
                <i class="fas fa-times"></i> Abaikan
            </button>
        </div>
    `;

    // Store enhancement data
    resultsContainer.setAttribute('data-enhancement', JSON.stringify(enhancement));
    resultsContainer.style.display = 'block';
}

function applyEnhancement(button) {
    const questionItem = button.closest('.question-item');
    const enhancementContainer = questionItem.querySelector('.enhancement-suggestions');
    const enhancementData = JSON.parse(enhancementContainer.getAttribute('data-enhancement'));

    // Update question text
    questionItem.querySelector('.question-text').value = enhancementData.enhanced_question;

    // Update difficulty level
    updateDifficultyLevel(questionItem, enhancementData.difficulty_level);

    // Hide enhancement suggestions
    enhancementContainer.style.display = 'none';

    // Update status
    updateAnalysisStatus(questionItem, 'Perbaikan diterapkan', 'success');

    alert('Perbaikan berhasil diterapkan!');
}

function dismissEnhancement(button) {
    const questionItem = button.closest('.question-item');
    const enhancementContainer = questionItem.querySelector('.enhancement-suggestions');

    enhancementContainer.style.display = 'none';
    updateAnalysisStatus(questionItem, 'Perbaikan diabaikan', 'secondary');
}

function validateQuestionVerbs(questionText, questionType) {
    const text = questionText.toLowerCase();

    // Define inappropriate verbs for multiple choice
    const inappropriateForMC = [
        'jelaskan', 'uraikan', 'analisis', 'evaluasi', 'deskripsikan',
        'paparkan', 'jabarkan', 'rincikan', 'elaborasi', 'sintesis',
        'ciptakan', 'rancang', 'buat', 'susun', 'kembangkan', 'simpulkan'
    ];

    // Define appropriate verbs for multiple choice
    const appropriateForMC = [
        'pilih', 'tentukan', 'sebutkan', 'identifikasi', 'tunjukkan',
        'bandingkan', 'kategorikan', 'klasifikasikan', 'bedakan',
        'prediksi', 'manakah', 'yang mana'
    ];

    if (questionType === 'multiple_choice') {
        // Check for inappropriate verbs in multiple choice
        for (let verb of inappropriateForMC) {
            if (text.includes(verb)) {
                return {
                    isValid: false,
                    message: `Soal pilihan ganda menggunakan kata "${verb}" yang memerlukan penjelasan tertulis. Gunakan kata kerja seperti: ${appropriateForMC.slice(0, 5).join(', ')}, dll.`
                };
            }
        }

        // Check if question ends with question mark but doesn't use appropriate verbs
        const hasAppropriateVerb = appropriateForMC.some(verb => text.includes(verb));
        if (!hasAppropriateVerb && text.includes('?')) {
            return {
                isValid: false,
                message: 'Soal pilihan ganda sebaiknya menggunakan kata kerja yang sesuai seperti: pilih, tentukan, manakah, identifikasi, dll.'
            };
        }
    }

    return { isValid: true, message: '' };
}

// Add validation to form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('manualQuestionForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const questions = document.querySelectorAll('.question-item[data-question-index]');
            let hasInappropriateVerbs = false;
            let inappropriateQuestions = [];

            questions.forEach((questionItem, index) => {
                const questionText = questionItem.querySelector('.question-text').value.trim();
                const questionType = questionItem.querySelector('.question-type').value;

                if (questionText) {
                    const validation = validateQuestionVerbs(questionText, questionType);
                    if (!validation.isValid) {
                        hasInappropriateVerbs = true;
                        inappropriateQuestions.push(`Soal ${index + 1}: ${validation.message}`);
                    }
                }
            });

            if (hasInappropriateVerbs) {
                const message = 'Ditemukan masalah pada soal berikut:\n\n' +
                              inappropriateQuestions.join('\n\n') +
                              '\n\nApakah Anda tetap ingin menyimpan soal-soal ini?';

                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    }
});
</script>

<!-- Enhancement Modal Styles -->
<style>
.enhancement-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.enhancement-option:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.enhancement-option .card-body {
    padding: 1.5rem;
}

.enhancement-option i {
    display: block;
}

.modal-lg {
    max-width: 800px;
}
</style>

<?php require_once '../template/footer.php'; ?>
