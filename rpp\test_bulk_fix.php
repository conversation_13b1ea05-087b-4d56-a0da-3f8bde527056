<?php
// Test file to verify the bulk essay answer fix
require_once '../models/GeminiApi.php';

// Test the parseBulkEssayAnswerResponse method with sample data
$geminiApi = new GeminiApi();

// Sample response that might come from AI with "ID: X" format
$sample_response = [
    'candidates' => [
        [
            'content' => [
                'parts' => [
                    [
                        'text' => json_encode([
                            'answers' => [
                                [
                                    'question_id' => 'ID: 2',
                                    'expected_answer' => 'Sample answer',
                                    'answer_points' => 'Sample points',
                                    'scoring_rubric' => [
                                        'excellent' => 'Excellent criteria',
                                        'good' => 'Good criteria',
                                        'fair' => 'Fair criteria',
                                        'poor' => 'Poor criteria'
                                    ],
                                    'generation_metadata' => [
                                        'confidence_score' => 0.9,
                                        'reasoning' => 'Test reasoning'
                                    ]
                                ]
                            ]
                        ])
                    ]
                ]
            ]
        ]
    ]
];

$questions_data = [
    ['id' => 2, 'question_text' => 'Test question']
];

try {
    // Use reflection to access private method
    $reflection = new ReflectionClass($geminiApi);
    $method = $reflection->getMethod('parseBulkEssayAnswerResponse');
    $method->setAccessible(true);
    
    $result = $method->invoke($geminiApi, $sample_response, $questions_data);
    
    echo "<h2>Test Result:</h2>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // Check if question_id is properly cleaned
    if (isset($result['answers'][0]['question_id']) && $result['answers'][0]['question_id'] === 2) {
        echo "<p style='color: green;'><strong>✅ SUCCESS: question_id properly cleaned from 'ID: 2' to 2</strong></p>";
    } else {
        echo "<p style='color: red;'><strong>❌ FAILED: question_id not properly cleaned</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
