<?php
require_once '../template/public_header.php';
require_once '../models/Siswa.php';
require_once '../models/Tugas.php';
require_once '../models/NilaiTugas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';

// Check if required parameters are provided
if (!isset($_GET['siswa_id']) || !isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: index.php");
    exit();
}

// Get parameters
$siswa_id = $_GET['siswa_id'];
$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Initialize models
$siswaModel = new Siswa();
$tugasModel = new Tugas();
$nilaiTugasModel = new NilaiTugas();
$mapelModel = new MataPelajaran();
$kelasModel = new Kelas();

// Get student data
$siswaModel->id = $siswa_id;
if (!$siswaModel->getOne()) {
    $_SESSION['error'] = "Siswa tidak ditemukan.";
    header("Location: index.php");
    exit();
}
$siswa = [
    'id' => $siswa_id,
    'nis' => $siswaModel->nis,
    'nama_siswa' => $siswaModel->nama_siswa,
    'kelas_id' => $siswaModel->kelas_id
];

// Get subject data
$mapelModel->id = $mapel_id;
if (!$mapelModel->getOne()) {
    $_SESSION['error'] = "Mata pelajaran tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Get class data
$kelasModel->id = $siswa['kelas_id'];
if ($kelasModel->getOne()) {
    $nama_kelas = $kelasModel->nama_kelas;
} else {
    $nama_kelas = "Tidak diketahui";
}

// Get detailed assignment data
$tugas_list = $tugasModel->getTugasMapelKelas($mapel_id, $siswa['kelas_id'], $semester, $tahun_ajaran);
?>

<div class="row mb-4">
    <div class="col-12">
        <a href="javascript:history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-tasks me-2"></i> Detail Tugas
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">NIS</th>
                                <td>: <?php echo $siswa['nis']; ?></td>
                            </tr>
                            <tr>
                                <th>Nama</th>
                                <td>: <?php echo $siswa['nama_siswa']; ?></td>
                            </tr>
                            <tr>
                                <th>Kelas</th>
                                <td>: <?php echo $nama_kelas; ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Mata Pelajaran</th>
                                <td>: <?php echo $mapelModel->nama_mapel; ?></td>
                            </tr>
                            <tr>
                                <th>Semester</th>
                                <td>: <?php echo $semester; ?></td>
                            </tr>
                            <tr>
                                <th>Tahun Ajaran</th>
                                <td>: <?php echo $tahun_ajaran; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered datatable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Judul Tugas</th>
                                <th>Tanggal</th>
                                <th>Deskripsi</th>
                                <th>Status</th>
                                <th>Nilai</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            if ($tugas_list && $tugas_list->rowCount() > 0) {
                                while ($tugas = $tugas_list->fetch(PDO::FETCH_ASSOC)) {
                                    // Get assignment submission status
                                    $nilai_tugas = $nilaiTugasModel->getNilaiTugasSiswa($siswa_id, $tugas['id']);
                                    $submitted = false;

                                    if ($nilai_tugas && $nilai_tugas['nilai'] > 0) {
                                        $submitted = true;
                                    }

                                    $status_class = $submitted ? 'badge-success' : 'badge-danger';
                                    $status_text = $submitted ? 'Sudah Dikumpulkan' : 'Belum Dikumpulkan';
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $tugas['judul']; ?></td>
                                <td><?php echo date('d-m-Y', strtotime($tugas['tanggal'])); ?></td>
                                <td><?php echo $tugas['deskripsi'] ?: '-'; ?></td>
                                <td>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <?php echo $status_text; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    if ($submitted && isset($nilai_tugas['nilai'])) {
                                        echo '<span class="badge badge-primary">' . $nilai_tugas['nilai'] . '</span>';
                                    } else {
                                        echo '<span class="text-muted">-</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php
                                }
                            } else {
                            ?>
                            <tr>
                                <td colspan="6" class="text-center">Tidak ada data tugas</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
