<?php
// Turn off error display to prevent HTML output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Start output buffering to catch any unexpected output
ob_start();

try {
    require_once __DIR__ . '/../middleware/auth.php';
    checkGuruAccess();
    require_once '../config/database.php';
    require_once '../models/RppQuestion.php';
    require_once '../models/EssayAnswer.php';
    require_once '../models/GeminiApi.php';
    require_once '../models/Rpp.php';
    require_once '../models/Guru.php';
} catch (Exception $e) {
    // Clear any output and send error
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Error loading required files: ' . $e->getMessage()]);
    exit();
}

// Clear any unexpected output from includes
ob_clean();

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
    exit();
}

try {
    // Check if essay_answers table exists
    $database = new Database();
    $conn = $database->getConnection();

    $check_table = $conn->prepare("SHOW TABLES LIKE 'essay_answers'");
    $check_table->execute();

    if ($check_table->rowCount() == 0) {
        throw new Exception("Table essay_answers tidak ditemukan. Silakan jalankan migration script terlebih dahulu.");
    }

    // Validate input
    if (!isset($_POST['question_id'])) {
        throw new Exception("Question ID tidak ditemukan");
    }

    $question_id = $_POST['question_id'];
    $question_type = $_POST['question_type'] ?? 'rpp_question';

    // Get question data
    if ($question_type === 'rpp_question') {
        $rppQuestion = new RppQuestion();
        $question_data = $rppQuestion->getOne($question_id);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        // Verify ownership through RPP
        $rpp = new Rpp();
        $rpp_data = $rpp->getOne($question_data['rpp_id']);
        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }

        // Build RPP context
        $rpp_context = "Mata Pelajaran: " . ($rpp_data['nama_mapel'] ?? 'N/A') . "\n";
        $rpp_context .= "Kelas: " . ($rpp_data['nama_kelas'] ?? 'N/A') . "\n";
        $rpp_context .= "Materi Pokok: " . ($rpp_data['materi_pokok'] ?? 'N/A') . "\n";
        $rpp_context .= "Tujuan Pembelajaran: " . ($rpp_data['tujuan_pembelajaran'] ?? 'N/A') . "\n";
        $rpp_context .= "Kompetensi Dasar: " . ($rpp_data['kompetensi_dasar'] ?? 'N/A');

    } else {
        // Handle multi-RPP questions
        require_once '../models/MultiRppExam.php';
        $multiRppExam = new MultiRppExam();
        
        // Get question from multi_rpp_exam_questions
        $query = "SELECT meq.*, me.guru_id, me.exam_title 
                 FROM multi_rpp_exam_questions meq
                 JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                 WHERE meq.id = :question_id";
        
        $database = new Database();
        $conn = $database->getConnection();
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->execute();
        
        $question_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        if ($question_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }

        // Build context for multi-RPP
        $rpp_context = "Ujian Multi-RPP: " . $question_data['exam_title'] . "\n";
        $rpp_context .= "Tipe Soal: Essay\n";
        $rpp_context .= "Tingkat Kesulitan: " . $question_data['difficulty_level'];
    }

    // Check if question is essay type
    if ($question_data['question_type'] !== 'essay') {
        throw new Exception("Soal ini bukan tipe essay");
    }

    // Check if answer already exists
    $essayAnswer = new EssayAnswer();
    $existing_answer = $essayAnswer->getByQuestionId($question_id, $question_type);
    
    if ($existing_answer) {
        echo json_encode([
            'success' => true,
            'message' => 'Jawaban sudah ada',
            'answer' => $existing_answer,
            'already_exists' => true
        ]);
        exit();
    }

    // Prepare question context
    $question_context = [
        'difficulty_level' => $question_data['difficulty_level'],
        'category' => $question_data['category'] ?? ''
    ];

    // Generate answer using Gemini API
    $geminiApi = new GeminiApi();
    $generated_answer = $geminiApi->generateEssayAnswer(
        $question_data['question_text'],
        $rpp_context,
        $question_context
    );

    // Save to database
    $essayAnswer->question_id = $question_id;
    $essayAnswer->question_type = $question_type;
    $essayAnswer->expected_answer = $generated_answer['expected_answer'];
    $essayAnswer->answer_points = $generated_answer['answer_points'];
    $essayAnswer->scoring_rubric = $generated_answer['scoring_rubric'];
    $essayAnswer->generation_metadata = $generated_answer['generation_metadata'];

    $answer_id = $essayAnswer->create();

    if ($answer_id) {
        // Get the saved answer
        $saved_answer = $essayAnswer->getOne($answer_id);
        
        echo json_encode([
            'success' => true,
            'message' => 'Jawaban berhasil dibuat',
            'answer' => $saved_answer,
            'already_exists' => false
        ]);
    } else {
        throw new Exception("Gagal menyimpan jawaban ke database");
    }

} catch (Exception $e) {
    // Log error for debugging
    error_log("Essay generation error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
} catch (Error $e) {
    // Catch fatal errors
    error_log("Essay generation fatal error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => 'Fatal error: ' . $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
