<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo 'Unauthorized';
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

$popupBerita = new PopupBerita();
$popupData = $popupBerita->getPopupData();

if (!$popupData) {
    echo '<div class="alert alert-warning">Popup tidak aktif atau tidak ada berita yang dipilih.</div>';
    exit();
}

$settings = $popupData['settings'];
$berita_items = $popupData['berita'];
?>

<div class="popup-preview">
    <div class="modal-header bg-primary text-white">
        <h5 class="modal-title">
            <i class="fas fa-newspaper"></i> <?php echo htmlspecialchars($settings['title']); ?>
        </h5>
    </div>
    
    <div class="modal-body">
        <?php if (empty($berita_items)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Tidak ada berita yang dipilih untuk ditampilkan.
            </div>
        <?php else: ?>
            <div class="news-items">
                <?php foreach ($berita_items as $index => $berita): ?>
                    <div class="news-item <?php echo $index > 0 ? 'border-top pt-3 mt-3' : ''; ?>">
                        <h6 class="news-title mb-2">
                            <i class="fas fa-newspaper text-primary"></i>
                            <?php echo htmlspecialchars($berita['judul']); ?>
                        </h6>
                        
                        <?php if ($settings['show_excerpt'] && !empty($berita['excerpt'])): ?>
                            <p class="news-excerpt text-muted mb-2">
                                <?php echo htmlspecialchars($berita['excerpt']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="news-meta mb-2">
                            <small class="text-muted">
                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($berita['nama_pembuat']); ?>
                                <i class="fas fa-calendar ms-2"></i> <?php echo date('d/m/Y H:i', strtotime($berita['created_at'])); ?>
                            </small>
                        </div>
                        
                        <div class="news-actions">
                            <button type="button" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i> Lihat Berita
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="modal-footer">
        <?php if ($settings['show_dont_show_again']): ?>
            <div class="form-check me-auto">
                <input class="form-check-input" type="checkbox" id="dontShowAgain">
                <label class="form-check-label" for="dontShowAgain">
                    Jangan tampilkan lagi
                </label>
            </div>
        <?php endif; ?>
        
        <button type="button" class="btn btn-secondary">
            <i class="fas fa-times"></i> Tutup
        </button>
    </div>
</div>

<style>
.popup-preview {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

.popup-preview .modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
    padding: 1rem;
}

.popup-preview .modal-body {
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
}

.popup-preview .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem;
    background-color: #f8f9fa;
}

.news-item {
    margin-bottom: 1rem;
}

.news-title {
    color: #495057;
    font-weight: 600;
}

.news-excerpt {
    font-size: 0.9rem;
    line-height: 1.4;
}

.news-meta {
    font-size: 0.8rem;
}

.news-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}
</style>
