-- Database Migration Script: Fix Restore Conflicts
-- This script handles existing tables and views gracefully during restoration
-- Created: 2025-08-08
-- Purpose: Prevent "Table already exists" errors during database restoration

-- Set session variables for better error handling
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';

-- Create a stored procedure to safely drop views if they exist
DELIMITER $$

DROP PROCEDURE IF EXISTS SafeDropViews$$

CREATE PROCEDURE SafeDropViews()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    
    -- Drop views that commonly cause conflicts
    DROP VIEW IF EXISTS v_siswa_all_periods;
    DROP VIEW IF EXISTS v_siswa_current;
    DROP VIEW IF EXISTS v_absensi_summary;
    DROP VIEW IF EXISTS v_nilai_summary;
    DROP VIEW IF EXISTS v_jadwal_detail;
    
    -- Add any other views that might cause conflicts
    -- You can add more DROP VIEW statements here as needed
    
END$$

DELIMITER ;

-- Create a stored procedure to safely drop tables if they exist
DELIMITER $$

DROP PROCEDURE IF EXISTS SafeDropTables$$

CREATE PROCEDURE SafeDropTables()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    
    -- Disable foreign key checks temporarily
    SET FOREIGN_KEY_CHECKS = 0;
    
    -- Drop tables in reverse dependency order to avoid foreign key conflicts
    -- Note: Add tables here if they commonly cause conflicts during restore
    -- This is mainly for emergency cleanup - normal backups should handle this
    
    -- Re-enable foreign key checks
    SET FOREIGN_KEY_CHECKS = 1;
    
END$$

DELIMITER ;

-- Create a stored procedure for pre-restoration cleanup
DELIMITER $$

DROP PROCEDURE IF EXISTS PreRestorationCleanup$$

CREATE PROCEDURE PreRestorationCleanup()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    
    -- Log the cleanup start
    SELECT 'Starting pre-restoration cleanup...' as status;
    
    -- First, drop all views to prevent conflicts
    CALL SafeDropViews();
    
    -- Then handle any problematic tables if needed
    -- CALL SafeDropTables(); -- Uncomment only if needed for emergency cleanup
    
    -- Log the cleanup completion
    SELECT 'Pre-restoration cleanup completed.' as status;
    
END$$

DELIMITER ;

-- Create a stored procedure for post-restoration validation
DELIMITER $$

DROP PROCEDURE IF EXISTS PostRestorationValidation$$

CREATE PROCEDURE PostRestorationValidation()
BEGIN
    DECLARE view_count INT DEFAULT 0;
    DECLARE table_count INT DEFAULT 0;
    
    -- Count views
    SELECT COUNT(*) INTO view_count 
    FROM information_schema.VIEWS 
    WHERE TABLE_SCHEMA = DATABASE();
    
    -- Count tables
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE';
    
    -- Display results
    SELECT 
        'Post-restoration validation completed' as status,
        table_count as total_tables,
        view_count as total_views,
        CASE 
            WHEN view_count >= 2 AND table_count >= 10 THEN 'PASSED'
            ELSE 'WARNING: Some objects may be missing'
        END as validation_result;
        
END$$

DELIMITER ;

-- Restore session variables
SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;

-- Usage Instructions:
-- 1. Before restoration: CALL PreRestorationCleanup();
-- 2. Run your restoration process
-- 3. After restoration: CALL PostRestorationValidation();

SELECT 'Migration script completed successfully. Procedures created:' as status;
SELECT 'SafeDropViews, SafeDropTables, PreRestorationCleanup, PostRestorationValidation' as procedures;
