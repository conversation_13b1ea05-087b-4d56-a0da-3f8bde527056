<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

// Start output buffering to catch any unexpected output
ob_start();

try {
    require_once '../middleware/auth.php';
    checkGuruAccess();
    require_once '../config/database.php';
    require_once '../models/KompetensiDasar.php';
    require_once '../models/PeriodeAktif.php';
} catch (Exception $e) {
    // Clear any output and send error response
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error loading required files: ' . $e->getMessage(),
        'debug' => 'Include error'
    ]);
    exit();
}

// Clear any unexpected output from includes
ob_clean();
header('Content-Type: application/json');

// Check if POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
    exit();
}

// Validate required parameters
if (!isset($_POST['mapel_id']) || !isset($_POST['kelas_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Parameter mapel_id dan kelas_id wajib diisi',
        'debug' => 'Missing parameters',
        'received_post' => array_keys($_POST)
    ]);
    exit();
}

// Validate session
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Session tidak valid. Silakan login ulang.',
        'debug' => 'No session user_id'
    ]);
    exit();
}

$mapel_id = $_POST['mapel_id'];
$kelas_id = $_POST['kelas_id'];
$guru_id = $_SESSION['user_id'];

// Validate parameter values
if (empty($mapel_id) || empty($kelas_id)) {
    echo json_encode([
        'success' => false,
        'message' => 'Parameter mapel_id dan kelas_id tidak boleh kosong',
        'debug' => 'Empty parameters',
        'mapel_id' => $mapel_id,
        'kelas_id' => $kelas_id
    ]);
    exit();
}

// Get optional parameters
$semester = isset($_POST['semester']) ? $_POST['semester'] : null;
$tahun_ajaran = isset($_POST['tahun_ajaran']) ? $_POST['tahun_ajaran'] : null;

// If semester and tahun_ajaran not provided, get from active period
if (!$semester || !$tahun_ajaran) {
    try {
        $periodeAktif = new PeriodeAktif();
        $periode_active = $periodeAktif->getActive();
        
        if ($periode_active) {
            if (!$semester) {
                $semester = $periode_active['semester'];
            }
            if (!$tahun_ajaran) {
                $tahun_ajaran = $periode_active['tahun_ajaran'];
            }
        }
    } catch (Exception $e) {
        // Continue without active period data
    }
}

try {
    // Test database connection first
    $database = new Database();
    $conn = $database->getConnection();

    if (!$conn) {
        echo json_encode([
            'success' => false,
            'message' => 'Koneksi database gagal',
            'debug' => 'Database connection failed'
        ]);
        exit();
    }

    $kd = new KompetensiDasar();

    // Get KD data for the specified parameters
    $kd_data = $kd->getKdForRpp($mapel_id, $kelas_id, $guru_id, $semester, $tahun_ajaran);

    if (empty($kd_data)) {
        echo json_encode([
            'success' => true,
            'message' => 'Tidak ada data Kompetensi Dasar yang ditemukan untuk mata pelajaran dan kelas ini',
            'data' => [
                'kd_list' => [],
                'formatted_kd' => '',
                'count' => 0
            ],
            'debug' => [
                'mapel_id' => $mapel_id,
                'kelas_id' => $kelas_id,
                'guru_id' => $guru_id,
                'semester' => $semester,
                'tahun_ajaran' => $tahun_ajaran
            ]
        ]);
        exit();
    }

    // Format KD for RPP field
    $formatted_kd = $kd->formatKdForRpp($kd_data);

    echo json_encode([
        'success' => true,
        'message' => 'Data Kompetensi Dasar berhasil dimuat',
        'data' => [
            'kd_list' => $kd_data,
            'formatted_kd' => $formatted_kd,
            'count' => count($kd_data),
            'semester' => $semester,
            'tahun_ajaran' => $tahun_ajaran
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        'debug' => [
            'error_type' => get_class($e),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'stack_trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
