<?php
session_start();
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';
require_once '../models/MataPelajaran.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

// Ambil parameter filter
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$semester = isset($_GET['semester']) ? $_GET['semester'] : '';
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : '';
$mapel_id = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';

// Jika mapel_id tidak ada, kembalikan ke halaman index
if (!$mapel_id) {
    $_SESSION['error'] = "Mata pelajaran harus dipilih";
    header("Location: index.php");
    exit();
}

$nilaiSikap = new NilaiSikap();
$mataPelajaran = new MataPelajaran();

$data = $nilaiSikap->getByKelas($kelas_id, $semester, $tahun_ajaran, $mapel_id);
$data_mapel = $mataPelajaran->getById($mapel_id);

// Set header untuk download Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="Nilai_Sikap_' . date('Y-m-d') . '.xls"');
header('Cache-Control: max-age=0');

// Output Excel content
?>
<table border="1">
    <thead>
        <tr>
            <th colspan="8" style="text-align: center; font-size: 14pt;">DATA NILAI SIKAP SISWA</th>
        </tr>
        <tr>
            <th colspan="8" style="text-align: center;">Tahun Ajaran: <?= $tahun_ajaran ?> - Semester: <?= $semester ?></th>
        </tr>
        <tr>
            <th colspan="8" style="text-align: center;">Mata Pelajaran: <?= $data_mapel['nama_mapel'] ?></th>
        </tr>
        <tr>
            <th>No</th>
            <th>NIS</th>
            <th>Nama Siswa</th>
            <th>Kelas</th>
            <th>Nilai Spiritual</th>
            <th>Deskripsi Spiritual</th>
            <th>Nilai Sosial</th>
            <th>Deskripsi Sosial</th>
        </tr>
    </thead>
    <tbody>
        <?php 
        $no = 1;
        foreach ($data as $row): 
        ?>
        <tr>
            <td><?= $no++ ?></td>
            <td><?= $row['nis'] ?></td>
            <td><?= $row['nama_siswa'] ?></td>
            <td><?= $row['nama_kelas'] ?></td>
            <td><?= $row['nilai_spiritual'] ?></td>
            <td><?= $row['deskripsi_spiritual'] ?></td>
            <td><?= $row['nilai_sosial'] ?></td>
            <td><?= $row['deskripsi_sosial'] ?></td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>