<?php
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'Kode Mapel');
$sheet->setCellValue('B1', 'Nama Mapel');
$sheet->setCellValue('C1', 'Tingkat');
$sheet->setCellValue('D1', 'Jurusan');
$sheet->setCellValue('E1', 'Guru Pengampu');

// Example data
$sheet->setCellValue('A2', 'MTK');
$sheet->setCellValue('B2', 'Matematika');
$sheet->setCellValue('C2', 'X');
$sheet->setCellValue('D2', 'IPA, IPS');
$sheet->setCellValue('E2', '<PERSON>a Guru');

// Add note for multiple values
$sheet->setCellValue('A4', 'Catatan:');
$sheet->setCellValue('A5', '* Untuk kolom Jurusan, bisa diisi lebih dari satu jurusan dengan dipisahkan tanda koma. Contoh: IPA, IPS');
$sheet->setCellValue('A6', '* Untuk kolom Guru Pengampu, bisa diisi lebih dari satu guru dengan dipisahkan tanda koma. Contoh: Guru 1, Guru 2');

// Auto-size columns
foreach(range('A','E') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Style the header row
$sheet->getStyle('A1:E1')->getFont()->setBold(true);
$sheet->getStyle('A1:E1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Style the notes
$sheet->getStyle('A4')->getFont()->setBold(true);

// Create the Excel file
$writer = new Xlsx($spreadsheet);
$writer->save('template_mapel.xlsx');

echo "Template created successfully!";
?>
