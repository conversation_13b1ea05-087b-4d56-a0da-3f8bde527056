<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();
require_once '../template/header.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/JamPelajaranConfig.php';
require_once '../models/DetailJamPelajaran.php';
require_once '../models/DetailJadwalJam.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';

$jadwal = new JadwalPelajaran();
$kelas = new Kelas();
$mapel = new MataPelajaran();
$guru = new Guru();
$jamConfig = new JamPelajaranConfig();
$detailJam = new DetailJamPelajaran();
$detailJadwalJam = new DetailJadwalJam();
$tingkat = new Tingkat();
$jurusan = new Jurusan();

$kelas_list = $kelas->getAll();
$mapel_list = $mapel->getAll();
$tingkat_list = $tingkat->getAll();
$jurusan_list = $jurusan->getAll();
$error_msg = "";
$conflict_warning = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $jadwal->kelas_id = $_POST['kelas_id'];
    $jadwal->mapel_id = $_POST['mapel_id'];
    $jadwal->hari = $_POST['hari'];
    $jadwal->guru_id = $_POST['guru_id'];
    $jadwal->tingkat_id = $_POST['tingkat_id'];
    
    // Get selected jurusan
    $jurusan_ids = isset($_POST['jurusan_ids']) ? $_POST['jurusan_ids'] : [];
    
    // Get selected jam pelajaran
    $selected_jam = isset($_POST['jam_ke']) ? $_POST['jam_ke'] : [];
    
    if (empty($selected_jam)) {
        $error_msg = "Pilih minimal satu jam pelajaran";
    } else {
        try {
            // Get the time details from configuration
            $config = $jamConfig->getByHari($jadwal->hari);
            if ($config) {
                $details = $detailJam->getByConfigId($config['id']);
                $time_details = [];
                while ($detail = $details->fetch(PDO::FETCH_ASSOC)) {
                    if (in_array($detail['jam_ke'], $selected_jam)) {
                        $time_details[] = $detail;
                    }
                }
                
                // Set first time slot for jadwal_pelajaran table
                if (!empty($time_details)) {
                    $jadwal->jam_mulai = $time_details[0]['jam_mulai'];
                    $jadwal->jam_selesai = end($time_details)['jam_selesai'];
                }
                
                // Check for conflicts
                $conflicts = $jadwal->checkConflicts(0, $jadwal->hari, $jadwal->guru_id, $selected_jam);
                
                if (!empty($conflicts)) {
                    $conflict_warning = "<div class='alert alert-warning'><strong>Peringatan!</strong> Jadwal ini bertabrakan dengan:<ul>";
                    foreach ($conflicts as $conflict) {
                        $conflict_warning .= "<li>{$conflict['mapel']} - {$conflict['kelas']} (Jam ke-{$conflict['jam_ke']})</li>";
                    }
                    $conflict_warning .= "</ul>Anda masih bisa menyimpan jadwal ini, tapi mohon pertimbangkan kembali.</div>";
                }

                if (isset($_POST['confirm_save']) || empty($conflicts)) {
                    // Proceed with saving
                    if ($jadwal->create()) {
                        // Get the last inserted ID
                        $last_id = $jadwal->getLastInsertedId();
                        
                        // Save all selected time slots
                        foreach ($time_details as $time) {
                            $detailJadwalJam->jadwal_id = $last_id;
                            $detailJadwalJam->jam_ke = $time['jam_ke'];
                            $detailJadwalJam->jam_mulai = $time['jam_mulai'];
                            $detailJadwalJam->jam_selesai = $time['jam_selesai'];
                            $detailJadwalJam->create();
                        }

                        // Simpan relasi dengan jurusan
                        $jurusan_id = $_POST['jurusan_ids'];
                        if (!empty($jurusan_id)) {
                            $jadwal->saveJurusan($last_id, $jurusan_id);
                        }

                        header("Location: index.php?success=1");
                        exit;
                    }
                }
            }
        } catch (Exception $e) {
            $error_msg = "Gagal menyimpan jadwal: " . $e->getMessage();
        }
    }
}

$hari_list = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Tambah Jadwal Pelajaran</h5>
            </div>
            <div class="card-body">
                <?php if ($error_msg): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_msg; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($conflict_warning): ?>
                    <?php echo $conflict_warning; ?>
                    <form method="post" class="mb-3">
                        <input type="hidden" name="kelas_id" value="<?php echo htmlspecialchars($jadwal->kelas_id); ?>">
                        <input type="hidden" name="mapel_id" value="<?php echo htmlspecialchars($jadwal->mapel_id); ?>">
                        <input type="hidden" name="hari" value="<?php echo htmlspecialchars($jadwal->hari); ?>">
                        <input type="hidden" name="guru_id" value="<?php echo htmlspecialchars($jadwal->guru_id); ?>">
                        <input type="hidden" name="tingkat_id" value="<?php echo htmlspecialchars($jadwal->tingkat_id); ?>">
                        <input type="hidden" name="jurusan_ids" value="<?php echo htmlspecialchars($_POST['jurusan_ids']); ?>">
                        <?php foreach ($selected_jam as $jam): ?>
                            <input type="hidden" name="jam_ke[]" value="<?php echo htmlspecialchars($jam); ?>">
                        <?php endforeach; ?>
                        <input type="hidden" name="confirm_save" value="1">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> Simpan Meskipun Bertabrakan
                        </button>
                        <a href="create.php" class="btn btn-secondary">Ubah Jadwal</a>
                    </form>
                <?php endif; ?>

                <form action="" method="post">
                    <div class="mb-3">
                        <label for="tingkat_id" class="form-label">Tingkat</label>
                        <select class="form-select" id="tingkat_id" name="tingkat_id" required>
                            <option value="">Pilih Tingkat</option>
                            <?php while ($row = $tingkat_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>">
                                    <?php echo htmlspecialchars($row['nama_tingkat']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="jurusan_ids" class="form-label">Jurusan</label>
                        <select class="form-select" id="jurusan_ids" name="jurusan_ids" required>
                            <option value="">Pilih Jurusan</option>
                            <?php while ($row = $jurusan_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>">
                                    <?php echo htmlspecialchars($row['nama_jurusan']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="kelas_id" class="form-label">Kelas</label>
                        <select class="form-select" id="kelas_id" name="kelas_id" required>
                            <option value="">Pilih Kelas</option>
                            <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>">
                                    <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                        <select class="form-select" id="mapel_id" name="mapel_id" required onchange="loadGuruPengampu()">
                            <option value="">Pilih Mata Pelajaran</option>
                            <?php while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row['id']; ?>">
                                    <?php echo htmlspecialchars($row['nama_mapel']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="hari" class="form-label">Hari</label>
                        <select class="form-select" id="hari" name="hari" required onchange="loadJamPelajaran()">
                            <option value="">Pilih Hari</option>
                            <option value="Senin">Senin</option>
                            <option value="Selasa">Selasa</option>
                            <option value="Rabu">Rabu</option>
                            <option value="Kamis">Kamis</option>
                            <option value="Jumat">Jumat</option>
                            <option value="Sabtu">Sabtu</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Jam Pelajaran</label>
                        <div class="row" id="jamPelajaranContainer">
                            <!-- Time slots will be loaded here -->
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="guru_id" class="form-label">Guru Pengampu</label>
                        <select class="form-select" id="guru_id" name="guru_id" required disabled>
                            <option value="">Pilih Mata Pelajaran Terlebih Dahulu</option>
                        </select>
                        <small class="text-muted">Guru pengampu akan otomatis terisi sesuai dengan mata pelajaran yang dipilih</small>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize empty selected_jam array for create page
const selected_jam = [];

// Function to update kelas and mapel based on tingkat and jurusan
function updateKelasAndMapel() {
    const tingkat_id = document.getElementById('tingkat_id').value;
    const jurusan_id = document.getElementById('jurusan_ids').value;
    const kelas_select = document.getElementById('kelas_id');
    const mapel_select = document.getElementById('mapel_id');

    if (tingkat_id && jurusan_id) {
        // Update kelas
        fetch(`get_kelas.php?tingkat_id=${tingkat_id}&jurusan_id=${jurusan_id}`)
            .then(response => response.json())
            .then(data => {
                kelas_select.innerHTML = '<option value="">Pilih Kelas</option>';
                data.forEach(kelas => {
                    kelas_select.innerHTML += `<option value="${kelas.id}">${kelas.nama_kelas}</option>`;
                });
            });

        // Update mapel
        fetch(`get_mapel.php?tingkat_id=${tingkat_id}&jurusan_id=${jurusan_id}`)
            .then(response => response.json())
            .then(data => {
                mapel_select.innerHTML = '<option value="">Pilih Mata Pelajaran</option>';
                data.forEach(mapel => {
                    mapel_select.innerHTML += `<option value="${mapel.id}">${mapel.nama_mapel}</option>`;
                });
            });
    }
}

// Add event listeners
document.getElementById('tingkat_id').addEventListener('change', updateKelasAndMapel);
document.getElementById('jurusan_ids').addEventListener('change', updateKelasAndMapel);

function loadJamPelajaran() {
    const hari = document.getElementById('hari').value;
    if (hari) {
        fetch(`get_config.php?hari=${hari}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('jamPelajaranContainer');
                container.innerHTML = '';
                if (data.details && data.details.length > 0) {
                    data.details.forEach(jam => {
                        const col = document.createElement('div');
                        col.className = 'col-md-4 mb-2';
                        col.innerHTML = `
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="jam_ke[]" 
                                       value="${jam.jam_ke}" id="jam_${jam.jam_ke}">
                                <label class="form-check-label" for="jam_${jam.jam_ke}">
                                    Jam ke-${jam.jam_ke} (${jam.jam_mulai}-${jam.jam_selesai})
                                </label>
                            </div>
                        `;
                        container.appendChild(col);
                    });
                }
            });
    }
}

function loadGuruPengampu() {
    const mapelId = document.getElementById('mapel_id').value;
    const guruSelect = document.getElementById('guru_id');
    
    if (mapelId) {
        fetch(`get_guru_pengampu.php?mapel_id=${mapelId}`)
            .then(response => response.json())
            .then(data => {
                guruSelect.innerHTML = '';
                if (data.length === 0) {
                    guruSelect.innerHTML = '<option value="">Tidak ada guru pengampu untuk mata pelajaran ini</option>';
                } else if (data.length === 1) {
                    // If only one teacher, auto-select them
                    const guru = data[0];
                    guruSelect.innerHTML = `<option value="${guru.id}" selected>${guru.nama_lengkap}</option>`;
                } else {
                    // If multiple teachers, let user choose
                    guruSelect.innerHTML = '<option value="">Pilih Guru Pengampu</option>';
                    data.forEach(guru => {
                        guruSelect.innerHTML += `<option value="${guru.id}">${guru.nama_lengkap}</option>`;
                    });
                }
                guruSelect.disabled = false;
            });
    } else {
        guruSelect.innerHTML = '<option value="">Pilih Mata Pelajaran Terlebih Dahulu</option>';
        guruSelect.disabled = true;
    }
}
</script>

<?php include '../template/footer.php'; ?>
