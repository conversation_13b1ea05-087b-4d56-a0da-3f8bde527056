<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/MataPelajaran.php';

$mapel = new MataPelajaran();
$result = $mapel->getAll();

// Handle success message
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Daftar Mata Pelajaran</h5>
                <div>
                    <a href="import.php" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Import Excel
                    </a>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Mata Pelajaran
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableMapel">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Kode Mapel</th>
                                <th>Nama Mapel</th>
                                <th>Tingkat</th>
                                <th>Jurusan</th>
                                <th>Guru Pengampu</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $hasData = false;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)):
                                $hasData = true;
                                $guru_pengampu = $mapel->getGuruPengampu($row['id']);
                                $guru_list = [];
                                while ($guru = $guru_pengampu->fetch(PDO::FETCH_ASSOC)) {
                                    $guru_list[] = $guru['nama_lengkap'];
                                }
                            ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= htmlspecialchars($row['kode_mapel']) ?></td>
                                    <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                    <td><?= htmlspecialchars($row['nama_tingkat']) ?></td>
                                    <td><?= htmlspecialchars($row['nama_jurusan']) ?></td>
                                    <td><?= htmlspecialchars(implode(', ', $guru_list)) ?></td>
                                    <td>
                                        <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="delete.php?id=<?php echo $row['id']; ?>"
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirmDelete()">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <?php if (!$hasData): ?>
                        <p class="text-center mt-3">Tidak ada data mata pelajaran</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableMapel').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data mata pelajaran",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[2, "asc"]], // Urutkan berdasarkan nama mapel
        "columnDefs": [
            {"orderable": false, "targets": 6}, // Kolom aksi tidak bisa diurutkan
            {"width": "100px", "targets": 6} // Atur lebar kolom aksi
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});

function confirmDelete() {
    return confirm("Apakah Anda yakin ingin menghapus data ini?");
}
</script>

<?php
require_once '../template/footer.php';
?>
