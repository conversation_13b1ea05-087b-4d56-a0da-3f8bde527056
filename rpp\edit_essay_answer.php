<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/EssayAnswer.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validate input
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "Answer ID tidak ditemukan";
    header("Location: essay_answers_management.php");
    exit();
}

$answer_id = $_GET['id'];

try {
    // Get answer data
    $essayAnswer = new EssayAnswer();
    $answer_data = $essayAnswer->getOne($answer_id);

    if (!$answer_data) {
        $_SESSION['error'] = "Jawaban tidak ditemukan";
        header("Location: essay_answers_management.php");
        exit();
    }

    // Get question data and verify ownership
    if ($answer_data['question_type'] === 'rpp_question') {
        $rppQuestion = new RppQuestion();
        $question_data = $rppQuestion->getOne($answer_data['question_id']);
        
        if (!$question_data) {
            $_SESSION['error'] = "Soal tidak ditemukan";
            header("Location: essay_answers_management.php");
            exit();
        }

        // Check RPP ownership
        $database = new Database();
        $conn = $database->getConnection();
        
        $verify_query = "SELECT r.*, mp.nama_mapel, k.nama_kelas 
                        FROM rpp r
                        LEFT JOIN mata_pelajaran mp ON r.mapel_id = mp.id
                        LEFT JOIN kelas k ON r.kelas_id = k.id
                        WHERE r.id = :rpp_id";
        $verify_stmt = $conn->prepare($verify_query);
        $verify_stmt->bindParam(":rpp_id", $question_data['rpp_id']);
        $verify_stmt->execute();
        $rpp_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            $_SESSION['error'] = "Anda tidak memiliki akses untuk mengedit jawaban ini";
            header("Location: essay_answers_management.php");
            exit();
        }

        $source_title = $rpp_data['tema_subtema'];
        $source_description = $rpp_data['nama_mapel'] . ' - ' . $rpp_data['nama_kelas'];
    } else {
        // Handle multi-RPP questions
        $database = new Database();
        $conn = $database->getConnection();
        
        $verify_query = "SELECT meq.*, me.exam_title, me.guru_id 
                        FROM multi_rpp_exam_questions meq
                        JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                        WHERE meq.id = :question_id";
        $verify_stmt = $conn->prepare($verify_query);
        $verify_stmt->bindParam(":question_id", $answer_data['question_id']);
        $verify_stmt->execute();
        $exam_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
            $_SESSION['error'] = "Anda tidak memiliki akses untuk mengedit jawaban ini";
            header("Location: essay_answers_management.php");
            exit();
        }

        $question_data = $exam_data;
        $source_title = $exam_data['exam_title'];
        $source_description = 'Multi-RPP Exam';
    }

    // Decode JSON fields
    $scoring_rubric = json_decode($answer_data['scoring_rubric'], true) ?: [];
    $generation_metadata = json_decode($answer_data['generation_metadata'], true) ?: [];

} catch (Exception $e) {
    $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
    header("Location: essay_answers_management.php");
    exit();
}

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Edit Jawaban Essay</h5>
                    <a href="essay_answers_management.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($success_msg): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($success_msg) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_msg): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($error_msg) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Question Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Informasi Soal</h6>
                                    <p><strong>Sumber:</strong> <?= htmlspecialchars($source_title) ?></p>
                                    <p><strong>Deskripsi:</strong> <?= htmlspecialchars($source_description) ?></p>
                                    <p><strong>Tipe:</strong> <?= $answer_data['question_type'] === 'rpp_question' ? 'Single RPP' : 'Multi-RPP' ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Soal Essay</h6>
                                    <div class="question-text" style="line-height: 1.6;">
                                        <?= nl2br(htmlspecialchars($question_data['question_text'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <form action="update_essay_answer.php" method="POST" id="editAnswerForm">
                        <input type="hidden" name="answer_id" value="<?= $answer_id ?>">
                        
                        <div class="mb-4">
                            <label for="expected_answer" class="form-label">
                                <strong>Jawaban yang Diharapkan</strong>
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="expected_answer" name="expected_answer" 
                                    rows="8" required><?= htmlspecialchars($answer_data['expected_answer']) ?></textarea>
                            <div class="form-text">Berikan jawaban lengkap dan komprehensif yang diharapkan dari siswa.</div>
                        </div>

                        <div class="mb-4">
                            <label for="answer_points" class="form-label">
                                <strong>Poin-poin Kunci</strong>
                            </label>
                            <textarea class="form-control" id="answer_points" name="answer_points" 
                                    rows="6"><?= htmlspecialchars($answer_data['answer_points']) ?></textarea>
                            <div class="form-text">Tuliskan poin-poin kunci yang harus ada dalam jawaban siswa (opsional).</div>
                        </div>

                        <!-- Scoring Rubric -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Rubrik Penilaian</strong></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="rubric_excellent" class="form-label">Sangat Baik (90-100)</label>
                                        <textarea class="form-control" id="rubric_excellent" name="rubric_excellent" 
                                                rows="3"><?= htmlspecialchars($scoring_rubric['excellent'] ?? '') ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="rubric_good" class="form-label">Baik (80-89)</label>
                                        <textarea class="form-control" id="rubric_good" name="rubric_good" 
                                                rows="3"><?= htmlspecialchars($scoring_rubric['good'] ?? '') ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="rubric_fair" class="form-label">Cukup (70-79)</label>
                                        <textarea class="form-control" id="rubric_fair" name="rubric_fair" 
                                                rows="3"><?= htmlspecialchars($scoring_rubric['fair'] ?? '') ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="rubric_poor" class="form-label">Kurang (60-69)</label>
                                        <textarea class="form-control" id="rubric_poor" name="rubric_poor" 
                                                rows="3"><?= htmlspecialchars($scoring_rubric['poor'] ?? '') ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="essay_answers_management.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('editAnswerForm').addEventListener('submit', function(e) {
    const expectedAnswer = document.getElementById('expected_answer').value.trim();
    
    if (!expectedAnswer) {
        e.preventDefault();
        alert('Jawaban yang diharapkan harus diisi!');
        return false;
    }
    
    return true;
});
</script>

<?php require_once '../template/footer.php'; ?>
