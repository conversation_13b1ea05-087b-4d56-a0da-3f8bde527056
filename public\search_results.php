<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../template/public_header.php';
require_once '../models/Siswa.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';

// Check if search parameters are provided
if (!isset($_GET['search_type']) || empty($_GET['search_type'])) {
    $_SESSION['error'] = "Tipe pencarian tidak valid.";
    header("Location: index.php");
    exit();
}

// Get search parameters
$search_type = $_GET['search_type'];
$semester = $_GET['semester'] ?? '1';
$tahun_ajaran = $_GET['tahun_ajaran'] ?? '';

// Initialize models
$siswaModel = new Siswa();

// Handle search based on type
if ($search_type === 'nama') {
    if (!isset($_GET['nama']) || empty($_GET['nama'])) {
        $_SESSION['error'] = "Nama siswa tidak boleh kosong.";
        header("Location: index.php");
        exit();
    }

    $nama = $_GET['nama'];
    $result = $siswaModel->getByNama($nama);

    if (!$result || $result->rowCount() === 0) {
        $_SESSION['error'] = "Siswa dengan nama yang mengandung '$nama' tidak ditemukan.";
        header("Location: index.php");
        exit();
    }

    // If only one result, redirect directly to student_data.php
    if ($result->rowCount() === 1) {
        $siswa = $result->fetch(PDO::FETCH_ASSOC);
        header("Location: student_data.php?nis=" . $siswa['nis'] . "&semester=" . $semester . "&tahun_ajaran=" . urlencode($tahun_ajaran));
        exit();
    }

    // Multiple results, show selection page
    $search_results = $result;
} else {
    // For NIS search, redirect directly to student_data.php
    if (!isset($_GET['nis']) || empty($_GET['nis'])) {
        $_SESSION['error'] = "NISN tidak boleh kosong.";
        header("Location: index.php");
        exit();
    }

    $nis = $_GET['nis'];
    header("Location: student_data.php?nis=" . $nis . "&semester=" . $semester . "&tahun_ajaran=" . urlencode($tahun_ajaran));
    exit();
}
?>

<div class="row mb-4">
    <div class="col-12">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-search me-2"></i> Hasil Pencarian
                </h5>
            </div>
            <div class="card-body">
                <p>Ditemukan <?php echo $search_results->rowCount(); ?> siswa dengan nama yang mengandung "<?php echo htmlspecialchars($nama); ?>". Silakan pilih siswa:</p>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NISN</th>
                                <th>Nama Siswa</th>
                                <th>Kelas</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while ($siswa = $search_results->fetch(PDO::FETCH_ASSOC)):
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $siswa['nis']; ?></td>
                                <td><?php echo $siswa['nama_siswa']; ?></td>
                                <td><?php echo $siswa['nama_kelas']; ?></td>
                                <td>
                                    <a href="student_data.php?nis=<?php echo $siswa['nis']; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> Lihat Data
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
