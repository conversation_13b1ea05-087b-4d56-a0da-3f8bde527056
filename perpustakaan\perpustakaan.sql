-- <PERSON>ruktur tabel untuk perpustakaan

-- <PERSON><PERSON> konfigurasi_perpustakaan
CREATE TABLE IF NOT EXISTS `konfigurasi_perpustakaan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kunci` varchar(50) NOT NULL,
  `nilai` text NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kunci` (`kunci`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabel pustakawan
CREATE TABLE IF NOT EXISTS `pustakawan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_guru` int(11) NOT NULL,
  `status` enum('aktif','nonaktif') NOT NULL DEFAULT 'aktif',
  `tanggal_mulai` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_guru` (`id_guru`),
  CONSTRAINT `pustakawan_ibfk_1` FOREIGN KEY (`id_guru`) REFERENCES `guru` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabel kategori_buku
CREATE TABLE IF NOT EXISTS `kategori_buku` (
  `id_kategori` int(11) NOT NULL AUTO_INCREMENT,
  `nama_kategori` varchar(50) NOT NULL,
  PRIMARY KEY (`id_kategori`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabel buku
CREATE TABLE IF NOT EXISTS `buku` (
  `id_buku` int(11) NOT NULL AUTO_INCREMENT,
  `judul_buku` varchar(100) NOT NULL,
  `id_kategori` int(11) NOT NULL,
  `pengarang` varchar(100) NOT NULL,
  `penerbit` varchar(100) NOT NULL,
  `tahun_terbit` year(4) NOT NULL,
  `isbn` varchar(20) DEFAULT NULL,
  `jumlah_buku` int(11) NOT NULL,
  `lokasi` varchar(50) DEFAULT NULL,
  `tanggal_input` datetime NOT NULL,
  PRIMARY KEY (`id_buku`),
  KEY `id_kategori` (`id_kategori`),
  CONSTRAINT `buku_ibfk_1` FOREIGN KEY (`id_kategori`) REFERENCES `kategori_buku` (`id_kategori`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabel peminjaman
CREATE TABLE IF NOT EXISTS `peminjaman` (
  `id_peminjaman` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_peminjaman` varchar(50) DEFAULT NULL,
  `id_anggota` int(11) NOT NULL,
  `tipe_anggota` enum('siswa','guru') NOT NULL,
  `id_buku` int(11) NOT NULL,
  `jumlah_pinjam_awal` int(11) NOT NULL,
  `jumlah_buku` int(11) NOT NULL DEFAULT 1,
  `tanggal_pinjam` date NOT NULL,
  `tanggal_kembali` date NOT NULL,
  `status` enum('dipinjam','kembali') NOT NULL DEFAULT 'dipinjam',
  PRIMARY KEY (`id_peminjaman`),
  KEY `id_buku` (`id_buku`),
  KEY `id_anggota_tipe` (`id_anggota`, `tipe_anggota`),
  CONSTRAINT `peminjaman_ibfk_1` FOREIGN KEY (`id_buku`) REFERENCES `buku` (`id_buku`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tambah kolom nomor_peminjaman
ALTER TABLE `peminjaman` ADD COLUMN IF NOT EXISTS `nomor_peminjaman` varchar(50) DEFAULT NULL AFTER `id_peminjaman`;

-- Tabel riwayat_pengembalian
CREATE TABLE IF NOT EXISTS `riwayat_pengembalian` (
  `id_riwayat` int(11) NOT NULL AUTO_INCREMENT,
  `id_peminjaman` int(11) NOT NULL,
  `jumlah_dikembalikan` int(11) NOT NULL,
  `tanggal_pengembalian` date NOT NULL,
  PRIMARY KEY (`id_riwayat`),
  KEY `id_peminjaman` (`id_peminjaman`),
  CONSTRAINT `riwayat_pengembalian_ibfk_1` FOREIGN KEY (`id_peminjaman`) REFERENCES `peminjaman` (`id_peminjaman`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
