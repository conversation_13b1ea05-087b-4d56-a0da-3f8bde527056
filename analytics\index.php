<?php
require_once '../config/config.php';
require_once '../template/header.php';

// Cek autentikasi
if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit();
}

// Fungsi untuk mendapatkan statistik kehadiran per kelas
function getKehadiranPerKelas($conn) {
    $query = "SELECT k.nama_kelas, 
              COUNT(CASE WHEN a.status = 'Hadir' THEN 1 END) as hadir,
              COUNT(CASE WHEN a.status = 'Izin' THEN 1 END) as izin,
              COUNT(CASE WHEN a.status = 'Sakit' THEN 1 END) as sakit,
              COUNT(CASE WHEN a.status = 'Alpha' THEN 1 END) as alpha
              FROM kelas k
              LEFT JOIN siswa s ON k.id = s.id_kelas
              LEFT JOIN absensi a ON s.id = a.id_siswa
              WHERE a.tanggal BETWEEN DATE_SUB(NOW(), INTERVAL 30 DAY) AND NOW()
              GROUP BY k.id";
    return mysqli_query($conn, $query);
}

// Fungsi untuk mendapatkan trend kehadiran dalam 7 hari terakhir
function getTrendKehadiran($conn) {
    $query = "SELECT DATE(tanggal) as tanggal, 
              COUNT(CASE WHEN status = 'Hadir' THEN 1 END) as hadir,
              COUNT(CASE WHEN status != 'Hadir' THEN 1 END) as tidak_hadir
              FROM absensi
              WHERE tanggal BETWEEN DATE_SUB(NOW(), INTERVAL 7 DAY) AND NOW()
              GROUP BY DATE(tanggal)
              ORDER BY tanggal";
    return mysqli_query($conn, $query);
}

$kehadiranPerKelas = getKehadiranPerKelas($conn);
$trendKehadiran = getTrendKehadiran($conn);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Analitik</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-container {
            padding: 20px;
        }
        .chart-container {
            width: 100%;
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .alert-container {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="analytics-container">
        <h2>Dashboard Analitik Kehadiran</h2>
        
        <!-- Grafik Kehadiran per Kelas -->
        <div class="chart-container">
            <canvas id="kehadiranChart"></canvas>
        </div>

        <!-- Trend Kehadiran -->
        <div class="chart-container">
            <canvas id="trendChart"></canvas>
        </div>

        <!-- Peringatan Kehadiran Rendah -->
        <div id="alertContainer"></div>
    </div>

    <script>
        // Data untuk grafik kehadiran per kelas
        const kehadiranData = {
            labels: <?php 
                $labels = [];
                $hadir = [];
                $izin = [];
                $sakit = [];
                $alpha = [];
                mysqli_data_seek($kehadiranPerKelas, 0);
                while($row = mysqli_fetch_assoc($kehadiranPerKelas)) {
                    $labels[] = $row['nama_kelas'];
                    $hadir[] = $row['hadir'];
                    $izin[] = $row['izin'];
                    $sakit[] = $row['sakit'];
                    $alpha[] = $row['alpha'];
                }
                echo json_encode($labels);
            ?>,
            datasets: [{
                label: 'Hadir',
                data: <?php echo json_encode($hadir); ?>,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }, {
                label: 'Izin',
                data: <?php echo json_encode($izin); ?>,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: 'Sakit',
                data: <?php echo json_encode($sakit); ?>,
                backgroundColor: 'rgba(255, 206, 86, 0.2)',
                borderColor: 'rgba(255, 206, 86, 1)',
                borderWidth: 1
            }, {
                label: 'Alpha',
                data: <?php echo json_encode($alpha); ?>,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        };

        // Membuat grafik kehadiran
        const kehadiranChart = new Chart(
            document.getElementById('kehadiranChart'),
            {
                type: 'bar',
                data: kehadiranData,
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Statistik Kehadiran per Kelas (30 Hari Terakhir)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            }
        );

        // Data untuk grafik trend
        const trendData = {
            labels: <?php 
                $dates = [];
                $hadirTrend = [];
                $tidakHadirTrend = [];
                mysqli_data_seek($trendKehadiran, 0);
                while($row = mysqli_fetch_assoc($trendKehadiran)) {
                    $dates[] = $row['tanggal'];
                    $hadirTrend[] = $row['hadir'];
                    $tidakHadirTrend[] = $row['tidak_hadir'];
                }
                echo json_encode($dates);
            ?>,
            datasets: [{
                label: 'Hadir',
                data: <?php echo json_encode($hadirTrend); ?>,
                borderColor: 'rgba(75, 192, 192, 1)',
                tension: 0.1
            }, {
                label: 'Tidak Hadir',
                data: <?php echo json_encode($tidakHadirTrend); ?>,
                borderColor: 'rgba(255, 99, 132, 1)',
                tension: 0.1
            }]
        };

        // Membuat grafik trend
        const trendChart = new Chart(
            document.getElementById('trendChart'),
            {
                type: 'line',
                data: trendData,
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Trend Kehadiran (7 Hari Terakhir)'
                        }
                    }
                }
            }
        );

        // Fungsi untuk menampilkan peringatan
        function showAlerts() {
            const alertContainer = document.getElementById('alertContainer');
            const threshold = 0.8; // 80% kehadiran minimum

            <?php
            mysqli_data_seek($kehadiranPerKelas, 0);
            while($row = mysqli_fetch_assoc($kehadiranPerKelas)) {
                $total = $row['hadir'] + $row['izin'] + $row['sakit'] + $row['alpha'];
                if($total > 0) {
                    $kehadiranRate = $row['hadir'] / $total;
                    if($kehadiranRate < 0.8) {
                        echo "alertContainer.innerHTML += `
                            <div class='alert-container alert-warning'>
                                <strong>Peringatan!</strong> Tingkat kehadiran kelas ${'" . $row['nama_kelas'] . "'} di bawah 80%
                            </div>
                        `;";
                    }
                }
            }
            ?>
        }

        // Menampilkan peringatan saat halaman dimuat
        showAlerts();
    </script>
</body>
</html>
