<?php
require_once __DIR__ . '/../config/database.php';

class Jurusan {
    private $conn;
    private $table_name = "jurusan";

    public $id;
    public $nama_jurusan;
    public $kode_jurusan;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY nama_jurusan ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                (nama_jurusan, kode_jurusan) 
                VALUES 
                (:nama_jurusan, :kode_jurusan)";
        
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':nama_jurusan', $this->nama_jurusan);
        $stmt->bindParam(':kode_jurusan', $this->kode_jurusan);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                SET nama_jurusan = :nama_jurusan,
                    kode_jurusan = :kode_jurusan
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':nama_jurusan', $this->nama_jurusan);
        $stmt->bindParam(':kode_jurusan', $this->kode_jurusan);
        $stmt->bindParam(':id', $this->id);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':id', $this->id);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->nama_jurusan = $row['nama_jurusan'];
            $this->kode_jurusan = $row['kode_jurusan'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        return false;
    }
}