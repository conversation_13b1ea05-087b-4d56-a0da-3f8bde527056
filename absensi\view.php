<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit;
}

$absensi = new Absensi();
$absensi->id = $_GET['id'];

if (!$absensi->getOne()) {
    header("Location: index.php");
    exit;
}

// Get attendance details
$details = $absensi->getDetails($absensi->id);
$summary = $absensi->getDetailSummary($absensi->id);
?>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Detail Absensi</h5>
                <div>
                    <a href="edit.php?id=<?php echo $absensi->id; ?>" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <th width="150">Tanggal</th>
                                <td>: <?php echo date('d/m/Y', strtotime($absensi->tanggal)); ?></td>
                            </tr>
                            <tr>
                                <th>Kelas</th>
                                <td>: <?php echo htmlspecialchars($absensi->nama_kelas); ?></td>
                            </tr>
                            <tr>
                                <th>Mata Pelajaran</th>
                                <td>: <?php echo htmlspecialchars($absensi->nama_mapel); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="row g-3">
                            <div class="col-md-6 col-lg-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Hadir</h6>
                                        <h2 class="mb-0"><?php echo $summary['hadir']; ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Sakit</h6>
                                        <h2 class="mb-0"><?php echo $summary['sakit']; ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Izin</h6>
                                        <h2 class="mb-0"><?php echo $summary['izin']; ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Alpha</h6>
                                        <h2 class="mb-0"><?php echo $summary['alpha']; ?></h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="detailTable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>NIS</th>
                                <th>Nama Siswa</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while ($row = $details->fetch(PDO::FETCH_ASSOC)):
                                $status_class = '';
                                switch($row['status_absensi']) {
                                    case 'Hadir':
                                        $status_class = 'success';
                                        break;
                                    case 'Sakit':
                                        $status_class = 'warning';
                                        break;
                                    case 'Izin':
                                        $status_class = 'info';
                                        break;
                                    case 'Alpha':
                                        $status_class = 'danger';
                                        break;
                                }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nis']); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_siswa']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars($row['status_absensi']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#detailTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/id.json'
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
