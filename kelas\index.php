<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';

$kelas = new Kelas();
$guru = new Guru();

// Get all teachers for wali kelas selection
$guru_list = $guru->getAll();

// Get all classes
$result = $kelas->getAll();

// Handle success message
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Data Kelas</h5>
                <div>
                    <a href="import.php" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Import Excel
                    </a>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Kelas
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableKelas">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tingkat</th>
                                <th>Jurusan</th>
                                <th>Nama Kelas</th>
                                <th>Wali Kelas</th>
                                <th>Tahun Ajaran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $hasData = false;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)) :
                                $hasData = true;
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_tingkat']); ?></td>
                                <td><?php echo htmlspecialchars($row['kode_jurusan'] . ' - ' . $row['nama_jurusan']); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_wali_kelas'] ?: 'Belum ditentukan'); ?></td>
                                <td><?php echo htmlspecialchars($row['tahun_ajaran']); ?></td>
                                <td>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirmDelete()">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <?php if (!$hasData): ?>
                        <p class="text-center mt-3">Tidak ada data kelas</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableKelas').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data kelas",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[3, "asc"]], // Urutkan berdasarkan nama kelas
        "columnDefs": [
            {"orderable": false, "targets": 6}, // Kolom aksi tidak bisa diurutkan
            {"width": "100px", "targets": 6} // Atur lebar kolom aksi
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});

function confirmDelete() {
    return confirm("Apakah Anda yakin ingin menghapus data ini?");
}
</script>

<?php
require_once '../template/footer.php';
?>
