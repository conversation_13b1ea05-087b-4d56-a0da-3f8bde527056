<?php
// Turn off error display to prevent HTML output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Start output buffering to catch any unexpected output
ob_start();

try {
    require_once __DIR__ . '/../middleware/auth.php';
    checkGuruAccess();
    require_once '../config/database.php';
    require_once '../models/RppQuestion.php';
    require_once '../models/EssayAnswer.php';
    require_once '../models/GeminiApi.php';
    require_once '../models/Rpp.php';
    require_once '../models/Guru.php';
} catch (Exception $e) {
    // Clear any output and send error
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Error loading required files: ' . $e->getMessage()]);
    exit();
}

// Clear any unexpected output from includes
ob_clean();

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
    exit();
}

try {
    // Check if essay_answers table exists
    if (!isset($database)) {
        $database = new Database();
    }
    if (!isset($conn)) {
        $conn = $database->getConnection();
    }

    $check_table = $conn->prepare("SHOW TABLES LIKE 'essay_answers'");
    $check_table->execute();

    if ($check_table->rowCount() == 0) {
        throw new Exception("Table essay_answers tidak ditemukan. Silakan jalankan migration script terlebih dahulu.");
    }

    // Validate input
    $source_type = $_POST['source_type'] ?? 'rpp'; // 'rpp' or 'multi_rpp'
    $source_id = $_POST['source_id'] ?? null;

    if (!$source_id) {
        throw new Exception("Source ID tidak ditemukan");
    }

    $questions_data = [];
    $rpp_context = '';

    if ($source_type === 'rpp') {
        // Handle single RPP
        $rpp_id = $source_id;
        
        // Verify RPP ownership
        $rpp = new Rpp();
        $rpp_data = $rpp->getOne($rpp_id);
        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke RPP ini");
        }

        // Get all essay questions for this RPP
        $rppQuestion = new RppQuestion();
        $questions_stmt = $rppQuestion->getByRppIdWithFilter($rpp_id, 'essay');
        
        while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
            $questions_data[] = [
                'id' => $question['id'],
                'question_text' => $question['question_text'],
                'difficulty_level' => $question['difficulty_level'],
                'category' => $question['category'] ?? '',
                'question_type' => 'rpp_question'
            ];
        }

        // Build RPP context
        $rpp_context = "Mata Pelajaran: " . ($rpp_data['nama_mapel'] ?? 'N/A') . "\n";
        $rpp_context .= "Kelas: " . ($rpp_data['nama_kelas'] ?? 'N/A') . "\n";
        $rpp_context .= "Materi Pokok: " . ($rpp_data['materi_pokok'] ?? 'N/A') . "\n";
        $rpp_context .= "Tujuan Pembelajaran: " . ($rpp_data['tujuan_pembelajaran'] ?? 'N/A') . "\n";
        $rpp_context .= "Kompetensi Dasar: " . ($rpp_data['kompetensi_dasar'] ?? 'N/A');

    } else {
        // Handle multi-RPP
        $exam_id = $source_id;
        
        require_once '../models/MultiRppExam.php';
        $multiRppExam = new MultiRppExam();
        
        // Verify exam ownership
        $exam_data = $multiRppExam->getOne($exam_id);
        
        if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke ujian ini");
        }

        // Get all essay questions for this exam
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT * FROM multi_rpp_exam_questions 
                 WHERE multi_exam_id = :exam_id AND question_type = 'essay'";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":exam_id", $exam_id);
        $stmt->execute();
        
        while ($question = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $questions_data[] = [
                'id' => $question['id'],
                'question_text' => $question['question_text'],
                'difficulty_level' => $question['difficulty_level'],
                'category' => $question['category'] ?? '',
                'question_type' => 'multi_rpp_question'
            ];
        }

        // Build context for multi-RPP
        $rpp_context = "Ujian Multi-RPP: " . $exam_data['exam_title'] . "\n";
        $rpp_context .= "Jenis Ujian: " . $exam_data['exam_type'] . "\n";
        $rpp_context .= "Semester: " . $exam_data['semester'] . "\n";
        $rpp_context .= "Tahun Ajaran: " . $exam_data['tahun_ajaran'];
    }

    if (empty($questions_data)) {
        throw new Exception("Tidak ada soal essay yang ditemukan");
    }

    // Check which questions already have answers
    $essayAnswer = new EssayAnswer();
    $question_ids = array_column($questions_data, 'id');
    $question_type = $questions_data[0]['question_type'];
    
    $existing_answers = $essayAnswer->getAnswersByQuestionIds($question_ids, $question_type);
    
    // Filter out questions that already have answers
    $questions_without_answers = [];
    foreach ($questions_data as $question) {
        if (!isset($existing_answers[$question['id']])) {
            $questions_without_answers[] = $question;
        }
    }

    if (empty($questions_without_answers)) {
        echo json_encode([
            'success' => true,
            'message' => 'Semua soal essay sudah memiliki jawaban',
            'generated_count' => 0,
            'existing_count' => count($questions_data),
            'total_count' => count($questions_data)
        ]);
        exit();
    }

    // Generate answers using Gemini API
    $geminiApi = new GeminiApi();
    $generated_answers = $geminiApi->generateBulkEssayAnswers($questions_without_answers, $rpp_context);

    if (!isset($generated_answers['answers']) || empty($generated_answers['answers'])) {
        throw new Exception("Gagal menghasilkan jawaban dari AI");
    }

    // Prepare data for bulk insert
    $answers_data = [];
    foreach ($generated_answers['answers'] as $answer) {
        $answers_data[] = [
            'question_id' => $answer['question_id'],
            'question_type' => $question_type,
            'expected_answer' => $answer['expected_answer'],
            'answer_points' => $answer['answer_points'],
            'scoring_rubric' => $answer['scoring_rubric'],
            'generation_metadata' => $answer['generation_metadata']
        ];
    }

    // Save to database
    $created_ids = $essayAnswer->createBulk($answers_data);

    echo json_encode([
        'success' => true,
        'message' => 'Jawaban berhasil dibuat untuk ' . count($created_ids) . ' soal',
        'generated_count' => count($created_ids),
        'existing_count' => count($existing_answers),
        'total_count' => count($questions_data),
        'created_ids' => $created_ids
    ]);

} catch (Exception $e) {
    // Log error for debugging
    error_log("Essay bulk generation error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
} catch (Error $e) {
    // Catch fatal errors
    error_log("Essay bulk generation fatal error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => 'Fatal error: ' . $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
