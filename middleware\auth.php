<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function checkAdminAccess() {
    // Check if user is not logged in
    if (!isset($_SESSION['user_id'])) {
        header("Location: /absen/login.php");
        exit();
    }

    // Check if user is not admin
    if ($_SESSION['role'] !== 'admin') {
        header("Location: /absen/403.php");
        exit();
    }
}

function checkLoginAccess() {
    if (!isset($_SESSION['user_id'])) {
        header("Location: /absen/login.php");
        exit();
    }
}

function checkGuruAccess() {
    // Check if user is not logged in
    if (!isset($_SESSION['user_id'])) {
        header("Location: /absen/login.php");
        exit();
    }

    // Check if user is not guru
    if ($_SESSION['role'] !== 'guru') {
        header("Location: /absen/403.php");
        exit();
    }
}
?>
