<?php
require_once __DIR__ . '/../config/database.php';

class JamPelajaranConfig {
    private $conn;
    public $id;
    public $hari;
    public $jumlah_jam;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO jam_pelajaran_config (hari, jumlah_jam) VALUES (:hari, :jumlah_jam)";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':hari', $this->hari);
        $stmt->bindParam(':jumlah_jam', $this->jumlah_jam);
        
        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE jam_pelajaran_config SET jumlah_jam = :jumlah_jam WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':jumlah_jam', $this->jumlah_jam);
        $stmt->bindParam(':id', $this->id);
        
        return $stmt->execute();
    }

    public function getByHari($hari) {
        $query = "SELECT * FROM jam_pelajaran_config WHERE hari = :hari";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':hari', $hari);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getAll() {
        $query = "SELECT * FROM jam_pelajaran_config ORDER BY FIELD(hari, 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu')";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
}
