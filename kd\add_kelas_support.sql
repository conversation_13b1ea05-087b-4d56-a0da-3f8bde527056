-- Add class support to existing kompetensi_dasar table
-- Run this script if you already have the kompetensi_dasar table without kelas_id

-- Check if kelas_id column already exists
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kompetensi_dasar'
    AND COLUMN_NAME = 'kelas_id'
);

-- Add kelas_id column if it doesn't exist
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE kompetensi_dasar ADD COLUMN kelas_id int(11) DEFAULT NULL AFTER guru_id',
    'SELECT "Column kelas_id already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint if column was added
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kompetensi_dasar'
    AND CONSTRAINT_NAME = 'kompetensi_dasar_ibfk_3'
);

SET @sql = IF(@constraint_exists = 0 AND @column_exists = 0,
    'ALTER TABLE kompetensi_dasar ADD CONSTRAINT kompetensi_dasar_ibfk_3 FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE SET NULL',
    'SELECT "Foreign key constraint already exists or column was not added" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for kelas_id if it doesn't exist
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kompetensi_dasar'
    AND INDEX_NAME = 'kelas_id'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE kompetensi_dasar ADD KEY kelas_id (kelas_id)',
    'SELECT "Index kelas_id already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update unique constraint to include kelas_id
-- First drop the old constraint if it exists
SET @old_constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kompetensi_dasar'
    AND CONSTRAINT_NAME = 'unique_kd_mapel_semester'
);

SET @sql = IF(@old_constraint_exists > 0,
    'ALTER TABLE kompetensi_dasar DROP INDEX unique_kd_mapel_semester',
    'SELECT "Old unique constraint does not exist" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add new unique constraint including kelas_id
SET @new_constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kompetensi_dasar'
    AND CONSTRAINT_NAME = 'unique_kd_mapel_kelas_semester'
);

SET @sql = IF(@new_constraint_exists = 0,
    'ALTER TABLE kompetensi_dasar ADD UNIQUE KEY unique_kd_mapel_kelas_semester (kode_kd, mapel_id, kelas_id, semester, tahun_ajaran)',
    'SELECT "New unique constraint already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show final table structure
DESCRIBE kompetensi_dasar;
