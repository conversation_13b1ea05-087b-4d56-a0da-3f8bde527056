<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/EssayAnswer.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Validasi parameter
if (!isset($_GET['exam_id'])) {
    $_SESSION['error'] = "ID ujian tidak ditemukan.";
    header("Location: multi_rpp_generate.php");
    exit();
}

$exam_id = $_GET['exam_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data ujian multi-RPP
$multiRppExam = new MultiRppExam();
$exam_data = $multiRppExam->getOne($exam_id);

if (!$exam_data || $exam_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    header("Location: multi_rpp_generate.php");
    exit();
}

// Decode JSON data
$selected_rpp_ids = json_decode($exam_data['selected_rpp_ids'], true);
$question_distribution = json_decode($exam_data['question_distribution'], true);

// Ambil data RPP yang terkait
$rpp = new Rpp();
$related_rpps = [];
foreach ($selected_rpp_ids as $rpp_id) {
    $rpp_data = $rpp->getOne($rpp_id);
    if ($rpp_data) {
        $related_rpps[] = $rpp_data;
    }
}

// Ambil soal-soal ujian
$questions_stmt = $multiRppExam->getQuestionsByExamId($exam_id);
$questions = [];
while ($question = $questions_stmt->fetch(PDO::FETCH_ASSOC)) {
    $questions[] = $question;
}

// Get essay answers for multi-RPP questions
$essayAnswer = new EssayAnswer();
$essay_question_ids = [];
foreach ($questions as $question) {
    if ($question['question_type'] === 'essay') {
        $essay_question_ids[] = $question['id'];
    }
}
$essay_answers = [];
if (!empty($essay_question_ids)) {
    $essay_answers = $essayAnswer->getAnswersByQuestionIds($essay_question_ids, 'multi_rpp_question');
}

// Ambil statistik soal
$stats_stmt = $multiRppExam->getQuestionStats($exam_id);
$question_stats = [];
while ($stat = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
    $question_stats[] = $stat;
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-layer-group"></i> Detail Ujian Multi-RPP
            </h5>
            <div>
                <a href="multi_blueprint_generate.php?exam_id=<?= $exam_id ?>" class="btn btn-info me-2">
                    <i class="fas fa-file-alt"></i> Generate Kisi-kisi
                </a>
                <a href="multi_rpp_generate.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <!-- Exam Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Ujian</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="150"><strong>Judul Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_title']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Jenis Ujian:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_type']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Semester:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['semester']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tahun Ajaran:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['tahun_ajaran']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="150"><strong>Durasi:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['exam_duration']) ?> menit</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Skor:</strong></td>
                                            <td><?= htmlspecialchars($exam_data['total_score']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Soal:</strong></td>
                                            <td><?= count($questions) ?> soal</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dibuat:</strong></td>
                                            <td><?= date('d/m/Y H:i', strtotime($exam_data['created_at'])) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if (!empty($exam_data['additional_notes'])): ?>
                                <div class="mt-3">
                                    <strong>Catatan Tambahan:</strong>
                                    <p class="mt-2"><?= nl2br(htmlspecialchars($exam_data['additional_notes'])) ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Related RPPs -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-layer-group"></i> RPP/Chapter Terkait (<?= count($related_rpps) ?> RPP)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>No</th>
                                            <th>Mata Pelajaran</th>
                                            <th>Kelas</th>
                                            <th>Tema/Chapter</th>
                                            <th>Materi Pokok</th>
                                            <th>Soal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($related_rpps as $index => $rpp_data): ?>
                                            <?php
                                            $rpp_question_count = 0;
                                            foreach ($question_stats as $stat) {
                                                if ($stat['source_rpp_id'] == $rpp_data['id']) {
                                                    $rpp_question_count = $stat['total'];
                                                    break;
                                                }
                                            }
                                            ?>
                                            <tr>
                                                <td><?= $index + 1 ?></td>
                                                <td><?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                                                <td><?= htmlspecialchars($rpp_data['nama_kelas']) ?></td>
                                                <td><strong><?= htmlspecialchars($rpp_data['tema_subtema']) ?></strong></td>
                                                <td><?= htmlspecialchars($rpp_data['materi_pokok']) ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?= $rpp_question_count ?> soal</span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Questions List -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list"></i> Daftar Soal (<?= count($questions) ?> soal)</h6>
                            <div>
                                <button type="button" class="btn btn-sm btn-warning me-2" onclick="generateAllMultiEssayAnswers()" id="bulkGenerateMultiAnswersBtn">
                                    <i class="fas fa-magic"></i> Generate Semua Jawaban Essay
                                </button>
                                <a href="multi_rpp_export.php?exam_id=<?= $exam_id ?>&format=pdf" class="btn btn-sm btn-outline-danger" target="_blank">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                                <a href="multi_rpp_export.php?exam_id=<?= $exam_id ?>&format=word" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-file-word"></i> Word
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($questions)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Belum ada soal yang tersimpan untuk ujian ini.
                                </div>
                            <?php else: ?>
                                <div class="accordion" id="questionsAccordion">
                                    <?php foreach ($questions as $index => $question): ?>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading<?= $index ?>">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                        data-bs-target="#collapse<?= $index ?>" aria-expanded="false" 
                                                        aria-controls="collapse<?= $index ?>">
                                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                        <span>
                                                            <strong>Soal <?= $index + 1 ?></strong> - <?= htmlspecialchars($question['tema_subtema']) ?>
                                                        </span>
                                                        <div>
                                                            <span class="badge bg-primary me-1"><?= ucfirst(str_replace('_', ' ', $question['question_type'])) ?></span>
                                                            <span class="badge bg-secondary me-1"><?= ucfirst(str_replace('_', ' ', $question['difficulty_level'])) ?></span>
                                                            <span class="badge bg-info me-1"><?= $question['cognitive_level'] ?></span>
                                                            <?php if ($question['question_type'] === 'essay' && isset($essay_answers[$question['id']])): ?>
                                                                <span class="badge bg-success" title="Memiliki jawaban yang diharapkan">
                                                                    <i class="fas fa-check"></i> Jawaban Tersedia
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id="collapse<?= $index ?>" class="accordion-collapse collapse" 
                                                 aria-labelledby="heading<?= $index ?>" data-bs-parent="#questionsAccordion">
                                                <div class="accordion-body">
                                                    <div class="mb-3">
                                                        <strong>Pertanyaan:</strong>
                                                        <p class="mt-2"><?= nl2br(htmlspecialchars($question['question_text'])) ?></p>
                                                    </div>
                                                    
                                                    <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                                                        <?php $options = json_decode($question['options'], true); ?>
                                                        <div class="mb-3">
                                                            <strong>Pilihan Jawaban:</strong>
                                                            <div class="mt-2">
                                                                <?php foreach ($options as $option): ?>
                                                                    <div class="mb-1">
                                                                        <?php if ($option === $question['correct_answer'] || 
                                                                                  (is_string($option) && substr($option, 0, 1) === $question['correct_answer'])): ?>
                                                                            <span class="text-success fw-bold"><?= htmlspecialchars($option) ?> ✓</span>
                                                                        <?php else: ?>
                                                                            <?= htmlspecialchars($option) ?>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if ($question['question_type'] === 'essay'): ?>
                                                        <div class="mb-3">
                                                            <?php $has_answer = isset($essay_answers[$question['id']]); ?>
                                                            <button type="button" class="btn btn-<?= $has_answer ? 'success' : 'warning' ?> btn-sm"
                                                                    onclick="<?= $has_answer ? 'viewMultiEssayAnswer' : 'generateMultiEssayAnswer' ?>(<?= $question['id'] ?>)"
                                                                    title="<?= $has_answer ? 'Lihat Jawaban' : 'Generate Jawaban' ?>">
                                                                <i class="fas fa-<?= $has_answer ? 'eye' : 'magic' ?>"></i>
                                                                <?= $has_answer ? 'Lihat Jawaban' : 'Generate Jawaban' ?>
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <strong>Source RPP:</strong> <?= htmlspecialchars($question['tema_subtema']) ?><br>
                                                                <strong>Materi:</strong> <?= htmlspecialchars($question['materi_pokok']) ?>
                                                            </small>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <strong>Mata Pelajaran:</strong> <?= htmlspecialchars($question['nama_mapel']) ?><br>
                                                                <strong>Kategori:</strong> <?= htmlspecialchars($question['category'] ?? 'Generated') ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="fas fa-chart-pie"></i> Statistik Soal</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $total_questions = count($questions);
                            $mc_count = 0;
                            $essay_count = 0;
                            $difficulty_stats = ['regular' => 0, 'hots_easy' => 0, 'hots_medium' => 0, 'hots_hard' => 0];
                            $cognitive_stats = ['C1' => 0, 'C2' => 0, 'C3' => 0, 'C4' => 0, 'C5' => 0, 'C6' => 0];
                            
                            foreach ($questions as $question) {
                                if ($question['question_type'] === 'multiple_choice') {
                                    $mc_count++;
                                } else {
                                    $essay_count++;
                                }
                                
                                if (isset($difficulty_stats[$question['difficulty_level']])) {
                                    $difficulty_stats[$question['difficulty_level']]++;
                                }
                                
                                if (isset($cognitive_stats[$question['cognitive_level']])) {
                                    $cognitive_stats[$question['cognitive_level']]++;
                                }
                            }
                            ?>
                            
                            <h6>Jenis Soal:</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Pilihan Ganda:</span>
                                    <span><strong><?= $mc_count ?></strong></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Essay:</span>
                                    <span><strong><?= $essay_count ?></strong></span>
                                </div>
                            </div>
                            
                            <h6>Tingkat Kesulitan:</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Regular:</span>
                                    <span><?= $difficulty_stats['regular'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Mudah:</span>
                                    <span><?= $difficulty_stats['hots_easy'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Sedang:</span>
                                    <span><?= $difficulty_stats['hots_medium'] ?></span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>HOTS Tinggi:</span>
                                    <span><?= $difficulty_stats['hots_hard'] ?></span>
                                </div>
                            </div>
                            
                            <h6>Level Kognitif:</h6>
                            <div class="small">
                                <?php foreach ($cognitive_stats as $level => $count): ?>
                                    <?php if ($count > 0): ?>
                                        <div class="d-flex justify-content-between">
                                            <span><?= $level ?>:</span>
                                            <span><?= $count ?></span>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Distribution by RPP -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="fas fa-balance-scale"></i> Distribusi per RPP</h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($question_stats as $stat): ?>
                                <div class="mb-3">
                                    <h6 class="mb-1"><?= htmlspecialchars($stat['tema_subtema']) ?></h6>
                                    <div class="small text-muted mb-2"><?= htmlspecialchars($stat['materi_pokok']) ?></div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total Soal:</span>
                                        <span><strong><?= $stat['total'] ?></strong></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Pilihan Ganda:</span>
                                        <span><?= $stat['multiple_choice'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Essay:</span>
                                        <span><?= $stat['essay'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Regular:</span>
                                        <span><?= $stat['regular'] ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>HOTS:</span>
                                        <span><?= $stat['hots_easy'] + $stat['hots_medium'] + $stat['hots_hard'] ?></span>
                                    </div>
                                </div>
                                <hr>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Essay Answer Modal -->
<div class="modal fade" id="essayAnswerModal" tabindex="-1" aria-labelledby="essayAnswerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="essayAnswerModalLabel">Jawaban Essay</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="essayAnswerContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="editAnswerBtn" style="display: none;">Edit Jawaban</button>
                <button type="button" class="btn btn-success" id="saveAnswerBtn" style="display: none;">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.card-title {
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0c63e4;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Essay Answer Display Formatting */
.essay-answer-display {
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.essay-answer-display p {
    margin-bottom: 1rem;
}

.essay-answer-display ul, .essay-answer-display ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.essay-answer-display li {
    margin-bottom: 0.5rem;
}

.essay-answer-display strong {
    font-weight: 600;
    color: #2c3e50;
}

.essay-answer-display em {
    font-style: italic;
    color: #34495e;
}

/* Modal content formatting */
.modal-body .form-control {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

.modal-body .form-control[readonly] {
    background-color: #f8f9fa;
}
</style>

<script>
// Multi-RPP Essay Answer Functions
function generateMultiEssayAnswer(questionId) {
    const btn = document.querySelector(`button[onclick="generateMultiEssayAnswer(${questionId})"]`);
    const originalText = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    fetch('generate_essay_answer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `question_id=${questionId}&question_type=multi_rpp_question`
    })
    .then(response => {
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response bukan JSON. Kemungkinan ada error PHP.');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            if (data.already_exists) {
                showEssayAnswer(data.answer);
            } else {
                showEssayAnswer(data.answer);
                // Update button to show answer is available
                btn.className = 'btn btn-success btn-sm';
                btn.setAttribute('onclick', `viewMultiEssayAnswer(${questionId})`);
                btn.setAttribute('title', 'Lihat Jawaban');
                btn.innerHTML = '<i class="fas fa-eye"></i> Lihat Jawaban';
                btn.disabled = false; // Ensure button is clickable

                // Add badge to question header
                const accordionHeader = btn.closest('.accordion-item').querySelector('.accordion-button');
                const badgeContainer = accordionHeader.querySelector('div > div');
                if (!badgeContainer.querySelector('.badge.bg-success')) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-success';
                    badge.title = 'Memiliki jawaban yang diharapkan';
                    badge.innerHTML = '<i class="fas fa-check"></i> Jawaban Tersedia';
                    badgeContainer.appendChild(badge);
                }

                // Update bulk generation button state
                updateBulkGenerationButtonState();
            }

            // Show success message
            showAlert('success', data.message);
        } else {
            showAlert('danger', 'Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Terjadi kesalahan saat generate jawaban');
    })
    .finally(() => {
        // Restore button if generation failed
        if (!btn.classList.contains('btn-success')) {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    });
}

function updateBulkGenerationButtonState() {
    // Check if all essay questions have answers
    const essayQuestions = document.querySelectorAll('[id^="question-"]:has(.btn[onclick*="generateMultiEssayAnswer"])');
    const questionsWithAnswers = document.querySelectorAll('[id^="question-"]:has(.btn[onclick*="viewMultiEssayAnswer"])');

    const bulkBtn = document.getElementById('bulkGenerateMultiAnswersBtn');
    if (bulkBtn) {
        if (essayQuestions.length === questionsWithAnswers.length && essayQuestions.length > 0) {
            // All questions have answers
            bulkBtn.innerHTML = '<i class="fas fa-check"></i> Semua Jawaban Sudah Ada';
            bulkBtn.disabled = true;
            bulkBtn.className = 'btn btn-secondary';
        } else {
            // Some questions still need answers
            const remaining = essayQuestions.length - questionsWithAnswers.length;
            bulkBtn.innerHTML = `<i class="fas fa-magic"></i> Generate Semua Jawaban Essay (${remaining} soal)`;
            bulkBtn.disabled = false;
            bulkBtn.className = 'btn btn-primary';
        }
    }
}

function updateAllEssayButtonStates() {
    // Get all essay questions and check if they have answers
    const essayButtons = document.querySelectorAll('.btn[onclick*="generateMultiEssayAnswer"]');

    essayButtons.forEach(btn => {
        const onclickAttr = btn.getAttribute('onclick');
        const questionIdMatch = onclickAttr.match(/generateMultiEssayAnswer\((\d+)\)/);

        if (questionIdMatch) {
            const questionId = questionIdMatch[1];

            // Check if this question has an answer
            fetch(`get_essay_answer.php?question_id=${questionId}&question_type=multi_rpp_question`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.has_answer) {
                    // Update button to show answer is available
                    btn.className = 'btn btn-success btn-sm';
                    btn.setAttribute('onclick', `viewMultiEssayAnswer(${questionId})`);
                    btn.setAttribute('title', 'Lihat Jawaban');
                    btn.innerHTML = '<i class="fas fa-eye"></i> Lihat Jawaban';
                    btn.disabled = false;

                    // Add badge to question header
                    const accordionHeader = btn.closest('.accordion-item').querySelector('.accordion-button');
                    const badgeContainer = accordionHeader.querySelector('div > div');
                    if (!badgeContainer.querySelector('.badge.bg-success')) {
                        const badge = document.createElement('span');
                        badge.className = 'badge bg-success';
                        badge.title = 'Memiliki jawaban yang diharapkan';
                        badge.innerHTML = '<i class="fas fa-check"></i> Jawaban Tersedia';
                        badgeContainer.appendChild(badge);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking answer status:', error);
            });
        }
    });

    // Update bulk generation button state
    setTimeout(() => {
        updateBulkGenerationButtonState();
    }, 1000); // Delay to allow individual checks to complete
}

function viewMultiEssayAnswer(questionId) {
    fetch(`get_essay_answer.php?question_id=${questionId}&question_type=multi_rpp_question`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.has_answer) {
            showEssayAnswer(data.answer);
        } else {
            showAlert('warning', 'Jawaban tidak ditemukan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Terjadi kesalahan saat mengambil jawaban');
    });
}

function generateAllMultiEssayAnswers() {
    const btn = document.getElementById('bulkGenerateMultiAnswersBtn');
    const originalText = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    fetch('generate_bulk_essay_answers.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `source_type=multi_rpp&source_id=<?= $exam_id ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            if (data.generated_count > 0) {
                // Update individual button states without full page reload
                updateAllEssayButtonStates();

                // Also refresh page after a delay to ensure everything is synced
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                // Update bulk button state even if no new answers were generated
                updateBulkGenerationButtonState();
            }
        } else {
            showAlert('danger', 'Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Terjadi kesalahan saat generate jawaban');
    })
    .finally(() => {
        // Restore button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function showEssayAnswer(answer) {
    const modal = document.getElementById('essayAnswerModal');
    const content = document.getElementById('essayAnswerContent');
    const editBtn = document.getElementById('editAnswerBtn');
    const saveBtn = document.getElementById('saveAnswerBtn');

    // Parse JSON fields
    let scoringRubric = {};
    let generationMetadata = {};

    try {
        scoringRubric = typeof answer.scoring_rubric === 'string' ?
            JSON.parse(answer.scoring_rubric) : answer.scoring_rubric || {};
        generationMetadata = typeof answer.generation_metadata === 'string' ?
            JSON.parse(answer.generation_metadata) : answer.generation_metadata || {};
    } catch (e) {
        console.error('Error parsing JSON fields:', e);
    }

    // Format text with proper line breaks and structure
    const formatText = (text) => {
        if (!text) return 'Tidak ada data';
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^\s*[-•]\s*/gm, '• ')
            .replace(/^\s*\d+\.\s*/gm, (match) => `<strong>${match}</strong>`);
    };

    content.innerHTML = `
        <div class="mb-3">
            <label class="form-label"><strong>Jawaban yang Diharapkan:</strong></label>
            <div class="form-control essay-answer-display" style="min-height: 100px;" id="expectedAnswerDisplay">${formatText(answer.expected_answer)}</div>
            <textarea class="form-control" style="min-height: 100px; display: none;" id="expectedAnswerEdit">${answer.expected_answer}</textarea>
        </div>

        <div class="mb-3">
            <label class="form-label"><strong>Poin-poin Kunci:</strong></label>
            <div class="form-control essay-answer-display" style="min-height: 80px;" id="answerPointsDisplay">${formatText(answer.answer_points || 'Tidak ada poin khusus')}</div>
            <textarea class="form-control" style="min-height: 80px; display: none;" id="answerPointsEdit">${answer.answer_points || ''}</textarea>
        </div>

        <div class="mb-3">
            <label class="form-label"><strong>Rubrik Penilaian:</strong></label>
            <div id="scoringRubricDisplay">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-success mb-2">
                            <div class="card-body py-2">
                                <strong class="text-success">Sangat Baik (90-100):</strong><br>
                                <small>${scoringRubric.excellent || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                        <div class="card border-info mb-2">
                            <div class="card-body py-2">
                                <strong class="text-info">Baik (80-89):</strong><br>
                                <small>${scoringRubric.good || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-warning mb-2">
                            <div class="card-body py-2">
                                <strong class="text-warning">Cukup (70-79):</strong><br>
                                <small>${scoringRubric.fair || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                        <div class="card border-danger mb-2">
                            <div class="card-body py-2">
                                <strong class="text-danger">Kurang (60-69):</strong><br>
                                <small>${scoringRubric.poor || 'Tidak ada kriteria'}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Dibuat: ${new Date(answer.created_at).toLocaleString('id-ID')}
                ${generationMetadata.confidence_score ? ` | Confidence: ${Math.round(generationMetadata.confidence_score * 100)}%` : ''}
            </small>
        </div>
    `;

    // Store answer data for editing
    modal.setAttribute('data-answer-id', answer.id);

    // Show edit button
    editBtn.style.display = 'inline-block';
    saveBtn.style.display = 'none';

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Edit answer functionality
document.addEventListener('DOMContentLoaded', function() {
    const editBtn = document.getElementById('editAnswerBtn');
    const saveBtn = document.getElementById('saveAnswerBtn');

    if (editBtn) {
        editBtn.addEventListener('click', function() {
            // Switch to edit mode
            document.getElementById('expectedAnswerDisplay').style.display = 'none';
            document.getElementById('expectedAnswerEdit').style.display = 'block';
            document.getElementById('answerPointsDisplay').style.display = 'none';
            document.getElementById('answerPointsEdit').style.display = 'block';

            editBtn.style.display = 'none';
            saveBtn.style.display = 'inline-block';
        });
    }

    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            const modal = document.getElementById('essayAnswerModal');
            const answerId = modal.getAttribute('data-answer-id');
            const expectedAnswer = document.getElementById('expectedAnswerEdit').value;
            const answerPoints = document.getElementById('answerPointsEdit').value;

            if (!expectedAnswer.trim()) {
                showAlert('warning', 'Jawaban yang diharapkan tidak boleh kosong');
                return;
            }

            // Show loading
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
            saveBtn.disabled = true;

            fetch('update_essay_answer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `answer_id=${answerId}&expected_answer=${encodeURIComponent(expectedAnswer)}&answer_points=${encodeURIComponent(answerPoints)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);

                    // Update display with proper formatting
                    const formatText = (text) => {
                        if (!text) return 'Tidak ada data';
                        return text
                            .replace(/\n/g, '<br>')
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em>$1</em>')
                            .replace(/^\s*[-•]\s*/gm, '• ')
                            .replace(/^\s*\d+\.\s*/gm, (match) => `<strong>${match}</strong>`);
                    };

                    document.getElementById('expectedAnswerDisplay').innerHTML = formatText(expectedAnswer);
                    document.getElementById('answerPointsDisplay').innerHTML = formatText(answerPoints || 'Tidak ada poin khusus');

                    // Switch back to view mode
                    document.getElementById('expectedAnswerDisplay').style.display = 'block';
                    document.getElementById('expectedAnswerEdit').style.display = 'none';
                    document.getElementById('answerPointsDisplay').style.display = 'block';
                    document.getElementById('answerPointsEdit').style.display = 'none';

                    editBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'none';
                } else {
                    showAlert('danger', 'Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', 'Terjadi kesalahan saat menyimpan jawaban');
            })
            .finally(() => {
                // Restore button
                saveBtn.innerHTML = 'Simpan Perubahan';
                saveBtn.disabled = false;
            });
        });
    }
});

// Helper function to show alerts
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert at the top of card-body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }
    }, 5000);
}

// Initialize bulk generation button state on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkGenerationButtonState();
});
</script>

<?php require_once '../template/footer.php'; ?>
