<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/ProfilSekolah.php';

$profilSekolah = new ProfilSekolah();
$profilSekolah->get();

// Handle success message
$success_msg = isset($_GET['success']) ? "Data profil sekolah berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan saat menyimpan data" : "";
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Profil Sekolah</h5>
                <div>
                    <a href="edit.php" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Profil Sekolah
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php
                // Check if the required columns exist
                try {
                    $database = new Database();
                    $conn = $database->getConnection();
                    $stmt = $conn->prepare("SHOW COLUMNS FROM profil_sekolah LIKE 'kode_provinsi'");
                    $stmt->execute();
                    $columnExists = $stmt->fetch();

                    if (!$columnExists) {
                        echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <strong>Perhatian!</strong> Struktur tabel profil_sekolah perlu diperbarui untuk menggunakan API Wilayah Indonesia.
                            <a href="update_table.php" class="btn btn-sm btn-warning ms-2">Update Tabel Sekarang</a>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>';
                    }
                } catch (Exception $e) {
                    // If table doesn't exist yet
                    echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <strong>Perhatian!</strong> Tabel profil_sekolah belum dibuat.
                        <a href="initialize.php" class="btn btn-sm btn-warning ms-2">Buat Tabel Sekarang</a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>';
                }
                ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Informasi Umum Sekolah</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="40%">Nama Sekolah</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->nama_sekolah ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>NPSN</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->npsn ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Sekolah</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->status_sekolah ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jenjang Pendidikan</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->jenjang_pendidikan ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Alamat Lengkap</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="40%">Jalan</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->alamat_jalan ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Kelurahan/Desa</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->desa_kelurahan ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Kecamatan</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->kecamatan ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Kabupaten/Kota</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->kabupaten_kota ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Provinsi</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->provinsi ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Kode Pos</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->kode_pos ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Kontak</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="40%">Nomor Telepon</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->no_telepon ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email Sekolah</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->email ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Website Resmi</th>
                                        <td>
                                            <?php if (!empty($profilSekolah->website)): ?>
                                                <a href="<?php echo htmlspecialchars($profilSekolah->website); ?>" target="_blank">
                                                    <?php echo htmlspecialchars($profilSekolah->website); ?>
                                                </a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Identitas Kepemimpinan</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="40%">Nama Kepala Sekolah</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->nama_kepala_sekolah ?? '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>NIP Kepala Sekolah</th>
                                        <td><?php echo htmlspecialchars($profilSekolah->nip_kepala_sekolah ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <?php
                        // Cek apakah logo mengandung path
                        if (!empty($profilSekolah->logo) && strpos($profilSekolah->logo, 'assets/img/') === 0) {
                            // Jika logo mengandung path, ambil hanya nama filenya
                            $logo_filename = basename($profilSekolah->logo);
                        } else {
                            // Jika tidak, gunakan nilai logo langsung
                            $logo_filename = $profilSekolah->logo;
                        }

                        // Cek keberadaan logo
                        $logo_path = '../assets/img/' . $logo_filename;
                        $logo_exists = !empty($logo_filename) && file_exists($logo_path);
                        ?>

                        <?php if ($logo_exists): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Logo Sekolah</h5>
                            </div>
                            <div class="card-body text-center">
                                <img src="../assets/img/<?php echo htmlspecialchars($logo_filename); ?>" alt="Logo Sekolah" class="img-fluid" style="max-height: 200px;">
                            </div>
                        </div>
                        <?php elseif (!empty($profilSekolah->logo)): ?>
                        <!-- Tampilkan pesan jika logo ada di database tapi file tidak ditemukan -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Logo Sekolah</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <p><strong>Logo tidak ditemukan</strong></p>
                                    <p>File logo tidak ditemukan di server. Silakan upload logo baru di halaman edit.</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if (!empty($profilSekolah->visi) || !empty($profilSekolah->misi)): ?>
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Visi & Misi</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($profilSekolah->visi)): ?>
                                <h6>Visi</h6>
                                <div class="mb-4">
                                    <?php echo $profilSekolah->visi; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($profilSekolah->misi)): ?>
                                <h6>Misi</h6>
                                <div>
                                    <?php echo $profilSekolah->misi; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
