<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/EssayAnswer.php';
require_once '../models/RppQuestion.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Check if this is a form submission (from edit page) or AJAX request
$is_form_submission = isset($_POST['rubric_excellent']) || isset($_POST['rubric_good']) || isset($_POST['rubric_fair']) || isset($_POST['rubric_poor']);

// Set content type based on request type
if (!$is_form_submission) {
    header('Content-Type: application/json');
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    if ($is_form_submission) {
        $_SESSION['error'] = "Method not allowed";
        header("Location: essay_answers_management.php");
        exit();
    } else {
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit();
    }
}

// Get guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    if ($is_form_submission) {
        $_SESSION['error'] = "Data guru tidak ditemukan";
        header("Location: essay_answers_management.php");
        exit();
    } else {
        echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
        exit();
    }
}

try {
    // Validate input
    if (!isset($_POST['answer_id'])) {
        throw new Exception("Answer ID tidak ditemukan");
    }

    $answer_id = $_POST['answer_id'];
    $expected_answer = $_POST['expected_answer'] ?? '';
    $answer_points = $_POST['answer_points'] ?? '';

    if (empty($expected_answer)) {
        throw new Exception("Jawaban yang diharapkan tidak boleh kosong");
    }

    // Handle scoring rubric for form submissions
    $scoring_rubric = [];
    if ($is_form_submission) {
        $scoring_rubric = [
            'excellent' => $_POST['rubric_excellent'] ?? '',
            'good' => $_POST['rubric_good'] ?? '',
            'fair' => $_POST['rubric_fair'] ?? '',
            'poor' => $_POST['rubric_poor'] ?? ''
        ];
    } else {
        // For AJAX requests, use existing rubric or default
        $scoring_rubric = json_decode($_POST['scoring_rubric'] ?? '{}', true) ?: [];
    }

    // Get existing answer
    $essayAnswer = new EssayAnswer();
    $existing_answer = $essayAnswer->getOne($answer_id);

    if (!$existing_answer) {
        throw new Exception("Jawaban tidak ditemukan");
    }

    // Verify ownership
    $question_id = $existing_answer['question_id'];
    $question_type = $existing_answer['question_type'];

    if ($question_type === 'rpp_question') {
        $rppQuestion = new RppQuestion();
        $question_data = $rppQuestion->getOne($question_id);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        // Verify ownership through RPP
        $rpp = new Rpp();
        $rpp_data = $rpp->getOne($question_data['rpp_id']);
        if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }
    } else {
        // Handle multi-RPP questions
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT meq.*, me.guru_id 
                 FROM multi_rpp_exam_questions meq
                 JOIN multi_rpp_exams me ON meq.multi_exam_id = me.id
                 WHERE meq.id = :question_id";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":question_id", $question_id);
        $stmt->execute();
        
        $question_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$question_data) {
            throw new Exception("Soal tidak ditemukan");
        }

        if ($question_data['guru_id'] != $guru_id) {
            throw new Exception("Anda tidak memiliki akses ke soal ini");
        }
    }

    // Handle scoring rubric
    $scoring_rubric = $existing_answer['scoring_rubric'];
    if (isset($_POST['scoring_rubric'])) {
        $rubric_data = $_POST['scoring_rubric'];
        if (is_array($rubric_data)) {
            $scoring_rubric = $rubric_data;
        } else {
            $scoring_rubric = json_decode($rubric_data, true) ?: $scoring_rubric;
        }
    }

    // Update generation metadata to indicate manual edit
    $generation_metadata = json_decode($existing_answer['generation_metadata'], true) ?: [];
    $generation_metadata['last_edited'] = date('Y-m-d H:i:s');
    $generation_metadata['edited_by'] = 'manual';

    // Update the answer
    $essayAnswer->id = $answer_id;
    $essayAnswer->expected_answer = $expected_answer;
    $essayAnswer->answer_points = $answer_points;
    $essayAnswer->scoring_rubric = $scoring_rubric;
    $essayAnswer->generation_metadata = $generation_metadata;

    if ($essayAnswer->update()) {
        if ($is_form_submission) {
            $_SESSION['success'] = "Jawaban berhasil diperbarui";
            header("Location: essay_answers_management.php");
            exit();
        } else {
            // Get updated answer for AJAX response
            $updated_answer = $essayAnswer->getOne($answer_id);

            // Parse JSON fields for response
            if (!empty($updated_answer['scoring_rubric'])) {
                $updated_answer['scoring_rubric'] = json_decode($updated_answer['scoring_rubric'], true);
            }

            if (!empty($updated_answer['generation_metadata'])) {
                $updated_answer['generation_metadata'] = json_decode($updated_answer['generation_metadata'], true);
            }

            echo json_encode([
                'success' => true,
                'message' => 'Jawaban berhasil diperbarui',
                'answer' => $updated_answer
            ]);
        }
    } else {
        if ($is_form_submission) {
            $_SESSION['error'] = "Gagal memperbarui jawaban";
            header("Location: edit_essay_answer.php?id=" . $answer_id);
            exit();
        } else {
            throw new Exception("Gagal memperbarui jawaban");
        }
    }

} catch (Exception $e) {
    if ($is_form_submission) {
        $_SESSION['error'] = $e->getMessage();
        if (isset($answer_id)) {
            header("Location: edit_essay_answer.php?id=" . $answer_id);
        } else {
            header("Location: essay_answers_management.php");
        }
        exit();
    } else {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}
?>
