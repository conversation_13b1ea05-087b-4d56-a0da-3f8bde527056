<?php
require_once '../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';

header('Content-Type: application/json');

// Get guru_id from session
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    echo json_encode([]);
    exit();
}

if (!isset($_GET['kelas_id']) || empty($_GET['kelas_id'])) {
    echo json_encode([]);
    exit();
}

$kelas_id = $_GET['kelas_id'];

try {
    $mapel = new MataPelajaran();

    // Get subjects that the teacher teaches in the selected class
    $result = $mapel->getByKelasAndGuru($kelas_id, $guru_id);

    echo json_encode($result);
    
} catch (Exception $e) {
    error_log("Error in get_mapel_by_kelas.php: " . $e->getMessage());
    echo json_encode([]);
}
?>
