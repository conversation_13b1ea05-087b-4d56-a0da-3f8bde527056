<?php
require_once __DIR__ . '/../config/database.php';

class TugasTambahan {
    private $conn;
    private $table_name = "tugas_tambahan";
    private $siswa_table = "tugas_tambahan_siswa";

    public $id;
    public $mapel_id;
    public $guru_id;
    public $judul;
    public $deskripsi;
    public $tanggal;
    public $semester;
    public $tahun_ajaran;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Create a new additional assignment
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                (mapel_id, guru_id, judul, deskripsi, tanggal, semester, tahun_ajaran)
                VALUES
                (:mapel_id, :guru_id, :judul, :deskripsi, :tanggal, :semester, :tahun_ajaran)";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind values
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
        $this->guru_id = htmlspecialchars(strip_tags($this->guru_id));
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        $this->semester = htmlspecialchars(strip_tags($this->semester));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));

        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    /**
     * Update an existing additional assignment
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET judul = :judul,
                    deskripsi = :deskripsi,
                    tanggal = :tanggal
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize and bind values
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    /**
     * Delete an additional assignment
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    /**
     * Get a single additional assignment by ID
     */
    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->mapel_id = $row['mapel_id'];
            $this->guru_id = $row['guru_id'];
            $this->judul = $row['judul'];
            $this->deskripsi = $row['deskripsi'];
            $this->tanggal = $row['tanggal'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    /**
     * Get all additional assignments for a specific subject, semester, and academic year
     */
    public function getTugasTambahanMapel($mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT tt.*, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " tt
                LEFT JOIN mata_pelajaran m ON tt.mapel_id = m.id
                LEFT JOIN guru g ON tt.guru_id = g.id
                WHERE tt.mapel_id = :mapel_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                ORDER BY tt.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get all additional assignments for a specific teacher, semester, and academic year
     */
    public function getTugasTambahanGuru($guru_id, $semester, $tahun_ajaran) {
        $query = "SELECT tt.*, m.nama_mapel
                FROM " . $this->table_name . " tt
                LEFT JOIN mata_pelajaran m ON tt.mapel_id = m.id
                WHERE tt.guru_id = :guru_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                ORDER BY tt.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get all additional assignments for a specific teacher, subject, semester, and academic year
     */
    public function getTugasTambahanGuruMapel($guru_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT tt.*, m.nama_mapel
                FROM " . $this->table_name . " tt
                LEFT JOIN mata_pelajaran m ON tt.mapel_id = m.id
                WHERE tt.guru_id = :guru_id
                AND tt.mapel_id = :mapel_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                ORDER BY tt.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Assign an additional assignment to a student
     */
    public function assignToStudent($tugas_tambahan_id, $siswa_id) {
        $query = "INSERT INTO " . $this->siswa_table . "
                (tugas_tambahan_id, siswa_id)
                VALUES (:tugas_tambahan_id, :siswa_id)
                ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        return $stmt->execute();
    }

    /**
     * Assign an additional assignment to multiple students
     */
    public function assignToMultipleStudents($tugas_tambahan_id, $siswa_ids) {
        $success = true;
        foreach ($siswa_ids as $siswa_id) {
            if (!$this->assignToStudent($tugas_tambahan_id, $siswa_id)) {
                $success = false;
            }
        }
        return $success;
    }

    /**
     * Assign an additional assignment to all students in a class
     */
    public function assignToClass($tugas_tambahan_id, $kelas_id) {
        // Get the academic period from the current assignment
        $tahun_ajaran = $this->tahun_ajaran;
        $semester = $this->semester;

        // If academic period is not set in the assignment, try to get it from active period
        if (!$tahun_ajaran || !$semester) {
            require_once __DIR__ . '/PeriodeAktif.php';
            $periode = new PeriodeAktif();
            if ($periode->getActive()) {
                $tahun_ajaran = $periode->tahun_ajaran;
                $semester = $periode->semester;
            }
        }

        // Use period-based student selection if academic period is available
        if ($tahun_ajaran && $semester) {
            $query = "INSERT INTO " . $this->siswa_table . " (tugas_tambahan_id, siswa_id)
                    SELECT :tugas_tambahan_id, s.id
                    FROM siswa s
                    JOIN siswa_periode sp ON s.id = sp.siswa_id
                    WHERE sp.kelas_id = :kelas_id
                    AND sp.tahun_ajaran = :tahun_ajaran
                    AND sp.semester = :semester
                    AND sp.is_current = 1
                    AND sp.status = 'aktif'
                    ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
            $stmt->bindParam(":kelas_id", $kelas_id);
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
            $stmt->bindParam(":semester", $semester);
        } else {
            // Fallback to original method for backward compatibility
            $query = "INSERT INTO " . $this->siswa_table . " (tugas_tambahan_id, siswa_id)
                    SELECT :tugas_tambahan_id, id FROM siswa WHERE kelas_id = :kelas_id
                    ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        return $stmt->execute();
    }

    /**
     * Get students with below-standard grades for a specific subject
     */
    public function getSiswaBelowKKM($mapel_id, $kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, n.nilai_akhir, m.kkm
                FROM siswa s
                JOIN siswa_periode sp ON s.id = sp.siswa_id
                JOIN nilai n ON s.id = n.siswa_id
                JOIN mata_pelajaran m ON n.mapel_id = m.id
                WHERE sp.kelas_id = :kelas_id
                AND sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.is_current = 1
                AND sp.status = 'aktif'
                AND n.mapel_id = :mapel_id
                AND n.semester = :semester
                AND n.tahun_ajaran = :tahun_ajaran
                AND n.nilai_akhir < m.kkm
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get students with low attendance for a specific subject
     */
    public function getSiswaLowAttendance($mapel_id, $kelas_id, $semester, $tahun_ajaran, $min_percentage) {
        // Convert semester and tahun_ajaran to date range
        $tahun = explode('/', $tahun_ajaran)[0];
        $start_date = $semester == '1' ?
            $tahun . '-07-01' :  // First semester starts in July
            ($tahun + 1) . '-01-01';  // Second semester starts in January

        $end_date = $semester == '1' ?
            $tahun . '-12-31' :  // First semester ends in December
            ($tahun + 1) . '-06-30';  // Second semester ends in June

        $query = "SELECT
                    s.id,
                    s.nis,
                    s.nama_siswa,
                    COUNT(DISTINCT a.id) as total_pertemuan,
                    SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) as hadir,
                    SUM(CASE WHEN da.status = 'Sakit' THEN 1 ELSE 0 END) as sakit,
                    SUM(CASE WHEN da.status = 'Izin' THEN 1 ELSE 0 END) as izin,
                    SUM(CASE WHEN da.status = 'Alpha' THEN 1 ELSE 0 END) as alpha,
                    ROUND(
                        (SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) * 100) /
                        COUNT(DISTINCT a.id), 2
                    ) as attendance_percentage
                FROM siswa s
                JOIN siswa_periode sp ON s.id = sp.siswa_id
                JOIN kelas k ON sp.kelas_id = k.id
                JOIN detail_absensi da ON s.id = da.siswa_id
                JOIN absensi a ON da.absensi_id = a.id
                WHERE sp.kelas_id = :kelas_id
                AND sp.tahun_ajaran = :tahun_ajaran
                AND sp.semester = :semester
                AND sp.is_current = 1
                AND sp.status = 'aktif'
                AND a.mapel_id = :mapel_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran
                AND a.tanggal BETWEEN :start_date AND :end_date
                GROUP BY s.id, s.nis, s.nama_siswa
                HAVING
                    COUNT(DISTINCT a.id) > 0 AND
                    (SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) * 100) /
                    COUNT(DISTINCT a.id) < :min_percentage
                ORDER BY attendance_percentage ASC, s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);
        $stmt->bindParam(":min_percentage", $min_percentage, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Get all students in a class for a specific academic period
     */
    public function getAllSiswaInClass($kelas_id, $tahun_ajaran = null, $semester = null) {
        // If academic period is not specified, try to get it from the current assignment
        if ($tahun_ajaran === null || $semester === null) {
            if ($this->tahun_ajaran && $this->semester) {
                $tahun_ajaran = $this->tahun_ajaran;
                $semester = $this->semester;
            } else {
                // Fallback to active period
                require_once __DIR__ . '/PeriodeAktif.php';
                $periode = new PeriodeAktif();
                if ($periode->getActive()) {
                    $tahun_ajaran = $periode->tahun_ajaran;
                    $semester = $periode->semester;
                }
            }
        }

        // Use period-based student retrieval if academic period is available
        if ($tahun_ajaran && $semester) {
            $query = "SELECT s.id, s.nis, s.nama_siswa
                    FROM siswa s
                    JOIN siswa_periode sp ON s.id = sp.siswa_id
                    WHERE sp.kelas_id = :kelas_id
                    AND sp.tahun_ajaran = :tahun_ajaran
                    AND sp.semester = :semester
                    ORDER BY s.nama_siswa ASC";
        } else {
            // Fallback to original method for backward compatibility
            $query = "SELECT s.id, s.nis, s.nama_siswa
                    FROM siswa s
                    WHERE s.kelas_id = :kelas_id
                    ORDER BY s.nama_siswa ASC";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);

        // Bind academic period parameters if they are used in the query
        if ($tahun_ajaran && $semester) {
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
            $stmt->bindParam(":semester", $semester);
        }

        $stmt->execute();
        return $stmt;
    }

    /**
     * Get students assigned to an additional assignment
     */
    public function getAssignedStudents($tugas_tambahan_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, tts.status, tts.nilai
                FROM " . $this->siswa_table . " tts
                JOIN siswa s ON tts.siswa_id = s.id
                WHERE tts.tugas_tambahan_id = :tugas_tambahan_id
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Update the status and grade of a student's additional assignment
     */
    public function updateNilai($tugas_tambahan_id, $siswa_id, $status, $nilai = null) {
        $query = "UPDATE " . $this->siswa_table . "
                SET status = :status, nilai = :nilai
                WHERE tugas_tambahan_id = :tugas_tambahan_id AND siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":nilai", $nilai);
        $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
        $stmt->bindParam(":siswa_id", $siswa_id);

        return $stmt->execute();
    }

    /**
     * Get additional assignments for a specific student
     */
    public function getTugasTambahanSiswa($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT tt.*, m.nama_mapel, tts.status, tts.nilai
                FROM " . $this->table_name . " tt
                JOIN " . $this->siswa_table . " tts ON tt.id = tts.tugas_tambahan_id
                JOIN mata_pelajaran m ON tt.mapel_id = m.id
                WHERE tts.siswa_id = :siswa_id
                AND tt.semester = :semester
                AND tt.tahun_ajaran = :tahun_ajaran
                ORDER BY tt.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    /**
     * Remove a student from an additional assignment
     */
    public function removeStudent($tugas_tambahan_id, $siswa_id) {
        $query = "DELETE FROM " . $this->siswa_table . "
                WHERE tugas_tambahan_id = :tugas_tambahan_id AND siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_tambahan_id", $tugas_tambahan_id);
        $stmt->bindParam(":siswa_id", $siswa_id);

        return $stmt->execute();
    }
}
