<?php
require_once '../config/database.php';
require_once '../models/Tugas.php';

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];
$tugas = new Tugas();
$tugas->id = $tugas_id;

// Get tugas details before deleting for redirect
if ($tugas->getOne()) {
    $mapel_id = $tugas->mapel_id;
    $semester = $tugas->semester;
    $tahun_ajaran = $tugas->tahun_ajaran;
    
    if ($tugas->delete()) {
        echo "<script>alert('Tugas berhasil dihapus!'); window.location.href='tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran';</script>";
    } else {
        echo "<script>alert('Gagal menghapus tugas!'); window.location.href='tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran';</script>";
    }
} else {
    header("Location: index.php");
}
?>
