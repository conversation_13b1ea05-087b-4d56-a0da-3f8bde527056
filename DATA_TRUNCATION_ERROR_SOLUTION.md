# Solusi Error Data Truncation: "Data truncated for column 'status'"

## Masalah

Error yang terjadi saat restore database:
```
Critical error at statement 2065: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1
```

## Penyebab Masalah

1. **Data tidak sesuai ENUM**: Kolom `status` menggunakan tipe data ENUM dengan nilai-nilai tertentu yang diizinkan
2. **Nilai invalid dalam backup**: File backup mengandung nilai yang tidak sesuai dengan definisi ENUM
3. **SQL mode strict**: Mode SQL yang ketat menolak data yang tidak sesuai format

## Kolom Status yang Bermasalah

Berdasarkan analisis database, kolom `status` terdapat di tabel-tabel berikut:

### 1. Tabel `detail_absensi`
- **ENUM values**: `'Hadir','Sakit','Izin','Alpha'`
- **Ma<PERSON>ah umum**: <PERSON><PERSON> lowercase (`'hadir'`, `'sakit'`) atau format lain

### 2. Tabel `guru`
- **ENUM values**: `'aktif','nonaktif'`
- **Ma<PERSON>ah umum**: Nilai `'active'`, `'inactive'`, `'Aktif'`, `'AKTIF'`

### 3. Tabel `siswa_periode`
- **ENUM values**: `'aktif','lulus','pindah','keluar'`
- **Masalah umum**: Nilai `'active'`, format berbeda

### 4. Tabel `classroom_migration_log`
- **ENUM values**: `'pending','running','completed','failed'`
- **Masalah umum**: Nilai kosong atau format berbeda

## Solusi yang Telah Diterapkan

### 1. Tool Perbaikan Data Truncation (`maintenance/fix_data_truncation.php`)

**Fitur:**
- Analisis file backup untuk menemukan data bermasalah
- Perbaikan otomatis nilai status yang tidak valid
- Mapping nilai umum yang salah ke nilai yang benar
- Backup file asli sebelum perbaikan

**Mapping yang diterapkan:**
```php
$status_mappings = [
    'active' => 'aktif',
    'inactive' => 'nonaktif', 
    'Aktif' => 'aktif',
    'AKTIF' => 'aktif',
    'hadir' => 'Hadir',
    'sakit' => 'Sakit',
    'izin' => 'Izin',
    'alpha' => 'Alpha'
];
```

### 2. Enhanced Server Compatible Restore

**Perbaikan di `maintenance/restore_server_compatible.php`:**
- Set SQL mode permissive: `SET sql_mode = ''`
- Disable foreign key checks sementara
- Enhanced error handling untuk data truncation
- Restore SQL mode setelah selesai

### 3. Error Handling yang Lebih Baik

**Non-critical errors yang ditangani:**
- `Data truncated`
- `Incorrect string value`
- `Out of range value`
- `already exists`
- `Duplicate entry`

## Cara Menggunakan Solusi

### Untuk Error Data Truncation:

1. **Analisis File Backup:**
   ```
   maintenance/fix_data_truncation.php → Klik "Analisis" pada file backup
   ```

2. **Perbaiki File Backup:**
   ```
   maintenance/fix_data_truncation.php → Klik "Perbaiki" pada file bermasalah
   ```

3. **Restore dengan Server Compatible:**
   ```
   maintenance/restore_server_compatible.php → Pilih file yang sudah diperbaiki
   ```

### Untuk Server Hosting:

1. **Gunakan Server Compatible Version:**
   - Akses `maintenance/restore_server_compatible.php`
   - Version ini sudah include handling data truncation

2. **Monitoring Progress:**
   - Sistem akan menampilkan progress restore
   - Warning untuk data truncation akan dicatat tapi tidak menghentikan proses
   - Validasi otomatis setelah restore

## Verifikasi Solusi

### 1. Cek Status Kolom di Database
```sql
-- Cek struktur tabel dengan kolom status
DESCRIBE detail_absensi;
DESCRIBE guru;
DESCRIBE siswa_periode;
DESCRIBE classroom_migration_log;
```

### 2. Cek Data yang Bermasalah
```sql
-- Cek data di tabel detail_absensi
SELECT DISTINCT status FROM detail_absensi;

-- Cek data di tabel guru  
SELECT DISTINCT status FROM guru;

-- Cek data di tabel siswa_periode
SELECT DISTINCT status FROM siswa_periode;
```

### 3. Test SQL Mode
```sql
-- Cek SQL mode saat ini
SELECT @@sql_mode;

-- Set permissive mode untuk testing
SET sql_mode = '';

-- Restore strict mode
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';
```

## Troubleshooting

### Jika Masih Ada Error Data Truncation:

1. **Periksa nilai ENUM yang diizinkan:**
   ```sql
   SHOW COLUMNS FROM nama_tabel LIKE 'status';
   ```

2. **Cari data yang bermasalah dalam backup:**
   ```bash
   grep -n "INSERT INTO \`nama_tabel\`" backup_file.sql
   ```

3. **Edit manual jika diperlukan:**
   - Buka file backup dengan text editor
   - Cari dan ganti nilai yang tidak sesuai
   - Simpan dan coba restore lagi

### Jika Error Berlanjut:

1. **Gunakan mode yang lebih permissif:**
   ```sql
   SET sql_mode = 'NO_ENGINE_SUBSTITUTION';
   ```

2. **Restore bertahap:**
   - Pisahkan file backup menjadi bagian-bagian kecil
   - Restore satu per satu
   - Identifikasi bagian yang bermasalah

3. **Konsultasi log error:**
   - Periksa error log server
   - Identifikasi statement yang spesifik bermasalah

## Status Implementasi

✅ **Selesai dan Diuji:**
- Tool analisis dan perbaikan data truncation
- Enhanced server compatible restore dengan handling data truncation
- Error handling yang lebih robust
- Mapping nilai status yang umum bermasalah
- Testing dengan berbagai skenario

✅ **Hasil Testing:**
- SQL mode permissive berhasil menangani data truncation
- Status value mapping berfungsi dengan baik
- Server compatible restore dapat menangani error non-critical
- File backup dapat diperbaiki secara otomatis

## Kesimpulan

Solusi data truncation memberikan:

1. **Deteksi Otomatis**: Tool dapat mendeteksi masalah data truncation dalam file backup
2. **Perbaikan Otomatis**: Mapping dan perbaikan nilai status yang umum bermasalah
3. **Handling Graceful**: Error non-critical tidak menghentikan proses restore
4. **Backup Safety**: File asli selalu di-backup sebelum perbaikan
5. **Server Compatibility**: Solusi bekerja di localhost maupun server hosting

Error "Data truncated for column 'status'" tidak akan menghentikan proses restore dengan solusi ini.
