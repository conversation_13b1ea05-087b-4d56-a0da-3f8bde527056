<?php
session_start();

// Check if already logged in
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

require_once 'config/database.php';
require_once 'models/User.php';

$error = '';

if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user = new User();
    if($user->login($_POST['username'], $_POST['password'])) {
        $_SESSION['user_id'] = $user->id;
        $_SESSION['username'] = $user->username;
        $_SESSION['nama_lengkap'] = $user->nama_lengkap;
        $_SESSION['role'] = $user->role;

        // Handle Remember Me
        if(isset($_POST['remember']) && $_POST['remember'] == 'on') {
            $token = bin2hex(random_bytes(32)); // Generate secure token
            setcookie('remember_token', $token, time() + (86400 * 30), "/"); // 30 days

            // Store token in database (you'll need to add this field to your users table)
            $user->updateRememberToken($token);
        }

        header("Location: index.php");
        exit();
    } else {
        $error = 'Username atau password salah';
    }
}

// Check for remember me cookie
if(!isset($_SESSION['user_id']) && isset($_COOKIE['remember_token'])) {
    $user = new User();
    $userData = $user->getUserByRememberToken($_COOKIE['remember_token']);
    if($userData) {
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['username'] = $userData['username'];
        $_SESSION['nama_lengkap'] = $userData['nama_lengkap'];
        $_SESSION['role'] = $userData['role'];
        header("Location: index.php");
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - SIHADIR | Sistem Informasi Kehadiran Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --bg-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }

        body {
            min-height: 100vh;
            background: var(--bg-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            margin: auto;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .login-header .logo-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .login-header h2 {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .login-header p {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
            font-size: 1rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .card-body {
            padding: 2rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .form-control {
            border-radius: 8px;
            padding: 0.75rem 1rem;
            border: 1px solid #e3e6f0;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--secondary-color);
            padding: 5px;
            transition: all 0.2s;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .form-check {
            padding-left: 1.75rem;
        }

        .form-check-input {
            width: 1.1rem;
            height: 1.1rem;
            margin-left: -1.75rem;
            margin-top: 0.2rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--secondary-color);
            cursor: pointer;
            font-size: 0.9rem;
            user-select: none;
        }

        .btn-primary {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            background: var(--bg-gradient);
            border: none;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.25);
        }

        .alert {
            border-radius: 8px;
            font-size: 0.9rem;
            border: none;
        }

        .alert-danger {
            background-color: #fff5f5;
            color: #dc3545;
            border-left: 4px solid #dc3545;
        }

        .version-info {
            color: #ffffff;
            font-weight: 500;
            font-size: 0.9rem;
        }

        footer {
            position: fixed;
            bottom: 20px;
            width: 100%;
            left: 0;
        }

        @media (max-width: 576px) {
            .card-body {
                padding: 1.5rem;
            }

            .login-header .logo-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }
        }

        @media (max-height: 700px) {
            footer {
                position: relative;
                margin-top: 40px;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-icon">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>SIHADIR</h2>
                <p>Sistem Informasi Kehadiran Siswa</p>
            </div>

            <div class="card">
                <div class="card-body">
                    <?php if($error): ?>
                        <div class="alert alert-danger mb-4">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-4">
                            <label for="username" class="form-label">Username</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-user text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label for="password" class="form-label">Password</label>
                            <div class="password-container">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0" id="password" name="password" required>
                                    <span class="password-toggle" onclick="togglePassword()">
                                        <i class="far fa-eye" id="toggleIcon"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">Ingat Saya</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </form>

                    <div class="text-center mt-4">
                        <a href="public/" class="text-primary">
                            <i class="fas fa-external-link-alt me-1"></i> Lihat Data Siswa (Tanpa Login)
                        </a>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <span class="version-info">
                    SIHADIR - Sistem Informasi Kehadiran Siswa &copy; <?php echo date('Y'); ?> | Versi 2.19.0 <br> Oleh: Muh Ikhsan Hamid
                </span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
