<?php
include '../config/database.php';

session_start();

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
$database = new Database();
$db = $database->getConnection();

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Begin transaction
$db->beginTransaction();

try {
    // Get guru data for user deletion
    $get_guru = "SELECT nama_lengkap FROM guru WHERE id = :id";
    $stmt = $db->prepare($get_guru);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $guru = $stmt->fetch(PDO::FETCH_ASSOC);

    // Hapus data guru
    $query = "DELETE FROM guru WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    // Hapus juga data user jika ada
    if ($guru) {
        $delete_user = "DELETE FROM users WHERE username LIKE 'guru%' AND LOWER(name) = :name";
        $stmt = $db->prepare($delete_user);
        $stmt->bindParam(':name', strtolower($guru['nama_lengkap']));
        $stmt->execute();
    }

    $db->commit();
    $_SESSION['success'] = "Data guru berhasil dihapus!";
} catch (Exception $e) {
    $db->rollBack();
    $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
}

header('Location: index.php');
