<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

// Cek apakah user adalah admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    $_SESSION['error'] = "Anda tidak memiliki akses ke halaman ini";
    header("Location: index.php");
    exit();
}

$perpustakaan = new Perpustakaan();

try {
    // Ambil data guru untuk pustakawan
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT id, nip, nama_lengkap FROM guru WHERE status = 'aktif' ORDER BY nama_lengkap ASC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $guru = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ambil data pustakawan aktif
    $pustakawan_aktif = $perpustakaan->getPustakawan();
    $id_pustakawan_aktif = array_column($pustakawan_aktif, 'id_guru');

    // Ambil konfigurasi dari database
    $config = $perpustakaan->getKonfigurasi();

    // Proses form jika ada POST request
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $db->beginTransaction();
        try {
            $new_config = [
                'durasi_pinjam_default' => $_POST['durasi_pinjam_default'],
                'max_pinjam_siswa' => $_POST['max_pinjam_siswa'],
                'max_pinjam_guru' => $_POST['max_pinjam_guru'],
                'denda_per_hari' => $_POST['denda_per_hari'],
                'format_nomor_pinjam' => $_POST['format_nomor_pinjam'],
                'status_peminjaman' => $config['status_peminjaman'],
                'lokasi_rak' => $config['lokasi_rak'],
                'pengaturan_pustakawan' => [
                    'jam_buka' => $_POST['jam_buka'],
                    'jam_tutup' => $_POST['jam_tutup'],
                    'hari_kerja' => isset($_POST['hari_kerja']) ? $_POST['hari_kerja'] : ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'],
                    'hak_akses' => $config['pengaturan_pustakawan']['hak_akses']
                ]
            ];

            // Simpan konfigurasi
            $perpustakaan->simpanKonfigurasi($new_config);

            // Update pustakawan
            $pustakawan_baru = isset($_POST['pustakawan']) ? $_POST['pustakawan'] : [];
            
            // Nonaktifkan pustakawan yang tidak dipilih
            foreach ($id_pustakawan_aktif as $id_guru) {
                if (!in_array($id_guru, $pustakawan_baru)) {
                    $perpustakaan->hapusPustakawan($id_guru);
                }
            }

            // Tambahkan pustakawan baru
            foreach ($pustakawan_baru as $id_guru) {
                if (!in_array($id_guru, $id_pustakawan_aktif)) {
                    $perpustakaan->tambahPustakawan($id_guru);
                }
            }

            $db->commit();
            $_SESSION['success'] = "Konfigurasi berhasil disimpan";
            
            // Refresh data
            $pustakawan_aktif = $perpustakaan->getPustakawan();
            $id_pustakawan_aktif = array_column($pustakawan_aktif, 'id_guru');
            $config = $new_config;
        } catch (Exception $e) {
            $db->rollBack();
            $_SESSION['error'] = "Gagal menyimpan konfigurasi: " . $e->getMessage();
        }
    }
} catch (PDOException $e) {
    $_SESSION['error'] = "Terjadi kesalahan database: " . $e->getMessage();
    $guru = [];
    $pustakawan_aktif = [];
    $id_pustakawan_aktif = [];
    $config = $perpustakaan->getDefaultKonfigurasi();
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Konfigurasi Perpustakaan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Konfigurasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pengaturan Umum</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="durasi_pinjam_default">Durasi Peminjaman Default (hari)</label>
                                    <input type="number" class="form-control" id="durasi_pinjam_default" name="durasi_pinjam_default" value="<?= $config['durasi_pinjam_default']; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="max_pinjam_siswa">Maksimal Peminjaman Siswa</label>
                                    <input type="number" class="form-control" id="max_pinjam_siswa" name="max_pinjam_siswa" value="<?= $config['max_pinjam_siswa']; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="max_pinjam_guru">Maksimal Peminjaman Guru</label>
                                    <input type="number" class="form-control" id="max_pinjam_guru" name="max_pinjam_guru" value="<?= $config['max_pinjam_guru']; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="denda_per_hari">Denda per Hari (Rp)</label>
                                    <input type="number" class="form-control" id="denda_per_hari" name="denda_per_hari" value="<?= $config['denda_per_hari']; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="format_nomor_pinjam">Format Nomor Peminjaman</label>
                                    <input type="text" class="form-control" id="format_nomor_pinjam" name="format_nomor_pinjam" value="<?= $config['format_nomor_pinjam']; ?>" required>
                                    <small class="form-text text-muted">Gunakan {TAHUN}, {BULAN}, {NOMOR} sebagai placeholder</small>
                                </div>
                                <div class="form-group">
                                    <label for="jam_buka">Jam Buka</label>
                                    <input type="time" class="form-control" id="jam_buka" name="jam_buka" value="<?= $config['pengaturan_pustakawan']['jam_buka']; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="jam_tutup">Jam Tutup</label>
                                    <input type="time" class="form-control" id="jam_tutup" name="jam_tutup" value="<?= $config['pengaturan_pustakawan']['jam_tutup']; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label>Hari Kerja</label><br>
                                    <?php
                                    $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
                                    foreach ($hari as $h) {
                                        $checked = in_array($h, $config['pengaturan_pustakawan']['hari_kerja']) ? 'checked' : '';
                                        echo '<div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="hari_kerja[]" value="' . $h . '" ' . $checked . '>
                                                <label class="form-check-label">' . $h . '</label>
                                            </div>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Simpan Konfigurasi</button>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Daftar Pustakawan</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST" id="formPustakawan">
                        <input type="hidden" name="durasi_pinjam_default" value="<?= $config['durasi_pinjam_default']; ?>">
                        <input type="hidden" name="max_pinjam_siswa" value="<?= $config['max_pinjam_siswa']; ?>">
                        <input type="hidden" name="max_pinjam_guru" value="<?= $config['max_pinjam_guru']; ?>">
                        <input type="hidden" name="denda_per_hari" value="<?= $config['denda_per_hari']; ?>">
                        <input type="hidden" name="format_nomor_pinjam" value="<?= $config['format_nomor_pinjam']; ?>">
                        <input type="hidden" name="jam_buka" value="<?= $config['pengaturan_pustakawan']['jam_buka']; ?>">
                        <input type="hidden" name="jam_tutup" value="<?= $config['pengaturan_pustakawan']['jam_tutup']; ?>">
                        <?php foreach ($config['pengaturan_pustakawan']['hari_kerja'] as $hari): ?>
                            <input type="hidden" name="hari_kerja[]" value="<?= $hari; ?>">
                        <?php endforeach; ?>
                        
                        <div class="table-responsive">
                            <table id="tablePustakawan" class="table table-striped table-hover">
                                <thead>
                                    <tr class="bg-light">
                                        <th width="5%">Pilih</th>
                                        <th>NIP</th>
                                        <th>Nama Lengkap</th>
                                        <th>Status</th>
                                        <th>Tanggal Mulai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($guru as $g): ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                    name="pustakawan[]" 
                                                    value="<?= $g['id']; ?>"
                                                    <?= in_array($g['id'], $id_pustakawan_aktif) ? 'checked' : ''; ?>>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($g['nip']); ?></td>
                                        <td><?= htmlspecialchars($g['nama_lengkap']); ?></td>
                                        <td>
                                            <?php if (in_array($g['id'], $id_pustakawan_aktif)): ?>
                                                <span class="badge bg-success">Pustakawan Aktif</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Bukan Pustakawan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            foreach ($pustakawan_aktif as $p) {
                                                if ($p['id_guru'] == $g['id']) {
                                                    echo date('d/m/Y', strtotime($p['tanggal_mulai']));
                                                    break;
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">Simpan Pustakawan</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>

<script>
$(document).ready(function() {
    $('#tablePustakawan').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data guru"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[2, "asc"]], // Urutkan berdasarkan nama lengkap
        "columnDefs": [
            {"orderable": false, "targets": 0} // Kolom checkbox tidak bisa diurutkan
        ]
    });
});
</script>
