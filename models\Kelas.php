<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/Guru.php';
require_once __DIR__ . '/Tingkat.php';
require_once __DIR__ . '/Jurusan.php';

class Kelas {
    private $conn;
    private $table_name = "kelas";

    public $id;
    public $nama_kelas;
    public $guru_id;
    public $tahun_ajaran;
    public $semester;
    public $tingkat_id;
    public $jurusan_id;
    public $nama_wali_kelas;  // For display purposes
    public $nama_tingkat;     // For display purposes
    public $nama_jurusan;     // For display purposes

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAll() {
        // Check if semester column exists
        $check_query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'semester'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->execute();
        $has_semester = $check_stmt->rowCount() > 0;

        if ($has_semester) {
            $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                    t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                    FROM " . $this->table_name . " k
                    LEFT JOIN guru g ON k.guru_id = g.id
                    LEFT JOIN tingkat t ON k.tingkat_id = t.id
                    LEFT JOIN jurusan j ON k.jurusan_id = j.id
                    ORDER BY k.tahun_ajaran DESC, k.semester DESC, k.nama_kelas ASC";
        } else {
            $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                    t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                    FROM " . $this->table_name . " k
                    LEFT JOIN guru g ON k.guru_id = g.id
                    LEFT JOIN tingkat t ON k.tingkat_id = t.id
                    LEFT JOIN jurusan j ON k.jurusan_id = j.id
                    ORDER BY k.tahun_ajaran DESC, k.nama_kelas ASC";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllAsArray() {
        // Check if semester column exists
        $check_query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'semester'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->execute();
        $has_semester = $check_stmt->rowCount() > 0;

        if ($has_semester) {
            $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                    t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                    FROM " . $this->table_name . " k
                    LEFT JOIN guru g ON k.guru_id = g.id
                    LEFT JOIN tingkat t ON k.tingkat_id = t.id
                    LEFT JOIN jurusan j ON k.jurusan_id = j.id
                    ORDER BY k.tahun_ajaran DESC, k.semester DESC, k.nama_kelas ASC";
        } else {
            $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                    t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                    FROM " . $this->table_name . " k
                    LEFT JOIN guru g ON k.guru_id = g.id
                    LEFT JOIN tingkat t ON k.tingkat_id = t.id
                    LEFT JOIN jurusan j ON k.jurusan_id = j.id
                    ORDER BY k.tahun_ajaran DESC, k.nama_kelas ASC";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                FROM " . $this->table_name . " k
                LEFT JOIN guru g ON k.guru_id = g.id 
                LEFT JOIN tingkat t ON k.tingkat_id = t.id
                LEFT JOIN jurusan j ON k.jurusan_id = j.id
                WHERE k.id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create() {
        // Check if semester column exists
        $check_query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'semester'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->execute();
        $has_semester = $check_stmt->rowCount() > 0;

        if ($has_semester) {
            $query = "INSERT INTO " . $this->table_name . "
                    (nama_kelas, guru_id, tahun_ajaran, semester, tingkat_id, jurusan_id)
                    VALUES
                    (:nama_kelas, :guru_id, :tahun_ajaran, :semester, :tingkat_id, :jurusan_id)";
        } else {
            $query = "INSERT INTO " . $this->table_name . "
                    (nama_kelas, guru_id, tahun_ajaran, tingkat_id, jurusan_id)
                    VALUES
                    (:nama_kelas, :guru_id, :tahun_ajaran, :tingkat_id, :jurusan_id)";
        }

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':nama_kelas', $this->nama_kelas);
        $stmt->bindParam(':guru_id', $this->guru_id);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        if ($has_semester) {
            $stmt->bindParam(':semester', $this->semester);
        }
        $stmt->bindParam(':tingkat_id', $this->tingkat_id);
        $stmt->bindParam(':jurusan_id', $this->jurusan_id);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function update() {
        // Check if semester column exists
        $check_query = "SHOW COLUMNS FROM " . $this->table_name . " LIKE 'semester'";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->execute();
        $has_semester = $check_stmt->rowCount() > 0;

        if ($has_semester) {
            $query = "UPDATE " . $this->table_name . "
                    SET nama_kelas = :nama_kelas,
                        guru_id = :guru_id,
                        tahun_ajaran = :tahun_ajaran,
                        semester = :semester,
                        tingkat_id = :tingkat_id,
                        jurusan_id = :jurusan_id
                    WHERE id = :id";
        } else {
            $query = "UPDATE " . $this->table_name . "
                    SET nama_kelas = :nama_kelas,
                        guru_id = :guru_id,
                        tahun_ajaran = :tahun_ajaran,
                        tingkat_id = :tingkat_id,
                        jurusan_id = :jurusan_id
                    WHERE id = :id";
        }

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':nama_kelas', $this->nama_kelas);
        $stmt->bindParam(':guru_id', $this->guru_id);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        if ($has_semester) {
            $stmt->bindParam(':semester', $this->semester);
        }
        $stmt->bindParam(':tingkat_id', $this->tingkat_id);
        $stmt->bindParam(':jurusan_id', $this->jurusan_id);
        $stmt->bindParam(':id', $this->id);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function getByTingkatAndJurusan($tingkat_id, $jurusan_id) {
        $query = "SELECT DISTINCT k.* 
                  FROM kelas k 
                  WHERE k.tingkat_id = :tingkat_id 
                  AND k.jurusan_id = :jurusan_id 
                  ORDER BY k.nama_kelas";
                  
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tingkat_id', $tingkat_id);
        $stmt->bindParam(':jurusan_id', $jurusan_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':id', $this->id);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function getOne() {
        $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas 
                FROM " . $this->table_name . " k
                LEFT JOIN guru g ON k.guru_id = g.id 
                WHERE k.id = :id 
                LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->nama_kelas = $row['nama_kelas'];
            $this->guru_id = $row['guru_id'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            $this->nama_wali_kelas = $row['nama_wali_kelas'];
            return true;
        }
        return false;
    }

    public function getOneNew() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$this->id]);
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->nama_kelas = $row['nama_kelas'];
            $this->guru_id = $row['guru_id'];
            return $row;
        }
        return false;
    }

    public function getAllTeachers() {
        $guru = new Guru();
        return $guru->getAll();
    }

    private function getGuruIdByName($nama_lengkap) {
        $query = "SELECT id FROM guru WHERE nama_lengkap = :nama_lengkap AND status = 'aktif' LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nama_lengkap", $nama_lengkap);
        $stmt->execute();
        
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            return $row['id'];
        }
        return null;
    }

    public function importFromArray($data) {
        $this->conn->beginTransaction();
        try {
            foreach ($data as $row) {
                $guru_id = null;
                if (!empty($row['wali_kelas'])) {
                    $guru_id = $this->getGuruIdByName($row['wali_kelas']);
                    if (!$guru_id) {
                        throw new Exception("Wali kelas '" . $row['wali_kelas'] . "' tidak ditemukan");
                    }
                }

                $query = "INSERT INTO " . $this->table_name . " 
                        (nama_kelas, guru_id, tahun_ajaran, tingkat_id, jurusan_id) 
                        VALUES (:nama_kelas, :guru_id, :tahun_ajaran, :tingkat_id, :jurusan_id)";
                
                $stmt = $this->conn->prepare($query);
                
                // Sanitize input
                $nama_kelas = htmlspecialchars(strip_tags($row['nama_kelas']));
                $tahun_ajaran = htmlspecialchars(strip_tags($row['tahun_ajaran']));
                
                // Bind parameters
                $stmt->bindParam(":nama_kelas", $nama_kelas);
                $stmt->bindParam(":guru_id", $guru_id);
                $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
                $stmt->bindParam(":tingkat_id", $row['tingkat_id']);
                $stmt->bindParam(":jurusan_id", $row['jurusan_id']);
                
                if (!$stmt->execute()) {
                    throw new Exception("Gagal menambahkan kelas: " . $nama_kelas);
                }
            }
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function getTahunAjaran() {
        $query = "SELECT DISTINCT tahun_ajaran FROM kelas ORDER BY tahun_ajaran DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Get classes by academic period
     */
    public function getByPeriode($tahun_ajaran, $semester) {
        $query = "SELECT k.*, g.nama_lengkap as nama_wali_kelas,
                t.nama_tingkat, j.nama_jurusan, j.kode_jurusan
                FROM " . $this->table_name . " k
                LEFT JOIN guru g ON k.guru_id = g.id
                LEFT JOIN tingkat t ON k.tingkat_id = t.id
                LEFT JOIN jurusan j ON k.jurusan_id = j.id
                WHERE k.tahun_ajaran = :tahun_ajaran
                AND k.semester = :semester
                ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->bindParam(':semester', $semester);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Get classes by academic period as array
     */
    public function getByPeriodeAsArray($tahun_ajaran, $semester) {
        $result = $this->getByPeriode($tahun_ajaran, $semester);
        return $result ? $result->fetchAll(PDO::FETCH_ASSOC) : [];
    }

    /**
     * Get current classes (uses active period)
     */
    public function getCurrentClasses() {
        require_once __DIR__ . '/PeriodeAktif.php';
        $periode = new PeriodeAktif();
        if ($periode->getActive()) {
            return $this->getByPeriode($periode->tahun_ajaran, $periode->semester);
        }

        // Fallback to all classes if no active period
        return $this->getAll();
    }

    /**
     * Get current classes as array
     */
    public function getCurrentClassesAsArray() {
        $result = $this->getCurrentClasses();
        return $result ? $result->fetchAll(PDO::FETCH_ASSOC) : [];
    }

    public function getEmptyResult() {
        $query = "SELECT * FROM kelas WHERE 1=0";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }
}
