<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/TugasTambahan.php';
require_once '../models/Siswa.php';

// Check if kelas_id is provided
if(!isset($_GET['kelas_id']) || !isset($_GET['tugas_id'])) {
    echo '<p class="text-center text-danger">Parameter tidak lengkap</p>';
    exit();
}

$kelas_id = $_GET['kelas_id'];
$tugas_id = $_GET['tugas_id'];

// Get tugas tambahan details
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;

if(!$tugasTambahan->getOne()) {
    echo '<p class="text-center text-danger">Tugas tambahan tidak ditemukan</p>';
    exit();
}

// Get all students in the class
$students = $tugasTambahan->getAllSiswaInClass($kelas_id);

// Get assigned students
$assigned_students = $tugasTambahan->getAssignedStudents($tugas_id);
$assigned_student_ids = [];
while($student = $assigned_students->fetch(PDO::FETCH_ASSOC)) {
    $assigned_student_ids[] = $student['id'];
}

// Display students
if($students->rowCount() > 0) {
    while($student = $students->fetch(PDO::FETCH_ASSOC)) {
        $checked = in_array($student['id'], $assigned_student_ids) ? 'checked disabled' : '';
        $label_class = in_array($student['id'], $assigned_student_ids) ? 'text-muted' : '';
        
        echo '<div class="form-check">';
        echo '<input class="form-check-input student-checkbox" type="checkbox" name="siswa_ids[]" id="student_' . $student['id'] . '" value="' . $student['id'] . '" ' . $checked . '>';
        echo '<label class="form-check-label ' . $label_class . '" for="student_' . $student['id'] . '">';
        echo $student['nis'] . ' - ' . $student['nama_siswa'];
        if(in_array($student['id'], $assigned_student_ids)) {
            echo ' <small>(Sudah ditambahkan)</small>';
        }
        echo '</label>';
        echo '</div>';
    }
} else {
    echo '<p class="text-center text-muted">Tidak ada siswa di kelas ini</p>';
}
?>
