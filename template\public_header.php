<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../config/database.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIHADIR | Sistem Informasi Kehadiran Siswa</title>

    <!-- jQuery first -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        /* Custom styles */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .footer {
            margin-top: auto;
        }

        /* Card styles */
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0,0,0,.125);
        }

        /* Table styles */
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        /* Button styles */
        .btn {
            margin-right: 0.5rem;
        }

        .btn i {
            margin-right: 0.5rem;
        }

        /* Form styles */
        .form-group {
            margin-bottom: 1rem;
        }

        /* Alert styles */
        .alert {
            margin-bottom: 1rem;
        }

        /* Navigation active state */
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 0.25rem;
        }

        /* Custom spacing */
        .mt-4 {
            margin-top: 2rem !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container-fluid {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --bg-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }

        body {
            background-color: #f8f9fc;
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .navbar {
            background: var(--bg-gradient);
            padding: 1rem;
        }

        .navbar-brand {
            color: white;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-brand i {
            margin-right: 0.5rem;
        }

        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            padding: 1rem 1.25rem;
            font-weight: 700;
            color: #4e73df;
        }

        .footer {
            background-color: #fff;
            border-top: 1px solid #e3e6f0;
            padding: 1.5rem 0;
            margin-top: 2rem;
        }

        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2653d4;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .table th {
            background-color: #f8f9fc;
        }

        .badge-success {
            background-color: #1cc88a;
        }

        .badge-warning {
            background-color: #f6c23e;
        }

        .badge-danger {
            background-color: #e74a3b;
        }

        .badge-info {
            background-color: #36b9cc;
        }

        .badge-primary {
            background-color: #4e73df;
        }

        .badge-secondary {
            background-color: #858796;
        }

        .badge {
            font-weight: 600;
            padding: 0.35em 0.65em;
            border-radius: 0.25rem;
            color: white;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="/absen/public">
                <i class="fas fa-user-clock"></i> SIHADIR
            </a>
            <div class="d-flex">
                <a href="/absen/login.php" class="btn btn-outline-light">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
