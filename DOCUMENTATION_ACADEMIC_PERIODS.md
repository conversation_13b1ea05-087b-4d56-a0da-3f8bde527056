# Panduan Sistem Pelacakan Periode Akademik

## G<PERSON>baran Umum

Sistem manajemen siswa telah diperbarui untuk mendukung pelacakan periode akademik yang komprehensif. Sekarang Anda dapat mengakses data historis siswa dari berbagai tahun ajaran dan semester tanpa kehilangan informasi ketika siswa naik kelas atau lulus.

## Fitur Utama

### 1. **Pelacakan Periode Akademik**
- Setiap siswa kini memiliki riwayat lengkap untuk setiap tahun ajaran dan semester
- Data historis tetap tersimpan meskipun siswa naik kelas atau lulus
- Tidak ada lagi kehilangan data saat promosi atau kelulusan siswa

### 2. **Filter Periode di Semua Modul**
- Semua modul kini mendukung filter berdasarkan tahun ajaran dan semester
- Anda dapat melihat data dari periode akademik manapun
- Filter tersedia di: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan la<PERSON>

### 3. **Sistem Promosi yang Aman**
- Sistem promosi/kelulusan otomatis telah dinonaktifkan untuk mencegah kehilangan data
- Guru dapat mengelola data siswa lintas periode dengan aman

## Cara Menggunakan

### A. Melihat Data Siswa Berdasarkan Periode

#### 1. **Di Modul Siswa (siswa/index.php)**
1. Buka halaman **Data Siswa**
2. Pilih **Mode Tampilan**: "Berdasarkan Periode Akademik"
3. Pilih **Tahun Ajaran** dan **Semester** yang diinginkan
4. Klik tombol **Filter**
5. Data siswa akan ditampilkan sesuai periode yang dipilih

#### 2. **Melihat Riwayat Periode Siswa**
1. Di halaman Data Siswa, klik tombol **Riwayat** (ikon jam) pada siswa yang diinginkan
2. Anda akan melihat semua periode akademik yang pernah diikuti siswa tersebut
3. Dari halaman ini, Anda dapat langsung mengakses nilai dan absensi untuk periode tertentu

### B. Mengakses Data Nilai Berdasarkan Periode

#### 1. **Di Modul Nilai (nilai/index.php)**
1. Buka halaman **Nilai**
2. Gunakan filter **Semester** dan **Tahun Ajaran** di bagian atas
3. Pilih periode yang diinginkan
4. Klik **Filter** untuk melihat mata pelajaran dan nilai untuk periode tersebut

#### 2. **Input Nilai untuk Periode Tertentu**
1. Setelah memfilter periode, klik **Input Nilai** pada mata pelajaran
2. Sistem akan menampilkan siswa yang terdaftar pada periode tersebut
3. Input nilai seperti biasa

### C. Melihat Absensi Berdasarkan Periode

**Note:** Modul rekap absensi telah dihapus karena bermasalah dengan sistem yang baru. Gunakan modul laporan atau riwayat absensi sebagai alternatif.

### D. Mengakses Data Tugas Berdasarkan Periode

#### 1. **Di Modul Tugas (tugas/index.php)**
1. Buka halaman **Tugas**
2. Filter berdasarkan **Semester** dan **Tahun Ajaran**
3. Pilih mata pelajaran untuk melihat daftar tugas pada periode tersebut

### E. Menggunakan Interface Publik

#### 1. **Untuk Siswa/Orang Tua (public/student_data.php)**
1. Masukkan NIS siswa
2. Gunakan dropdown **Tahun Ajaran** dan **Semester** untuk memilih periode
3. Klik tombol pencarian untuk melihat data periode tersebut

## Perubahan Penting

### ⚠️ **Sistem Promosi Dinonaktifkan**
- Fitur promosi/kelulusan otomatis di `siswa/promote.php` telah dinonaktifkan
- Ini mencegah kehilangan data historis siswa
- Untuk mengelola perpindahan siswa, gunakan filter periode akademik

### ✅ **Data Historis Terjaga**
- Semua data nilai, absensi, dan tugas dari periode sebelumnya tetap tersimpan
- Anda dapat mengakses data siswa dari tahun ajaran dan semester manapun
- Tidak ada data yang hilang saat siswa naik kelas atau lulus

## Tips Penggunaan

### 1. **Navigasi Cepat**
- Gunakan tombol **Reset** atau **Kembali ke Periode Aktif** untuk kembali ke periode saat ini
- Perhatikan indikator periode yang sedang ditampilkan (biasanya dalam kotak berwarna)

### 2. **Konsistensi Data**
- Pastikan selalu memilih periode yang tepat saat input data
- Periksa filter periode sebelum melakukan input nilai atau absensi

### 3. **Akses Riwayat**
- Manfaatkan fitur riwayat periode siswa untuk melihat perkembangan akademik
- Gunakan link langsung ke nilai dan absensi dari halaman riwayat

## Troubleshooting

### **Tidak Melihat Data Siswa**
- Pastikan filter periode sudah dipilih dengan benar
- Cek apakah siswa terdaftar pada periode yang dipilih
- Gunakan mode "Siswa Aktif Saat Ini" jika ingin melihat siswa periode aktif

### **Data Nilai/Absensi Kosong**
- Pastikan periode yang dipilih sesuai dengan periode input data
- Cek apakah data sudah diinput untuk periode tersebut

### **Error saat Mengakses Riwayat**
- Pastikan database migration sudah dijalankan
- Hubungi administrator sistem jika masalah berlanjut

## Migrasi Database

### **Untuk Administrator**
Sebelum menggunakan fitur ini, pastikan menjalankan script migrasi:

```sql
-- Jalankan file: database/migration_student_periods.sql
-- File ini akan:
-- 1. Membuat tabel siswa_periode
-- 2. Menambah field periode ke tabel siswa
-- 3. Migrasi data existing
-- 4. Membuat view dan stored procedure
```

### **Rollback (Jika Diperlukan)**
Jika perlu mengembalikan ke sistem lama:

```sql
-- Jalankan file: database/rollback_student_periods.sql
-- PERINGATAN: Ini akan menghapus fitur periode akademik
```

## Dukungan

Jika Anda mengalami masalah atau membutuhkan bantuan:

1. **Periksa dokumentasi ini terlebih dahulu**
2. **Jalankan test script**: `test_data_preservation.php` untuk memverifikasi sistem
3. **Hubungi administrator sistem** untuk bantuan teknis

---

**Catatan**: Sistem ini dirancang untuk menjaga integritas data historis siswa. Semua perubahan telah diuji untuk memastikan tidak ada data yang hilang selama proses migrasi.
