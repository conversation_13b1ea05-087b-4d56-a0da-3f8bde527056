<?php
// Clean version of KD endpoint with proper JSON handling
// This version ensures no unexpected output before JSON response

// Disable all output and error display
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Start output buffering immediately
ob_start();

// Function to send clean JSON response
function sendJsonResponse($data) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_clean();
    }
    
    // Set proper headers
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    // Send JSON response
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

// Function to send error response
function sendErrorResponse($message, $debug = null) {
    $response = [
        'success' => false,
        'message' => $message
    ];
    
    if ($debug !== null) {
        $response['debug'] = $debug;
    }
    
    sendJsonResponse($response);
}

try {
    // Include required files
    require_once '../middleware/auth.php';
    require_once '../config/database.php';
    require_once '../models/KompetensiDasar.php';
    
    // Check authentication
    if (!isset($_SESSION['user_id'])) {
        sendErrorResponse('Session tidak valid. Silakan login ulang.');
    }
    
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'guru') {
        sendErrorResponse('Akses ditolak. Hanya guru yang dapat mengakses fitur ini.');
    }
    
} catch (Exception $e) {
    sendErrorResponse('Error loading required files: ' . $e->getMessage());
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Only POST method allowed');
}

// Validate required parameters
if (!isset($_POST['mapel_id']) || !isset($_POST['kelas_id'])) {
    sendErrorResponse('Missing required parameters: mapel_id and kelas_id', [
        'received_post' => array_keys($_POST)
    ]);
}

// Get and validate parameters (handle array values)
$mapel_id = is_array($_POST['mapel_id']) ? $_POST['mapel_id'][0] : trim($_POST['mapel_id']);
$kelas_id = is_array($_POST['kelas_id']) ? $_POST['kelas_id'][0] : trim($_POST['kelas_id']);

// CRITICAL FIX: Get the correct guru_id using application-level mapping
// NO DATABASE STRUCTURE CHANGES - Pure application logic solution
require_once 'user_guru_mapping.php';

$user_id = $_SESSION['user_id']; // This is from users table
$guru_id = null;

// Try to find the corresponding guru record using simplified mapping
try {
    require_once '../models/Guru.php';

    // Use the simplified Guru::getByUserId() method
    $guru = new Guru();
    $stmt = $guru->getByUserId($user_id);
    $guru_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$guru_data) {
        sendErrorResponse('Guru record not found for current user', [
            'user_id' => $user_id,
            'message' => 'No active guru record found for this user account',
            'suggestion' => 'Check if user nama_lengkap matches guru nama_lengkap exactly'
        ]);
    }

    $guru_id = $guru_data['id'];

} catch (Exception $e) {
    sendErrorResponse('Error finding guru record: ' . $e->getMessage());
}

if (empty($mapel_id) || empty($kelas_id)) {
    sendErrorResponse('Empty parameter values', [
        'mapel_id' => $mapel_id,
        'kelas_id' => $kelas_id
    ]);
}

// Validate numeric IDs
if (!is_numeric($mapel_id) || !is_numeric($kelas_id)) {
    sendErrorResponse('Invalid parameter format - IDs must be numeric', [
        'mapel_id' => $mapel_id,
        'kelas_id' => $kelas_id
    ]);
}

// Get optional parameters
$semester = isset($_POST['semester']) && !empty($_POST['semester']) ? trim($_POST['semester']) : null;
$tahun_ajaran = isset($_POST['tahun_ajaran']) && !empty($_POST['tahun_ajaran']) ? trim($_POST['tahun_ajaran']) : null;

try {
    // Test database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        sendErrorResponse('Database connection failed');
    }
    
    // Create KD instance
    $kd = new KompetensiDasar();
    
    // Verify the methods exist
    if (!method_exists($kd, 'getKdForRpp')) {
        sendErrorResponse('KompetensiDasar::getKdForRpp method not found');
    }
    
    if (!method_exists($kd, 'formatKdForRpp')) {
        sendErrorResponse('KompetensiDasar::formatKdForRpp method not found');
    }
    
    // Get KD data
    $kd_data = $kd->getKdForRpp($mapel_id, $kelas_id, $guru_id, $semester, $tahun_ajaran);

    // Extra validation: Ensure all returned records belong to the current teacher
    if (!empty($kd_data)) {
        foreach ($kd_data as $record) {
            // This shouldn't happen if the query is correct, but let's verify
            if (isset($record['guru_id']) && $record['guru_id'] != $guru_id) {
                error_log("SECURITY WARNING: KD record {$record['id']} belongs to guru {$record['guru_id']} but was returned for guru $guru_id");
                sendErrorResponse('Data isolation error detected. Please contact administrator.');
            }
        }
    }
    
    if (empty($kd_data)) {
        sendJsonResponse([
            'success' => true,
            'message' => 'Tidak ada data Kompetensi Dasar yang ditemukan untuk mata pelajaran dan kelas ini',
            'data' => [
                'kd_list' => [],
                'formatted_kd' => '',
                'count' => 0
            ],
            'debug' => [
                'user_id' => $user_id,
                'guru_id' => $guru_id,
                'mapel_id' => $mapel_id,
                'kelas_id' => $kelas_id,
                'semester' => $semester,
                'tahun_ajaran' => $tahun_ajaran,
                'query_executed' => true
            ]
        ]);
    }
    
    // Format KD data
    $formatted_kd = $kd->formatKdForRpp($kd_data);
    
    // Send success response
    sendJsonResponse([
        'success' => true,
        'message' => 'Data Kompetensi Dasar berhasil dimuat',
        'data' => [
            'kd_list' => $kd_data,
            'formatted_kd' => $formatted_kd,
            'count' => count($kd_data),
            'semester' => $semester,
            'tahun_ajaran' => $tahun_ajaran
        ],
        'debug' => [
            'user_id' => $user_id,
            'guru_id' => $guru_id,
            'mapel_id' => $mapel_id,
            'kelas_id' => $kelas_id,
            'semester' => $semester,
            'tahun_ajaran' => $tahun_ajaran,
            'query_executed' => true,
            'records_found' => count($kd_data)
        ]
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log("KD Auto-population Error: " . $e->getMessage());
    error_log("File: " . $e->getFile() . " Line: " . $e->getLine());
    
    sendErrorResponse('Exception occurred: ' . $e->getMessage(), [
        'error_type' => get_class($e),
        'error_file' => basename($e->getFile()),
        'error_line' => $e->getLine(),
        'parameters' => [
            'user_id' => $user_id ?? 'not set',
            'guru_id' => $guru_id ?? 'not set',
            'mapel_id' => $mapel_id ?? 'not set',
            'kelas_id' => $kelas_id ?? 'not set',
            'semester' => $semester ?? 'not set',
            'tahun_ajaran' => $tahun_ajaran ?? 'not set'
        ]
    ]);
}
?>
