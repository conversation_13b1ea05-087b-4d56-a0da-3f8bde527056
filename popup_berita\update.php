<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../403.php");
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

$error = "";
$success = "";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $popupBerita = new PopupBerita();
    
    $popupBerita->id = $_POST['id'];
    $popupBerita->is_active = isset($_POST['is_active']) ? 1 : 0;
    $popupBerita->title = $_POST['title'];
    $popupBerita->show_excerpt = isset($_POST['show_excerpt']) ? 1 : 0;
    $popupBerita->excerpt_length = $_POST['excerpt_length'];
    $popupBerita->auto_show = isset($_POST['auto_show']) ? 1 : 0;
    $popupBerita->show_dont_show_again = isset($_POST['show_dont_show_again']) ? 1 : 0;

    if ($popupBerita->updateSettings()) {
        header("Location: index.php?success=1");
        exit();
    } else {
        header("Location: index.php?error=1");
        exit();
    }
} else {
    header("Location: index.php");
    exit();
}
?>
