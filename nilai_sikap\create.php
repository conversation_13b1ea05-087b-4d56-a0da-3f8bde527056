<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';
require_once '../models/Kelas.php';
require_once '../models/Siswa.php';
require_once '../models/MataPelajaran.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

$nilaiSikap = new NilaiSikap();
$kelas = new Kelas();
$siswa = new Siswa();
$mataPelajaran = new MataPelajaran();
$tahunAjaran = new TahunAjaran();

// Get active period
$periodeAktif = new PeriodeAktif();
$periodeAktif->getActive();

$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$semester = isset($_GET['semester']) ? $_GET['semester'] : ($periodeAktif->semester ?: '1');
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : ($periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1));
$mapel_id = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';

// Jika mapel_id tidak ada, kembalikan ke halaman index
if (!$mapel_id) {
    $_SESSION['error'] = "Mata pelajaran harus dipilih";
    header("Location: index.php");
    exit();
}

// Get academic years from TahunAjaran table instead of Kelas table for better reliability
$daftar_tahun_ajaran = [];
$tahunAjaranResult = $tahunAjaran->getAll();
while ($row = $tahunAjaranResult->fetch(PDO::FETCH_ASSOC)) {
    $daftar_tahun_ajaran[] = $row['tahun_ajaran'];
}

// If no academic years exist, create a default one
if (empty($daftar_tahun_ajaran)) {
    $currentYear = date('Y');
    $defaultTahunAjaran = $currentYear . '/' . ($currentYear + 1);
    $daftar_tahun_ajaran[] = $defaultTahunAjaran;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $success = true;
    $error_messages = [];

    foreach ($_POST['siswa'] as $siswa_id => $nilai) {
        // Skip jika siswa sudah memiliki nilai
        if ($nilaiSikap->checkNilaiExists($siswa_id, $_POST['kelas_id'], $_POST['semester'], $_POST['tahun_ajaran'])) {
            continue;
        }

        $nilaiSikap->siswa_id = $siswa_id;
        $nilaiSikap->kelas_id = $_POST['kelas_id'];
        $nilaiSikap->mapel_id = $_POST['mapel_id'];
        $nilaiSikap->semester = $_POST['semester'];
        $nilaiSikap->tahun_ajaran = $_POST['tahun_ajaran'];
        $nilaiSikap->nilai_spiritual = $nilai['spiritual'];
        $nilaiSikap->nilai_sosial = $nilai['sosial'];
        
        // Generate deskripsi otomatis
        $nilaiSikap->deskripsi_spiritual = $nilaiSikap->generateDeskripsiSpiritual($nilai['spiritual']);
        $nilaiSikap->deskripsi_sosial = $nilaiSikap->generateDeskripsiSosial($nilai['sosial']);

        if (!$nilaiSikap->create()) {
            $success = false;
            $error_messages[] = "Gagal menyimpan nilai untuk siswa ID: " . $siswa_id;
        }
    }

    if ($success) {
        $_SESSION['success'] = "Data nilai sikap berhasil ditambahkan";
        header("Location: index.php?kelas_id=" . $_POST['kelas_id'] . "&semester=" . $_POST['semester'] . "&tahun_ajaran=" . $_POST['tahun_ajaran'] . "&mapel_id=" . $_POST['mapel_id']);
        exit();
    } else {
        $_SESSION['error'] = "Gagal menambahkan beberapa data nilai sikap: " . implode(", ", $error_messages);
    }
}

// Use period-based student retrieval to ensure we get the right students for the selected academic period
$daftar_siswa = $siswa->getByPeriode($tahun_ajaran, $semester, $kelas_id);
$data_kelas = $kelas->getById($kelas_id);
$data_mapel = $mataPelajaran->getById($mapel_id);

if (!$data_kelas) {
    $_SESSION['error'] = "Kelas tidak ditemukan";
    header("Location: index.php");
    exit();
}

if (!$data_mapel) {
    $_SESSION['error'] = "Mata pelajaran tidak ditemukan";
    header("Location: index.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Input Nilai Sikap - <?= htmlspecialchars($data_kelas['nama_kelas']) ?> - <?= htmlspecialchars($data_mapel['nama_mapel']) ?></h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="kelas_id" value="<?= htmlspecialchars($kelas_id) ?>">
                        <input type="hidden" name="mapel_id" value="<?= htmlspecialchars($mapel_id) ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="semester">Semester</label>
                                    <select name="semester" id="semester" class="form-control" required>
                                        <option value="1" <?= $semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                                        <option value="2" <?= $semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tahun_ajaran">Tahun Ajaran</label>
                                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-control" required>
                                        <?php foreach ($daftar_tahun_ajaran as $ta) : ?>
                                            <option value="<?= htmlspecialchars($ta) ?>" <?= $tahun_ajaran == $ta ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($ta) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Siswa</th>
                                        <th>Nilai Spiritual (A/B/C/D)</th>
                                        <th>Nilai Sosial (A/B/C/D)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    while ($row = $daftar_siswa->fetch(PDO::FETCH_ASSOC)): 
                                        // Skip jika siswa sudah memiliki nilai
                                        if ($nilaiSikap->checkNilaiExists($row['siswa_id'], $kelas_id, $semester, $tahun_ajaran)) {
                                            continue;
                                        }
                                    ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= htmlspecialchars($row['nis']) ?></td>
                                        <td><?= htmlspecialchars($row['nama_siswa']) ?></td>
                                        <td>
                                            <select name="siswa[<?= $row['siswa_id'] ?>][spiritual]" class="form-control" required>
                                                <option value="">Pilih Nilai</option>
                                                <option value="A">A</option>
                                                <option value="B">B</option>
                                                <option value="C">C</option>
                                                <option value="D">D</option>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="siswa[<?= $row['siswa_id'] ?>][sosial]" class="form-control" required>
                                                <option value="">Pilih Nilai</option>
                                                <option value="A">A</option>
                                                <option value="B">B</option>
                                                <option value="C">C</option>
                                                <option value="D">D</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-primary">Simpan Semua</button>
                            <a href="index.php?kelas_id=<?= $kelas_id ?>&semester=<?= $semester ?>&tahun_ajaran=<?= $tahun_ajaran ?>&mapel_id=<?= $mapel_id ?>" 
                               class="btn btn-secondary">Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>