<?php
require_once '../middleware/auth.php';
checkAdminAccess();
require_once '../models/MataPelajaran.php';

header('Content-Type: application/json');

$mapel = new MataPelajaran();

if (isset($_GET['tingkat_id']) && isset($_GET['jurusan_id'])) {
    $result = $mapel->getByTingkatAndJurusan($_GET['tingkat_id'], $_GET['jurusan_id']);
    $mapel_list = [];
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $mapel_list[] = [
            'id' => $row['id'],
            'nama_mapel' => $row['nama_mapel']
        ];
    }
    
    echo json_encode($mapel_list);
} else {
    echo json_encode([]);
}
