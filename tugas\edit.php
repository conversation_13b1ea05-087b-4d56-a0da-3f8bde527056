<?php
require_once '../config/database.php';
require_once '../models/Tugas.php';
require_once '../template/header.php';

if(!isset($_GET['id'])) {
    $_SESSION['error'] = "ID tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

$tugas = new Tugas();
$tugas->id = $_GET['id'];
if(!$tugas->getOne()) {
    $_SESSION['error'] = "Data tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tugas->judul = $_POST['judul'];
    $tugas->deskripsi = $_POST['deskripsi'];
    $tugas->tanggal = $_POST['tanggal'];
    
    if($tugas->update()) {
        $_SESSION['success'] = "Tugas berhasil diperbarui!";
        header("Location: tugas.php?mapel_id=" . $tugas->mapel_id . "&semester=" . $tugas->semester . "&tahun_ajaran=" . $tugas->tahun_ajaran);
        exit();
    } else {
        $_SESSION['error'] = "Gagal memperbarui tugas!";
    }
}

// Get messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Edit Tugas</h5>
                    <small class="text-muted">Semester <?php echo $tugas->semester; ?> - Tahun Ajaran <?php echo $tugas->tahun_ajaran; ?></small>
                </div>
                <a href="tugas.php?mapel_id=<?php echo $tugas->mapel_id; ?>&semester=<?php echo $tugas->semester; ?>&tahun_ajaran=<?php echo $tugas->tahun_ajaran; ?>" 
                   class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>

        <?php if ($success_msg): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="judul" class="form-label">Judul Tugas <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="judul" name="judul" 
                               value="<?php echo htmlspecialchars($tugas->judul); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="deskripsi" class="form-label">Deskripsi Tugas <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" 
                                  rows="4" required><?php echo htmlspecialchars($tugas->deskripsi); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal Pemberian <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" 
                               value="<?php echo $tugas->tanggal; ?>" required>
                    </div>

                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
