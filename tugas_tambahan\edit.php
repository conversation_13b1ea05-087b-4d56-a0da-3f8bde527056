<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/TugasTambahan.php';
require_once '../models/MataPelajaran.php';
require_once '../template/header.php';

// Check if ID is provided
if(!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];

// Initialize models
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;

// Get tugas tambahan details
if(!$tugasTambahan->getOne()) {
    $_SESSION['error'] = "Tugas tambahan tidak ditemukan!";
    header("Location: index.php");
    exit();
}

// Get mapel details
$mapel = new MataPelajaran();
$mapel->id = $tugasTambahan->mapel_id;
$mapel->getOne();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['update_tugas'])) {
        $tugasTambahan->judul = $_POST['judul'];
        $tugasTambahan->deskripsi = $_POST['deskripsi'];
        $tugasTambahan->tanggal = $_POST['tanggal'];

        if($tugasTambahan->update()) {
            $_SESSION['success'] = "Tugas tambahan berhasil diperbarui!";
            header("Location: tugas.php?mapel_id=" . $tugasTambahan->mapel_id . "&semester=" . $tugasTambahan->semester . "&tahun_ajaran=" . $tugasTambahan->tahun_ajaran);
            exit();
        } else {
            $_SESSION['error'] = "Gagal memperbarui tugas tambahan!";
        }
    }
}

// Handle success and error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Edit Tugas Tambahan</h1>
        <a href="tugas.php?mapel_id=<?php echo $tugasTambahan->mapel_id; ?>&semester=<?php echo $tugasTambahan->semester; ?>&tahun_ajaran=<?php echo $tugasTambahan->tahun_ajaran; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if($success_msg): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if($error_msg): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Tugas Tambahan</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <input type="hidden" name="update_tugas" value="1">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="mapel" class="form-label">Mata Pelajaran</label>
                                <input type="text" class="form-control" id="mapel" value="<?php echo $mapel->nama_mapel; ?>" disabled>
                            </div>
                            <div class="col-md-6">
                                <label for="tanggal" class="form-label">Tanggal</label>
                                <input type="date" class="form-control" id="tanggal" name="tanggal" value="<?php echo $tugasTambahan->tanggal; ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="judul" class="form-label">Judul Tugas</label>
                            <input type="text" class="form-control" id="judul" name="judul" value="<?php echo $tugasTambahan->judul; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deskripsi" class="form-label">Deskripsi Tugas</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"><?php echo $tugasTambahan->deskripsi; ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
