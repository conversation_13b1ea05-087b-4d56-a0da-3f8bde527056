<?php
require_once '../template/public_header.php';
require_once '../models/Siswa.php';
require_once '../models/Absensi.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';

// Check if required parameters are provided
if (!isset($_GET['siswa_id']) || !isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: index.php");
    exit();
}

// Get parameters
$siswa_id = $_GET['siswa_id'];
$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Initialize models
$siswaModel = new Siswa();
$absensiModel = new Absensi();
$mapelModel = new MataPelajaran();
$kelasModel = new Kelas();

// Get student data
$siswaModel->id = $siswa_id;
if (!$siswaModel->getOne()) {
    $_SESSION['error'] = "Siswa tidak ditemukan.";
    header("Location: index.php");
    exit();
}
$siswa = [
    'id' => $siswa_id,
    'nis' => $siswaModel->nis,
    'nama_siswa' => $siswaModel->nama_siswa,
    'kelas_id' => $siswaModel->kelas_id
];

// Get subject data
$mapelModel->id = $mapel_id;
if (!$mapelModel->getOne()) {
    $_SESSION['error'] = "Mata pelajaran tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Get class data
$kelasModel->id = $siswa['kelas_id'];
if ($kelasModel->getOne()) {
    $nama_kelas = $kelasModel->nama_kelas;
} else {
    $nama_kelas = "Tidak diketahui";
}

// Get detailed attendance data
$absensi_detail = $absensiModel->getDetailBySiswaMapel($siswa_id, $mapel_id, $semester, $tahun_ajaran);
?>

<div class="row mb-4">
    <div class="col-12">
        <a href="javascript:history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold">
                    <i class="fas fa-calendar-check me-2"></i> Detail Kehadiran
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">NIS</th>
                                <td>: <?php echo $siswa['nis']; ?></td>
                            </tr>
                            <tr>
                                <th>Nama</th>
                                <td>: <?php echo $siswa['nama_siswa']; ?></td>
                            </tr>
                            <tr>
                                <th>Kelas</th>
                                <td>: <?php echo $nama_kelas; ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Mata Pelajaran</th>
                                <td>: <?php echo $mapelModel->nama_mapel; ?></td>
                            </tr>
                            <tr>
                                <th>Semester</th>
                                <td>: <?php echo $semester; ?></td>
                            </tr>
                            <tr>
                                <th>Tahun Ajaran</th>
                                <td>: <?php echo $tahun_ajaran; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered datatable">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tanggal</th>
                                <th>Hari</th>
                                <th>Status</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            if ($absensi_detail && $absensi_detail->rowCount() > 0) {
                                while ($row = $absensi_detail->fetch(PDO::FETCH_ASSOC)) {
                                    $status = trim($row['status']);

                                    // Default: Hadir
                                    $badge_class = 'badge-success';
                                    $status_text = 'Hadir';

                                    // Debugging - Uncomment untuk melihat nilai status
                                    // echo '<pre>Status: "' . htmlspecialchars($status) . '"</pre>';

                                    // Periksa status dengan case insensitive
                                    $status_lower = strtolower($status);

                                    if ($status_lower == 'sakit' || $status_lower == 's') {
                                        $badge_class = 'badge-warning';
                                        $status_text = 'Sakit';
                                    } elseif ($status_lower == 'izin' || $status_lower == 'i') {
                                        $badge_class = 'badge-info';
                                        $status_text = 'Izin';
                                    } elseif ($status_lower == 'alpha' || $status_lower == 'a') {
                                        $badge_class = 'badge-danger';
                                        $status_text = 'Alpha';
                                    }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo date('d-m-Y', strtotime($row['tanggal'])); ?></td>
                                <td><?php
                                    $timestamp = strtotime($row['tanggal']);
                                    $hari = date('w', $timestamp);
                                    $nama_hari = '';
                                    switch($hari) {
                                        case 0: $nama_hari = 'Minggu'; break;
                                        case 1: $nama_hari = 'Senin'; break;
                                        case 2: $nama_hari = 'Selasa'; break;
                                        case 3: $nama_hari = 'Rabu'; break;
                                        case 4: $nama_hari = 'Kamis'; break;
                                        case 5: $nama_hari = 'Jumat'; break;
                                        case 6: $nama_hari = 'Sabtu'; break;
                                    }
                                    echo $nama_hari;
                                ?></td>
                                <td>
                                    <span class="badge <?php echo $badge_class; ?>">
                                        <?php echo $status_text; ?>
                                    </span>
                                </td>
                                <td><?php echo $row['keterangan'] ?: '-'; ?></td>
                            </tr>
                            <?php
                                }
                            } else {
                            ?>
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data kehadiran</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
