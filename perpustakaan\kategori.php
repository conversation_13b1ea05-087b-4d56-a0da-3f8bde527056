<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

$perpustakaan = new Perpustakaan();
$kategori = $perpustakaan->getAllKategori();

// Proses form jika ada POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['tambah'])) {
        $data = ['nama_kategori' => $_POST['nama_kategori']];
        try {
            if ($perpustakaan->tambahKategori($data)) {
                $_SESSION['success'] = "Kategori berhasil ditambahkan";
            } else {
                throw new Exception("Gagal menambahkan kategori");
            }
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: kategori.php");
        exit();
    }

    if (isset($_POST['edit'])) {
        $id = $_POST['id_kategori'];
        $data = ['nama_kategori' => $_POST['nama_kategori']];
        try {
            if ($perpustakaan->editKategori($id, $data)) {
                $_SESSION['success'] = "Kategori berhasil diperbarui";
            } else {
                throw new Exception("Gagal memperbarui kategori");
            }
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: kategori.php");
        exit();
    }
}

// Proses hapus kategori
if (isset($_GET['hapus'])) {
    $id = $_GET['hapus'];
    try {
        // Cek apakah kategori masih digunakan
        $query = "SELECT COUNT(*) as total FROM buku WHERE id_kategori = :id";
        $stmt = $perpustakaan->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['total'] > 0) {
            throw new Exception("Kategori tidak dapat dihapus karena masih digunakan oleh buku.");
        }

        if ($perpustakaan->hapusKategori($id)) {
            $_SESSION['success'] = "Kategori berhasil dihapus";
        } else {
            throw new Exception("Gagal menghapus kategori");
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
    header("Location: kategori.php");
    exit();
}
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Manajemen Kategori Buku</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Perpustakaan</a></li>
                        <li class="breadcrumb-item active">Kategori</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-5">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Tambah Kategori</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST">
                                <div class="form-group">
                                    <label for="nama_kategori">Nama Kategori</label>
                                    <input type="text" class="form-control" id="nama_kategori" name="nama_kategori" required>
                                </div>
                                <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-7">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Daftar Kategori</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="tableKategori" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Nama Kategori</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        foreach ($kategori as $k):
                                        ?>
                                            <tr>
                                                <td><?= $no++; ?></td>
                                                <td><?= htmlspecialchars($k['nama_kategori']); ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal<?= $k['id_kategori']; ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="?hapus=<?= $k['id_kategori']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus kategori ini?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>

                                            <!-- Modal Edit -->
                                            <div class="modal fade" id="editModal<?= $k['id_kategori']; ?>" tabindex="-1" aria-labelledby="editModalLabel<?= $k['id_kategori']; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="editModalLabel<?= $k['id_kategori']; ?>">Edit Kategori</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <form action="" method="POST">
                                                            <div class="modal-body">
                                                                <input type="hidden" name="id_kategori" value="<?= $k['id_kategori']; ?>">
                                                                <div class="form-group">
                                                                    <label for="nama_kategori<?= $k['id_kategori']; ?>">Nama Kategori</label>
                                                                    <input type="text" class="form-control" id="nama_kategori<?= $k['id_kategori']; ?>" name="nama_kategori" value="<?= htmlspecialchars($k['nama_kategori']); ?>" required>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                <button type="submit" name="edit" class="btn btn-primary">Simpan</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>

<script>
$(document).ready(function() {
    $('#tableKategori').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data kategori"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[1, "asc"]], // Urutkan berdasarkan nama kategori
        "columnDefs": [
            {"orderable": false, "targets": 2} // Kolom aksi tidak bisa diurutkan
        ]
    });
});
</script>
