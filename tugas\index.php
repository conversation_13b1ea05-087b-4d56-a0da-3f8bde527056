<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tugas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Kelas.php';
require_once '../models/JadwalPelajaran.php';
require_once '../template/header.php';

// Get active period
$periode = new PeriodeAktif();
$periode->getActive();

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

// Get available tahun ajaran
$ta = new TahunAjaran();
$tahun_ajaran_list = $ta->getAllAsArray();

// If no tahun ajaran exists, add current one
if(empty($tahun_ajaran_list)) {
    $ta->tahun_ajaran = $periode->tahun_ajaran;
    $ta->create();
    $tahun_ajaran_list = [$periode->tahun_ajaran];
}

// Get selected kelas if any
$selected_kelas = isset($_GET['kelas']) ? $_GET['kelas'] : '';

// Get mata pelajaran based on filter
$mapel = new MataPelajaran();
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    require_once '../models/User.php';
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if ($guru_id) {
        $result_mapel = $mapel->getByGuru($guru_id);
        if ($selected_kelas) {
            // If kelas is selected, filter further by kelas
            $jadwal = new JadwalPelajaran();
            $filtered_mapel = [];
            $all_mapel = $result_mapel->fetchAll(PDO::FETCH_ASSOC);
            foreach ($all_mapel as $row) {
                $kelas_check = $jadwal->getKelasByMapel($row['id']);
                while ($kelas_row = $kelas_check->fetch(PDO::FETCH_ASSOC)) {
                    if ($kelas_row['kelas_id'] == $selected_kelas) {
                        $filtered_mapel[] = $row;
                        break;
                    }
                }
            }

            // Use a new query to filter the results
            if (!empty($filtered_mapel)) {
                $ids = array_column($filtered_mapel, 'id');
                $placeholders = str_repeat('?,', count($ids) - 1) . '?';
                $query = "SELECT * FROM mata_pelajaran WHERE id IN ($placeholders) ORDER BY nama_mapel ASC";
                $result_mapel = $mapel->getByCustomQuery($query, $ids);
            } else {
                $result_mapel = $mapel->getEmptyResult();
            }
        }
    } else {
        $result_mapel = $mapel->getEmptyResult();
    }
} else {
    $result_mapel = $selected_kelas ? $mapel->getByKelas($selected_kelas) : $mapel->getAll();
}

// Get all kelas for filter
$kelas = new Kelas();
$kelas_list = $kelas->getAll();

?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Filter Data Tugas</h5>
                </div>
                <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Kembali ke Periode Aktif
                    </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="semester" class="form-label">Semester</label>
                        <select name="semester" id="semester" class="form-select">
                            <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                            <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                        <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                            <?php foreach($tahun_ajaran_list as $ta): ?>
                                <option value="<?php echo $ta['tahun_ajaran']; ?>" <?php echo $tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : ''; ?>>
                                    <?php echo $ta['tahun_ajaran']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            <a href="/absen/tahun_ajaran" target="_blank">
                                <i class="fas fa-plus"></i> Tambah Tahun Ajaran
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="kelas" class="form-label">Kelas</label>
                        <select name="kelas" id="kelas" class="form-select">
                            <option value="">Semua Kelas</option>
                            <?php while($row_kelas = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                <option value="<?php echo $row_kelas['id']; ?>" <?php echo (isset($_GET['kelas']) && $_GET['kelas'] == $row_kelas['id']) ? 'selected' : ''; ?>>
                                    <?php echo $row_kelas['nama_kelas']; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Daftar Mata Pelajaran</h5>
                    <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                        <small class="text-muted">Periode: Semester <?php echo $semester; ?> - TA <?php echo $tahun_ajaran; ?></small>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableMapel">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Kode Mapel</th>
                                <th>Nama Mata Pelajaran</th>
                                <th>Kelas</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while($row = $result_mapel->fetch(PDO::FETCH_ASSOC)) {
                                echo "<tr>";
                                echo "<td>" . $no++ . "</td>";
                                echo "<td>" . $row['kode_mapel'] . "</td>";
                                echo "<td>" . $row['nama_mapel'] . "</td>";

                                // Get jadwal for this mapel to show assigned classes
                                $jadwal = new JadwalPelajaran();
                                $jadwal_kelas = $jadwal->getByMapel($row['id']);
                                $kelas_names = [];
                                while($row_kelas = $jadwal_kelas->fetch(PDO::FETCH_ASSOC)) {
                                    $kelas_names[] = $row_kelas['nama_kelas'];
                                }
                                echo "<td>" . ($kelas_names ? implode(", ", array_unique($kelas_names)) : '-') . "</td>";

                                // Add kelas parameter to URL if selected
                                $kelas_param = $selected_kelas ? "&kelas=" . $selected_kelas : "";
                                echo "<td>
                                        <a href='tugas.php?mapel_id=" . $row['id'] . "&semester=$semester&tahun_ajaran=$tahun_ajaran$kelas_param'
                                           class='btn btn-primary btn-sm'>
                                            <i class='fas fa-list'></i> Daftar Tugas
                                        </a>
                                      </td>";
                                echo "</tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableMapel').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
