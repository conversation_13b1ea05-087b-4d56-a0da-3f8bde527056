<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Validate input
if (!isset($_POST['question_id']) || !isset($_POST['rpp_id'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: generate_questions.php");
    exit();
}

$question_id = $_POST['question_id'];
$rpp_id = $_POST['rpp_id'];

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: generate_questions.php");
        exit();
    }

    // Verify ownership through RPP
    $database = new Database();
    $db = $database->getConnection();
    
    $verify_query = "SELECT r.guru_id FROM rpp r 
                     INNER JOIN rpp_questions rq ON r.id = rq.rpp_id 
                     WHERE rq.id = :question_id AND r.id = :rpp_id";
    $verify_stmt = $db->prepare($verify_query);
    $verify_stmt->bindParam(":question_id", $question_id);
    $verify_stmt->bindParam(":rpp_id", $rpp_id);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$verify_result || $verify_result['guru_id'] != $guru_id) {
        $_SESSION['error'] = "Anda tidak memiliki akses untuk mengedit soal ini.";
        header("Location: questions_list.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Validate required fields
    if (!isset($_POST['question_text']) || !isset($_POST['question_type']) || !isset($_POST['difficulty_level'])) {
        $_SESSION['error'] = "Data soal tidak lengkap.";
        header("Location: questions_list.php?rpp_id=" . $rpp_id);
        exit();
    }

    $question_text = trim($_POST['question_text']);
    $question_type = $_POST['question_type'];
    $difficulty_level = $_POST['difficulty_level'];
    $category = isset($_POST['category']) ? trim($_POST['category']) : '';

    // Validate question text
    if (empty($question_text)) {
        $_SESSION['error'] = "Teks soal tidak boleh kosong.";
        header("Location: questions_list.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Validate question type
    if (!in_array($question_type, ['multiple_choice', 'essay'])) {
        $_SESSION['error'] = "Jenis soal tidak valid.";
        header("Location: questions_list.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Validate difficulty level
    $valid_difficulties = ['regular', 'hots_easy', 'hots_medium', 'hots_hard'];
    if (!in_array($difficulty_level, $valid_difficulties)) {
        $_SESSION['error'] = "Tingkat kesulitan tidak valid.";
        header("Location: questions_list.php?rpp_id=" . $rpp_id);
        exit();
    }

    // Validate verb appropriateness
    $verb_validation = validateQuestionVerbs($question_text, $question_type);
    $verb_warning = '';
    if (!$verb_validation['is_valid']) {
        $verb_warning = " (Peringatan: " . $verb_validation['message'] . ")";
    }

    // Prepare update data
    $options = null;
    $correct_answer = null;

    // Process multiple choice options
    if ($question_type === 'multiple_choice') {
        if (isset($_POST['options']) && is_array($_POST['options'])) {
            // Filter out empty options
            $option_values = array_filter($_POST['options'], function($option) {
                return !empty(trim($option));
            });

            if (count($option_values) >= 2) {
                // Create formatted options with letters (support up to 5 options)
                $formatted_options = [];
                $letters = ['A', 'B', 'C', 'D', 'E'];
                $option_index = 0;

                foreach ($option_values as $option) {
                    if ($option_index < count($letters) && $option_index < 5) { // Max 5 options
                        $formatted_options[] = $letters[$option_index] . '. ' . trim($option);
                        $option_index++;
                    }
                }

                $options = json_encode($formatted_options);

                // Validate correct answer
                if (isset($_POST['correct_answer']) &&
                    in_array($_POST['correct_answer'], array_slice($letters, 0, count($formatted_options)))) {
                    $correct_answer = $_POST['correct_answer'];
                } else {
                    // Default to first option if not specified
                    $correct_answer = 'A';
                }
            } else {
                $_SESSION['error'] = "Soal pilihan ganda harus memiliki minimal 2 opsi jawaban.";
                header("Location: questions_list.php?rpp_id=" . $rpp_id);
                exit();
            }
        } else {
            $_SESSION['error'] = "Opsi jawaban tidak boleh kosong untuk soal pilihan ganda.";
            header("Location: questions_list.php?rpp_id=" . $rpp_id);
            exit();
        }
    }

    // Update question in database
    $update_query = "UPDATE rpp_questions SET 
                     question_text = :question_text,
                     question_type = :question_type,
                     difficulty_level = :difficulty_level,
                     category = :category,
                     options = :options,
                     correct_answer = :correct_answer,
                     updated_at = CURRENT_TIMESTAMP
                     WHERE id = :question_id";

    $stmt = $db->prepare($update_query);
    $stmt->bindParam(":question_text", $question_text);
    $stmt->bindParam(":question_type", $question_type);
    $stmt->bindParam(":difficulty_level", $difficulty_level);
    $stmt->bindParam(":category", $category);
    $stmt->bindParam(":options", $options);
    $stmt->bindParam(":correct_answer", $correct_answer);
    $stmt->bindParam(":question_id", $question_id);

    if ($stmt->execute()) {
        $_SESSION['success'] = "Soal berhasil diperbarui." . $verb_warning;
    } else {
        $_SESSION['error'] = "Gagal memperbarui soal.";
    }

} catch (Exception $e) {
    error_log("Edit Question Error: " . $e->getMessage());
    $_SESSION['error'] = "Terjadi kesalahan saat memperbarui soal: " . $e->getMessage();
}

// Redirect back to questions list
header("Location: questions_list.php?rpp_id=" . $rpp_id);
exit();

function validateQuestionVerbs($question_text, $question_type) {
    $text = strtolower($question_text);
    
    // Define inappropriate verbs for multiple choice
    $inappropriate_for_mc = [
        'jelaskan', 'uraikan', 'analisis', 'evaluasi', 'deskripsikan',
        'paparkan', 'jabarkan', 'rincikan', 'elaborasi', 'sintesis',
        'ciptakan', 'rancang', 'buat', 'susun', 'kembangkan', 'simpulkan'
    ];
    
    // Define appropriate verbs for multiple choice
    $appropriate_for_mc = [
        'pilih', 'tentukan', 'sebutkan', 'identifikasi', 'tunjukkan',
        'bandingkan', 'kategorikan', 'klasifikasikan', 'bedakan',
        'prediksi', 'manakah', 'yang mana'
    ];
    
    if ($question_type === 'multiple_choice') {
        // Check for inappropriate verbs in multiple choice
        foreach ($inappropriate_for_mc as $verb) {
            if (strpos($text, $verb) !== false) {
                return [
                    'is_valid' => false,
                    'message' => "Menggunakan kata '$verb' yang memerlukan penjelasan tertulis. Gunakan kata kerja seperti: " . implode(', ', array_slice($appropriate_for_mc, 0, 5)) . ", dll."
                ];
            }
        }
        
        // Check if question has appropriate verbs
        $has_appropriate_verb = false;
        foreach ($appropriate_for_mc as $verb) {
            if (strpos($text, $verb) !== false) {
                $has_appropriate_verb = true;
                break;
            }
        }
        
        if (!$has_appropriate_verb && strpos($text, '?') !== false) {
            return [
                'is_valid' => false,
                'message' => 'Sebaiknya menggunakan kata kerja yang sesuai seperti: pilih, tentukan, manakah, identifikasi, dll.'
            ];
        }
    }
    
    return ['is_valid' => true, 'message' => ''];
}
?>
