<?php
require_once __DIR__ . '/../config/database.php';

class MataPelajaran {
    private $conn;
    private $table_name = "mata_pelajaran";

    public $id;
    public $kode_mapel;
    public $nama_mapel;
    public $kkm;
    public $tingkat_id;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getAll() {
        $query = "SELECT m.*,
                        t.nama_tingkat,
                        GROUP_CONCAT(DISTINCT j.nama_jurusan ORDER BY j.nama_jurusan ASC SEPARATOR ', ') as nama_jurusan,
                        GROUP_CONCAT(DISTINCT j.id ORDER BY j.nama_jurusan ASC) as jurusan_ids,
                        GROUP_CONCAT(DISTINCT g.nama_lengkap ORDER BY g.nama_lengkap ASC SEPARATOR ', ') as guru_pengampu,
                        GROUP_CONCAT(DISTINCT mg.guru_id ORDER BY g.nama_lengkap ASC) as guru_ids
                 FROM " . $this->table_name . " m
                 LEFT JOIN tingkat t ON m.tingkat_id = t.id
                 LEFT JOIN mapel_jurusan mj ON m.id = mj.mapel_id
                 LEFT JOIN jurusan j ON mj.jurusan_id = j.id
                 LEFT JOIN mapel_guru mg ON m.id = mg.mapel_id
                 LEFT JOIN guru g ON mg.guru_id = g.id
                 GROUP BY m.id
                 ORDER BY m.nama_mapel ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        $this->conn->beginTransaction();
        try {
            $query = "INSERT INTO " . $this->table_name . "
                    (kode_mapel, nama_mapel, kkm, tingkat_id)
                    VALUES (:kode_mapel, :nama_mapel, :kkm, :tingkat_id)";

            $stmt = $this->conn->prepare($query);

            // Sanitize input dengan pengecekan null
            $this->kode_mapel = htmlspecialchars(strip_tags($this->kode_mapel ?? ''));
            $this->nama_mapel = htmlspecialchars(strip_tags($this->nama_mapel ?? ''));

            // Handle KKM - set default value jika null atau kosong
            $kkm_value = $this->kkm ?? '';
            if (empty($kkm_value) || !is_numeric($kkm_value)) {
                $kkm_value = 75; // Default KKM value
            }
            $this->kkm = $kkm_value;

            $this->tingkat_id = htmlspecialchars(strip_tags($this->tingkat_id ?? ''));

            $stmt->bindParam(":kode_mapel", $this->kode_mapel);
            $stmt->bindParam(":nama_mapel", $this->nama_mapel);
            $stmt->bindParam(":kkm", $this->kkm);
            $stmt->bindParam(":tingkat_id", $this->tingkat_id);

            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                $this->conn->commit();
                return true;
            }
            $this->conn->rollBack();
            return false;
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$this->id]);

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->kode_mapel = $row['kode_mapel'];
            $this->nama_mapel = $row['nama_mapel'];
            $this->kkm = $row['kkm'];
            $this->tingkat_id = $row['tingkat_id'];
            return $row;
        }
        return false;
    }

    public function getOneOld() {
        $query = "SELECT m.*, GROUP_CONCAT(mg.guru_id) as guru_ids
                 FROM " . $this->table_name . " m
                 LEFT JOIN mapel_guru mg ON m.id = mg.mapel_id
                 WHERE m.id = ?
                 GROUP BY m.id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$this->id]);

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->kode_mapel = $row['kode_mapel'];
            $this->nama_mapel = $row['nama_mapel'];
            $this->kkm = $row['kkm'];
            return $row;
        }
        return false;
    }

    public function update() {
        $sql = "UPDATE mata_pelajaran
                SET kode_mapel = :kode_mapel,
                    nama_mapel = :nama_mapel,
                    kkm = :kkm,
                    tingkat_id = :tingkat_id
                WHERE id = :id";

        $stmt = $this->conn->prepare($sql);

        // Handle KKM - set default value jika null atau kosong
        $kkm_value = $this->kkm ?? '';
        if (empty($kkm_value) || !is_numeric($kkm_value)) {
            $kkm_value = 75; // Default KKM value
        }

        $stmt->bindParam(':kode_mapel', $this->kode_mapel);
        $stmt->bindParam(':nama_mapel', $this->nama_mapel);
        $stmt->bindParam(':kkm', $kkm_value);
        $stmt->bindParam(':tingkat_id', $this->tingkat_id);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    public function updateGuruPengampu($mapel_id, $guru_ids) {
        $this->conn->beginTransaction();
        try {
            // Hapus guru pengampu yang ada
            $query = "DELETE FROM mapel_guru WHERE mapel_id = :mapel_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":mapel_id", $mapel_id);
            $stmt->execute();

            // Tambahkan guru pengampu baru
            if (!empty($guru_ids)) {
                $values = [];
                $params = [];
                foreach ($guru_ids as $i => $guru_id) {
                    $mapel_key = ":mapel_id" . $i;
                    $guru_key = ":guru_id" . $i;
                    $values[] = "($mapel_key, $guru_key)";
                    $params[$mapel_key] = $mapel_id;
                    $params[$guru_key] = $guru_id;
                }

                $query = "INSERT INTO mapel_guru (mapel_id, guru_id) VALUES " . implode(", ", $values);
                $stmt = $this->conn->prepare($query);
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                $stmt->execute();
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function getGuruPengampu($mapel_id) {
        $query = "SELECT g.*
                 FROM guru g
                 JOIN mapel_guru mg ON g.id = mg.guru_id
                 WHERE mg.mapel_id = :mapel_id
                 ORDER BY g.nama_lengkap ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->execute();
        return $stmt;
    }

    public function getGuruPengampuByMapelId($mapel_id) {
        $query = "SELECT g.*
                 FROM guru g
                 JOIN mapel_guru mg ON g.id = mg.guru_id
                 WHERE mg.mapel_id = :mapel_id
                 ORDER BY g.nama_lengkap ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByKelas($kelas_id) {
        $query = "SELECT DISTINCT m.*
                FROM " . $this->table_name . " m
                INNER JOIN jadwal_pelajaran j ON m.id = j.mapel_id
                WHERE j.kelas_id = :kelas_id
                ORDER BY m.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();
        return $stmt;
    }

    public function getByGuru($guru_id) {
        $query = "SELECT DISTINCT m.*
                FROM " . $this->table_name . " m
                INNER JOIN jadwal_pelajaran j ON m.id = j.mapel_id
                WHERE j.guru_id = :guru_id
                ORDER BY m.nama_mapel ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        return $stmt;
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function importFromArray($data) {
        $query = "INSERT INTO " . $this->table_name . "
                (kode_mapel, nama_mapel, kkm, tingkat_id)
                VALUES (:kode_mapel, :nama_mapel, :kkm, :tingkat_id)";

        $stmt = $this->conn->prepare($query);
        $success = true;

        foreach($data as $row) {
            // Sanitize input dengan pengecekan null
            $kode_mapel = htmlspecialchars(strip_tags($row['kode_mapel'] ?? ''));
            $nama_mapel = htmlspecialchars(strip_tags($row['nama_mapel'] ?? ''));

            // Handle KKM - set default value jika null atau kosong
            $kkm_value = $row['kkm'] ?? '';
            if (empty($kkm_value) || !is_numeric($kkm_value)) {
                $kkm_value = 75; // Default KKM value
            }
            $kkm = $kkm_value;

            $tingkat_id = htmlspecialchars(strip_tags($row['tingkat_id'] ?? ''));

            $stmt->bindParam(":kode_mapel", $kode_mapel);
            $stmt->bindParam(":nama_mapel", $nama_mapel);
            $stmt->bindParam(":kkm", $kkm);
            $stmt->bindParam(":tingkat_id", $tingkat_id);

            if(!$stmt->execute()) {
                $success = false;
                break;
            }
        }
        return $success;
    }

    public function getEmptyResult() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE 1=0";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getByCustomQuery($query, $params = []) {
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt;
    }

    public function getMapelByGuruId($guru_id) {
        $query = "SELECT DISTINCT m.*
                 FROM mata_pelajaran m
                 INNER JOIN jadwal_pelajaran j ON m.id = j.mapel_id
                 WHERE j.guru_id = :guru_id
                 ORDER BY m.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();

        return $stmt;
    }

    public function getKelasByMapel($mapel_id) {
        $query = "SELECT DISTINCT k.*
                 FROM kelas k
                 INNER JOIN jadwal_pelajaran jp ON k.id = jp.kelas_id
                 WHERE jp.mapel_id = :mapel_id
                 ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->execute();

        return $stmt;
    }

    public function getMapelByGuru($guru_id) {
        $query = "SELECT m.*
                FROM " . $this->table_name . " m
                JOIN mapel_guru mg ON m.id = mg.mapel_id
                WHERE mg.guru_id = :guru_id
                ORDER BY m.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getJurusanByMapel($mapel_id) {
        $query = "SELECT j.*
                 FROM jurusan j
                 JOIN mapel_jurusan mj ON j.id = mj.jurusan_id
                 WHERE mj.mapel_id = :mapel_id
                 ORDER BY j.nama_jurusan ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updateJurusan($mapel_id, $jurusan_ids) {
        $this->conn->beginTransaction();
        try {
            // Hapus jurusan yang ada
            $query = "DELETE FROM mapel_jurusan WHERE mapel_id = :mapel_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":mapel_id", $mapel_id);
            $stmt->execute();

            // Tambahkan jurusan baru
            if (!empty($jurusan_ids)) {
                $values = [];
                $params = [];
                foreach ($jurusan_ids as $i => $jurusan_id) {
                    $mapel_key = ":mapel_id" . $i;
                    $jurusan_key = ":jurusan_id" . $i;
                    $values[] = "($mapel_key, $jurusan_key)";
                    $params[$mapel_key] = $mapel_id;
                    $params[$jurusan_key] = $jurusan_id;
                }

                $query = "INSERT INTO mapel_jurusan (mapel_id, jurusan_id) VALUES " . implode(", ", $values);
                $stmt = $this->conn->prepare($query);
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                $stmt->execute();
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }

    public function getByTingkatAndJurusan($tingkat_id, $jurusan_id) {
        $query = "SELECT DISTINCT m.*
                  FROM mata_pelajaran m
                  JOIN mapel_jurusan mj ON m.id = mj.mapel_id
                  WHERE m.tingkat_id = :tingkat_id
                  AND mj.jurusan_id = :jurusan_id
                  ORDER BY m.nama_mapel";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':tingkat_id', $tingkat_id);
        $stmt->bindParam(':jurusan_id', $jurusan_id);
        $stmt->execute();

        return $stmt;
    }

    public function getByKelasAndGuru($kelas_id, $guru_id) {
        // First try: Get subjects where the teacher is directly assigned to teach in the specific class
        $query = "SELECT DISTINCT m.id, m.nama_mapel, m.kode_mapel
                  FROM mata_pelajaran m
                  INNER JOIN jadwal_pelajaran jp ON m.id = jp.mapel_id
                  WHERE jp.kelas_id = :kelas_id
                  AND jp.guru_id = :guru_id
                  ORDER BY m.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();

        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no results from jadwal_pelajaran, try fallback: subjects assigned to teacher that are taught in the class
        if (empty($result)) {
            $query = "SELECT DISTINCT m.id, m.nama_mapel, m.kode_mapel
                      FROM mata_pelajaran m
                      INNER JOIN jadwal_pelajaran jp ON m.id = jp.mapel_id
                      INNER JOIN mapel_guru mg ON m.id = mg.mapel_id
                      WHERE jp.kelas_id = :kelas_id
                      AND mg.guru_id = :guru_id
                      ORDER BY m.nama_mapel ASC";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':kelas_id', $kelas_id);
            $stmt->bindParam(':guru_id', $guru_id);
            $stmt->execute();

            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        return $result;
    }
}
