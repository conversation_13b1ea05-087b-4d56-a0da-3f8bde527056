<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $popupBerita = new PopupBerita();

    try {
        $result = $popupBerita->clearAllItems();

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Semua berita berhasil dihapus dari popup']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal menghapus berita']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '<PERSON><PERSON><PERSON><PERSON> k<PERSON>: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
}
?>
