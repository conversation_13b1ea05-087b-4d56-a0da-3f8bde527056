<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/TahunAjaran.php';

// Get teacher ID from logged in user
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Check if ID is provided
if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Initialize models
$kd = new KompetensiDasar();
$mapel = new MataPelajaran();
$tahunAjaran = new TahunAjaran();

// Get KD data
$kd_data = $kd->getById($_GET['id']);
if (!$kd_data || $kd_data['guru_id'] != $guru_id) {
    header("Location: index.php?error=1");
    exit();
}

// Get teacher's subjects - try both methods for compatibility
$mapel_list_array = [];

// Method 1: Try mapel_guru table (direct assignment)
try {
    $mapel_direct = $mapel->getMapelByGuru($guru_id);
    if (!empty($mapel_direct)) {
        $mapel_list_array = $mapel_direct;
    }
} catch (Exception $e) {
    // Continue to method 2
}

// Method 2: Try jadwal_pelajaran table (schedule-based) if no direct assignments
if (empty($mapel_list_array)) {
    try {
        $mapel_schedule = $mapel->getMapelByGuruId($guru_id);
        while ($row = $mapel_schedule->fetch(PDO::FETCH_ASSOC)) {
            $mapel_list_array[] = $row;
        }
    } catch (Exception $e) {
        // Log error or handle as needed
    }
}

// Convert array back to statement-like object for compatibility
$database = new Database();
$conn = $database->getConnection();
if (!empty($mapel_list_array)) {
    $mapel_ids = array_column($mapel_list_array, 'id');
    $placeholders = str_repeat('?,', count($mapel_ids) - 1) . '?';
    $query = "SELECT * FROM mata_pelajaran WHERE id IN ($placeholders) ORDER BY nama_mapel ASC";
    $mapel_list = $conn->prepare($query);
    $mapel_list->execute($mapel_ids);
} else {
    // Return empty result set
    $query = "SELECT * FROM mata_pelajaran WHERE 1=0";
    $mapel_list = $conn->prepare($query);
    $mapel_list->execute();
}

$tahun_ajaran_list = $tahunAjaran->getAll();

// Get teacher's classes
$kelas_list = $kd->getKelasByGuruId($guru_id);

$error = "";
$success = "";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate input
    $kode_kd = trim($_POST['kode_kd']);
    $deskripsi_kd = trim($_POST['deskripsi_kd']);
    $tema_subtema = trim($_POST['tema_subtema']);
    $materi_pokok = trim($_POST['materi_pokok']);
    $tujuan_pembelajaran = trim($_POST['tujuan_pembelajaran']);
    $mapel_id = $_POST['mapel_id'];
    $kelas_id = !empty($_POST['kelas_id']) ? $_POST['kelas_id'] : null;
    $semester = $_POST['semester'];
    $tahun_ajaran = $_POST['tahun_ajaran'];

    // Basic validation
    if (empty($kode_kd) || empty($deskripsi_kd) || empty($mapel_id) || empty($semester) || empty($tahun_ajaran)) {
        $error = "Semua field wajib diisi kecuali Kelas, Tema/Subtema, Materi Pokok, dan Tujuan Pembelajaran";
    } else {
        // Check if KD code already exists for the same subject, class, semester, and academic year (excluding current record)
        if ($kd->isKodeExists($kode_kd, $mapel_id, $kelas_id, $semester, $tahun_ajaran, $_GET['id'])) {
            $error = "Kode KD sudah ada untuk mata pelajaran, kelas, semester, dan tahun ajaran yang sama";
        } else {
            // Set KD properties
            $kd->id = $_GET['id'];
            $kd->kode_kd = $kode_kd;
            $kd->deskripsi_kd = $deskripsi_kd;
            $kd->tema_subtema = $tema_subtema;
            $kd->materi_pokok = $materi_pokok;
            $kd->tujuan_pembelajaran = $tujuan_pembelajaran;
            $kd->mapel_id = $mapel_id;
            $kd->guru_id = $guru_id;
            $kd->kelas_id = $kelas_id;
            $kd->semester = $semester;
            $kd->tahun_ajaran = $tahun_ajaran;

            // Update KD
            if ($kd->update()) {
                header("Location: index.php?success=1");
                exit();
            } else {
                $error = "Gagal mengupdate data Kompetensi Dasar";
            }
        }
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Edit Kompetensi Dasar</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="/absen/">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="index.php">Kompetensi Dasar</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Edit Kompetensi Dasar</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="kelas_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                                    <select class="form-select" id="kelas_id" name="kelas_id" onchange="loadMapelOptions()" required>
                                        <option value="">Pilih Kelas</option>
                                        <?php foreach ($kelas_list as $kelas_row): ?>
                                            <option value="<?= $kelas_row['id'] ?>"
                                                    <?= (isset($_POST['kelas_id']) ? $_POST['kelas_id'] : $kd_data['kelas_id']) == $kelas_row['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($kelas_row['nama_kelas']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Pilih kelas terlebih dahulu untuk melihat mata pelajaran yang tersedia</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="mapel_id" class="form-label">Mata Pelajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" id="mapel_id" name="mapel_id" required>
                                        <option value="">Pilih kelas terlebih dahulu</option>
                                    </select>
                                    <div class="form-text">Mata pelajaran akan muncul setelah memilih kelas</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="semester" class="form-label">Semester <span class="text-danger">*</span></label>
                                    <select class="form-select" id="semester" name="semester" required>
                                        <option value="1" <?= (isset($_POST['semester']) ? $_POST['semester'] : $kd_data['semester']) == '1' ? 'selected' : '' ?>>
                                            Semester 1
                                        </option>
                                        <option value="2" <?= (isset($_POST['semester']) ? $_POST['semester'] : $kd_data['semester']) == '2' ? 'selected' : '' ?>>
                                            Semester 2
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                        <?php while ($ta_row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                            <option value="<?= $ta_row['tahun_ajaran'] ?>"
                                                    <?= (isset($_POST['tahun_ajaran']) ? $_POST['tahun_ajaran'] : $kd_data['tahun_ajaran']) == $ta_row['tahun_ajaran'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($ta_row['tahun_ajaran']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="kode_kd" class="form-label">Kode KD <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="kode_kd" name="kode_kd" 
                                   value="<?= isset($_POST['kode_kd']) ? htmlspecialchars($_POST['kode_kd']) : htmlspecialchars($kd_data['kode_kd']) ?>" 
                                   placeholder="Contoh: 3.1, 4.2, dll" required>
                            <div class="form-text">Masukkan kode Kompetensi Dasar sesuai kurikulum</div>
                        </div>

                        <div class="mb-3">
                            <label for="deskripsi_kd" class="form-label">Deskripsi Kompetensi Dasar <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="deskripsi_kd" name="deskripsi_kd" rows="4"
                                      placeholder="Masukkan deskripsi lengkap Kompetensi Dasar" required><?= isset($_POST['deskripsi_kd']) ? htmlspecialchars($_POST['deskripsi_kd']) : htmlspecialchars($kd_data['deskripsi_kd']) ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tema_subtema" class="form-label">Tema/Subtema</label>
                                    <input type="text" class="form-control" id="tema_subtema" name="tema_subtema"
                                           value="<?= isset($_POST['tema_subtema']) ? htmlspecialchars($_POST['tema_subtema']) : htmlspecialchars($kd_data['tema_subtema']) ?>"
                                           placeholder="Contoh: Keselamatan dan Kesehatan Kerja">
                                    <div class="form-text">Field ini bersifat opsional</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="materi_pokok" class="form-label">Materi Pokok</label>
                                    <input type="text" class="form-control" id="materi_pokok" name="materi_pokok"
                                           value="<?= isset($_POST['materi_pokok']) ? htmlspecialchars($_POST['materi_pokok']) : htmlspecialchars($kd_data['materi_pokok']) ?>"
                                           placeholder="Contoh: K3LH (Keselamatan, Kesehatan Kerja dan Lingkungan Hidup)">
                                    <div class="form-text">Field ini bersifat opsional</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="tujuan_pembelajaran" class="form-label">Tujuan Pembelajaran</label>
                            <textarea class="form-control" id="tujuan_pembelajaran" name="tujuan_pembelajaran" rows="3"
                                      placeholder="Masukkan tujuan pembelajaran (opsional)"><?= isset($_POST['tujuan_pembelajaran']) ? htmlspecialchars($_POST['tujuan_pembelajaran']) : htmlspecialchars($kd_data['tujuan_pembelajaran']) ?></textarea>
                            <div class="form-text">Field ini bersifat opsional</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update
                            </button>
                        </div>
                    </form>
            </div>
        </div>
    </div>
</div>

<script>
function loadMapelOptions() {
    const kelasId = document.getElementById('kelas_id').value;
    const mapelSelect = document.getElementById('mapel_id');
    const currentMapelId = '<?= isset($_POST['mapel_id']) ? $_POST['mapel_id'] : $kd_data['mapel_id'] ?>';

    // Clear existing options
    mapelSelect.innerHTML = '<option value="">Pilih mata pelajaran</option>';

    if (kelasId) {
        // Create AJAX request to get subjects for selected class
        fetch('get_mapel_by_kelas.php?kelas_id=' + kelasId)
            .then(response => response.json())
            .then(data => {
                if (data.length === 0) {
                    mapelSelect.innerHTML = '<option value="">Tidak ada mata pelajaran tersedia</option>';
                } else {
                    data.forEach(mapel => {
                        const option = document.createElement('option');
                        option.value = mapel.id;
                        option.textContent = mapel.nama_mapel;

                        // Pre-select the current subject if it matches
                        if (mapel.id == currentMapelId) {
                            option.selected = true;
                        }

                        mapelSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading subjects:', error);
                mapelSelect.innerHTML = '<option value="">Error loading mata pelajaran</option>';
            });
    }
}

$(document).ready(function() {
    // Load subjects for the initially selected class
    if (document.getElementById('kelas_id').value) {
        loadMapelOptions();
    }

    // Auto-format KD code
    $('#kode_kd').on('input', function() {
        let value = $(this).val().toUpperCase();
        $(this).val(value);
    });

    // Character counter for textarea
    function updateCharCount(textarea, maxLength = 1000) {
        const current = textarea.val().length;
        const remaining = maxLength - current;
        let countElement = textarea.next('.char-count');

        if (countElement.length === 0) {
            countElement = $('<small class="char-count text-muted"></small>');
            textarea.after(countElement);
        }

        countElement.text(`${current}/${maxLength} karakter`);

        if (remaining < 50) {
            countElement.removeClass('text-muted').addClass('text-warning');
        } else {
            countElement.removeClass('text-warning').addClass('text-muted');
        }
    }

    $('#deskripsi_kd').on('input', function() {
        updateCharCount($(this), 1000);
    });

    $('#tema_subtema').on('input', function() {
        updateCharCount($(this), 255);
    });

    $('#materi_pokok').on('input', function() {
        updateCharCount($(this), 255);
    });

    $('#tujuan_pembelajaran').on('input', function() {
        updateCharCount($(this), 500);
    });

    // Initialize character counters
    updateCharCount($('#deskripsi_kd'), 1000);
    updateCharCount($('#tema_subtema'), 255);
    updateCharCount($('#materi_pokok'), 255);
    updateCharCount($('#tujuan_pembelajaran'), 500);
});
</script>

<?php require_once '../template/footer.php'; ?>
