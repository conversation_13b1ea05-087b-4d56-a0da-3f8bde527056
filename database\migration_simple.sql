-- Simple Database Migration Script: Fix Restore Conflicts
-- This script creates essential stored procedures to handle restore conflicts
-- Created: 2025-08-08

-- Set session variables for better error handling
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';

-- Drop existing procedures if they exist
DROP PROCEDURE IF EXISTS SafeDropViews;
DROP PROCEDURE IF EXISTS PreRestorationCleanup;
DROP PROCEDURE IF EXISTS PostRestorationValidation;

-- Create SafeDropViews procedure
DELIMITER $$
CREATE PROCEDURE SafeDropViews()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    
    -- Drop views that commonly cause conflicts
    DROP VIEW IF EXISTS v_siswa_all_periods;
    DROP VIEW IF EXISTS v_siswa_current;
    DROP VIEW IF EXISTS v_absensi_summary;
    DROP VIEW IF EXISTS v_nilai_summary;
    DROP VIEW IF EXISTS v_jadwal_detail;
    
END$$
DELIMITER ;

-- Create PreRestorationCleanup procedure
DELIMITER $$
CREATE PROCEDURE PreRestorationCleanup()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    
    -- First, drop all views to prevent conflicts
    CALL SafeDropViews();
    
END$$
DELIMITER ;

-- Create PostRestorationValidation procedure
DELIMITER $$
CREATE PROCEDURE PostRestorationValidation()
BEGIN
    DECLARE view_count INT DEFAULT 0;
    DECLARE table_count INT DEFAULT 0;
    
    -- Count views
    SELECT COUNT(*) INTO view_count 
    FROM information_schema.VIEWS 
    WHERE TABLE_SCHEMA = DATABASE();
    
    -- Count tables
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE';
    
    -- Display results
    SELECT 
        'Post-restoration validation completed' as status,
        table_count as total_tables,
        view_count as total_views,
        CASE 
            WHEN view_count >= 2 AND table_count >= 10 THEN 'PASSED'
            ELSE 'WARNING: Some objects may be missing'
        END as validation_result;
        
END$$
DELIMITER ;

-- Restore session variables
SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
