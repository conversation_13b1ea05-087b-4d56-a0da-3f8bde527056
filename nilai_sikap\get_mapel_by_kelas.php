<?php
session_start();
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/Guru.php';

// Inisialisasi model
$mapel = new MataPelajaran();
$jadwal = new JadwalPelajaran();
$user = new User();
$guru = new Guru();

$kelas_id = isset($_POST['kelas_id']) ? $_POST['kelas_id'] : '';

if($kelas_id) {
    // Jika role guru, ambil mata pelajaran yang diampuh saja
    if ($_SESSION['role'] === 'guru') {
        $guru_id = $guru->getIdByUserId($_SESSION['user_id']);
        if ($guru_id) {
            $mapel_list = $jadwal->getMapelByKelasAndGuru($kelas_id, $guru_id);
            
            echo '<option value=""><PERSON><PERSON><PERSON></option>';
            while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
                echo '<option value="' . $row['id'] . '">' . htmlspecialchars($row['nama_mapel']) . '</option>';
            }
        } else {
            echo '<option value="">Tidak ada mata pelajaran</option>';
        }
    } else {
        // Jika admin, tampilkan semua mata pelajaran di kelas tersebut
        $mapel_list = $jadwal->getMapelByKelas($kelas_id);
        
        echo '<option value="">Pilih Mata Pelajaran</option>';
        while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
            echo '<option value="' . $row['id'] . '">' . htmlspecialchars($row['nama_mapel']) . '</option>';
        }
    }
}
?>