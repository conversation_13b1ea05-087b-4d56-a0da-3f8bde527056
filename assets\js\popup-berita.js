/**
 * News Popup System
 * Handles the display and management of news popup notifications
 */

class NewsPopup {
    constructor() {
        this.modalId = 'newsPopupModal';
        this.storageKey = 'sihadir_news_popup_dismissed';
        this.init();
    }

    init() {
        this.createModal();
        this.loadPopupData();
    }

    createModal() {
        // Check if modal already exists
        if (document.getElementById(this.modalId)) {
            return;
        }

        // Add CSS styles for the popup
        this.addPopupStyles();

        const modalHTML = `
            <div class="modal fade" id="${this.modalId}" tabindex="-1" aria-labelledby="newsPopupModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-lg modal-dialog-scrollable">
                    <div class="modal-content news-popup-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="newsPopupModalLabel">
                                <i class="fas fa-newspaper"></i> <span id="popupTitle">Berita Terbaru</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body news-popup-body" id="newsPopupBody">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Memuat berita...</p>
                            </div>
                        </div>
                        <div class="modal-footer news-popup-footer" id="newsPopupFooter">
                            <!-- Footer content will be added dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    addPopupStyles() {
        // Check if styles already added
        if (document.getElementById('newsPopupStyles')) {
            return;
        }

        const styles = `
            <style id="newsPopupStyles">
                .news-popup-content {
                    border: none;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                }

                .news-popup-body {
                    max-height: 500px;
                    overflow-y: auto;
                }

                .news-item {
                    padding: 1rem 0;
                    border-bottom: 1px solid #e9ecef;
                }

                .news-item:last-child {
                    border-bottom: none;
                }

                .news-title {
                    color: #495057;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                    line-height: 1.4;
                }

                .news-title i {
                    margin-right: 0.5rem;
                }

                .news-excerpt {
                    font-size: 0.9rem;
                    line-height: 1.5;
                    color: #6c757d;
                    margin-bottom: 0.75rem;
                }

                .news-meta {
                    font-size: 0.8rem;
                    color: #6c757d;
                    margin-bottom: 0.75rem;
                }

                .news-meta i {
                    margin-right: 0.25rem;
                }

                .news-actions .btn {
                    font-size: 0.85rem;
                    padding: 0.375rem 0.75rem;
                    border-radius: 0.25rem;
                    transition: all 0.2s ease;
                }

                .news-actions .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                }

                .news-popup-footer {
                    background-color: #f8f9fa;
                    border-top: 1px solid #dee2e6;
                }

                .news-popup-footer .form-check {
                    margin-bottom: 0;
                }

                .news-popup-footer .form-check-label {
                    font-size: 0.9rem;
                    color: #6c757d;
                }

                /* Animation for news items */
                .news-item {
                    opacity: 0;
                    transform: translateY(10px);
                    animation: slideInUp 0.3s ease forwards;
                }

                .news-item:nth-child(1) { animation-delay: 0.1s; }
                .news-item:nth-child(2) { animation-delay: 0.2s; }
                .news-item:nth-child(3) { animation-delay: 0.3s; }
                .news-item:nth-child(4) { animation-delay: 0.4s; }
                .news-item:nth-child(5) { animation-delay: 0.5s; }

                @keyframes slideInUp {
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                /* Responsive adjustments */
                @media (max-width: 768px) {
                    .news-popup-body {
                        max-height: 400px;
                    }

                    .news-title {
                        font-size: 1rem;
                    }

                    .news-excerpt {
                        font-size: 0.85rem;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    loadPopupData() {
        fetch('/absen/popup_berita/ajax_get_popup_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    this.displayPopup(data.data);
                } else {
                    console.log('No popup data available or popup is disabled');
                }
            })
            .catch(error => {
                console.error('Error loading popup data:', error);
            });
    }

    displayPopup(popupData) {
        const { settings, berita } = popupData;

        // Check if user has dismissed this popup
        if (this.isDismissed() && !settings.auto_show) {
            return;
        }

        // Update modal title
        document.getElementById('popupTitle').textContent = settings.title;

        // Generate news content
        const newsContent = this.generateNewsContent(berita, settings);
        document.getElementById('newsPopupBody').innerHTML = newsContent;

        // Generate footer
        const footerContent = this.generateFooter(settings);
        document.getElementById('newsPopupFooter').innerHTML = footerContent;

        // Show modal if auto_show is enabled
        if (settings.auto_show) {
            const modal = new bootstrap.Modal(document.getElementById(this.modalId));
            modal.show();
        }
    }

    generateNewsContent(berita, settings) {
        if (!berita || berita.length === 0) {
            return `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Tidak ada berita terbaru untuk ditampilkan.
                </div>
            `;
        }

        let content = '<div class="news-items">';
        
        berita.forEach((item, index) => {
            const borderClass = index > 0 ? 'border-top pt-3 mt-3' : '';
            
            content += `
                <div class="news-item ${borderClass}">
                    <h6 class="news-title mb-2">
                        <i class="fas fa-newspaper text-primary"></i>
                        ${this.escapeHtml(item.judul)}
                    </h6>
            `;

            if (settings.show_excerpt && item.excerpt) {
                content += `
                    <p class="news-excerpt text-muted mb-2">
                        ${this.escapeHtml(item.excerpt)}
                    </p>
                `;
            }

            content += `
                    <div class="news-meta mb-2">
                        <small class="text-muted">
                            <i class="fas fa-user"></i> ${this.escapeHtml(item.nama_pembuat)}
                            <i class="fas fa-calendar ms-2"></i> ${this.formatDate(item.created_at)}
                        </small>
                    </div>
                    
                    <div class="news-actions">
                        <a href="/absen/berita/view.php?id=${item.id}" class="btn btn-primary btn-sm" target="_blank">
                            <i class="fas fa-eye"></i> Lihat Berita
                        </a>
                    </div>
                </div>
            `;
        });

        content += '</div>';
        return content;
    }

    generateFooter(settings) {
        let footer = '';

        if (settings.show_dont_show_again) {
            footer += `
                <div class="form-check me-auto">
                    <input class="form-check-input" type="checkbox" id="dontShowAgain">
                    <label class="form-check-label" for="dontShowAgain">
                        Jangan tampilkan lagi
                    </label>
                </div>
            `;
        }

        footer += `
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                <i class="fas fa-times"></i> Tutup
            </button>
        `;

        return footer;
    }

    isDismissed() {
        const dismissed = localStorage.getItem(this.storageKey);
        if (!dismissed) return false;

        const dismissedDate = new Date(dismissed);
        const now = new Date();
        const daysDiff = Math.floor((now - dismissedDate) / (1000 * 60 * 60 * 24));

        // Reset dismissal after 7 days
        return daysDiff < 7;
    }

    setDismissed() {
        localStorage.setItem(this.storageKey, new Date().toISOString());
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on dashboard page and if user is logged in
    if (window.location.pathname === '/absen/' || window.location.pathname === '/absen/index.php') {
        const newsPopup = new NewsPopup();

        // Handle "don't show again" checkbox
        document.addEventListener('hidden.bs.modal', function(event) {
            if (event.target.id === 'newsPopupModal') {
                const dontShowCheckbox = document.getElementById('dontShowAgain');
                if (dontShowCheckbox && dontShowCheckbox.checked) {
                    newsPopup.setDismissed();
                }
            }
        });
    }
});

// Global function to manually show popup (for testing or manual trigger)
window.showNewsPopup = function() {
    const modal = document.getElementById('newsPopupModal');
    if (modal) {
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
};
