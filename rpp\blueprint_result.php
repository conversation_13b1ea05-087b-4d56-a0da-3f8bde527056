<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();

// Check if blueprint data exists in session
if (!isset($_SESSION['blueprint_data']) || !isset($_SESSION['blueprint_config'])) {
    $_SESSION['error'] = "Data kisi-kisi tidak ditemukan. Silakan generate ulang.";
    header("Location: generate_questions.php");
    exit();
}

$blueprint_data = $_SESSION['blueprint_data'];
$config = $_SESSION['blueprint_config'];
$rpp_id = $_GET['rpp_id'] ?? '';

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);

require_once '../template/header.php';
?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="page-title mb-1">
                <i class="fas fa-clipboard-check text-primary"></i> Hasil Generate Kisi-kisi
            </h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-modern">
                    <li class="breadcrumb-item">
                        <a href="../dashboard.php">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="generate_questions.php">
                            <i class="fas fa-file-alt"></i> RPP
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="questions_list.php?rpp_id=<?= $rpp_id ?>">
                            <i class="fas fa-question-circle"></i> Soal
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="generate_blueprint.php?rpp_id=<?= $rpp_id ?>">
                            <i class="fas fa-magic"></i> Generate Kisi-kisi
                        </a>
                    </li>
                    <li class="breadcrumb-item active">
                        <i class="fas fa-check-circle"></i> Hasil
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="questions_list.php?rpp_id=<?= $rpp_id ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if ($success_msg): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($success_msg) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_msg): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error_msg) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

            <!-- Blueprint Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Kisi-kisi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Judul Kisi-kisi:</strong></td>
                                    <td><?= htmlspecialchars($blueprint_data['exam_info']['title'] ?? 'Kisi-kisi Single RPP') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">Single RPP Blueprint</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Dibuat:</strong></td>
                                    <td><?= date('d/m/Y H:i') ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Mata Pelajaran:</strong></td>
                                    <td><?= htmlspecialchars($blueprint_data['exam_info']['subject'] ?? '') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis Ujian:</strong></td>
                                    <td><?= htmlspecialchars($blueprint_data['exam_info']['type'] ?? '') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Durasi:</strong></td>
                                    <td><?= htmlspecialchars($blueprint_data['exam_info']['duration'] ?? '') ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blueprint Content -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-table"></i> Isi Kisi-kisi</h6>
                </div>
                <div class="card-body">
                    <?php if ($blueprint_data): ?>
                        <?php
                        // Debug: Show blueprint data structure
                        if (isset($_GET['debug'])) {
                            echo '<div class="alert alert-info">';
                            echo '<h6>Debug: Blueprint Data Structure</h6>';
                            echo '<pre>' . htmlspecialchars(json_encode($blueprint_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
                            echo '</div>';
                        }

                        // Use standardized blueprint preview template
                        $generated_blueprint = $blueprint_data;

                        // Check if template file exists
                        if (file_exists('blueprint_preview_template.php')) {
                            include 'blueprint_preview_template.php';
                        } else {
                            echo '<div class="alert alert-danger">';
                            echo '<i class="fas fa-exclamation-triangle"></i> ';
                            echo 'Template file blueprint_preview_template.php tidak ditemukan.';
                            echo '</div>';
                        }
                        ?>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Data kisi-kisi tidak dapat ditampilkan. Mungkin format data tidak valid.
                        </div>
                    <?php endif; ?>

                    <!-- Debug link -->
                    <?php if (!isset($_GET['debug'])): ?>
                        <div class="mt-3">
                            <a href="?<?= http_build_query(array_merge($_GET, ['debug' => '1'])) ?>"
                               class="btn btn-sm btn-outline-info">
                                <i class="fas fa-bug"></i> Debug Data
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-cog"></i> Aksi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Export Kisi-kisi:</h6>
                            <div class="d-flex gap-2 mb-3">
                                <a href="export_blueprint.php?format=pdf&rpp_id=<?= $rpp_id ?>"
                                   class="btn btn-outline-danger" target="_blank">
                                    <i class="fas fa-file-pdf"></i> Download PDF
                                </a>
                                <a href="export_blueprint.php?format=word&rpp_id=<?= $rpp_id ?>"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-file-word"></i> Download Word
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Kelola Kisi-kisi:</h6>
                            <div class="d-flex gap-2 mb-3">
                                <a href="generate_blueprint.php?rpp_id=<?= $rpp_id ?>"
                                   class="btn btn-outline-warning">
                                    <i class="fas fa-redo"></i> Generate Ulang
                                </a>
                                <a href="questions_list.php?rpp_id=<?= $rpp_id ?>"
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Soal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
</div>

<script>
// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Fix for table-dark header visibility */
.table-dark th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border-color: #32383e !important;
}

.table-dark th,
.table-dark td {
    border-color: #32383e !important;
}

/* Ensure blueprint preview table headers are visible */
.blueprint-preview .table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    font-weight: 600;
    text-align: center;
}

.blueprint-preview .table th {
    font-weight: 600;
    font-size: 0.9rem;
    color: inherit;
}

.badge {
    font-size: 0.75rem;
}

.card-title {
    color: #495057;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Modern Breadcrumb Styling */
.breadcrumb-modern {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 8px 16px;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.breadcrumb-modern .breadcrumb-item {
    font-size: 0.875rem;
    font-weight: 500;
}

.breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
    font-size: 1.1rem;
}

.breadcrumb-modern .breadcrumb-item a {
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
    padding: 2px 6px;
    border-radius: 4px;
}

.breadcrumb-modern .breadcrumb-item a:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.breadcrumb-modern .breadcrumb-item.active {
    color: #007bff;
    font-weight: 600;
}

.breadcrumb-modern .breadcrumb-item i {
    margin-right: 4px;
    font-size: 0.8rem;
}

/* Page Title Styling */
.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0;
}

.page-title i {
    margin-right: 8px;
}

/* Debug button styling */
.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}
</style>

<?php require_once '../template/footer.php'; ?>
