<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Validate input
if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Question ID tidak ditemukan']);
    exit();
}

$question_id = $_GET['id'];

try {
    // Get guru_id
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        echo json_encode(['success' => false, 'message' => 'Data guru tidak ditemukan']);
        exit();
    }

    // Get question data
    $rppQuestion = new RppQuestion();
    $question = $rppQuestion->getOne($question_id);

    if (!$question) {
        echo json_encode(['success' => false, 'message' => 'Soal tidak ditemukan']);
        exit();
    }

    // Verify ownership through RPP
    $database = new Database();
    $db = $database->getConnection();
    
    $verify_query = "SELECT r.guru_id FROM rpp r 
                     INNER JOIN rpp_questions rq ON r.id = rq.rpp_id 
                     WHERE rq.id = :question_id";
    $verify_stmt = $db->prepare($verify_query);
    $verify_stmt->bindParam(":question_id", $question_id);
    $verify_stmt->execute();
    $verify_result = $verify_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$verify_result || $verify_result['guru_id'] != $guru_id) {
        echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk mengedit soal ini']);
        exit();
    }

    echo json_encode([
        'success' => true,
        'question' => $question
    ]);

} catch (Exception $e) {
    error_log("Get Question Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
