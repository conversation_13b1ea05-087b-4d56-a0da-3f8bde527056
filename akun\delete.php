<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$user = new User();
$user->id = $_GET['id'];

if (!$user->getOne()) {
    header("Location: index.php");
    exit();
}

// Don't allow deleting admin account
if ($user->username === 'admin') {
    header("Location: index.php");
    exit();
}

// Start transaction
$database = new Database();
$conn = $database->getConnection();
$conn->beginTransaction();

try {
    // If user is a guru, set their status to nonaktif
    if ($user->role === 'guru') {
        $guru = new Guru();
        if (!$guru->setStatusByNama($user->nama_lengkap, 'nonaktif')) {
            throw new Exception("Gagal mengubah status guru!");
        }
    }

    // Delete the user account
    if (!$user->delete()) {
        throw new Exception("Gagal menghapus akun!");
    }

    // If everything is successful, commit the transaction
    $conn->commit();
    $_SESSION['message'] = "Akun berhasil dihapus!";
} catch (Exception $e) {
    // If there's an error, rollback the changes
    $conn->rollBack();
    $_SESSION['error'] = $e->getMessage();
}

header("Location: index.php");
exit();
?>
