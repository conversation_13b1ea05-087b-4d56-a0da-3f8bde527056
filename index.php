<?php
require_once 'template/header.php';
require_once 'models/Kelas.php';
require_once 'models/Siswa.php';
require_once 'models/JadwalPelajaran.php';
require_once 'models/Absensi.php';
require_once 'models/MataPelajaran.php';
require_once 'models/User.php';
require_once 'models/DetailJadwalJam.php';
require_once 'models/Guru.php';
require_once 'models/Berita.php';
require_once 'models/PeriodeAktif.php';

// Get counts
$kelas = new Kelas();
$siswa = new Siswa();
$jadwal = new JadwalPelajaran();
$absensi = new Absensi();
$mapel = new MataPelajaran();
$user = new User();
$detailJadwalJam = new DetailJadwalJam();
$guru = new Guru();
$berita = new Berita();
$periodeAktif = new PeriodeAktif();

// Get active period
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?? null;
$current_semester = $periodeAktif->semester ?? null;

$total_kelas = $kelas->getCount();
$total_siswa = $siswa->getCount();
$total_mapel = $mapel->getCount();
$total_guru = $guru->getCount(); // Get total guru

// Get guru_id if user is a teacher
$guru_id = null;
if(isset($_SESSION['user_id']) && $_SESSION['role'] == 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get latest news
$latest_news = $berita->getAll(5);
?>

<?php if($_SESSION['role'] == 'guru'): ?>
    <!-- Teacher Dashboard -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Dashboard Guru</h5>
                </div>
                <div class="card-body px-2 px-md-3">
                    <!-- Latest News Section -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Berita Terbaru</h5>
                                    <a href="berita/" class="btn btn-primary btn-sm">
                                        <i class="fas fa-newspaper"></i> Kelola Berita
                                    </a>
                                </div>
                                <div class="card-body">
                                    <?php if ($latest_news && $latest_news->rowCount() > 0): ?>
                                        <div class="list-group">
                                            <?php while ($news = $latest_news->fetch(PDO::FETCH_ASSOC)): ?>
                                                <a href="berita/view.php?id=<?php echo $news['id']; ?>" class="list-group-item list-group-item-action">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($news['judul']); ?></h6>
                                                        <small class="text-muted">
                                                            <?php echo date('d/m/Y', strtotime($news['created_at'])); ?>
                                                        </small>
                                                    </div>
                                                    <small class="text-muted">
                                                        Oleh: <?php echo htmlspecialchars($news['nama_pembuat']); ?>
                                                    </small>
                                                </a>
                                            <?php endwhile; ?>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted text-center mb-0">Belum ada berita</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Jadwal Mengajar Hari Ini</h5>
                                </div>
                                <div class="card-body p-0 p-md-3">
                                    <div class="table-responsive">
                                        <table class="table table-hover table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Jam</th>
                                                    <th>Mata Pelajaran</th>
                                                    <th>Kelas</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $today_schedule = $jadwal->getTodaySchedule($guru_id, $current_semester, $current_tahun_ajaran);
                                                while ($row = $today_schedule->fetch(PDO::FETCH_ASSOC)):
                                                    $details = $detailJadwalJam->getByJadwalId($row['id']);
                                                    $jam_ranges = [];
                                                    $current_range = [];
                                                    $prev_jam = null;
                                                    
                                                    while ($detail = $details->fetch(PDO::FETCH_ASSOC)) {
                                                        if ($prev_jam === null) {
                                                            $current_range = [$detail];
                                                        } else {
                                                            $prev_detail = end($current_range);
                                                            // Check if times are continuous
                                                            if ($detail['jam_ke'] == $prev_jam + 1 && 
                                                                $prev_detail['jam_selesai'] == $detail['jam_mulai']) {
                                                                $current_range[] = $detail;
                                                            } else {
                                                                if (!empty($current_range)) {
                                                                    $jam_ranges[] = $current_range;
                                                                }
                                                                $current_range = [$detail];
                                                            }
                                                        }
                                                        $prev_jam = $detail['jam_ke'];
                                                    }
                                                    if (!empty($current_range)) {
                                                        $jam_ranges[] = $current_range;
                                                    }
                                                    
                                                    $jam_display = [];
                                                    foreach ($jam_ranges as $range) {
                                                        $start = reset($range);
                                                        $end = end($range);
                                                        if (count($range) > 1) {
                                                            $jam_display[] = "Jam ke-" . $start['jam_ke'] . "-" . $end['jam_ke'] . 
                                                                           " (" . substr($start['jam_mulai'], 0, 5) . "-" . 
                                                                           substr($end['jam_selesai'], 0, 5) . ")";
                                                        } else {
                                                            $jam_display[] = "Jam ke-" . $start['jam_ke'] . 
                                                                           " (" . substr($start['jam_mulai'], 0, 5) . "-" . 
                                                                           substr($start['jam_selesai'], 0, 5) . ")";
                                                        }
                                                    }
                                                ?>
                                                <tr>
                                                    <td><?php echo implode("<br>", $jam_display); ?></td>
                                                    <td><?php echo htmlspecialchars($row['nama_mapel']); ?></td>
                                                    <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                                    <td>
                                                        <?php if ($absensi->getByJadwalAndDate($row['id'], date('Y-m-d'))): ?>
                                                        <a href="/absen/absensi/edit.php?id=<?php echo $absensi->getByJadwalAndDate($row['id'], date('Y-m-d')); ?>" class="btn btn-warning btn-sm">
                                                            <i class="fas fa-edit"></i> Edit Absensi
                                                        </a>
                                                        <?php else: ?>
                                                        <a href="/absen/absensi/create.php?jadwal_id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-clipboard-check"></i> Input Absensi
                                                        </a>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions for Teachers -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Aksi Cepat</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-2">
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/jadwal" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-calendar-alt mb-2"></i>
                                                <span class="text-wrap text-center">Lihat Jadwal Lengkap</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/absensi" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-clipboard-list mb-2"></i>
                                                <span class="text-wrap text-center">Absensi</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/nilai" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-star mb-2"></i>
                                                <span class="text-wrap text-center">Input Nilai</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/tugas" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-tasks mb-2"></i>
                                                <span class="text-wrap text-center">Tugas</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Dashboard Sistem Absensi Siswa</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-md-3 mb-4">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <h3 class="display-4"><?php echo $total_kelas; ?></h3>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>Total Kelas</div>
                                        <i class="fas fa-school fa-2x"></i>
                                    </div>
                                    <a href="kelas" class="text-white">Lihat Detail <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-3 mb-4">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <h3 class="display-4"><?php echo $total_siswa; ?></h3>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>Total Siswa</div>
                                        <i class="fas fa-user-graduate fa-2x"></i>
                                    </div>
                                    <a href="siswa" class="text-white">Lihat Detail <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-3 mb-4">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <h3 class="display-4"><?php echo $total_mapel; ?></h3>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>Mata Pelajaran</div>
                                        <i class="fas fa-book fa-2x"></i>
                                    </div>
                                    <a href="mapel" class="text-white">Lihat Detail <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-3 mb-4">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <h3 class="display-4"><?php echo $total_guru; ?></h3>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>Total Guru</div>
                                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                                    </div>
                                    <a href="guru" class="text-white">Lihat Detail <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Aksi Cepat</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-2">
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/absensi/create.php" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-plus mb-2"></i>
                                                <span class="text-wrap text-center">Input Absensi</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/siswa/import.php" class="btn btn-success btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-file-excel mb-2"></i>
                                                <span class="text-wrap text-center">Import Data Siswa</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/jadwal/create.php" class="btn btn-info btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-calendar-plus mb-2"></i>
                                                <span class="text-wrap text-center">Tambah Jadwal</span>
                                            </a>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <a href="/absen/laporan" class="btn btn-warning btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                                <i class="fas fa-print mb-2"></i>
                                                <span class="text-wrap text-center">Cetak Laporan</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Jadwal Pelajaran Hari Ini</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Waktu</th>
                                                    <th>Kelas</th>
                                                    <th>Mata Pelajaran</th>
                                                    <th>Guru Pengampu</th>
                                                    <th>Status</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $today_schedule = $jadwal->getAllTodaySchedule($current_semester, $current_tahun_ajaran);
                                                if ($today_schedule && $today_schedule->rowCount() > 0):
                                                    while ($row = $today_schedule->fetch(PDO::FETCH_ASSOC)):
                                                        $details = $detailJadwalJam->getByJadwalId($row['id']);
                                                        $jam_ranges = [];
                                                        $current_range = [];
                                                        $prev_jam = null;
                                                        
                                                        while ($detail = $details->fetch(PDO::FETCH_ASSOC)) {
                                                            if ($prev_jam === null) {
                                                                $current_range = [$detail];
                                                            } else {
                                                                $prev_detail = end($current_range);
                                                                if ($detail['jam_ke'] == $prev_jam + 1 && 
                                                                    $prev_detail['jam_selesai'] == $detail['jam_mulai']) {
                                                                    $current_range[] = $detail;
                                                                } else {
                                                                    if (!empty($current_range)) {
                                                                        $jam_ranges[] = $current_range;
                                                                    }
                                                                    $current_range = [$detail];
                                                                }
                                                            }
                                                            $prev_jam = $detail['jam_ke'];
                                                        }
                                                        if (!empty($current_range)) {
                                                            $jam_ranges[] = $current_range;
                                                        }
                                                        
                                                        $jam_display = [];
                                                        foreach ($jam_ranges as $range) {
                                                            $start = reset($range);
                                                            $end = end($range);
                                                            $jam_display[] = sprintf(
                                                                "%s - %s (Jam ke-%d-%d)",
                                                                $start['jam_mulai'],
                                                                $end['jam_selesai'],
                                                                $start['jam_ke'],
                                                                $end['jam_ke']
                                                            );
                                                        }

                                                        // Get absensi status
                                                        $absensi_status = $absensi->getStatusByJadwalId($row['id'], date('Y-m-d'));
                                                        $status_class = '';
                                                        $status_text = 'Belum Input';
                                                        
                                                        if ($absensi_status) {
                                                            $status_class = 'success';
                                                            $status_text = 'Sudah Input';
                                                        }
                                                ?>
                                                <tr>
                                                    <td><?php echo implode("<br>", $jam_display); ?></td>
                                                    <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                                    <td><?php echo htmlspecialchars($row['nama_mapel']); ?></td>
                                                    <td><?php echo htmlspecialchars($row['nama_guru']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $status_class ?: 'warning'; ?>">
                                                            <?php echo $status_text; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        if ($absensi_status) {
                                                            // Get the absensi ID first
                                                            $absensi_id = $absensi->getAbsensiId($row['kelas_id'], $row['mapel_id'], date('Y-m-d'));
                                                            if ($absensi_id) {
                                                        ?>
                                                            <a href="absensi/view.php?id=<?php echo $absensi_id; ?>" 
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i> Lihat
                                                            </a>
                                                        <?php 
                                                            }
                                                        }
                                                        ?>
                                                    </td>
                                                </tr>
                                                <?php 
                                                    endwhile;
                                                else:
                                                ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">Tidak ada jadwal pelajaran hari ini</td>
                                                </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
require_once 'template/footer.php';
?>
