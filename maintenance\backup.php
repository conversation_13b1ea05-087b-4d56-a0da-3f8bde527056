<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../template/header.php';

// Hanya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Dapatkan kredensial database dari kelas Database
        $database = new Database();
        $host = $database->getHost();
        $username = $database->getUsername();
        $password = $database->getPassword();
        $db_name = $database->getDatabase();

        // Periksa apakah ada tabel yang dipilih
        if (!isset($_POST['tables']) || empty($_POST['tables'])) {
            throw new Exception("Silakan pilih minimal satu tabel untuk di-backup.");
        }

        // Buat direktori backup jika belum ada
        $backup_dir = __DIR__ . '/../database/backups/';
        if (!file_exists($backup_dir)) {
            mkdir($backup_dir, 0777, true);
        }

        // Buat nama file backup dengan timestamp
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = "backup_{$timestamp}.sql";
        $backup_path = $backup_dir . $backup_file;

        try {
            // Set timeout lebih lama untuk proses backup
            set_time_limit(300); // 5 menit
            ini_set('memory_limit', '512M'); // Tambah memory limit jika diperlukan

            // Mulai membuat backup
            $fp = fopen($backup_path, 'w');
            if (!$fp) {
                throw new Exception("Tidak dapat membuat file backup");
            }

            // Tulis header SQL
            fwrite($fp, "-- PHP MySQL Backup\n");
            fwrite($fp, "-- Generated: " . date('Y-m-d H:i:s') . "\n\n");
            fwrite($fp, "SET FOREIGN_KEY_CHECKS=0;\n\n");
            
            $pdo = $database->getConnection();
            
            foreach ($_POST['tables'] as $table) {
                // Check if this is a view or a table
                $stmt = $pdo->prepare("SELECT TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?");
                $stmt->execute([$db_name, $table]);
                $table_info = $stmt->fetch(PDO::FETCH_ASSOC);

                $is_view = ($table_info && $table_info['TABLE_TYPE'] === 'VIEW');

                if ($is_view) {
                    // Handle views - only backup the view definition
                    $stmt = $pdo->prepare("SHOW CREATE VIEW `$table`");
                    $stmt->execute();
                    $row = $stmt->fetch(PDO::FETCH_NUM);

                    fwrite($fp, "DROP VIEW IF EXISTS `$table`;\n");
                    fwrite($fp, $row[1] . ";\n\n");

                    // Skip data insertion for views - views don't contain data
                    continue;
                } else {
                    // Handle regular tables
                    $stmt = $pdo->prepare("SHOW CREATE TABLE `$table`");
                    $stmt->execute();
                    $row = $stmt->fetch(PDO::FETCH_NUM);

                    fwrite($fp, "DROP TABLE IF EXISTS `$table`;\n");
                    fwrite($fp, $row[1] . ";\n\n");

                    // Get table data in batches to handle large tables
                    $stmt = $pdo->prepare("SELECT * FROM `$table`");
                    $stmt->execute();

                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        $values = array_map(function($value) use ($pdo) {
                            if ($value === null) {
                                return 'NULL';
                            }
                            return $pdo->quote($value);
                        }, $row);

                        $sql = "INSERT INTO `$table` VALUES (" . implode(',', $values) . ");\n";
                        if (fwrite($fp, $sql) === false) {
                            throw new Exception("Gagal menulis data ke file backup");
                        }
                    }
                }

                fwrite($fp, "\n"); // Add newline between tables/views
            }
            
            fwrite($fp, "\nSET FOREIGN_KEY_CHECKS=1;\n");
            fclose($fp);
            
            $message = "Database berhasil dibackup: {$backup_file}";
            error_log("Backup berhasil dibuat: {$backup_path}");
            
        } catch (Exception $e) {
            error_log("Error backup database: " . $e->getMessage());
            $error = "Gagal membuat backup database. Error: " . $e->getMessage();
            
            // Hapus file backup jika ada error
            if (isset($fp) && is_resource($fp)) {
                fclose($fp);
            }
            if (file_exists($backup_path)) {
                unlink($backup_path);
            }
            throw $e;
        }
    } catch (Exception $e) {
        error_log("Error di backup.php: " . $e->getMessage());
        $error = "Error saat membuat backup: " . $e->getMessage();
    }
}

// Dapatkan daftar tabel dari database
try {
    $database = new Database();
    $pdo = $database->getConnection();
    $tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
} catch (PDOException $e) {
    error_log("Error mengambil daftar tabel: " . $e->getMessage());
    $error = "Error mengambil daftar tabel: " . $e->getMessage();
    $tables = [];
}

// Periksa pesan sesi
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    unset($_SESSION['success']);
}
if (isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Backup Database</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post" id="backupForm">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6>Pilih Tabel untuk Backup:</h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllTables(true)">Pilih Semua</button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllTables(false)">Batalkan Semua</button>
                                </div>
                            </div>
                            <div class="table-selection border p-3" style="max-height: 300px; overflow-y: auto;">
                                <?php foreach ($tables as $table): ?>
                                <div class="form-check">
                                    <input class="form-check-input table-checkbox" type="checkbox" name="tables[]" value="<?php echo htmlspecialchars($table); ?>" id="table_<?php echo htmlspecialchars($table); ?>">
                                    <label class="form-check-label" for="table_<?php echo htmlspecialchars($table); ?>">
                                        <?php echo htmlspecialchars($table); ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" onclick="return validateForm()">
                            <i class="fas fa-download"></i> Backup Database
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Informasi Backup -->
    <div class="card mt-4">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-info-circle"></i> Informasi Backup
            </h5>
            <div class="alert alert-info">
                <h6>Tentang Backup Database</h6>
                <ul>
                    <li>Backup mencakup struktur dan data dari semua tabel</li>
                    <li>File backup disimpan dengan format: <code>backup_YYYY-MM-DD_HH-mm-ss.sql</code></li>
                    <li>Lokasi backup: <code>database/backups/</code></li>
                    <li>Backup termasuk:
                        <ul>
                            <li>Struktur tabel</li>
                            <li>Data tabel</li>
                            <li>Stored procedures dan functions</li>
                            <li>Events</li>
                            <li>Triggers</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="alert alert-warning">
                <h6>Penting!</h6>
                <ul>
                    <li>Pastikan ada cukup ruang disk sebelum membuat backup</li>
                    <li>Backup secara rutin untuk menghindari kehilangan data</li>
                    <li>Simpan backup di lokasi yang aman</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function selectAllTables(select) {
    document.querySelectorAll('.table-checkbox').forEach(checkbox => {
        checkbox.checked = select;
    });
}

function validateForm() {
    const checkedBoxes = document.querySelectorAll('.table-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Silakan pilih minimal satu tabel untuk di-backup.');
        return false;
    }
    return true;
}
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
