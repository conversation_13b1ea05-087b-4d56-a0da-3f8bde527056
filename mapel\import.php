<?php
require_once '../template/header.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tingkat.php';
require_once '../models/Jurusan.php';
require_once '../models/Guru.php';
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $file = $_FILES['file']['tmp_name'];
        $reader = new Xlsx();
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];

        $tingkatModel = new Tingkat();
        $jurusanModel = new Jurusan();
        $guruModel = new Guru();

        foreach ($worksheet->getRowIterator(2) as $row) {
            $rowData = [];
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }

            // Skip empty rows
            if (empty($cells[0]) && empty($cells[1])) {
                continue;
            }

            // Get tingkat_id based on nama_tingkat
            $tingkat = $tingkatModel->getByNamaTingkat($cells[2]);
            $tingkat_id = $tingkat ? $tingkat['id'] : null;

            // Get jurusan_ids based on nama_jurusan (multiple)
            $jurusan_ids = [];
            if (!empty($cells[3])) {
                $jurusan_names = array_map('trim', explode(',', $cells[3]));
                foreach ($jurusan_names as $nama_jurusan) {
                    $jurusan = $jurusanModel->getByNamaJurusan($nama_jurusan);
                    if ($jurusan) {
                        $jurusan_ids[] = $jurusan['id'];
                    }
                }
            }

            // Get guru_id based on nama_guru (multiple)
            $guru_ids = [];
            if (!empty($cells[4])) {
                $guru_names = array_map('trim', explode(',', $cells[4]));
                foreach ($guru_names as $nama_guru) {
                    $guru = $guruModel->getByNama($nama_guru);
                    if ($guru) {
                        $guru_ids[] = $guru['id'];
                    }
                }
            }

            $rowData = [
                'kode_mapel' => $cells[0],
                'nama_mapel' => $cells[1],
                'kkm' => isset($cells[5]) && is_numeric($cells[5]) ? $cells[5] : 75, // KKM dari kolom ke-6, default 75
                'tingkat_id' => $tingkat_id,
                'jurusan_ids' => $jurusan_ids,
                'guru_ids' => $guru_ids
            ];
            
            $data[] = $rowData;
        }

        $mapel = new MataPelajaran();
        $success = true;

        try {
            foreach ($data as $row) {
                $mapel->kode_mapel = $row['kode_mapel'];
                $mapel->nama_mapel = $row['nama_mapel'];
                $mapel->kkm = $row['kkm'];
                $mapel->tingkat_id = $row['tingkat_id'];

                if ($mapel->create()) {
                    if (!empty($row['guru_ids'])) {
                        $mapel->updateGuruPengampu($mapel->id, $row['guru_ids']);
                    }
                    if (!empty($row['jurusan_ids'])) {
                        $mapel->updateJurusan($mapel->id, $row['jurusan_ids']);
                    }
                } else {
                    $success = false;
                    break;
                }
            }

            if ($success) {
                header("Location: index.php?success=1");
                exit;
            } else {
                $error = "Gagal mengimpor data";
            }
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Import Data Mata Pelajaran</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">File Excel</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        <div class="form-text">
                            Format file: .xlsx<br>
                            Kolom yang dibutuhkan:
                            <ul>
                                <li>Kode Mapel (kolom A)</li>
                                <li>Nama Mapel (kolom B)</li>
                                <li>Tingkat (kolom C)</li>
                                <li>Jurusan (kolom D, pisahkan dengan koma jika lebih dari satu)</li>
                                <li>Guru Pengampu (kolom E, pisahkan dengan koma jika lebih dari satu)</li>
                            </ul>
                            <a href="template_mapel.xlsx" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
