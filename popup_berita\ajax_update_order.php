<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $berita_id = $_POST['berita_id'] ?? '';
    $order = $_POST['order'] ?? 0;

    if (empty($berita_id)) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
        exit();
    }

    $popupBerita = new PopupBerita();

    try {
        $result = $popupBerita->updateBeritaOrder($berita_id, $order);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Berhasil']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal memproses permintaan']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
}
?>
