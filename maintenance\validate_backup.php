<?php
/**
 * Backup Validation Tool
 * Validates backup files before restoration to prevent common errors
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';

// Only admin can access this tool
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

class BackupValidator {
    private $errors = [];
    private $warnings = [];
    private $info = [];
    
    public function validateFile($file_path) {
        $this->errors = [];
        $this->warnings = [];
        $this->info = [];
        
        if (!file_exists($file_path)) {
            $this->errors[] = "File tidak ditemukan: {$file_path}";
            return false;
        }
        
        if (!is_readable($file_path)) {
            $this->errors[] = "File tidak dapat dibaca: {$file_path}";
            return false;
        }
        
        $file_size = filesize($file_path);
        $this->info[] = "Ukuran file: " . number_format($file_size / 1024, 2) . " KB";
        
        // Read file content
        $content = file_get_contents($file_path);
        if ($content === false) {
            $this->errors[] = "Gagal membaca isi file";
            return false;
        }
        
        // Check for common issues
        $this->checkViewHandling($content);
        $this->checkSqlSyntax($content);
        $this->checkForeignKeySettings($content);
        $this->checkCharacterSet($content);
        $this->analyzeStructure($content);
        
        return empty($this->errors);
    }
    
    private function checkViewHandling($content) {
        // Check for incorrect DROP TABLE on views
        $view_names = ['v_siswa_all_periods', 'v_siswa_current'];
        
        foreach ($view_names as $view_name) {
            if (preg_match("/DROP TABLE IF EXISTS `{$view_name}`/", $content)) {
                $this->errors[] = "Ditemukan 'DROP TABLE' untuk view '{$view_name}' - seharusnya 'DROP VIEW'";
            }
            
            if (preg_match("/INSERT INTO `{$view_name}`/", $content)) {
                $this->errors[] = "Ditemukan 'INSERT INTO' untuk view '{$view_name}' - views tidak bisa menerima INSERT";
            }
            
            if (preg_match("/DROP VIEW IF EXISTS `{$view_name}`/", $content)) {
                $this->info[] = "View '{$view_name}' ditangani dengan benar";
            }
        }
    }
    
    private function checkSqlSyntax($content) {
        // Check for basic SQL syntax issues
        $lines = explode("\n", $content);
        $line_number = 0;
        
        foreach ($lines as $line) {
            $line_number++;
            $line = trim($line);
            
            if (empty($line) || strpos($line, '--') === 0) {
                continue; // Skip empty lines and comments
            }
            
            // Check for unterminated statements
            if (preg_match('/^(CREATE|INSERT|UPDATE|DELETE|ALTER|DROP)/', $line) && 
                !preg_match('/;$/', $line) && 
                !preg_match('/\$$/', $line)) { // Allow for stored procedures
                
                // Look ahead to see if the statement continues
                $next_lines = array_slice($lines, $line_number, 5);
                $continues = false;
                foreach ($next_lines as $next_line) {
                    if (preg_match('/;$/', trim($next_line))) {
                        $continues = true;
                        break;
                    }
                }
                
                if (!$continues) {
                    $this->warnings[] = "Baris {$line_number}: Statement mungkin tidak diakhiri dengan semicolon";
                }
            }
        }
    }
    
    private function checkForeignKeySettings($content) {
        if (!preg_match('/SET FOREIGN_KEY_CHECKS\s*=\s*0/', $content)) {
            $this->warnings[] = "Tidak ditemukan 'SET FOREIGN_KEY_CHECKS=0' - mungkin akan ada masalah foreign key";
        }
        
        if (!preg_match('/SET FOREIGN_KEY_CHECKS\s*=\s*1/', $content)) {
            $this->warnings[] = "Tidak ditemukan 'SET FOREIGN_KEY_CHECKS=1' - foreign key checks tidak diaktifkan kembali";
        }
    }
    
    private function checkCharacterSet($content) {
        if (preg_match('/utf8mb4/', $content)) {
            $this->info[] = "Menggunakan character set utf8mb4 (recommended)";
        } elseif (preg_match('/utf8/', $content)) {
            $this->warnings[] = "Menggunakan character set utf8 - pertimbangkan upgrade ke utf8mb4";
        }
    }
    
    private function analyzeStructure($content) {
        // Count tables and views
        $table_count = preg_match_all('/CREATE TABLE/', $content);
        $view_count = preg_match_all('/CREATE.*VIEW/', $content);
        $insert_count = preg_match_all('/INSERT INTO/', $content);
        
        $this->info[] = "Struktur backup: {$table_count} tabel, {$view_count} view, {$insert_count} INSERT statements";
        
        if ($table_count < 5) {
            $this->warnings[] = "Jumlah tabel sedikit ({$table_count}) - pastikan backup lengkap";
        }
        
        if ($view_count < 2) {
            $this->warnings[] = "Jumlah view sedikit ({$view_count}) - mungkin ada view yang hilang";
        }
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getWarnings() {
        return $this->warnings;
    }
    
    public function getInfo() {
        return $this->info;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
}

// Handle AJAX validation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['validate_file'])) {
    header('Content-Type: application/json');
    
    $file_name = basename($_POST['validate_file']);
    $file_path = __DIR__ . '/../database/backups/' . $file_name;
    
    $validator = new BackupValidator();
    $is_valid = $validator->validateFile($file_path);
    
    echo json_encode([
        'valid' => $is_valid,
        'errors' => $validator->getErrors(),
        'warnings' => $validator->getWarnings(),
        'info' => $validator->getInfo()
    ]);
    exit();
}

require_once __DIR__ . '/../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Validasi File Backup</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        Tool ini memvalidasi file backup sebelum proses restore untuk mencegah error umum seperti konflik view dan tabel.
                    </div>

                    <h6>File Backup yang Tersedia</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>File Backup</th>
                                    <th>Ukuran</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Status Validasi</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $backup_dir = __DIR__ . '/../database/backups/';
                                $backup_files = glob($backup_dir . '*.sql');
                                
                                if (!empty($backup_files)) {
                                    rsort($backup_files); // Sort from newest
                                    foreach ($backup_files as $file) {
                                        $filename = basename($file);
                                        $size = filesize($file);
                                        $created = date("d-m-Y H:i:s", filemtime($file));
                                        
                                        echo "<tr>";
                                        echo "<td>{$filename}</td>";
                                        echo "<td>" . number_format($size / 1024, 2) . " KB</td>";
                                        echo "<td>{$created}</td>";
                                        echo "<td><span class='validation-status' data-file='{$filename}'>Belum divalidasi</span></td>";
                                        echo "<td>";
                                        echo "<button class='btn btn-sm btn-primary validate-btn' data-file='{$filename}'>";
                                        echo "<i class='fas fa-check-circle'></i> Validasi</button>";
                                        echo "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='5' class='text-center'>Tidak ada file backup tersedia.</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Validation Results -->
                    <div id="validation-results" class="mt-4" style="display: none;">
                        <h6>Hasil Validasi</h6>
                        <div id="validation-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.validate-btn').click(function() {
        const filename = $(this).data('file');
        const statusCell = $(`.validation-status[data-file="${filename}"]`);
        const button = $(this);
        
        // Show loading
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Validating...');
        statusCell.html('<span class="text-info">Memvalidasi...</span>');
        
        $.post('validate_backup.php', {
            validate_file: filename
        }, function(response) {
            displayValidationResults(filename, response);
            
            // Update status
            if (response.valid) {
                statusCell.html('<span class="text-success"><i class="fas fa-check-circle"></i> Valid</span>');
            } else {
                statusCell.html('<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Ada masalah</span>');
            }
            
            button.prop('disabled', false).html('<i class="fas fa-check-circle"></i> Validasi');
        }).fail(function() {
            statusCell.html('<span class="text-danger">Error validasi</span>');
            button.prop('disabled', false).html('<i class="fas fa-check-circle"></i> Validasi');
        });
    });
});

function displayValidationResults(filename, result) {
    let html = `<div class="card">
        <div class="card-header">
            <h6 class="mb-0">Hasil Validasi: ${filename}</h6>
        </div>
        <div class="card-body">`;
    
    if (result.errors.length > 0) {
        html += '<div class="alert alert-danger"><h6>Errors (Harus diperbaiki):</h6><ul>';
        result.errors.forEach(error => {
            html += `<li>${error}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (result.warnings.length > 0) {
        html += '<div class="alert alert-warning"><h6>Warnings (Perlu perhatian):</h6><ul>';
        result.warnings.forEach(warning => {
            html += `<li>${warning}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (result.info.length > 0) {
        html += '<div class="alert alert-info"><h6>Informasi:</h6><ul>';
        result.info.forEach(info => {
            html += `<li>${info}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (result.valid) {
        html += '<div class="alert alert-success"><i class="fas fa-check-circle"></i> File backup valid dan siap untuk restore!</div>';
    } else {
        html += '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> File backup memiliki masalah yang harus diperbaiki sebelum restore.</div>';
    }
    
    html += '</div></div>';
    
    $('#validation-content').html(html);
    $('#validation-results').show();
}
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
