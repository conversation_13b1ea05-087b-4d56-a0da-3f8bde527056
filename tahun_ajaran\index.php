<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/TahunAjaran.php';
require_once __DIR__ . '/../template/header.php';

$tahun_ajaran = new TahunAjaran();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['add_tahun_ajaran'])) {
        $tahun_awal = $_POST['tahun_awal'];
        $tahun_akhir = $tahun_awal + 1;
        $ta = $tahun_awal . '/' . $tahun_akhir;
        
        if(!$tahun_ajaran->exists($ta)) {
            $tahun_ajaran->tahun_ajaran = $ta;
            $tahun_ajaran->semester_1_mulai = $_POST['semester_1_mulai'];
            $tahun_ajaran->semester_1_selesai = $_POST['semester_1_selesai'];
            $tahun_ajaran->semester_2_mulai = $_POST['semester_2_mulai'];
            $tahun_ajaran->semester_2_selesai = $_POST['semester_2_selesai'];
            
            if($tahun_ajaran->create()) {
                $_SESSION['success'] = "Tahun ajaran berhasil ditambahkan!";
                header("Location: index.php");
                exit();
            } else {
                $_SESSION['error'] = "Gagal menambahkan tahun ajaran. Pastikan tanggal sesuai urutan: Semester 1 Mulai < Semester 1 Selesai < Semester 2 Mulai < Semester 2 Selesai";
            }
        } else {
            $_SESSION['error'] = "Tahun ajaran sudah ada!";
        }
    }
    
    if(isset($_POST['delete_tahun_ajaran'])) {
        $tahun_ajaran->id = $_POST['id'];
        if($tahun_ajaran->delete()) {
            $_SESSION['success'] = "Tahun ajaran berhasil dihapus!";
            header("Location: index.php");
            exit();
        }
    }
}

// Get all tahun ajaran
$result = $tahun_ajaran->getAll();

// Get default dates
$current_year = date('Y');
$default_dates = $tahun_ajaran->getDefaultDates($current_year);

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Manajemen Tahun Ajaran</h5>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Tambah Tahun Ajaran</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" class="row g-3">
                                    <div class="col-md-6">
                                        <label for="tahun_awal" class="form-label">Tahun Awal</label>
                                        <input type="number" class="form-control" id="tahun_awal" name="tahun_awal" 
                                               min="2000" max="2100" value="<?php echo $current_year; ?>" required>
                                        <div class="form-text">
                                            Format: YYYY (contoh: <?php echo $current_year; ?>)
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Tahun Akhir</label>
                                        <input type="text" class="form-control" id="tahun_akhir" 
                                               value="<?php echo $current_year + 1; ?>" disabled>
                                        <div class="form-text">
                                            Otomatis tahun berikutnya
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <h6 class="mt-3">Semester 1</h6>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="semester_1_mulai" class="form-label">Tanggal Mulai</label>
                                        <input type="date" class="form-control" id="semester_1_mulai" name="semester_1_mulai" 
                                               value="<?php echo $default_dates['semester_1_mulai']; ?>" required>
                                        <div class="form-text">
                                            Default: 1 Juli
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="semester_1_selesai" class="form-label">Tanggal Selesai</label>
                                        <input type="date" class="form-control" id="semester_1_selesai" name="semester_1_selesai" 
                                               value="<?php echo $default_dates['semester_1_selesai']; ?>" required>
                                        <div class="form-text">
                                            Default: 31 Desember
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <h6 class="mt-3">Semester 2</h6>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="semester_2_mulai" class="form-label">Tanggal Mulai</label>
                                        <input type="date" class="form-control" id="semester_2_mulai" name="semester_2_mulai" 
                                               value="<?php echo $default_dates['semester_2_mulai']; ?>" required>
                                        <div class="form-text">
                                            Default: 1 Januari
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="semester_2_selesai" class="form-label">Tanggal Selesai</label>
                                        <input type="date" class="form-control" id="semester_2_selesai" name="semester_2_selesai" 
                                               value="<?php echo $default_dates['semester_2_selesai']; ?>" required>
                                        <div class="form-text">
                                            Default: 30 Juni
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <button type="submit" name="add_tahun_ajaran" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Tambah
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Daftar Tahun Ajaran</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="tableTahunAjaran">
                                        <thead>
                                            <tr>
                                                <th>No</th>
                                                <th>Tahun Ajaran</th>
                                                <th>Semester 1</th>
                                                <th>Semester 2</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $no = 1;
                                            while($row = $result->fetch(PDO::FETCH_ASSOC)):
                                            ?>
                                            <tr>
                                                <td><?php echo $no++; ?></td>
                                                <td><?php echo $row['tahun_ajaran']; ?></td>
                                                <td><?php echo $tahun_ajaran->getSemesterPeriod(1, $row); ?></td>
                                                <td><?php echo $tahun_ajaran->getSemesterPeriod(2, $row); ?></td>
                                                <td>
                                                    <form method="POST" action="" style="display: inline;">
                                                        <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                                        <button type="submit" name="delete_tahun_ajaran" 
                                                                class="btn btn-sm btn-danger"
                                                                onclick="return confirm('Yakin ingin menghapus tahun ajaran ini?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableTahunAjaran').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});

document.getElementById('tahun_awal').addEventListener('input', function() {
    var tahunAwal = parseInt(this.value) || 0;
    var tahunAkhir = tahunAwal + 1;
    document.getElementById('tahun_akhir').value = tahunAkhir;
    
    // Update default dates
    if(tahunAwal > 0) {
        document.getElementById('semester_1_mulai').value = tahunAwal + '-07-01';
        document.getElementById('semester_1_selesai').value = tahunAwal + '-12-31';
        document.getElementById('semester_2_mulai').value = tahunAkhir + '-01-01';
        document.getElementById('semester_2_selesai').value = tahunAkhir + '-06-30';
    }
});
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
