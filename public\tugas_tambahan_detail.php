<?php
require_once '../config/database.php';
require_once '../models/TugasTambahan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Siswa.php';
require_once '../template/public_header.php';

// Check if tugas_id and siswa_id are provided
if (!isset($_GET['tugas_id']) || !isset($_GET['siswa_id']) || !isset($_GET['nis'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['tugas_id'];
$siswa_id = $_GET['siswa_id'];
$nis = $_GET['nis'];

// Verify NIS matches siswa_id for security
$siswa = new Siswa();
$siswa->id = $siswa_id;
if (!$siswa->getOne() || $siswa->nis != $nis) {
    header("Location: index.php");
    exit();
}

// Get tugas tambahan details
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;
if (!$tugasTambahan->getOne()) {
    header("Location: index.php");
    exit();
}

// Get mapel details
$mapel = new MataPelajaran();
$mapel->id = $tugasTambahan->mapel_id;
$mapel->getOne();

// Get assignment status and grade for this student
$query = "SELECT status, nilai FROM tugas_tambahan_siswa
          WHERE tugas_tambahan_id = :tugas_id AND siswa_id = :siswa_id";
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare($query);
$stmt->bindParam(':tugas_id', $tugas_id);
$stmt->bindParam(':siswa_id', $siswa_id);
$stmt->execute();
$assignment_data = $stmt->fetch(PDO::FETCH_ASSOC);

$status = $assignment_data['status'];
$nilai = $assignment_data['nilai'];
$status_text = $status == 'sudah_dikerjakan' ? 'Sudah Dikerjakan' : 'Belum Dikerjakan';
$status_class = $status == 'sudah_dikerjakan' ? 'success' : 'warning';
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Detail Tugas Tambahan</h2>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold">
                        <i class="fas fa-clipboard-list me-2"></i> <?php echo $tugasTambahan->judul; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Mata Pelajaran</th>
                                    <td>: <?php echo $mapel->nama_mapel; ?></td>
                                </tr>
                                <tr>
                                    <th>Tanggal</th>
                                    <td>: <?php echo date('d-m-Y', strtotime($tugasTambahan->tanggal)); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Status</th>
                                    <td>: <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                                </tr>
                                <tr>
                                    <th>Nilai</th>
                                    <td>: <?php echo ($status == 'sudah_dikerjakan' && $nilai !== null) ? $nilai : '-'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold">Deskripsi Tugas</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($tugasTambahan->deskripsi)): ?>
                                        <p><?php echo nl2br($tugasTambahan->deskripsi); ?></p>
                                    <?php else: ?>
                                        <p class="text-muted">Tidak ada deskripsi tugas.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
