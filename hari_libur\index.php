<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/HariLibur.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';

$hariLibur = new HariLibur();
$periodeAktif = new PeriodeAktif();
$tahunAjaran = new TahunAjaran();

// Get active period
$periodeAktif->getActive();
$current_tahun_ajaran = $periodeAktif->tahun_ajaran ?? null;
$current_semester = $periodeAktif->semester ?? null;

// Get filter parameters
$filter_tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $current_tahun_ajaran;
$filter_semester = isset($_GET['semester']) ? $_GET['semester'] : $current_semester;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['tanggal']) && isset($_POST['keterangan']) && !isset($_POST['update_holiday'])) {
        $hariLibur->tanggal = $_POST['tanggal'];
        $hariLibur->keterangan = $_POST['keterangan'];
        $hariLibur->tahun_ajaran = $_POST['tahun_ajaran'] ?? $current_tahun_ajaran;
        $hariLibur->semester = $_POST['semester'] ?? $current_semester;

        if ($hariLibur->create()) {
            echo "<script>alert('Data berhasil disimpan!'); window.location.href='index.php';</script>";
            exit;
        } else {
            echo "<script>alert('Gagal menyimpan data!');</script>";
        }
    } elseif (isset($_POST['delete_id'])) {
        if ($hariLibur->delete($_POST['delete_id'])) {
            echo "<script>alert('Data berhasil dihapus!'); window.location.href='index.php';</script>";
            exit;
        }
    } elseif (isset($_POST['update_holiday'])) {
        $hariLibur->id = $_POST['holiday_id'];
        $hariLibur->tanggal = $_POST['edit_tanggal'];
        $hariLibur->keterangan = $_POST['edit_keterangan'];
        $hariLibur->tahun_ajaran = $_POST['edit_tahun_ajaran'];
        $hariLibur->semester = $_POST['edit_semester'];

        if ($hariLibur->update()) {
            echo "<script>alert('Data berhasil diperbarui!'); window.location.href='index.php';</script>";
            exit;
        } else {
            echo "<script>alert('Gagal memperbarui data!');</script>";
        }
    } elseif (isset($_POST['fetch_holidays'])) {
        $tahun_ajaran_id = $_POST['tahun_ajaran_id'];
        $semester = $_POST['semester'];

        // Get tahun ajaran data
        $result = $tahunAjaran->getById($tahun_ajaran_id);
        $ta_data = $result->fetch(PDO::FETCH_ASSOC);

        if (!$ta_data) {
            echo "<script>alert('Periode tidak ditemukan!');</script>";
            exit;
        }

        // Get semester dates
        $start_date = new DateTime($semester == 1 ? $ta_data['semester_1_mulai'] : $ta_data['semester_2_mulai']);
        $end_date = new DateTime($semester == 1 ? $ta_data['semester_1_selesai'] : $ta_data['semester_2_selesai']);

        $holidays = [];
        $unique_dates = []; // Track unique date+name combinations

        // Fetch holidays month by month
        $current_date = clone $start_date;
        while ($current_date <= $end_date) {
            $year = $current_date->format('Y');
            $month = $current_date->format('m');

            // Fetch holidays from API
            $database = new Database();
            $api_key = $database->getKalenderApiKey();
            $url = "https://kalenderindonesia.com/api/{$api_key}/kalender/masehi/{$year}/{$month}";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $response = curl_exec($ch);

            if(curl_errno($ch)) {
                continue; // Skip if error, try next month
            }

            curl_close($ch);

            $data = json_decode($response, true);

            if ($data && isset($data['data']) && isset($data['data']['dates'])) {
                foreach ($data['data']['dates'] as $day) {
                    if (isset($day['holidays']) && !empty($day['holidays'])) {
                        $holiday_date = new DateTime($day['masehi']);
                        // Only include holidays within semester range
                        if ($holiday_date >= $start_date && $holiday_date <= $end_date) {
                            foreach ($day['holidays'] as $holiday) {
                                $name = ucwords(str_replace('_', ' ', $holiday['name']));
                                $date = $day['masehi'];

                                // Check for duplicates using date+name combination
                                $unique_key = $date . '|' . $name;
                                if (!in_array($unique_key, $unique_dates)) {
                                    $unique_dates[] = $unique_key;
                                    $holidays[] = [
                                        'date' => $date,
                                        'name' => $name
                                    ];
                                }
                            }
                        }
                    }
                }
            }

            $current_date->modify('+1 month');
        }

        if (!empty($holidays)) {
            $_SESSION['api_holidays'] = $holidays;
            echo "<script>window.location.href='index.php?show_api=1';</script>";
            exit;
        }

        echo "<script>alert('Tidak ada hari libur untuk periode yang dipilih');</script>";
    } elseif (isset($_POST['save_selected_holidays'])) {
        if (isset($_POST['selected_holidays']) && is_array($_POST['selected_holidays'])) {
            $success = true;
            foreach ($_POST['selected_holidays'] as $holiday) {
                $holiday = json_decode($holiday, true);
                $hariLibur->tanggal = $holiday['date'];
                $hariLibur->keterangan = $holiday['name'];
                if (!$hariLibur->create()) {
                    $success = false;
                }
            }
            if ($success) {
                unset($_SESSION['api_holidays']);
                echo "<script>alert('Data berhasil disimpan!'); window.location.href='index.php';</script>";
                exit;
            }
        }
    } elseif (isset($_POST['update_holiday'])) {
        $id = $_POST['holiday_id'];
        $tanggal = $_POST['edit_tanggal'];
        $keterangan = $_POST['edit_keterangan'];

        $hariLibur->id = $id;
        $hariLibur->tanggal = $tanggal;
        $hariLibur->keterangan = $keterangan;

        if ($hariLibur->update()) {
            echo "<script>alert('Berhasil mengupdate hari libur!'); window.location.href='index.php';</script>";
        } else {
            echo "<script>alert('Gagal mengupdate hari libur!');</script>";
        }
    }
}

// Get holidays for current filter
$result = $hariLibur->getAll($filter_tahun_ajaran, $filter_semester);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Data Hari Libur</h5>
                <div>
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#fetchHolidaysModal">
                        <i class="fas fa-cloud-download-alt"></i> Ambil Data API
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                        <i class="fas fa-plus"></i> Tambah Manual
                    </button>
                </div>
            </div>
            <div class="card-body">

                <!-- Period Filter -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <form method="GET" class="d-flex align-items-end gap-3">
                            <div class="form-group">
                                <label class="form-label">Tahun Ajaran</label>
                                <select name="tahun_ajaran" class="form-select">
                                    <option value="">Semua Periode</option>
                                    <?php
                                    $tahun_ajaran_list = $tahunAjaran->getAll();
                                    while ($ta = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                        <option value="<?= $ta['tahun_ajaran'] ?>" <?= $filter_tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : '' ?>>
                                            <?= $ta['tahun_ajaran'] ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Semester</label>
                                <select name="semester" class="form-select">
                                    <option value="">Semua Semester</option>
                                    <option value="1" <?= $filter_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                                    <option value="2" <?= $filter_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-refresh"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (isset($_GET['show_api']) && isset($_SESSION['api_holidays'])): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        Data Hari Libur dari API
                    </div>
                    <div class="card-body">
                        <form method="POST" id="holidayForm">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">
                                        Pilih Semua
                                    </label>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Pilih</th>
                                            <th>Tanggal</th>
                                            <th>Keterangan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($_SESSION['api_holidays'] as $holiday): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="selected_holidays[]"
                                                       class="form-check-input holiday-checkbox"
                                                       value="<?= htmlspecialchars(json_encode($holiday)) ?>">
                                            </td>
                                            <td><?= htmlspecialchars($holiday['date']) ?></td>
                                            <td><?= htmlspecialchars($holiday['name']) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <button type="submit" name="save_selected_holidays" class="btn btn-primary">Simpan</button>
                        </form>
                    </div>
                </div>

                <script>
                document.getElementById('selectAll').addEventListener('change', function() {
                    const checkboxes = document.getElementsByClassName('holiday-checkbox');
                    for (let checkbox of checkboxes) {
                        checkbox.checked = this.checked;
                    }
                });

                // Update select all checkbox when individual checkboxes change
                const holidayCheckboxes = document.getElementsByClassName('holiday-checkbox');
                for (let checkbox of holidayCheckboxes) {
                    checkbox.addEventListener('change', function() {
                        const selectAll = document.getElementById('selectAll');
                        const allChecked = Array.from(holidayCheckboxes).every(cb => cb.checked);
                        selectAll.checked = allChecked;
                    });
                }
                </script>
                <?php endif; ?>

                <div class="mt-4">
                    <h6 class="mb-3 fw-bold">Data Tersimpan</h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="holidayTable">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Keterangan</th>
                                    <th>Periode</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                $hasData = false;
                                while ($row = $result->fetch(PDO::FETCH_ASSOC)):
                                    $hasData = true;
                                ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= date('d F Y', strtotime($row['tanggal'])) ?></td>
                                        <td><?= htmlspecialchars($row['keterangan']) ?></td>
                                        <td>
                                            <?php if ($row['tahun_ajaran'] && $row['semester']): ?>
                                                <span class="badge bg-info">
                                                    <?= $row['tahun_ajaran'] ?> - Sem <?= $row['semester'] ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Belum diset</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning"
                                                    onclick="editHoliday('<?= $row['id'] ?>', '<?= $row['tanggal'] ?>', '<?= htmlspecialchars($row['keterangan']) ?>', '<?= $row['tahun_ajaran'] ?>', '<?= $row['semester'] ?>')"
                                                    data-bs-toggle="modal" data-bs-target="#editModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirmDelete()">
                                                <input type="hidden" name="delete_id" value="<?= $row['id'] ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                        <?php if (!$hasData): ?>
                            <p class="text-center mt-3">Tidak ada data hari libur</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Holiday Modal -->
<div class="modal fade" id="addHolidayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Hari Libur Manual</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                    </div>
                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <input type="text" class="form-control" id="keterangan" name="keterangan" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                    <?php
                                    $tahun_ajaran_list = $tahunAjaran->getAll();
                                    while ($ta = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                        <option value="<?= $ta['tahun_ajaran'] ?>" <?= $current_tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : '' ?>>
                                            <?= $ta['tahun_ajaran'] ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="semester" class="form-label">Semester</label>
                                <select class="form-select" id="semester" name="semester" required>
                                    <option value="1" <?= $current_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                                    <option value="2" <?= $current_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Edit Hari Libur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="holiday_id" id="edit_holiday_id">
                    <div class="mb-3">
                        <label for="edit_tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="edit_tanggal" name="edit_tanggal" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_keterangan" class="form-label">Keterangan</label>
                        <input type="text" class="form-control" id="edit_keterangan" name="edit_keterangan" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                <select class="form-select" id="edit_tahun_ajaran" name="edit_tahun_ajaran" required>
                                    <?php
                                    $tahun_ajaran_list = $tahunAjaran->getAll();
                                    while ($ta = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                        <option value="<?= $ta['tahun_ajaran'] ?>">
                                            <?= $ta['tahun_ajaran'] ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_semester" class="form-label">Semester</label>
                                <select class="form-select" id="edit_semester" name="edit_semester" required>
                                    <option value="1">Semester 1</option>
                                    <option value="2">Semester 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                    <button type="submit" name="update_holiday" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Fetch Holidays Modal -->
<div class="modal fade" id="fetchHolidaysModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ambil Data dari API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tahun_ajaran_id" class="form-label">Tahun Ajaran</label>
                        <select class="form-select" id="tahun_ajaran_id" name="tahun_ajaran_id" required>
                            <?php
                            $tahun_ajaran_list = $tahunAjaran->getAll();
                            while ($ta = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)) {
                                echo "<option value='{$ta['id']}'>{$ta['tahun_ajaran']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="semester" class="form-label">Semester</label>
                        <select class="form-select" id="semester" name="semester" required>
                            <option value="1">Semester 1</option>
                            <option value="2">Semester 2</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="fetch_holidays" class="btn btn-primary">Ambil Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editHoliday(id, tanggal, keterangan, tahun_ajaran, semester) {
    document.getElementById('edit_holiday_id').value = id;
    document.getElementById('edit_tanggal').value = tanggal;
    document.getElementById('edit_keterangan').value = keterangan;

    // Set tahun ajaran
    if (tahun_ajaran) {
        document.getElementById('edit_tahun_ajaran').value = tahun_ajaran;
    }

    // Set semester
    if (semester) {
        document.getElementById('edit_semester').value = semester;
    }
}

function confirmDelete() {
    return confirm("Apakah Anda yakin ingin menghapus data ini?");
}

$(document).ready(function() {
    $('#holidayTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data hari libur",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[1, "asc"]],
        "columnDefs": [
            {"orderable": false, "targets": 3}, // Kolom aksi tidak bisa diurutkan
            {"width": "100px", "targets": 3}, // Atur lebar kolom aksi
            {"type": "date", "targets": 1} // Tipe data tanggal
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>