<?php
require_once __DIR__ . '/../config/database.php';

class Tugas {
    private $conn;
    private $table_name = "tugas";
    private $nilai_table = "nilai_tugas";

    public $id;
    public $mapel_id;
    public $kelas_id;
    public $judul;
    public $deskripsi;
    public $tanggal;
    public $semester;
    public $tahun_ajaran;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                (mapel_id, kelas_id, judul, deskripsi, tanggal, semester, tahun_ajaran)
                VALUES
                (:mapel_id, :kelas_id, :judul, :deskripsi, :tanggal, :semester, :tahun_ajaran)";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        $this->semester = htmlspecialchars(strip_tags($this->semester));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));

        // Bind values
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET judul = :judul,
                    deskripsi = :deskripsi,
                    tanggal = :tanggal
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Bind values
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($row) {
            $this->mapel_id = $row['mapel_id'];
            $this->kelas_id = $row['kelas_id'];
            $this->judul = $row['judul'];
            $this->deskripsi = $row['deskripsi'];
            $this->tanggal = $row['tanggal'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    public function getTugasMapel($mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.*, k.nama_kelas 
                FROM " . $this->table_name . " t
                LEFT JOIN kelas k ON t.kelas_id = k.id
                WHERE t.mapel_id = :mapel_id 
                AND t.semester = :semester 
                AND t.tahun_ajaran = :tahun_ajaran
                ORDER BY t.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getTugasMapelKelas($mapel_id, $kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.*, k.nama_kelas 
                FROM " . $this->table_name . " t
                LEFT JOIN kelas k ON t.kelas_id = k.id
                WHERE t.mapel_id = :mapel_id 
                AND t.kelas_id = :kelas_id
                AND t.semester = :semester 
                AND t.tahun_ajaran = :tahun_ajaran
                ORDER BY t.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function addNilai($tugas_id, $siswa_id, $nilai) {
        $query = "INSERT INTO " . $this->nilai_table . "
                (tugas_id, siswa_id, nilai)
                VALUES (:tugas_id, :siswa_id, :nilai)
                ON DUPLICATE KEY UPDATE nilai = :nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":nilai", $nilai);
        return $stmt->execute();
    }

    public function getNilaiTugas($tugas_id) {
        $query = "SELECT nt.*, s.nis, s.nama_siswa, k.nama_kelas
                FROM " . $this->nilai_table . " nt
                JOIN siswa s ON nt.siswa_id = s.id
                JOIN kelas k ON s.kelas_id = k.id
                WHERE nt.tugas_id = :tugas_id
                ORDER BY k.nama_kelas ASC, s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();
        return $stmt;
    }

    public function getRataRataNilaiTugas($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT AVG(nt.nilai) as rata_rata
                FROM " . $this->nilai_table . " nt
                JOIN " . $this->table_name . " t ON nt.tugas_id = t.id
                WHERE t.mapel_id = :mapel_id 
                AND t.semester = :semester 
                AND t.tahun_ajaran = :tahun_ajaran
                AND nt.siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['rata_rata'] ? round($row['rata_rata'], 2) : 0;
    }

    public function getSiswaWithNilai($tugas_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, nt.nilai
                FROM siswa s
                LEFT JOIN " . $this->nilai_table . " nt ON s.id = nt.siswa_id AND nt.tugas_id = :tugas_id
                JOIN kelas k ON s.kelas_id = k.id
                JOIN " . $this->table_name . " t ON t.kelas_id = k.id
                WHERE t.id = :tugas_id
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();

        return $stmt;
    }

    public function updateNilai($tugas_id, $siswa_id, $nilai) {
        $query = "INSERT INTO " . $this->nilai_table . " 
                (tugas_id, siswa_id, nilai) 
                VALUES (:tugas_id, :siswa_id, :nilai)
                ON DUPLICATE KEY UPDATE nilai = :nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":nilai", $nilai);

        return $stmt->execute();
    }
}
