<?php
require_once __DIR__ . '/../config/database.php';

class ProfilSekolah {
    private $conn;
    private $table = 'profil_sekolah';

    public $id;
    public $nama_sekolah;
    public $npsn;
    public $status_sekolah;
    public $jenjang_pendidikan;
    public $alamat_jalan;
    public $desa_kelurahan;
    public $kecamatan;
    public $kabupaten_kota;
    public $provinsi;
    public $kode_pos;
    public $no_telepon;
    public $email;
    public $website;
    public $nama_kepala_sekolah;
    public $nip_kepala_sekolah;
    public $logo;
    public $visi;
    public $misi;
    public $kode_provinsi;
    public $kode_kabupaten;
    public $kode_kecamatan;
    public $kode_desa;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function get() {
        $query = "SELECT * FROM " . $this->table . " LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->id = $row['id'];
            $this->nama_sekolah = $row['nama_sekolah'];
            $this->npsn = $row['npsn'];
            $this->status_sekolah = $row['status_sekolah'];
            $this->jenjang_pendidikan = $row['jenjang_pendidikan'];
            $this->alamat_jalan = $row['alamat_jalan'];
            $this->desa_kelurahan = $row['desa_kelurahan'];
            $this->kecamatan = $row['kecamatan'];
            $this->kabupaten_kota = $row['kabupaten_kota'];
            $this->provinsi = $row['provinsi'];
            $this->kode_pos = $row['kode_pos'];
            $this->no_telepon = $row['no_telepon'];
            $this->email = $row['email'];
            $this->website = $row['website'];
            $this->nama_kepala_sekolah = $row['nama_kepala_sekolah'];
            $this->nip_kepala_sekolah = $row['nip_kepala_sekolah'];
            $this->logo = $row['logo'];
            $this->visi = $row['visi'];
            $this->misi = $row['misi'];
            $this->kode_provinsi = $row['kode_provinsi'];
            $this->kode_kabupaten = $row['kode_kabupaten'];
            $this->kode_kecamatan = $row['kode_kecamatan'];
            $this->kode_desa = $row['kode_desa'];
            return true;
        }
        return false;
    }

    public function save() {
        // Check if record exists without overwriting current properties
        $query_check = "SELECT COUNT(*) as count FROM " . $this->table;
        $stmt_check = $this->conn->prepare($query_check);
        $stmt_check->execute();
        $row = $stmt_check->fetch(PDO::FETCH_ASSOC);
        $exists = ($row['count'] > 0);

        // Store current ID if it exists
        $current_id = $this->id;

        if($exists) {
            // Update
            $query = "UPDATE " . $this->table . "
                    SET nama_sekolah = :nama_sekolah,
                        npsn = :npsn,
                        status_sekolah = :status_sekolah,
                        jenjang_pendidikan = :jenjang_pendidikan,
                        alamat_jalan = :alamat_jalan,
                        desa_kelurahan = :desa_kelurahan,
                        kecamatan = :kecamatan,
                        kabupaten_kota = :kabupaten_kota,
                        provinsi = :provinsi,
                        kode_pos = :kode_pos,
                        no_telepon = :no_telepon,
                        email = :email,
                        website = :website,
                        nama_kepala_sekolah = :nama_kepala_sekolah,
                        nip_kepala_sekolah = :nip_kepala_sekolah,
                        logo = :logo,
                        visi = :visi,
                        misi = :misi,
                        kode_provinsi = :kode_provinsi,
                        kode_kabupaten = :kode_kabupaten,
                        kode_kecamatan = :kode_kecamatan,
                        kode_desa = :kode_desa
                    WHERE id = :id";
        } else {
            // Insert
            $query = "INSERT INTO " . $this->table . "
                    (nama_sekolah, npsn, status_sekolah, jenjang_pendidikan,
                    alamat_jalan, desa_kelurahan, kecamatan, kabupaten_kota, provinsi,
                    kode_pos, no_telepon, email, website,
                    nama_kepala_sekolah, nip_kepala_sekolah, logo, visi, misi,
                    kode_provinsi, kode_kabupaten, kode_kecamatan, kode_desa)
                    VALUES
                    (:nama_sekolah, :npsn, :status_sekolah, :jenjang_pendidikan,
                    :alamat_jalan, :desa_kelurahan, :kecamatan, :kabupaten_kota, :provinsi,
                    :kode_pos, :no_telepon, :email, :website,
                    :nama_kepala_sekolah, :nip_kepala_sekolah, :logo, :visi, :misi,
                    :kode_provinsi, :kode_kabupaten, :kode_kecamatan, :kode_desa)";
        }

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nama_sekolah = htmlspecialchars(strip_tags($this->nama_sekolah));
        $this->npsn = htmlspecialchars(strip_tags($this->npsn));
        $this->status_sekolah = htmlspecialchars(strip_tags($this->status_sekolah));
        $this->jenjang_pendidikan = htmlspecialchars(strip_tags($this->jenjang_pendidikan));
        $this->alamat_jalan = htmlspecialchars(strip_tags($this->alamat_jalan));
        $this->desa_kelurahan = htmlspecialchars(strip_tags($this->desa_kelurahan));
        $this->kecamatan = htmlspecialchars(strip_tags($this->kecamatan));
        $this->kabupaten_kota = htmlspecialchars(strip_tags($this->kabupaten_kota));
        $this->provinsi = htmlspecialchars(strip_tags($this->provinsi));
        $this->kode_pos = htmlspecialchars(strip_tags($this->kode_pos));
        $this->no_telepon = htmlspecialchars(strip_tags($this->no_telepon));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->website = htmlspecialchars(strip_tags($this->website));
        $this->nama_kepala_sekolah = htmlspecialchars(strip_tags($this->nama_kepala_sekolah));
        $this->nip_kepala_sekolah = htmlspecialchars(strip_tags($this->nip_kepala_sekolah));
        $this->logo = htmlspecialchars(strip_tags($this->logo));
        $this->visi = htmlspecialchars(strip_tags($this->visi));
        $this->misi = htmlspecialchars(strip_tags($this->misi));
        $this->kode_provinsi = htmlspecialchars(strip_tags($this->kode_provinsi));
        $this->kode_kabupaten = htmlspecialchars(strip_tags($this->kode_kabupaten));
        $this->kode_kecamatan = htmlspecialchars(strip_tags($this->kode_kecamatan));
        $this->kode_desa = htmlspecialchars(strip_tags($this->kode_desa));

        // Bind parameters
        $stmt->bindParam(":nama_sekolah", $this->nama_sekolah);
        $stmt->bindParam(":npsn", $this->npsn);
        $stmt->bindParam(":status_sekolah", $this->status_sekolah);
        $stmt->bindParam(":jenjang_pendidikan", $this->jenjang_pendidikan);
        $stmt->bindParam(":alamat_jalan", $this->alamat_jalan);
        $stmt->bindParam(":desa_kelurahan", $this->desa_kelurahan);
        $stmt->bindParam(":kecamatan", $this->kecamatan);
        $stmt->bindParam(":kabupaten_kota", $this->kabupaten_kota);
        $stmt->bindParam(":provinsi", $this->provinsi);
        $stmt->bindParam(":kode_pos", $this->kode_pos);
        $stmt->bindParam(":no_telepon", $this->no_telepon);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":website", $this->website);
        $stmt->bindParam(":nama_kepala_sekolah", $this->nama_kepala_sekolah);
        $stmt->bindParam(":nip_kepala_sekolah", $this->nip_kepala_sekolah);
        $stmt->bindParam(":logo", $this->logo);
        $stmt->bindParam(":visi", $this->visi);
        $stmt->bindParam(":misi", $this->misi);
        $stmt->bindParam(":kode_provinsi", $this->kode_provinsi);
        $stmt->bindParam(":kode_kabupaten", $this->kode_kabupaten);
        $stmt->bindParam(":kode_kecamatan", $this->kode_kecamatan);
        $stmt->bindParam(":kode_desa", $this->kode_desa);

        if($exists) {
            // If we're updating, we need to get the ID from the database if it's not already set
            if (empty($current_id)) {
                $query_id = "SELECT id FROM " . $this->table . " LIMIT 1";
                $stmt_id = $this->conn->prepare($query_id);
                $stmt_id->execute();
                $row_id = $stmt_id->fetch(PDO::FETCH_ASSOC);
                $current_id = $row_id['id'];
            }
            $stmt->bindParam(":id", $current_id);
        }

        // Execute the query
        if($stmt->execute()) {
            return true;
        }

        // If there was an error, log it
        error_log("Error saving profil sekolah: " . print_r($stmt->errorInfo(), true));
        return false;
    }
}
