<?php
require '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Ambil data kategori
$perpustakaan = new Perpustakaan();
$kategori = $perpustakaan->getAllKategori();

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set header
$sheet->setCellValue('A1', 'TEMPLATE IMPORT BUKU');
$sheet->mergeCells('A1:H1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Set headers
$sheet->setCellValue('A3', 'Judul Buku*');
$sheet->setCellValue('B3', 'ID Kategori*');
$sheet->setCellValue('C3', 'Pengarang*');
$sheet->setCellValue('D3', 'Penerbit*');
$sheet->setCellValue('E3', 'Tahun Terbit*');
$sheet->setCellValue('F3', 'ISBN');
$sheet->setCellValue('G3', 'Jumlah Buku*');
$sheet->setCellValue('H3', 'Lokasi');

// Example data
$sheet->setCellValue('A4', 'Fisika Dasar');
$sheet->setCellValue('B4', '1');
$sheet->setCellValue('C4', 'Albert Einstein');
$sheet->setCellValue('D4', 'Penerbit ABC');
$sheet->setCellValue('E4', '2023');
$sheet->setCellValue('F4', '************-93-9');
$sheet->setCellValue('G4', '5');
$sheet->setCellValue('H4', 'RAK-A');

// Tambahkan daftar kategori di sheet kedua
$sheet2 = $spreadsheet->createSheet();
$sheet2->setTitle('Daftar Kategori');
$sheet2->setCellValue('A1', 'ID Kategori');
$sheet2->setCellValue('B1', 'Nama Kategori');

$row = 2;
foreach ($kategori as $k) {
    $sheet2->setCellValue('A' . $row, $k['id_kategori']);
    $sheet2->setCellValue('B' . $row, $k['nama_kategori']);
    $row++;
}

// Auto-size columns
foreach(range('A','H') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}
$sheet2->getColumnDimension('A')->setAutoSize(true);
$sheet2->getColumnDimension('B')->setAutoSize(true);

// Style the header row
$sheet->getStyle('A3:H3')->getFont()->setBold(true);
$sheet->getStyle('A3:H3')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');
$sheet2->getStyle('A1:B1')->getFont()->setBold(true);
$sheet2->getStyle('A1:B1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Create the Excel file
$writer = new Xlsx($spreadsheet);
$writer->save('template_buku.xlsx');

echo "Template created successfully!";
?> 