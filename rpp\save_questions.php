<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppQuestion.php';
require_once '../models/Guru.php';

// Cek jika bukan POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Akses tidak valid.";
    header("Location: generate_questions.php");
    exit();
}

// Debug logging untuk melihat data yang diterima
$debug_log = "=== SAVE QUESTIONS DEBUG " . date('Y-m-d H:i:s') . " ===\n";
$debug_log .= "POST keys: " . implode(', ', array_keys($_POST)) . "\n";

if (isset($_POST['questions'])) {
    $debug_log .= "Questions count: " . count($_POST['questions']) . "\n";
    foreach ($_POST['questions'] as $i => $q) {
        if (isset($q['question_type']) && $q['question_type'] === 'multiple_choice') {
            $debug_log .= "Question $i (MC):\n";
            $debug_log .= "  - Options raw: " . var_export($q['options'] ?? 'NOT_SET', true) . "\n";
            $debug_log .= "  - Options type: " . gettype($q['options'] ?? null) . "\n";
            $debug_log .= "  - Options length: " . (isset($q['options']) ? strlen($q['options']) : 'N/A') . "\n";
            $debug_log .= "  - Correct Answer: " . var_export($q['correct_answer'] ?? 'NOT_SET', true) . "\n";
        }
    }
}

// Write to log file
file_put_contents('debug_save_questions.log', $debug_log, FILE_APPEND);

// Validasi input
if (!isset($_POST['rpp_id']) || !isset($_POST['selected_questions']) || !isset($_POST['questions'])) {
    $_SESSION['error'] = "Data tidak lengkap.";
    header("Location: generate_questions.php");
    exit();
}

$rpp_id = $_POST['rpp_id'];
$selected_questions = $_POST['selected_questions'];
$questions_data = $_POST['questions'];

// Validasi jika ada soal yang dipilih
if (empty($selected_questions)) {
    $_SESSION['error'] = "Silakan pilih minimal satu soal untuk disimpan.";
    header("Location: configure_generation.php?rpp_id=" . $rpp_id);
    exit();
}

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Validasi RPP
$rpp = new Rpp();
$stmt = $rpp->getOne($rpp_id);
$rpp_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "RPP tidak ditemukan atau bukan milik Anda.";
    header("Location: generate_questions.php");
    exit();
}

// Mulai transaksi database
$database = new Database();
$conn = $database->getConnection();
$conn->beginTransaction();

try {
    $rppQuestion = new RppQuestion();
    $saved_count = 0;
    
    foreach ($selected_questions as $index) {
        if (!isset($questions_data[$index])) {
            continue;
        }
        
        $question_data = $questions_data[$index];
        
        // Validasi data soal
        if (empty($question_data['question_text']) || empty($question_data['question_type']) || 
            empty($question_data['difficulty_level'])) {
            continue;
        }
        
        // Set properties
        $rppQuestion->rpp_id = $rpp_id;
        $rppQuestion->question_text = $question_data['question_text'];
        $rppQuestion->question_type = $question_data['question_type'];
        $rppQuestion->difficulty_level = $question_data['difficulty_level'];
        $rppQuestion->category = $question_data['category'] ?? 'Generated';
        $rppQuestion->source_type = 'generated';
        $rppQuestion->analysis_data = null;

        // Handle options and correct answer for multiple choice
        if ($question_data['question_type'] === 'multiple_choice') {
            $debug_log = "Processing MC question for index $index:\n";
            $debug_log .= "  - Initial options: " . var_export($question_data['options'] ?? 'NOT_SET', true) . "\n";
            $debug_log .= "  - Initial options type: " . gettype($question_data['options'] ?? null) . "\n";

            // Validate and clean options data
            $options = $question_data['options'] ?? null;

            if ($options !== null) {
                // If options is a string, try to decode it as JSON
                if (is_string($options)) {
                    $options = trim($options);
                    if (empty($options) || $options === 'Array') {
                        // Empty string or "Array" string (from htmlspecialchars on array), set to null
                        $options = null;
                    } else {
                        $decoded_options = json_decode($options, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_options)) {
                            // Valid JSON string, use decoded array
                            $options = $decoded_options;
                        } else {
                            // Invalid JSON string, set to null
                            $options = null;
                        }
                    }
                }
                // If options is an array, validate it has content
                if (is_array($options)) {
                    // Filter out empty options
                    $valid_options = array_filter($options, function($option) {
                        return !empty(trim($option));
                    });

                    if (count($valid_options) >= 2) {
                        // Keep valid options
                        $options = array_values($valid_options); // Re-index array
                    } else {
                        // Not enough valid options
                        $options = null;
                    }
                }
            }

            $rppQuestion->options = $options;
            $rppQuestion->correct_answer = $question_data['correct_answer'] ?? null;

            $debug_log .= "  - Final options for RppQuestion: " . var_export($options, true) . "\n";
            $debug_log .= "  - Final correct_answer: " . var_export($rppQuestion->correct_answer, true) . "\n";
            file_put_contents('debug_save_questions.log', $debug_log, FILE_APPEND);
        } else {
            $rppQuestion->options = null;
            $rppQuestion->correct_answer = null;
        }
        
        if ($rppQuestion->create()) {
            $saved_count++;
        }
    }
    
    $conn->commit();
    
    if ($saved_count > 0) {
        $_SESSION['success'] = "Berhasil menyimpan $saved_count soal ke database.";
    } else {
        $_SESSION['error'] = "Tidak ada soal yang berhasil disimpan.";
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    $_SESSION['error'] = "Gagal menyimpan soal: " . $e->getMessage();
}

// Redirect ke halaman daftar soal
header("Location: questions_list.php?rpp_id=" . $rpp_id);
exit();
?>
