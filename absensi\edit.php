<?php
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/Absensi.php';
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../models/MataPelajaran.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/SiswaPeriode.php';
require_once __DIR__ . '/../models/PeriodeAktif.php';
require_once __DIR__ . '/../models/JadwalPelajaran.php';
require_once __DIR__ . '/../models/User.php';

if (!isset($_GET['id'])) {
    echo "<script>alert('ID tidak valid!'); window.location.href='index.php';</script>";
    exit;
}

$absensi = new Absensi();
$kelas = new Kelas();
$mapel = new MataPelajaran();
$siswa = new Siswa();
$jadwal = new JadwalPelajaran();
$user = new User();

$absensi->id = $_GET['id'];

if (!$absensi->getOne()) {
    echo "<script>alert('Data absensi tidak ditemukan!'); window.location.href='index.php';</script>";
    exit;
}

// Check if the logged-in teacher is authorized for this attendance
if ($_SESSION['role'] === 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
    $authorized = false;
    $stmt = $jadwal->getByMapelAndGuru($absensi->mapel_id, $guru_id);
    if ($stmt->rowCount() > 0) {
        $authorized = true;
    }
    
    if (!$authorized) {
        echo "<script>alert('Anda tidak memiliki akses untuk mengedit absensi mata pelajaran ini!'); window.location.href='index.php';</script>";
        exit;
    }
}

$error = '';
$success = '';

// Get list of classes
$kelas_list = $kelas->getAll();

// Get list of subjects
$mapel_list = $mapel->getAll();

// Get existing attendance details
$detail_absensi = $absensi->getDetailAbsensi($absensi->id);
$detail_map = [];
while ($row = $detail_absensi->fetch(PDO::FETCH_ASSOC)) {
    $detail_map[$row['siswa_id']] = $row['status'];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $absensi->tanggal = $_POST['tanggal'];
    $absensi->kelas_id = $_POST['kelas_id'];
    $absensi->mapel_id = $_POST['mapel_id'];
    
    if ($absensi->update()) {
        $success = true;
        
        // Update attendance details
        if (isset($_POST['status']) && is_array($_POST['status'])) {
            foreach ($_POST['status'] as $siswa_id => $status) {
                if (!empty($status)) { // Only process if status is not empty
                    // Capitalize first letter to match database format
                    $status = ucfirst($status);
                    if (!$absensi->updateDetail($absensi->id, $siswa_id, $status)) {
                        $success = false;
                        break;
                    }
                }
            }
        } else {
            $success = false;
            $error = "Tidak ada data kehadiran siswa yang dikirim";
        }
        
        if ($success) {
            echo "<script>
                alert('Data absensi berhasil diupdate!');
                window.location.href = 'index.php';
            </script>";
            exit;
        } else {
            $error = "Gagal mengupdate detail absensi";
        }
    } else {
        $error = "Gagal mengupdate data absensi";
    }
}

// Get students for selected class
// For editing, we should show students from the same period as the attendance record
// This ensures consistency - we show the same students who were enrolled when the attendance was created
$siswaPeriode = new SiswaPeriode();

// Use the attendance record's semester and tahun_ajaran for consistency
if ($absensi->semester && $absensi->tahun_ajaran) {
    // Use period-based student retrieval with the attendance record's period
    $siswa_list = $siswaPeriode->getSiswaByPeriode($absensi->tahun_ajaran, $absensi->semester, $absensi->kelas_id);
} else {
    // Fallback to regular method if attendance doesn't have period info
    $siswa_list = $siswa->getByKelas($absensi->kelas_id);
}
?>

<div class="container-fluid">
    <h2 class="mb-4">Edit Absensi</h2>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Form Edit Absensi</h5>
        </div>
        <div class="card-body">
            <form method="post">
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" 
                               value="<?php echo $absensi->tanggal; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="kelas_id" class="form-label">Kelas</label>
                        <?php if ($_SESSION['role'] === 'guru'): ?>
                            <input type="hidden" name="kelas_id" value="<?php echo $absensi->kelas_id; ?>">
                        <?php endif; ?>
                        <select name="<?php echo ($_SESSION['role'] === 'guru' ? 'kelas_id_display' : 'kelas_id'); ?>" id="kelas_id" class="form-select" <?php echo ($_SESSION['role'] === 'guru') ? 'disabled' : ''; ?>>
                            <option value="">Pilih Kelas</option>
                            <?php 
                            $kelas_list->execute();
                            while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): 
                            ?>
                                <option value="<?php echo $row['id']; ?>" 
                                        <?php echo ($absensi->kelas_id == $row['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                        <?php if ($_SESSION['role'] === 'guru'): ?>
                            <input type="hidden" name="mapel_id" value="<?php echo $absensi->mapel_id; ?>">
                        <?php endif; ?>
                        <select name="<?php echo ($_SESSION['role'] === 'guru' ? 'mapel_id_display' : 'mapel_id'); ?>" id="mapel_id" class="form-select" <?php echo ($_SESSION['role'] === 'guru') ? 'disabled' : ''; ?>>
                            <option value="">Pilih Mata Pelajaran</option>
                            <?php 
                            $mapel_list->execute();
                            while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)): 
                            ?>
                                <option value="<?php echo $row['id']; ?>"
                                        <?php echo ($absensi->mapel_id == $row['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($row['nama_mapel']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th width="15%">NIS</th>
                                <th>Nama Siswa</th>
                                <th width="20%">Status Kehadiran</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            if ($siswa_list):
                                while ($row = $siswa_list->fetch(PDO::FETCH_ASSOC)):
                                    // Handle different column names from different queries
                                    $student_id = isset($row['siswa_id']) ? $row['siswa_id'] : $row['id'];
                                    $saved_status = isset($detail_map[$student_id]) ? $detail_map[$student_id] : '';
                            ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo htmlspecialchars($row['nis']); ?></td>
                                    <td><?php echo htmlspecialchars($row['nama_siswa']); ?></td>
                                    <td>
                                        <select name="status[<?php echo $student_id; ?>]" class="form-select form-select-sm">
                                            <option value="">Pilih Status</option>
                                            <option value="hadir" <?php echo ($saved_status === 'Hadir') ? 'selected' : ''; ?>>Hadir</option>
                                            <option value="sakit" <?php echo ($saved_status === 'Sakit') ? 'selected' : ''; ?>>Sakit</option>
                                            <option value="izin" <?php echo ($saved_status === 'Izin') ? 'selected' : ''; ?>>Izin</option>
                                            <option value="alpha" <?php echo ($saved_status === 'Alpha') ? 'selected' : ''; ?>>Alpha</option>
                                        </select>
                                    </td>
                                </tr>
                            <?php
                                endwhile;
                            else:
                            ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        Tidak ada data siswa untuk kelas ini pada periode yang aktif
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
require_once __DIR__ . '/../template/footer.php';
?>
