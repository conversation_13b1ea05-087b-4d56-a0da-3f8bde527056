<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/DetailJadwalJam.php';
require_once '../models/HariLibur.php';

// Check if user is a teacher
if ($_SESSION['role'] !== 'guru') {
    echo "<script>alert('Akses ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

$jadwal_id = isset($_GET['jadwal_id']) ? $_GET['jadwal_id'] : null;
if (!$jadwal_id) {
    echo "<script>alert('Parameter tidak valid!'); window.location.href='index.php';</script>";
    exit;
}

$user = new User();
$jadwal = new JadwalPelajaran();
$absensi = new Absensi();
$detailJadwalJam = new DetailJadwalJam();

// Get guru_id
$guru_id = $user->getGuruId($_SESSION['user_id']);

// Get schedule details
$jadwal->id = $jadwal_id;
if (!$jadwal->getOne() || $jadwal->guru_id != $guru_id) {
    echo "<script>alert('Data jadwal tidak ditemukan!'); window.location.href='index.php';</script>";
    exit;
}

// Get detail jam for this jadwal
$detail_jam = $detailJadwalJam->getByJadwalId($jadwal_id);
$jam_detail = [];
$current_group = [];
$prev_jam = null;

while ($jam = $detail_jam->fetch(PDO::FETCH_ASSOC)) {
    if ($prev_jam) {
        $prev_end = strtotime($prev_jam['jam_selesai']);
        $current_start = strtotime($jam['jam_mulai']);
        $time_diff = $current_start - $prev_end;
        
        // If consecutive time and jam_ke
        if ($time_diff <= 300 && $jam['jam_ke'] == $prev_jam['jam_ke'] + 1) { // 5 minutes tolerance
            $current_group[] = $jam;
        } else {
            // Process previous group
            if (!empty($current_group)) {
                $first = reset($current_group);
                $last = end($current_group);
                if (count($current_group) > 1) {
                    $jam_detail[] = "Jam ke-{$first['jam_ke']}-{$last['jam_ke']} (" . 
                        date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                        date('H:i', strtotime($last['jam_selesai'])) . ")";
                } else {
                    $jam_detail[] = "Jam ke-{$first['jam_ke']} (" . 
                        date('H:i', strtotime($first['jam_mulai'])) . "-" . 
                        date('H:i', strtotime($first['jam_selesai'])) . ")";
                }
            }
            $current_group = [$jam];
        }
    } else {
        $current_group[] = $jam;
    }
    $prev_jam = $jam;
}

// Process the last group
if (!empty($current_group)) {
    $first = reset($current_group);
    $last = end($current_group);
    if (count($current_group) > 1) {
        $jam_detail[] = "Jam ke-{$first['jam_ke']}-{$last['jam_ke']} (" . 
            date('H:i', strtotime($first['jam_mulai'])) . "-" . 
            date('H:i', strtotime($last['jam_selesai'])) . ")";
    } else {
        $jam_detail[] = "Jam ke-{$first['jam_ke']} (" . 
            date('H:i', strtotime($first['jam_mulai'])) . "-" . 
            date('H:i', strtotime($first['jam_selesai'])) . ")";
    }
}

// Get active period
$active_period = new PeriodeAktif();
$active_period->getActive();

// Get selected period from GET parameters or use active period
$selected_semester = isset($_GET['semester']) ? $_GET['semester'] : $active_period->semester;
$selected_tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $active_period->tahun_ajaran;

// Get dates without attendance records for the selected period
$missing_dates = $absensi->getMissingDates($jadwal_id, $active_period->id);

// Initialize HariLibur model
$hariLibur = new HariLibur();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Riwayat Absensi yang Belum Diisi</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <!-- Period Selection Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Periode Akademik</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="jadwal_id" value="<?= $jadwal_id ?>">
                <div class="col-md-4">
                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                        <?php
                        // Get available academic years
                        require_once '../models/TahunAjaran.php';
                        $tahunAjaranModel = new TahunAjaran();
                        $tahun_ajaran_list = $tahunAjaranModel->getAllAsArray();
                        foreach ($tahun_ajaran_list as $ta) {
                            $selected = ($ta['tahun_ajaran'] == $selected_tahun_ajaran) ? 'selected' : '';
                            echo "<option value='{$ta['tahun_ajaran']}' $selected>{$ta['tahun_ajaran']}</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="semester" class="form-label">Semester</label>
                    <select name="semester" id="semester" class="form-select">
                        <option value="1" <?= $selected_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                        <option value="2" <?= $selected_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                    </div>
                </div>
                <?php if ($selected_semester != $active_period->semester || $selected_tahun_ajaran != $active_period->tahun_ajaran): ?>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="view.php?jadwal_id=<?= $jadwal_id ?>" class="btn btn-secondary">
                            <i class="fas fa-sync"></i> Reset
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </form>
            <?php if ($selected_semester != $active_period->semester || $selected_tahun_ajaran != $active_period->tahun_ajaran): ?>
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-info-circle"></i>
                    Menampilkan data untuk periode: <strong><?= $selected_tahun_ajaran ?> - Semester <?= $selected_semester ?></strong>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <?= htmlspecialchars($jadwal->nama_mapel) ?> -
                Kelas <?= htmlspecialchars($jadwal->nama_kelas) ?>
            </h6>
            <div class="mt-2 text-muted">
                <i class="fas fa-calendar"></i> <?= htmlspecialchars($jadwal->hari) ?>
                <br>
                <i class="fas fa-clock"></i>
                <?php if (!empty($jam_detail)): ?>
                    <?= implode(', ', $jam_detail) ?>
                <?php else: ?>
                    <?= date('H:i', strtotime($jadwal->jam_mulai)) ?> -
                    <?= date('H:i', strtotime($jadwal->jam_selesai)) ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($missing_dates)): ?>
                <div class="alert alert-success">
                    Semua absensi sudah terisi untuk periode aktif ini.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered" id="missingDatesTable">
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th>Tanggal</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($missing_dates as $date): 
                                // Check if date is a holiday
                                $holiday_info = $hariLibur->getByDate($date);
                            ?>
                                <tr>
                                    <td></td>
                                    <td><?= date('d F Y', strtotime($date)) ?></td>
                                    <td>
                                        <?php if ($holiday_info): ?>
                                            <span class="badge bg-info text-white">
                                                <i class="fas fa-calendar-alt"></i> 
                                                Hari Libur: <?= htmlspecialchars($holiday_info['keterangan']) ?>
                                            </span>
                                        <?php else: ?>
                                            <a href="/absen/absensi/create.php?jadwal_id=<?= $jadwal_id ?>&tanggal=<?= $date ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus"></i> Isi Absensi
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#missingDatesTable').DataTable({
        "order": [[1, "asc"]], // Sort by date column ascending
        "columnDefs": [
            {
                "targets": 1,
                "type": "date"
            },
            {
                "searchable": false,
                "orderable": false,
                "targets": 0
            }
        ],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    }).on('order.dt search.dt', function () {
        let i = 1;
        
        $(this).find('td:first-child').each(function () {
            $(this).html(i++);
        });
    }).draw();
});
</script>

<?php require_once '../template/footer.php'; ?>