<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../config/database.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Cek role
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
$database = new Database();
$db = $database->getConnection();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nip = $_POST['nip'];
    $nama_lengkap = $_POST['nama_lengkap'];
    $jenis_kelamin = $_POST['jenis_kelamin'];
    $alamat = $_POST['alamat'];
    $no_telp = $_POST['no_telp'];
    $email = $_POST['email'];

    // Validasi NIP unik
    $check_query = "SELECT id FROM guru WHERE nip = :nip";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->bindParam(':nip', $nip);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        $_SESSION['error'] = "NIP sudah terdaftar!";
    } else {
        $query = "INSERT INTO guru (nip, nama_lengkap, jenis_kelamin, alamat, no_telp, email)
                  VALUES (:nip, :nama_lengkap, :jenis_kelamin, :alamat, :no_telp, :email)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':nip', $nip);
        $stmt->bindParam(':nama_lengkap', $nama_lengkap);
        $stmt->bindParam(':jenis_kelamin', $jenis_kelamin);
        $stmt->bindParam(':alamat', $alamat);
        $stmt->bindParam(':no_telp', $no_telp);
        $stmt->bindParam(':email', $email);

        if ($stmt->execute()) {
            $_SESSION['success'] = "Data guru berhasil ditambahkan!";
            header('Location: index.php');
            exit;
        } else {
            $_SESSION['error'] = "Terjadi kesalahan: " . $stmt->errorInfo()[2];
        }
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Tambah Data Guru</h5>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['error'])) : ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>
                <form method="POST" action="">
                    <div class="form-group mb-3">
                        <label for="nip">NIP <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nip" name="nip" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="nama_lengkap">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="jenis_kelamin">Jenis Kelamin <span class="text-danger">*</span></label>
                        <select class="form-control" id="jenis_kelamin" name="jenis_kelamin" required>
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="L">Laki-laki</option>
                            <option value="P">Perempuan</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="alamat">Alamat</label>
                        <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                    </div>
                    <div class="form-group mb-3">
                        <label for="no_telp">No. Telepon</label>
                        <input type="text" class="form-control" id="no_telp" name="no_telp">
                    </div>
                    <div class="form-group mb-3">
                        <label for="email">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <hr>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
