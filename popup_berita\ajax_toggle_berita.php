<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../models/PopupBerita.php';
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $berita_id = $_POST['berita_id'] ?? '';

    if (empty($action) || empty($berita_id)) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
        exit();
    }

    $popupBerita = new PopupBerita();

    try {
        if ($action === 'add') {
            $result = $popupBerita->addBeritaToPopup($berita_id);
        } elseif ($action === 'remove') {
            $result = $popupBerita->removeBeritaFromPopup($berita_id);
        } else {
            echo json_encode(['success' => false, 'message' => 'Action tidak valid']);
            exit();
        }

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Berhasil']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal memproses permintaan']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
}
?>
