# Modul Popup Berita

Modul ini memungkinkan admin untuk mengelola sistem notifikasi popup berita yang akan muncul setelah user login ke sistem SIHADIR.

## Fitur Utama

### 1. Pengaturan Popup
- **Aktifkan/Nonaktifkan Popup**: Toggle untuk mengaktifkan atau menonaktifkan sistem popup
- **Judul Popup**: Kustomisasi judul yang ditampilkan di popup
- **<PERSON><PERSON><PERSON><PERSON>**: Opsi untuk menampilkan excerpt dari berita
- **Panjang Ring<PERSON>an**: Pengaturan jumlah karakter untuk excerpt (50-500 karakter)
- **<PERSON><PERSON><PERSON><PERSON>s**: Popup muncul otomatis setelah login
- **<PERSON>si "<PERSON><PERSON>"**: User dapat memilih untuk tidak menampilkan popup selama 7 hari

### 2. Manajemen Berita
- **<PERSON><PERSON>h <PERSON>**: Multi-select interface untuk memilih berita yang akan ditampilkan
- **Urutan Tampilan**: Pengaturan urutan tampilan berita dalam popup
- **Status Berita**: Indikator berita yang sudah dipilih atau belum
- **Pilih Semua/Hapus Semua**: Aksi cepat untuk mengelola seleksi berita

### 3. Preview Popup
- **Preview Real-time**: Melihat tampilan popup sebelum dipublikasikan
- **Responsive Design**: Popup yang responsif dan konsisten dengan UI sistem

## Struktur File

```
popup_berita/
├── index.php                  # Halaman utama manajemen popup
├── create.php                 # Proses pembuatan pengaturan baru
├── update.php                 # Proses update pengaturan
├── ajax_toggle_berita.php     # AJAX untuk menambah/hapus berita
├── ajax_update_order.php      # AJAX untuk update urutan berita
├── ajax_clear_all.php         # AJAX untuk hapus semua berita
├── ajax_preview.php           # AJAX untuk preview popup
├── ajax_get_popup_data.php    # AJAX untuk mendapatkan data popup
├── popup_berita.sql           # Struktur database
└── README.md                  # Dokumentasi ini
```

## Database

### Tabel: popup_berita_settings
Menyimpan pengaturan popup berita:
- `id`: Primary key
- `is_active`: Status aktif popup (0/1)
- `title`: Judul popup
- `show_excerpt`: Tampilkan ringkasan (0/1)
- `excerpt_length`: Panjang ringkasan (karakter)
- `auto_show`: Tampilkan otomatis (0/1)
- `show_dont_show_again`: Tampilkan opsi "jangan tampilkan lagi" (0/1)
- `created_by`: ID user yang membuat
- `created_at`: Waktu pembuatan
- `updated_at`: Waktu update terakhir

### Tabel: popup_berita_items
Menyimpan berita yang dipilih untuk popup:
- `id`: Primary key
- `berita_id`: ID berita (foreign key ke tabel berita)
- `display_order`: Urutan tampilan
- `is_active`: Status aktif item (0/1)
- `created_at`: Waktu pembuatan
- `updated_at`: Waktu update terakhir

## Instalasi

1. **Import Database**:
   ```sql
   -- Jalankan file popup_berita.sql
   SOURCE popup_berita/popup_berita.sql;
   ```

2. **File JavaScript**:
   File `assets/js/popup-berita.js` sudah otomatis dimuat di header template

3. **Menu Sidebar**:
   Menu "Popup Berita" sudah ditambahkan di grup "Sistem" (khusus admin)

## Penggunaan

### Untuk Admin:
1. **Akses Menu**: Sistem → Popup Berita
2. **Pengaturan Popup**: 
   - Aktifkan popup dengan centang "Aktifkan Popup Berita"
   - Atur judul dan pengaturan lainnya sesuai kebutuhan
   - Klik "Simpan Pengaturan"
3. **Pilih Berita**:
   - Centang berita yang ingin ditampilkan di popup
   - Atur urutan tampilan dengan mengisi kolom "Urutan"
   - Gunakan "Pilih Semua" atau "Hapus Semua" untuk aksi cepat
4. **Preview**: Klik "Lihat Preview" untuk melihat tampilan popup

### Untuk User:
1. **Popup Otomatis**: Popup akan muncul otomatis setelah login (jika diaktifkan)
2. **Lihat Berita**: Klik "Lihat Berita" untuk membaca berita lengkap
3. **Jangan Tampilkan Lagi**: Centang opsi ini untuk tidak menampilkan popup selama 7 hari

## Fitur Teknis

### JavaScript API:
- `window.showNewsPopup()`: Menampilkan popup secara manual
- Local Storage: Menyimpan preferensi "jangan tampilkan lagi"

### AJAX Endpoints:
- `ajax_get_popup_data.php`: Mendapatkan data popup untuk ditampilkan
- `ajax_toggle_berita.php`: Menambah/hapus berita dari popup
- `ajax_update_order.php`: Update urutan tampilan berita
- `ajax_clear_all.php`: Hapus semua berita dari popup
- `ajax_preview.php`: Generate preview popup

### Keamanan:
- Middleware auth untuk semua endpoint
- Role-based access (admin only untuk manajemen)
- Input sanitization dan validation
- CSRF protection melalui session

## Integrasi Sistem

### Dengan Modul Berita:
- Menggunakan data dari tabel `berita` yang sudah ada
- Foreign key relationship untuk data integrity
- Cascade delete untuk cleanup otomatis

### Dengan Template System:
- Konsisten dengan design pattern yang ada
- Menggunakan Bootstrap modal
- Responsive design

### Dengan User Management:
- Terintegrasi dengan sistem session
- Role-based access control
- User tracking untuk audit trail

## Troubleshooting

### Popup Tidak Muncul:
1. Pastikan popup diaktifkan di pengaturan
2. Pastikan ada berita yang dipilih
3. Check browser console untuk error JavaScript
4. Pastikan file `popup-berita.js` ter-load dengan benar

### Error AJAX:
1. Check network tab di browser developer tools
2. Pastikan endpoint AJAX dapat diakses
3. Verify session dan authentication
4. Check server error logs

### Database Error:
1. Pastikan tabel sudah dibuat dengan benar
2. Check foreign key constraints
3. Verify user permissions untuk database

## Changelog

### Version 1.0.0
- Initial release
- Basic popup functionality
- Admin management interface
- Multi-select berita system
- Preview functionality
- Responsive design
- Local storage integration
