<?php
session_start();
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';
require_once '../models/MataPelajaran.php';
require_once '../vendor/autoload.php';

use Dompdf\Dompdf;
use Dompdf\Options;

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

// Ambil parameter filter
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$semester = isset($_GET['semester']) ? $_GET['semester'] : '';
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : '';
$mapel_id = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';

// Jika mapel_id tidak ada, kembalikan ke halaman index
if (!$mapel_id) {
    $_SESSION['error'] = "Mata pelajaran harus dipilih";
    header("Location: index.php");
    exit();
}

$nilaiSikap = new NilaiSikap();
$mataPelajaran = new MataPelajaran();

$data = $nilaiSikap->getByKelas($kelas_id, $semester, $tahun_ajaran, $mapel_id);
$data_mapel = $mataPelajaran->getById($mapel_id);

// Konfigurasi DOMPDF
$options = new Options();
$options->set('isHtml5ParserEnabled', true);
$options->set('isPhpEnabled', true);

$dompdf = new Dompdf($options);

// Generate HTML content
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Nilai Sikap Siswa</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .subtitle {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .mapel {
            font-size: 14px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 4px;
        }
        th {
            background-color: #f0f0f0;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        .text-center {
            text-align: center;
        }
        .deskripsi {
            font-size: 10px;
            text-align: justify;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">DATA NILAI SIKAP SISWA</div>
        <div class="subtitle">Tahun Ajaran: ' . $tahun_ajaran . ' - Semester: ' . $semester . '</div>
        <div class="mapel">Mata Pelajaran: ' . $data_mapel['nama_mapel'] . '</div>
    </div>

    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>NIS</th>
                <th>Nama Siswa</th>
                <th>Kelas</th>
                <th>Nilai Spiritual</th>
                <th>Deskripsi Spiritual</th>
                <th>Nilai Sosial</th>
                <th>Deskripsi Sosial</th>
            </tr>
        </thead>
        <tbody>';

$no = 1;
foreach ($data as $row) {
    $html .= '
        <tr>
            <td class="text-center">' . $no++ . '</td>
            <td>' . $row['nis'] . '</td>
            <td>' . $row['nama_siswa'] . '</td>
            <td class="text-center">' . $row['nama_kelas'] . '</td>
            <td class="text-center">' . $row['nilai_spiritual'] . '</td>
            <td class="deskripsi">' . $row['deskripsi_spiritual'] . '</td>
            <td class="text-center">' . $row['nilai_sosial'] . '</td>
            <td class="deskripsi">' . $row['deskripsi_sosial'] . '</td>
        </tr>';
}

$html .= '
        </tbody>
    </table>
</body>
</html>';

$dompdf->loadHtml($html);
$dompdf->setPaper('A4', 'landscape');
$dompdf->render();
$dompdf->stream("Nilai_Sikap_" . date('Y-m-d') . ".pdf", array("Attachment" => true));