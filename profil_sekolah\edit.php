<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/ProfilSekolah.php';

$profilSekolah = new ProfilSekolah();
$profilSekolah->get();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get existing data first to preserve ID
    $existing_id = $profilSekolah->id;

    // Set properties
    $profilSekolah->id = $existing_id; // Preserve ID
    $profilSekolah->nama_sekolah = $_POST['nama_sekolah'];
    $profilSekolah->npsn = $_POST['npsn'];
    $profilSekolah->status_sekolah = $_POST['status_sekolah'];
    $profilSekolah->jenjang_pendidikan = $_POST['jenjang_pendidikan'];
    $profilSekolah->alamat_jalan = $_POST['alamat_jalan'];
    $profilSekolah->desa_kelurahan = $_POST['desa_kelurahan'];
    $profilSekolah->kecamatan = $_POST['kecamatan'];
    $profilSekolah->kabupaten_kota = $_POST['kabupaten_kota'];
    $profilSekolah->provinsi = $_POST['provinsi'];
    $profilSekolah->kode_pos = $_POST['kode_pos'];
    $profilSekolah->no_telepon = $_POST['no_telepon'];
    $profilSekolah->email = $_POST['email'];
    $profilSekolah->website = $_POST['website'];
    $profilSekolah->nama_kepala_sekolah = $_POST['nama_kepala_sekolah'];
    $profilSekolah->nip_kepala_sekolah = $_POST['nip_kepala_sekolah'];

    // Set Wilayah Indonesia API fields
    $profilSekolah->kode_provinsi = $_POST['kode_provinsi'];
    $profilSekolah->kode_kabupaten = $_POST['kode_kabupaten'];
    $profilSekolah->kode_kecamatan = $_POST['kode_kecamatan'];
    $profilSekolah->kode_desa = $_POST['kode_desa'];

    // Preserve existing logo if no new logo is uploaded
    $old_logo = $profilSekolah->logo;

    // Handle logo upload if provided
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/img/';
        $temp_name = $_FILES['logo']['tmp_name'];
        $original_name = $_FILES['logo']['name'];
        $file_name = 'logo_' . time() . '_' . $original_name;
        $file_path = $upload_dir . $file_name;



        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        if (move_uploaded_file($temp_name, $file_path)) {
            // Delete old logo if exists
            if (!empty($profilSekolah->logo)) {
                // Cek apakah logo mengandung path
                if (strpos($profilSekolah->logo, 'assets/img/') === 0) {
                    // Jika logo mengandung path, ambil hanya nama filenya
                    $old_logo_filename = basename($profilSekolah->logo);
                } else {
                    // Jika tidak, gunakan nilai logo langsung
                    $old_logo_filename = $profilSekolah->logo;
                }

                $old_logo_path = '../assets/img/' . $old_logo_filename;
                if (file_exists($old_logo_path)) {
                    unlink($old_logo_path);
                }
            }
            // Simpan hanya nama file di database, tanpa path
            // PENTING: Jangan menyimpan path di database, hanya nama file saja
            $profilSekolah->logo = $file_name;
        }
    }

    // Handle visi and misi
    $profilSekolah->visi = $_POST['visi'];
    $profilSekolah->misi = $_POST['misi'];

    // If no new logo was uploaded, restore the old logo
    if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
        $profilSekolah->logo = $old_logo;
    }



    // Save to database
    if ($profilSekolah->save()) {
        header("Location: index.php?success=1");
        exit;
    } else {
        header("Location: index.php?error=1");
        exit;
    }
}

// List of jenjang pendidikan options
$jenjang_options = [
    'SD' => 'SD (Sekolah Dasar)',
    'SMP' => 'SMP (Sekolah Menengah Pertama)',
    'SMA' => 'SMA (Sekolah Menengah Atas)',
    'SMK' => 'SMK (Sekolah Menengah Kejuruan)',
    'MI' => 'MI (Madrasah Ibtidaiyah)',
    'MTs' => 'MTs (Madrasah Tsanawiyah)',
    'MA' => 'MA (Madrasah Aliyah)'
];
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Edit Profil Sekolah</h5>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form action="edit.php" method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Informasi Umum Sekolah</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="nama_sekolah" class="form-label">Nama Sekolah <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="nama_sekolah" name="nama_sekolah" value="<?php echo htmlspecialchars($profilSekolah->nama_sekolah ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="npsn" class="form-label">NPSN (Nomor Pokok Sekolah Nasional) <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="npsn" name="npsn" value="<?php echo htmlspecialchars($profilSekolah->npsn ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="status_sekolah" class="form-label">Status Sekolah <span class="text-danger">*</span></label>
                                        <select class="form-select" id="status_sekolah" name="status_sekolah" required>
                                            <option value="">-- Pilih Status --</option>
                                            <option value="Negeri" <?php echo ($profilSekolah->status_sekolah ?? '') === 'Negeri' ? 'selected' : ''; ?>>Negeri</option>
                                            <option value="Swasta" <?php echo ($profilSekolah->status_sekolah ?? '') === 'Swasta' ? 'selected' : ''; ?>>Swasta</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="jenjang_pendidikan" class="form-label">Jenjang Pendidikan <span class="text-danger">*</span></label>
                                        <select class="form-select" id="jenjang_pendidikan" name="jenjang_pendidikan" required>
                                            <option value="">-- Pilih Jenjang --</option>
                                            <?php foreach ($jenjang_options as $value => $label): ?>
                                                <option value="<?php echo $value; ?>" <?php echo ($profilSekolah->jenjang_pendidikan ?? '') === $value ? 'selected' : ''; ?>>
                                                    <?php echo $label; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Alamat Lengkap</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="alamat_jalan" class="form-label">Jalan <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="alamat_jalan" name="alamat_jalan" rows="2" required><?php echo htmlspecialchars($profilSekolah->alamat_jalan ?? ''); ?></textarea>
                                    </div>

                                    <!-- Wilayah Indonesia API Integration -->
                                    <div id="wilayah-api-container">
                                        <div class="mb-3">
                                            <label for="provinsi_select" class="form-label">Provinsi <span class="text-danger">*</span></label>
                                            <select class="form-select" id="provinsi_select">
                                                <option value="">-- Pilih Provinsi --</option>
                                            </select>
                                            <input type="hidden" name="kode_provinsi" id="kode_provinsi" value="<?php echo htmlspecialchars($profilSekolah->kode_provinsi ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="kabupaten_select" class="form-label">Kabupaten/Kota <span class="text-danger">*</span></label>
                                            <select class="form-select" id="kabupaten_select">
                                                <option value="">-- Pilih Kabupaten/Kota --</option>
                                            </select>
                                            <input type="hidden" name="kode_kabupaten" id="kode_kabupaten" value="<?php echo htmlspecialchars($profilSekolah->kode_kabupaten ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="kecamatan_select" class="form-label">Kecamatan <span class="text-danger">*</span></label>
                                            <select class="form-select" id="kecamatan_select">
                                                <option value="">-- Pilih Kecamatan --</option>
                                            </select>
                                            <input type="hidden" name="kode_kecamatan" id="kode_kecamatan" value="<?php echo htmlspecialchars($profilSekolah->kode_kecamatan ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="desa_select" class="form-label">Desa/Kelurahan <span class="text-danger">*</span></label>
                                            <select class="form-select" id="desa_select">
                                                <option value="">-- Pilih Desa/Kelurahan --</option>
                                            </select>
                                            <input type="hidden" name="kode_desa" id="kode_desa" value="<?php echo htmlspecialchars($profilSekolah->kode_desa ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <button type="button" id="toggle-manual-input" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-keyboard"></i> Beralih ke Input Manual
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Manual Input (hidden by default) -->
                                    <div id="manual-input-container" style="display: none;">
                                        <div class="mb-3">
                                            <label for="provinsi_manual" class="form-label">Provinsi <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="provinsi_manual" value="<?php echo htmlspecialchars($profilSekolah->provinsi ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="kabupaten_kota_manual" class="form-label">Kabupaten/Kota <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="kabupaten_kota_manual" value="<?php echo htmlspecialchars($profilSekolah->kabupaten_kota ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="kecamatan_manual" class="form-label">Kecamatan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="kecamatan_manual" value="<?php echo htmlspecialchars($profilSekolah->kecamatan ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="desa_kelurahan_manual" class="form-label">Desa/Kelurahan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="desa_kelurahan_manual" value="<?php echo htmlspecialchars($profilSekolah->desa_kelurahan ?? ''); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <button type="button" id="toggle-api-input" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-globe"></i> Beralih ke API Wilayah
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Hidden fields for form submission -->
                                    <input type="hidden" name="provinsi" id="provinsi" value="<?php echo htmlspecialchars($profilSekolah->provinsi ?? ''); ?>">
                                    <input type="hidden" name="kabupaten_kota" id="kabupaten_kota" value="<?php echo htmlspecialchars($profilSekolah->kabupaten_kota ?? ''); ?>">
                                    <input type="hidden" name="kecamatan" id="kecamatan" value="<?php echo htmlspecialchars($profilSekolah->kecamatan ?? ''); ?>">
                                    <input type="hidden" name="desa_kelurahan" id="desa_kelurahan" value="<?php echo htmlspecialchars($profilSekolah->desa_kelurahan ?? ''); ?>">

                                    <div class="mb-3">
                                        <label for="kode_pos" class="form-label">Kode Pos <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="kode_pos" name="kode_pos"
                                                value="<?php echo htmlspecialchars($profilSekolah->kode_pos ?? ''); ?>"
                                                placeholder="Masukkan kode pos"
                                                pattern="[0-9]{5}"
                                                title="Kode pos harus 5 digit angka"
                                                required>
                                            <button class="btn btn-outline-secondary" type="button" id="refresh-kode-pos" title="Coba dapatkan kode pos dari API">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">
                                            Kode pos akan diisi otomatis jika tersedia dari API. Jika tidak, silakan isi manual dengan 5 digit angka.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Kontak</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="no_telepon" class="form-label">Nomor Telepon <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="no_telepon" name="no_telepon" value="<?php echo htmlspecialchars($profilSekolah->no_telepon ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Sekolah <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($profilSekolah->email ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="website" class="form-label">Website Resmi <span class="text-danger">*</span></label>
                                        <input type="url" class="form-control" id="website" name="website" value="<?php echo htmlspecialchars($profilSekolah->website ?? ''); ?>" required>
                                        <div class="form-text">Contoh: https://www.sekolah.sch.id</div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Identitas Kepemimpinan</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="nama_kepala_sekolah" class="form-label">Nama Kepala Sekolah <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="nama_kepala_sekolah" name="nama_kepala_sekolah" value="<?php echo htmlspecialchars($profilSekolah->nama_kepala_sekolah ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="nip_kepala_sekolah" class="form-label">NIP Kepala Sekolah <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="nip_kepala_sekolah" name="nip_kepala_sekolah" value="<?php echo htmlspecialchars($profilSekolah->nip_kepala_sekolah ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Logo Sekolah</h5>
                                </div>
                                <div class="card-body">
                                    <?php
                                    // Cek apakah logo mengandung path
                                    if (!empty($profilSekolah->logo) && strpos($profilSekolah->logo, 'assets/img/') === 0) {
                                        // Jika logo mengandung path, ambil hanya nama filenya
                                        $logo_filename = basename($profilSekolah->logo);
                                    } else {
                                        // Jika tidak, gunakan nilai logo langsung
                                        $logo_filename = $profilSekolah->logo;
                                    }

                                    // Cek keberadaan logo
                                    $logo_path = '../assets/img/' . $logo_filename;
                                    $logo_exists = !empty($logo_filename) && file_exists($logo_path);
                                    ?>

                                    <?php if ($logo_exists): ?>
                                    <div class="mb-3 text-center">
                                        <img src="../assets/img/<?php echo htmlspecialchars($logo_filename); ?>" alt="Logo Sekolah" class="img-fluid" style="max-height: 150px;">
                                    </div>
                                    <?php elseif (!empty($profilSekolah->logo)): ?>
                                    <div class="mb-3">
                                        <div class="alert alert-warning">
                                            <p><strong>Logo tidak ditemukan</strong></p>
                                            <p>File logo tidak ditemukan di server. Silakan upload logo baru menggunakan form di bawah.</p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">Upload Logo Baru</label>
                                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                        <div class="form-text">Format: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Visi & Misi</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="visi" class="form-label">Visi</label>
                                        <textarea class="form-control tinymce" id="visi" name="visi" rows="4"><?php echo htmlspecialchars($profilSekolah->visi ?? ''); ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="misi" class="form-label">Misi</label>
                                        <textarea class="form-control tinymce" id="misi" name="misi" rows="4"><?php echo htmlspecialchars($profilSekolah->misi ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="index.php" class="btn btn-secondary me-md-2">Batal</a>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/wilayah-indonesia.js?v=<?php echo time(); ?>"></script>
<script>
// Add a debug message to check if the script is loaded
console.log('Edit page loaded. Initializing Wilayah Indonesia API integration...');

document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE for rich text editors
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '.tinymce',
            height: 300,
            plugins: 'lists link image table code',
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | table | code',
            menubar: false
        });
    }

    // Initialize Wilayah Indonesia API dropdowns
    const provinsiSelect = document.getElementById('provinsi_select');
    const kabupatenSelect = document.getElementById('kabupaten_select');
    const kecamatanSelect = document.getElementById('kecamatan_select');
    const desaSelect = document.getElementById('desa_select');
    const kodePosInput = document.getElementById('kode_pos');

    // Hidden fields for form submission
    const kodeProvinsiInput = document.getElementById('kode_provinsi');
    const provinsiInput = document.getElementById('provinsi');
    const kodeKabupatenInput = document.getElementById('kode_kabupaten');
    const kabupatenKotaInput = document.getElementById('kabupaten_kota');
    const kodeKecamatanInput = document.getElementById('kode_kecamatan');
    const kecamatanInput = document.getElementById('kecamatan');
    const kodeDesaInput = document.getElementById('kode_desa');
    const desaKelurahanInput = document.getElementById('desa_kelurahan');

    // Get saved values
    const savedKodeProvinsi = kodeProvinsiInput.value;
    const savedKodeKabupaten = kodeKabupatenInput.value;
    const savedKodeKecamatan = kodeKecamatanInput.value;
    const savedKodeDesa = kodeDesaInput.value;

    // Initialize dropdowns with saved values
    wilayahIndonesia.initWilayahDropdowns(
        provinsiSelect,
        kabupatenSelect,
        kecamatanSelect,
        desaSelect,
        kodePosInput,
        savedKodeProvinsi,
        savedKodeKabupaten,
        savedKodeKecamatan,
        savedKodeDesa
    );

    // Update hidden fields when selections change
    provinsiSelect.addEventListener('change', function() {
        const selectedOption = provinsiSelect.options[provinsiSelect.selectedIndex];
        kodeProvinsiInput.value = provinsiSelect.value;
        provinsiInput.value = selectedOption.textContent;
    });

    kabupatenSelect.addEventListener('change', function() {
        const selectedOption = kabupatenSelect.options[kabupatenSelect.selectedIndex];
        kodeKabupatenInput.value = kabupatenSelect.value;
        kabupatenKotaInput.value = selectedOption.textContent;
    });

    kecamatanSelect.addEventListener('change', function() {
        const selectedOption = kecamatanSelect.options[kecamatanSelect.selectedIndex];
        kodeKecamatanInput.value = kecamatanSelect.value;
        kecamatanInput.value = selectedOption.textContent;
    });

    desaSelect.addEventListener('change', function() {
        const selectedOption = desaSelect.options[desaSelect.selectedIndex];
        kodeDesaInput.value = desaSelect.value;
        desaKelurahanInput.value = selectedOption.textContent;
    });

    // Manual input fields
    const provinsiManual = document.getElementById('provinsi_manual');
    const kabupatenKotaManual = document.getElementById('kabupaten_kota_manual');
    const kecamatanManual = document.getElementById('kecamatan_manual');
    const desaKelurahanManual = document.getElementById('desa_kelurahan_manual');

    // Toggle buttons
    const toggleManualBtn = document.getElementById('toggle-manual-input');
    const toggleApiBtn = document.getElementById('toggle-api-input');

    // Containers
    const apiContainer = document.getElementById('wilayah-api-container');
    const manualContainer = document.getElementById('manual-input-container');

    // Toggle between API and manual input
    toggleManualBtn.addEventListener('click', function() {
        apiContainer.style.display = 'none';
        manualContainer.style.display = 'block';

        // Copy values from API to manual inputs
        provinsiManual.value = provinsiInput.value;
        kabupatenKotaManual.value = kabupatenKotaInput.value;
        kecamatanManual.value = kecamatanInput.value;
        desaKelurahanManual.value = desaKelurahanInput.value;
    });

    toggleApiBtn.addEventListener('click', function() {
        apiContainer.style.display = 'block';
        manualContainer.style.display = 'none';
    });

    // Update hidden fields when manual inputs change
    provinsiManual.addEventListener('input', function() {
        provinsiInput.value = provinsiManual.value;
    });

    kabupatenKotaManual.addEventListener('input', function() {
        kabupatenKotaInput.value = kabupatenKotaManual.value;
    });

    kecamatanManual.addEventListener('input', function() {
        kecamatanInput.value = kecamatanManual.value;
    });

    desaKelurahanManual.addEventListener('input', function() {
        desaKelurahanInput.value = desaKelurahanManual.value;
    });

    // Handle refresh kode pos button
    const refreshKodePosBtn = document.getElementById('refresh-kode-pos');
    refreshKodePosBtn.addEventListener('click', async function() {
        // Check if we have a selected desa
        if (desaSelect.value) {
            // Try to load kode pos from server-side proxy using wilayah-indonesia.js function
            const success = await wilayahIndonesia.loadKodePosViaProxy(desaSelect.value, kodePosInput);
            if (!success) {
                alert('Kode pos tidak ditemukan untuk desa/kelurahan ini. Silakan masukkan secara manual.');
            }
        } else if (desaKelurahanInput.value) {
            // If we're in manual mode or no desa is selected, try to extract from the name
            const postalCodeMatch = desaKelurahanInput.value.match(/\b\d{5}\b/);
            if (postalCodeMatch) {
                kodePosInput.value = postalCodeMatch[0];
            } else {
                alert('Tidak dapat menemukan kode pos. Silakan masukkan secara manual.');
            }
        } else {
            alert('Silakan pilih desa/kelurahan terlebih dahulu untuk mendapatkan kode pos.');
        }
    });

    // If API fails to load provinces, automatically switch to manual input
    setTimeout(function() {
        if (provinsiSelect.options.length <= 1) {
            console.log('API failed to load provinces, switching to manual input');
            toggleManualBtn.click();
        }
    }, 5000); // Wait 5 seconds for API to load
});
</script>

<?php require_once '../template/footer.php'; ?>
