<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../config/database.php';
require_once '../models/Perpustakaan.php';
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$perpustakaan = new Perpustakaan();

// Set default tanggal
$tanggal_awal = isset($_GET['tanggal_awal']) ? $_GET['tanggal_awal'] : date('Y-m-01');
$tanggal_akhir = isset($_GET['tanggal_akhir']) ? $_GET['tanggal_akhir'] : date('Y-m-t');

// Ambil data peminjaman
$peminjaman = $perpustakaan->getLaporanPeminjaman($tanggal_awal, $tanggal_akhir);

// Buat spreadsheet baru
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set judul
$sheet->setCellValue('A1', 'LAPORAN PEMINJAMAN BUKU');
$sheet->mergeCells('A1:L1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Set periode
$sheet->setCellValue('A2', 'Periode: ' . date('d/m/Y', strtotime($tanggal_awal)) . ' - ' . date('d/m/Y', strtotime($tanggal_akhir)));
$sheet->mergeCells('A2:L2');
$sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

// Set header tabel
$sheet->setCellValue('A4', 'No');
$sheet->setCellValue('B4', 'Nomor Peminjaman');
$sheet->setCellValue('C4', 'Tanggal Pinjam');
$sheet->setCellValue('D4', 'Tanggal Kembali');
$sheet->setCellValue('E4', 'Peminjam');
$sheet->setCellValue('F4', 'Buku');
$sheet->setCellValue('G4', 'Jumlah Pinjam Awal');
$sheet->setCellValue('H4', 'Sudah Kembali');
$sheet->setCellValue('I4', 'Sisa Pinjam');
$sheet->setCellValue('J4', 'Riwayat Pengembalian');
$sheet->setCellValue('K4', 'Status');
$sheet->setCellValue('L4', 'Keterlambatan');

// Style header
$sheet->getStyle('A4:L4')->getFont()->setBold(true);
$sheet->getStyle('A4:L4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Isi data
$row = 5;
$no = 1;
$current_peminjaman = null;

foreach ($peminjaman as $p) {
    // Skip jika ini adalah riwayat dari peminjaman yang sama
    if ($current_peminjaman == $p['id_peminjaman']) {
        continue;
    }
    $current_peminjaman = $p['id_peminjaman'];

    $status = 'Dipinjam';
    if ($p['status'] == 'kembali') {
        $status = 'Dikembalikan';
    } elseif ($p['hari_terlambat'] > 0) {
        $status = 'Terlambat';
    }

    $keterlambatan = ($p['status'] == 'dipinjam' && $p['hari_terlambat'] > 0) ? $p['hari_terlambat'] . ' hari' : '-';

    // Hitung sisa pinjaman
    $sisa_pinjam = $p['jumlah_pinjam_awal'] - $p['total_dikembalikan'];

    // Ambil semua riwayat pengembalian untuk peminjaman ini
    $riwayat = '';
    foreach ($peminjaman as $r) {
        if ($r['id_peminjaman'] == $p['id_peminjaman'] && $r['tanggal_pengembalian'] !== null) {
            $riwayat .= date('d/m/Y', strtotime($r['tanggal_pengembalian'])) . 
                       ': ' . $r['jumlah_dikembalikan'] . " buku\n";
        }
    }

    $sheet->setCellValue('A' . $row, $no++);
    $sheet->setCellValue('B' . $row, $p['nomor_peminjaman']);
    $sheet->setCellValue('C' . $row, date('d/m/Y', strtotime($p['tanggal_pinjam'])));
    $sheet->setCellValue('D' . $row, date('d/m/Y', strtotime($p['tanggal_kembali'])));
    $sheet->setCellValue('E' . $row, $p['nama_peminjam']);
    $sheet->setCellValue('F' . $row, $p['judul_buku']);
    $sheet->setCellValue('G' . $row, $p['jumlah_pinjam_awal']);
    $sheet->setCellValue('H' . $row, $p['total_dikembalikan']);
    $sheet->setCellValue('I' . $row, $sisa_pinjam);
    $sheet->setCellValue('J' . $row, $riwayat ?: '-');
    $sheet->setCellValue('K' . $row, $status);
    $sheet->setCellValue('L' . $row, $keterlambatan);

    // Set wrap text untuk kolom riwayat
    $sheet->getStyle('J' . $row)->getAlignment()->setWrapText(true);
    
    $row++;
}

// Auto size kolom
foreach (range('A', 'L') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Set tinggi baris sesuai konten
foreach (range(5, $row - 1) as $rowNum) {
    $sheet->getRowDimension($rowNum)->setRowHeight(-1);
}

// Set border
$styleArray = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
        ],
    ],
];
$sheet->getStyle('A4:L' . ($row - 1))->applyFromArray($styleArray);

// Set nama file
$filename = 'Laporan_Peminjaman_' . date('Y-m-d_H-i-s') . '.xlsx';

// Header untuk download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Export ke Excel
$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
