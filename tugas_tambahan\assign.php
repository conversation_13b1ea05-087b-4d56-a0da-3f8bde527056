<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/TugasTambahan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if ID is provided
if(!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];

// Initialize models
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;

// Get tugas tambahan details
if(!$tugasTambahan->getOne()) {
    $_SESSION['error'] = "Tugas tambahan tidak ditemukan!";
    header("Location: index.php");
    exit();
}

// Get guru_id if user is a teacher
$guru_id = null;
$user = new User();
if(isset($_SESSION['user_id']) && $_SESSION['role'] == 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get mapel details
$mapel = new MataPelajaran();
$mapel->id = $tugasTambahan->mapel_id;
$mapel->getOne();

// Get kelas list for the mapel
$kelas = new Kelas();
$jadwal = new JadwalPelajaran();

// If user is a teacher, only show classes taught by this teacher
if($_SESSION['role'] == 'guru' && $guru_id) {
    // Get classes where this teacher teaches this subject
    $kelas_list = $jadwal->getKelasByMapelAndGuru($tugasTambahan->mapel_id, $guru_id);
} else {
    // Admin can see all classes for this subject
    $kelas_list = $mapel->getKelasByMapel($tugasTambahan->mapel_id);
}

// Get assigned students
$assigned_students = $tugasTambahan->getAssignedStudents($tugas_id);
$assigned_student_ids = [];
while($student = $assigned_students->fetch(PDO::FETCH_ASSOC)) {
    $assigned_student_ids[] = $student['id'];
}

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['assign_students'])) {
        $kelas_id = $_POST['kelas_id'];
        $selection_type = $_POST['selection_type'];

        if($selection_type == 'all') {
            // Assign to all students in the class
            if($tugasTambahan->assignToClass($tugas_id, $kelas_id)) {
                $_SESSION['success'] = "Tugas tambahan berhasil diberikan kepada semua siswa di kelas!";
                header("Location: assign.php?id=$tugas_id");
                exit();
            } else {
                $_SESSION['error'] = "Gagal memberikan tugas tambahan kepada siswa!";
            }
        } elseif($selection_type == 'below_kkm') {
            // Assign to students with below KKM grades
            $below_kkm_students = $tugasTambahan->getSiswaBelowKKM(
                $tugasTambahan->mapel_id,
                $kelas_id,
                $tugasTambahan->semester,
                $tugasTambahan->tahun_ajaran
            );

            $student_ids = [];
            while($student = $below_kkm_students->fetch(PDO::FETCH_ASSOC)) {
                $student_ids[] = $student['id'];
            }

            if(!empty($student_ids)) {
                if($tugasTambahan->assignToMultipleStudents($tugas_id, $student_ids)) {
                    $_SESSION['success'] = "Tugas tambahan berhasil diberikan kepada siswa dengan nilai di bawah KKM!";
                    header("Location: assign.php?id=$tugas_id");
                    exit();
                } else {
                    $_SESSION['error'] = "Gagal memberikan tugas tambahan kepada siswa!";
                }
            } else {
                $_SESSION['error'] = "Tidak ada siswa dengan nilai di bawah KKM!";
            }
        } elseif($selection_type == 'low_attendance') {
            // Get minimum attendance percentage
            $min_attendance = isset($_POST['min_attendance']) ? intval($_POST['min_attendance']) : 75;

            // Validate min_attendance
            if($min_attendance < 1 || $min_attendance > 99) {
                $min_attendance = 75; // Default to 75% if invalid
            }

            // Assign to students with low attendance
            $low_attendance_students = $tugasTambahan->getSiswaLowAttendance(
                $tugasTambahan->mapel_id,
                $kelas_id,
                $tugasTambahan->semester,
                $tugasTambahan->tahun_ajaran,
                $min_attendance
            );

            $student_ids = [];
            while($student = $low_attendance_students->fetch(PDO::FETCH_ASSOC)) {
                $student_ids[] = $student['id'];
            }

            if(!empty($student_ids)) {
                if($tugasTambahan->assignToMultipleStudents($tugas_id, $student_ids)) {
                    $_SESSION['success'] = "Tugas tambahan berhasil diberikan kepada siswa dengan kehadiran kurang dari {$min_attendance}%!";
                    header("Location: assign.php?id=$tugas_id");
                    exit();
                } else {
                    $_SESSION['error'] = "Gagal memberikan tugas tambahan kepada siswa!";
                }
            } else {
                $_SESSION['error'] = "Tidak ada siswa dengan kehadiran kurang dari {$min_attendance}%!";
            }
        } elseif($selection_type == 'selected' && isset($_POST['siswa_ids'])) {
            // Assign to selected students
            $siswa_ids = $_POST['siswa_ids'];

            if(!empty($siswa_ids)) {
                if($tugasTambahan->assignToMultipleStudents($tugas_id, $siswa_ids)) {
                    $_SESSION['success'] = "Tugas tambahan berhasil diberikan kepada siswa yang dipilih!";
                    header("Location: assign.php?id=$tugas_id");
                    exit();
                } else {
                    $_SESSION['error'] = "Gagal memberikan tugas tambahan kepada siswa!";
                }
            } else {
                $_SESSION['error'] = "Tidak ada siswa yang dipilih!";
            }
        } else {
            $_SESSION['error'] = "Pilihan tidak valid!";
        }
    } elseif(isset($_POST['remove_student'])) {
        $siswa_id = $_POST['siswa_id'];

        if($tugasTambahan->removeStudent($tugas_id, $siswa_id)) {
            $_SESSION['success'] = "Siswa berhasil dihapus dari tugas tambahan!";
            header("Location: assign.php?id=$tugas_id");
            exit();
        } else {
            $_SESSION['error'] = "Gagal menghapus siswa dari tugas tambahan!";
        }
    }
}

// Handle success and error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Pilih Siswa - <?php echo $tugasTambahan->judul; ?></h1>
        <a href="tugas.php?mapel_id=<?php echo $tugasTambahan->mapel_id; ?>&semester=<?php echo $tugasTambahan->semester; ?>&tahun_ajaran=<?php echo $tugasTambahan->tahun_ajaran; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if($success_msg): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if($error_msg): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tambah Siswa</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST" id="assignForm">
                        <input type="hidden" name="assign_students" value="1">

                        <div class="mb-3">
                            <label for="kelas_id" class="form-label">Pilih Kelas</label>
                            <select class="form-select" id="kelas_id" name="kelas_id" required>
                                <option value="">-- Pilih Kelas --</option>
                                <?php while($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?php echo $row['id']; ?>"><?php echo $row['nama_kelas']; ?></option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Pilih Siswa</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="selection_type" id="all_students" value="all" checked>
                                <label class="form-check-label" for="all_students">
                                    Semua siswa di kelas
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="selection_type" id="below_kkm" value="below_kkm">
                                <label class="form-check-label" for="below_kkm">
                                    Siswa dengan nilai di bawah KKM (<?php echo $mapel->kkm; ?>)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="selection_type" id="low_attendance" value="low_attendance">
                                <label class="form-check-label" for="low_attendance">
                                    Siswa dengan kehadiran kurang
                                </label>
                            </div>
                            <div id="attendance_threshold" class="ms-4 mb-3" style="display: none;">
                                <label for="min_attendance" class="form-label">Minimal persentase kehadiran:</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="min_attendance" name="min_attendance" min="1" max="99" value="75">
                                    <span class="input-group-text">%</span>
                                </div>
                                <small class="form-text text-muted">Siswa dengan persentase kehadiran di bawah nilai ini akan dipilih</small>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="selection_type" id="selected_students" value="selected">
                                <label class="form-check-label" for="selected_students">
                                    Pilih siswa secara manual
                                </label>
                            </div>
                        </div>

                        <div id="student_selection" class="mb-3" style="display: none;">
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select_all">
                                            <label class="form-check-label" for="select_all">
                                                <strong>Pilih Semua</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="student_list" class="overflow-auto" style="max-height: 300px;">
                                        <p class="text-center text-muted">Pilih kelas terlebih dahulu</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Tambahkan Siswa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Siswa yang Sudah Ditambahkan</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="assignedStudents" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Siswa</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $assigned_students = $tugasTambahan->getAssignedStudents($tugas_id);
                                $no = 1;
                                while($row = $assigned_students->fetch(PDO::FETCH_ASSOC)):
                                    $status_text = $row['status'] == 'sudah_dikerjakan' ? 'Sudah Dikerjakan' : 'Belum Dikerjakan';
                                    $status_class = $row['status'] == 'sudah_dikerjakan' ? 'success' : 'warning';
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo $row['nis']; ?></td>
                                    <td><?php echo $row['nama_siswa']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <form action="" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus siswa ini dari tugas tambahan?');">
                                            <input type="hidden" name="remove_student" value="1">
                                            <input type="hidden" name="siswa_id" value="<?php echo $row['id']; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#assignedStudents').DataTable({
        "pageLength": 10,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });

    // Show/hide student selection based on radio button
    $('input[name="selection_type"]').change(function() {
        // Hide all conditional sections first
        $('#student_selection').hide();
        $('#attendance_threshold').hide();

        // Show the appropriate section based on selection
        if($(this).val() === 'selected') {
            $('#student_selection').show();
        } else if($(this).val() === 'low_attendance') {
            $('#attendance_threshold').show();
        }
    });

    // Load students when class is selected
    $('#kelas_id').change(function() {
        var kelas_id = $(this).val();
        if(kelas_id) {
            $.ajax({
                url: 'get_students.php',
                type: 'GET',
                data: {
                    kelas_id: kelas_id,
                    tugas_id: <?php echo $tugas_id; ?>
                },
                success: function(response) {
                    $('#student_list').html(response);
                },
                error: function() {
                    $('#student_list').html('<p class="text-center text-danger">Gagal memuat data siswa</p>');
                }
            });
        } else {
            $('#student_list').html('<p class="text-center text-muted">Pilih kelas terlebih dahulu</p>');
        }
    });

    // Select all students
    $(document).on('change', '#select_all', function() {
        $('.student-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Form validation
    $('#assignForm').submit(function(e) {
        var selection_type = $('input[name="selection_type"]:checked').val();
        var kelas_id = $('#kelas_id').val();

        if(!kelas_id) {
            e.preventDefault();
            alert('Silakan pilih kelas terlebih dahulu!');
            return false;
        }

        if(selection_type === 'selected') {
            var selected = $('.student-checkbox:checked').length;
            if(selected === 0) {
                e.preventDefault();
                alert('Silakan pilih minimal satu siswa!');
                return false;
            }
        } else if(selection_type === 'low_attendance') {
            var min_attendance = parseInt($('#min_attendance').val());
            if(isNaN(min_attendance) || min_attendance < 1 || min_attendance > 99) {
                e.preventDefault();
                alert('Persentase kehadiran harus berupa angka antara 1 dan 99!');
                return false;
            }
        }

        return true;
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
