<?php
require_once __DIR__ . '/../config/database.php';

class DetailJadwalJam {
    private $conn;
    public $id;
    public $jadwal_id;
    public $jam_ke;
    public $jam_mulai;
    public $jam_selesai;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        $query = "INSERT INTO detail_jadwal_jam (jadwal_id, jam_ke, jam_mulai, jam_selesai) 
                 VALUES (:jadwal_id, :jam_ke, :jam_mulai, :jam_selesai)";
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':jadwal_id', $this->jadwal_id);
        $stmt->bindParam(':jam_ke', $this->jam_ke);
        $stmt->bindParam(':jam_mulai', $this->jam_mulai);
        $stmt->bindParam(':jam_selesai', $this->jam_selesai);
        
        return $stmt->execute();
    }

    public function getByJadwalId($jadwal_id) {
        $query = "SELECT * FROM detail_jadwal_jam WHERE jadwal_id = :jadwal_id ORDER BY jam_ke";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':jadwal_id', $jadwal_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function deleteByJadwalId($jadwal_id) {
        $query = "DELETE FROM detail_jadwal_jam WHERE jadwal_id = :jadwal_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':jadwal_id', $jadwal_id);
        
        return $stmt->execute();
    }
}
