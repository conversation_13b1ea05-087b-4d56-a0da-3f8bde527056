<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../template/header.php';

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../index.php");
    exit();
}

$user = new User();
$user->id = $_SESSION['user_id'];
$user->getOne();

$message = '';
$error = '';

// Check if username has been changed before
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM username_changes WHERE user_id = :user_id");
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$has_changed_username = $result['count'] > 0;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_username'])) {
    if ($has_changed_username) {
        $error = "Anda sudah pernah mengubah username sebelumnya!";
    } else {
        $new_username = $_POST['new_username'];
        
        // Check if username already exists
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE username = :username AND id != :user_id");
        $stmt->bindParam(':username', $new_username);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            $error = "Username sudah digunakan!";
        } else {
            // Begin transaction
            $conn->beginTransaction();
            try {
                // Update username
                $user->username = $new_username;
                if ($user->updateUsername()) {
                    // Record username change
                    $stmt = $conn->prepare("INSERT INTO username_changes (user_id, old_username, new_username) VALUES (:user_id, :old_username, :new_username)");
                    $stmt->bindParam(':user_id', $_SESSION['user_id']);
                    $stmt->bindParam(':old_username', $_POST['old_username']);
                    $stmt->bindParam(':new_username', $new_username);
                    $stmt->execute();
                    
                    $conn->commit();
                    $message = "Username berhasil diubah!";
                    $_SESSION['username'] = $new_username;
                } else {
                    throw new Exception("Gagal mengubah username");
                }
            } catch (Exception $e) {
                $conn->rollBack();
                $error = $e->getMessage();
            }
        }
    }
}
?>

<div class="container">
    <h2 class="mb-4">Ubah Username</h2>

    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Ubah Username</h5>
                </div>
                <div class="card-body">
                    <?php if ($has_changed_username): ?>
                        <div class="alert alert-warning">
                            Anda sudah pernah mengubah username sebelumnya. Username hanya dapat diubah satu kali.
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <input type="hidden" name="old_username" value="<?php echo htmlspecialchars($user->username); ?>">
                            
                            <div class="mb-3">
                                <label for="current_username" class="form-label">Username Saat Ini</label>
                                <input type="text" class="form-control" id="current_username" value="<?php echo htmlspecialchars($user->username); ?>" readonly>
                            </div>

                            <div class="mb-3">
                                <label for="new_username" class="form-label">Username Baru</label>
                                <input type="text" class="form-control" id="new_username" name="new_username" required>
                                <div class="form-text">Username hanya dapat diubah satu kali.</div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" name="update_username" class="btn btn-primary">Ubah Username</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <div class="text-center mt-3">
                <a href="index.php" class="btn btn-secondary">Kembali ke Profil</a>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
