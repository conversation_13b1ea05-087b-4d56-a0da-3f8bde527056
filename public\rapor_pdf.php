<?php
// Prevent any output before PDF generation
ob_end_clean();
ob_start();

require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Rapor.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/ProfilSekolah.php';
require_once '../models/Tingkat.php';

use Dompdf\Dompdf;
use Dompdf\Options;

// Check if required parameters are provided
if (!isset($_GET['nis']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    die("Parameter tidak lengkap.");
}

// Get parameters
$nis = $_GET['nis'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Initialize models
$siswaModel = new Siswa();
$raporModel = new Rapor();

// Get student data
$siswa = $siswaModel->getByNIS($nis);
if (!$siswa) {
    die("Siswa dengan NIS $nis tidak ditemukan.");
}

// Get complete report card data
$rapor_data = $raporModel->getCompleteRaporData($siswa['id'], $semester, $tahun_ajaran);

// Format semester display
$semester_text = $semester == '1' ? 'Ganjil' : 'Genap';

// Generate HTML content for PDF
$html = generateRaporHTML($rapor_data, $semester, $semester_text, $tahun_ajaran, $raporModel);

// Configure DomPDF
$options = new Options();
$options->set('isHtml5ParserEnabled', true);
$options->set('isPhpEnabled', true);
$options->set('defaultFont', 'DejaVu Sans');
$options->set('isRemoteEnabled', true);

$dompdf = new Dompdf($options);
$dompdf->setPaper('A4', 'portrait');

// Load HTML to DomPDF
$dompdf->loadHtml($html);

// Render the PDF
$dompdf->render();

// Generate filename
$filename = 'Rapor_' . str_replace(' ', '_', $rapor_data['siswa']['nama_siswa']) . '_Semester_' . $semester . '_' . str_replace('/', '-', $tahun_ajaran) . '.pdf';

// Output the generated PDF
$dompdf->stream($filename, [
    'Attachment' => true
]);

exit(0);

function generateRaporHTML($rapor_data, $semester, $semester_text, $tahun_ajaran, $raporModel) {
    $html = '<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapor K13 - ' . htmlspecialchars($rapor_data['siswa']['nama_siswa']) . '</title>
    <style>
        body {
            font-family: "DejaVu Sans", Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 0;
            padding: 15px;
        }
        
        .rapor-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
        }
        
        .rapor-header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .rapor-header h3 {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .rapor-header p {
            font-size: 12px;
            margin: 3px 0;
        }
        
        .rapor-title {
            font-size: 14px;
            font-weight: bold;
            margin: 8px 0;
        }
        
        .student-info {
            margin-bottom: 20px;
        }
        
        .student-info table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .student-info td {
            padding: 2px 5px;
            vertical-align: top;
            font-size: 11px;
        }
        
        .grades-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .grades-table th,
        .grades-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            font-size: 10px;
        }
        
        .grades-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .grades-table .subject-name {
            text-align: left;
            padding-left: 6px;
        }
        
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .attendance-table th,
        .attendance-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            font-size: 10px;
        }
        
        .character-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .character-table th,
        .character-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin: 15px 0 8px 0;
        }
        
        .signature-section {
            margin-top: 25px;
            display: table;
            width: 100%;
        }
        
        .signature-box {
            display: table-cell;
            text-align: center;
            width: 33.33%;
            vertical-align: top;
            padding: 0 10px;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            height: 50px;
            margin: 10px 0 5px 0;
        }
        
        .notes-box {
            border: 1px solid #000;
            padding: 8px;
            min-height: 50px;
            font-size: 10px;
        }
        
        .promotion-box {
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="rapor-container">
        <!-- Header Section -->
        <div class="rapor-header">
            <h3>' . htmlspecialchars($rapor_data['school']['nama_sekolah']) . '</h3>
            <p>' . htmlspecialchars($rapor_data['school']['alamat_sekolah']) . '</p>
            <div class="rapor-title">RAPOR PESERTA DIDIK</div>
            <div class="rapor-title">KURIKULUM 2013</div>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <table>
                <tr>
                    <td width="20%">Nama Peserta Didik</td>
                    <td width="2%">:</td>
                    <td width="28%">' . htmlspecialchars($rapor_data['siswa']['nama_siswa']) . '</td>
                    <td width="20%">Kelas</td>
                    <td width="2%">:</td>
                    <td width="26%">' . htmlspecialchars($rapor_data['kelas']['nama_kelas']) . '</td>
                </tr>
                <tr>
                    <td>NIS</td>
                    <td>:</td>
                    <td>' . htmlspecialchars($rapor_data['siswa']['nis']) . '</td>
                    <td>Semester</td>
                    <td>:</td>
                    <td>' . htmlspecialchars($semester_text) . '</td>
                </tr>
                <tr>
                    <td>Sekolah</td>
                    <td>:</td>
                    <td>' . htmlspecialchars($rapor_data['school']['nama_sekolah']) . '</td>
                    <td>Tahun Pelajaran</td>
                    <td>:</td>
                    <td>' . htmlspecialchars($tahun_ajaran) . '</td>
                </tr>
                <tr>
                    <td>Alamat</td>
                    <td>:</td>
                    <td colspan="4">' . htmlspecialchars($rapor_data['siswa']['alamat'] ?: '-') . '</td>
                </tr>
            </table>
        </div>

        <!-- Character Assessment Section -->
        <div class="section-title">A. SIKAP</div>
        <table class="character-table">
            <tr>
                <th width="30%">Aspek yang Dinilai</th>
                <th width="10%">Nilai</th>
                <th width="60%">Deskripsi</th>
            </tr>
            <tr>
                <td>1. Sikap Spiritual</td>
                <td>' . htmlspecialchars($rapor_data['penilaian_sikap']['sikap_spiritual']) . '</td>
                <td>' . htmlspecialchars($raporModel->getCharacterDescription($rapor_data['penilaian_sikap']['sikap_spiritual'])) . '</td>
            </tr>
            <tr>
                <td>2. Sikap Sosial</td>
                <td>' . htmlspecialchars($rapor_data['penilaian_sikap']['sikap_sosial']) . '</td>
                <td>' . htmlspecialchars($raporModel->getCharacterDescription($rapor_data['penilaian_sikap']['sikap_sosial'])) . '</td>
            </tr>
        </table>

        <!-- Academic Grades Section -->
        <div class="section-title">B. PENGETAHUAN DAN KETERAMPILAN</div>
        <table class="grades-table">
            <thead>
                <tr>
                    <th rowspan="2" width="5%">No</th>
                    <th rowspan="2" width="35%">Mata Pelajaran</th>
                    <th colspan="2" width="20%">Pengetahuan</th>
                    <th colspan="2" width="20%">Keterampilan</th>
                    <th rowspan="2" width="20%">Deskripsi</th>
                </tr>
                <tr>
                    <th>Nilai</th>
                    <th>Predikat</th>
                    <th>Nilai</th>
                    <th>Predikat</th>
                </tr>
            </thead>
            <tbody>';

    $no = 1;
    foreach ($rapor_data['nilai_akademik'] as $nilai) {
        $grade_desc = $raporModel->getGradeDescription($nilai['nilai_akhir'], $nilai['kkm']);
        $html .= '
                <tr>
                    <td>' . $no++ . '</td>
                    <td class="subject-name">' . htmlspecialchars($nilai['nama_mapel']) . '</td>
                    <td>' . ($nilai['nilai_akhir'] ?: '-') . '</td>
                    <td>' . htmlspecialchars($grade_desc['predikat']) . '</td>
                    <td>' . ($nilai['nilai_akhir'] ?: '-') . '</td>
                    <td>' . htmlspecialchars($grade_desc['predikat']) . '</td>
                    <td>' . htmlspecialchars($grade_desc['deskripsi']) . '</td>
                </tr>';
    }

    $html .= '
            </tbody>
        </table>

        <!-- Attendance Section -->
        <div class="section-title">C. KETIDAKHADIRAN</div>
        <table class="attendance-table">
            <tr>
                <th width="50%">Jenis Ketidakhadiran</th>
                <th width="50%">Jumlah</th>
            </tr>
            <tr>
                <td>Sakit</td>
                <td>' . $rapor_data['kehadiran']['sakit'] . ' hari</td>
            </tr>
            <tr>
                <td>Izin</td>
                <td>' . $rapor_data['kehadiran']['izin'] . ' hari</td>
            </tr>
            <tr>
                <td>Tanpa Keterangan</td>
                <td>' . $rapor_data['kehadiran']['alpha'] . ' hari</td>
            </tr>
        </table>';

    // Extracurricular Section
    if (!empty($rapor_data['ekstrakurikuler'])) {
        $html .= '
        <div class="section-title">D. EKSTRAKURIKULER</div>
        <table class="attendance-table">
            <tr>
                <th width="50%">Kegiatan Ekstrakurikuler</th>
                <th width="25%">Nilai</th>
                <th width="25%">Keterangan</th>
            </tr>';

        foreach ($rapor_data['ekstrakurikuler'] as $ekskul) {
            $html .= '
            <tr>
                <td>' . htmlspecialchars($ekskul['nama_ekstrakurikuler']) . '</td>
                <td>' . htmlspecialchars($ekskul['nilai']) . '</td>
                <td>' . htmlspecialchars($ekskul['keterangan']) . '</td>
            </tr>';
        }

        $html .= '
        </table>';
    }

    // Achievements Section
    if (!empty($rapor_data['prestasi'])) {
        $html .= '
        <div class="section-title">E. PRESTASI</div>
        <table class="attendance-table">
            <tr>
                <th width="50%">Jenis Prestasi</th>
                <th width="50%">Keterangan</th>
            </tr>';

        foreach ($rapor_data['prestasi'] as $prestasi) {
            $html .= '
            <tr>
                <td>' . htmlspecialchars($prestasi['jenis_prestasi']) . '</td>
                <td>' . htmlspecialchars($prestasi['keterangan']) . '</td>
            </tr>';
        }

        $html .= '
        </table>';
    }

    // Notes Section
    $html .= '
        <div class="section-title">F. CATATAN WALI KELAS</div>
        <div class="notes-box">
            ' . htmlspecialchars($rapor_data['catatan_wali_kelas'] ?: 'Tidak ada catatan khusus.') . '
        </div>';

    // Class Promotion Section
    if ($semester == '2' && !empty($rapor_data['status_kenaikan'])) {
        $html .= '
        <div class="section-title">G. KEPUTUSAN</div>
        <div class="promotion-box">
            Berdasarkan pencapaian kompetensi pada semester ke-1 dan ke-2, peserta didik ditetapkan:
            <br><br>
            <strong>' . htmlspecialchars($rapor_data['status_kenaikan']) . '</strong>
        </div>';
    }

    // Signature Section
    $html .= '
        <div class="signature-section">
            <div class="signature-box">
                <p>Mengetahui,<br>Orang Tua/Wali</p>
                <div class="signature-line"></div>
                <p>(...........................)</p>
            </div>

            <div class="signature-box">
                <p>' . htmlspecialchars($rapor_data['school']['kota']) . ', ' . date('d F Y') . '<br>Wali Kelas</p>
                <div class="signature-line"></div>
                <p>(...........................)</p>
            </div>

            <div class="signature-box">
                <p>Mengetahui,<br>Kepala Sekolah</p>
                <div class="signature-line"></div>
                <p>' . htmlspecialchars($rapor_data['school']['kepala_sekolah']) . '<br>NIP. ' . htmlspecialchars($rapor_data['school']['nip_kepala_sekolah']) . '</p>
            </div>
        </div>
    </div>
</body>
</html>';

    return $html;
}
