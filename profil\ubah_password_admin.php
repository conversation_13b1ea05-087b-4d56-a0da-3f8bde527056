<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_password'])) {
        $user = new User();
        $user->id = $_SESSION['user_id'];
        
        if ($user->getOne()) {
            $current_password = $_POST['current_password'];
            
            // Verify current password
            if (password_verify($current_password, $user->password)) {
                $new_password = $_POST['new_password'];
                $confirm_password = $_POST['confirm_password'];
                
                // Validate new password
                if ($new_password === $confirm_password) {
                    if (strlen($new_password) >= 6) {
                        $user->password = password_hash($new_password, PASSWORD_DEFAULT);
                        
                        if ($user->update()) {
                            $success = "Password berhasil diubah!";
                        } else {
                            $error = "Gagal mengubah password!";
                        }
                    } else {
                        $error = "Password baru minimal 6 karakter!";
                    }
                } else {
                    $error = "Password baru dan konfirmasi password tidak sama!";
                }
            } else {
                $error = "Password saat ini tidak valid!";
            }
        } else {
            $error = "User tidak ditemukan!";
        }
    }
}
?>

<div class="container">
    <h2 class="mb-4">Ubah Password Admin</h2>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Ubah Password</h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Password Saat Ini</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">Password Baru</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <div class="form-text">Password minimal 6 karakter.</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" name="update_password" class="btn btn-primary">Simpan</button>
                            <a href="../index.php" class="btn btn-secondary">Kembali</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
