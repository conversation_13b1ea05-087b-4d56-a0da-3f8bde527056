<?php
/**
 * AJAX endpoint for asynchronous question generation
 * This prevents browser timeouts and provides progress feedback
 */

// Set proper headers for AJAX response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Increase PHP execution time and memory for this operation
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '512M');

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Rpp.php';
require_once __DIR__ . '/../models/Guru.php';
require_once __DIR__ . '/../models/GeminiApi.php';

// Function to send JSON response
function sendResponse($success, $data = null, $message = '', $progress = null, $error_type = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s'),
        'server_info' => [
            'php_version' => phpversion(),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'execution_time' => round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 2) . 's'
        ]
    ];

    if ($progress !== null) {
        $response['progress'] = $progress;
    }

    if ($error_type !== null) {
        $response['error_type'] = $error_type;
    }

    // Ensure proper JSON headers
    if (!headers_sent()) {
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST');
        header('Access-Control-Allow-Headers: Content-Type');
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// Check authentication
try {
    checkGuruAccess();
} catch (Exception $e) {
    sendResponse(false, null, 'Akses tidak diizinkan: ' . $e->getMessage());
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, null, 'Method tidak diizinkan', null, 'method');
}

// Basic connectivity and configuration check
try {
    // Test database connection
    $database = new Database();
    $conn = $database->getConnection();
    if (!$conn) {
        sendResponse(false, null, 'Koneksi database gagal. Silakan coba lagi.', null, 'database');
    }

    // Test API key availability
    $api_key = $database->getGeminiApiKey();
    if (empty($api_key)) {
        sendResponse(false, null, 'Konfigurasi API tidak ditemukan. Silakan hubungi administrator.', null, 'config');
    }

    // Test basic network connectivity (quick test)
    if (!function_exists('curl_init')) {
        sendResponse(false, null, 'cURL extension tidak tersedia. Silakan hubungi administrator.', null, 'system');
    }

} catch (Exception $e) {
    sendResponse(false, null, 'Kesalahan sistem: ' . $e->getMessage(), null, 'system');
}

// Validate required fields
$required_fields = ['rpp_id', 'multiple_choice_count', 'essay_count', 'multiple_choice_options', 
                   'regular_count', 'hots_easy_count', 'hots_medium_count', 'hots_hard_count'];

foreach ($required_fields as $field) {
    if (!isset($_POST[$field])) {
        sendResponse(false, null, "Field '$field' tidak ditemukan");
    }
}

$rpp_id = (int)$_POST['rpp_id'];
$config = [
    'multiple_choice_count' => (int)$_POST['multiple_choice_count'],
    'essay_count' => (int)$_POST['essay_count'],
    'multiple_choice_options' => (int)$_POST['multiple_choice_options'],
    'regular_count' => (int)$_POST['regular_count'],
    'hots_easy_count' => (int)$_POST['hots_easy_count'],
    'hots_medium_count' => (int)$_POST['hots_medium_count'],
    'hots_hard_count' => (int)$_POST['hots_hard_count']
];

// Validate configuration
$total_by_type = $config['multiple_choice_count'] + $config['essay_count'];
$total_by_difficulty = $config['regular_count'] + $config['hots_easy_count'] + 
                      $config['hots_medium_count'] + $config['hots_hard_count'];
$max_total = max($total_by_type, $total_by_difficulty);

if ($max_total > 10 || $max_total === 0) {
    sendResponse(false, null, 'Total soal harus antara 1-10 untuk performa optimal');
}

try {
    // Get teacher ID
    $guru = new Guru();
    $stmt = $guru->getByUserId($_SESSION['user_id']);
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $guru_id = $row['id'];
    } else {
        sendResponse(false, null, 'Data guru tidak ditemukan');
    }

    // Get RPP data
    $rpp = new Rpp();
    $rpp_data = $rpp->getOne($rpp_id);

    if (!$rpp_data || $rpp_data['guru_id'] != $guru_id) {
        sendResponse(false, null, 'RPP tidak ditemukan atau bukan milik Anda');
    }

    // Log initial progress (don't send response yet as it would exit the script)
    error_log("Starting question generation process...");

    // Initialize Gemini API with progress tracking
    $geminiApi = new GeminiApi();
    
    // Add progress callback if supported
    if (method_exists($geminiApi, 'setProgressCallback')) {
        $geminiApi->setProgressCallback(function($progress, $message) {
            // Send progress update (this would require WebSocket or Server-Sent Events for real-time updates)
            // For now, we'll just log it
            error_log("Progress: $progress% - $message");
        });
    }

    // Generate questions with timeout handling
    $start_time = time();
    $timeout_seconds = 240; // 4 minutes timeout
    
    $generated_questions = $geminiApi->generateQuestions($rpp_data, $config);
    
    $execution_time = time() - $start_time;
    
    if (empty($generated_questions)) {
        sendResponse(false, null, 'Tidak ada soal yang berhasil dihasilkan. Silakan coba lagi.');
    }

    // Store questions in session for preview page
    $_SESSION['generated_questions'] = $generated_questions;
    $_SESSION['question_config'] = $config;
    $_SESSION['current_rpp_id'] = $rpp_id;
    $_SESSION['generation_timestamp'] = time();

    // Send success response
    sendResponse(true, [
        'questions' => $generated_questions,
        'total_questions' => count($generated_questions),
        'execution_time' => $execution_time,
        'rpp_id' => $rpp_id,
        'config' => $config
    ], 'Soal berhasil dihasilkan!', 100);

} catch (Exception $e) {
    // Log the error for debugging
    error_log("Question generation error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    $error_message = $e->getMessage();
    $error_type = 'unknown';

    // Categorize error types for better user feedback
    if (strpos($error_message, 'timeout') !== false ||
        strpos($error_message, 'Maximum execution time') !== false) {
        $error_type = 'timeout';
        $user_message = 'Proses generate soal timeout. Silakan coba dengan jumlah soal yang lebih sedikit atau coba lagi nanti.';
    } elseif (strpos($error_message, 'cURL Error') !== false ||
              strpos($error_message, 'connection') !== false) {
        $error_type = 'network';
        $user_message = 'Terjadi kesalahan koneksi jaringan. Periksa koneksi internet Anda dan coba lagi.';
    } elseif (strpos($error_message, 'JSON') !== false ||
              strpos($error_message, 'parsing') !== false) {
        $error_type = 'parsing';
        $user_message = 'Terjadi kesalahan dalam memproses response AI. Silakan coba lagi.';
    } elseif (strpos($error_message, 'API Error') !== false) {
        $error_type = 'api';
        $user_message = 'Terjadi kesalahan pada layanan AI. Silakan coba lagi dalam beberapa saat.';
    } elseif (strpos($error_message, 'API key') !== false) {
        $error_type = 'auth';
        $user_message = 'Konfigurasi API tidak valid. Silakan hubungi administrator.';
    } elseif (strpos($error_message, 'Data guru') !== false ||
              strpos($error_message, 'RPP tidak ditemukan') !== false) {
        $error_type = 'data';
        $user_message = $error_message;
    } else {
        $error_type = 'general';
        $user_message = 'Gagal generate soal: ' . $error_message;
    }

    sendResponse(false, null, $user_message, null, $error_type);
}
?>
