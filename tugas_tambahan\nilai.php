<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../config/database.php';
require_once '../models/TugasTambahan.php';
require_once '../models/MataPelajaran.php';
require_once '../template/header.php';

// Check if ID is provided
if(!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];

// Initialize models
$tugasTambahan = new TugasTambahan();
$tugasTambahan->id = $tugas_id;

// Get tugas tambahan details
if(!$tugasTambahan->getOne()) {
    $_SESSION['error'] = "Tugas tambahan tidak ditemukan!";
    header("Location: index.php");
    exit();
}

// Get mapel details
$mapel = new MataPelajaran();
$mapel->id = $tugasTambahan->mapel_id;
$mapel->getOne();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['update_nilai'])) {
        $success = true;

        foreach($_POST['status'] as $siswa_id => $status) {
            $nilai = isset($_POST['nilai'][$siswa_id]) ? $_POST['nilai'][$siswa_id] : null;

            if(!$tugasTambahan->updateNilai($tugas_id, $siswa_id, $status, $nilai)) {
                $success = false;
                break;
            }
        }

        if($success) {
            $_SESSION['success'] = "Nilai tugas tambahan berhasil disimpan!";
            header("Location: nilai.php?id=$tugas_id");
            exit();
        } else {
            $_SESSION['error'] = "Gagal menyimpan nilai tugas tambahan!";
        }
    }
}

// Get assigned students
$assigned_students = $tugasTambahan->getAssignedStudents($tugas_id);

// Handle success and error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success']);
unset($_SESSION['error']);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Input Nilai Tugas Tambahan</h1>
        <a href="tugas.php?mapel_id=<?php echo $tugasTambahan->mapel_id; ?>&semester=<?php echo $tugasTambahan->semester; ?>&tahun_ajaran=<?php echo $tugasTambahan->tahun_ajaran; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Detail Tugas Tambahan</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Mata Pelajaran</th>
                                    <td>: <?php echo $mapel->nama_mapel; ?></td>
                                </tr>
                                <tr>
                                    <th>Judul Tugas</th>
                                    <td>: <?php echo $tugasTambahan->judul; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Tanggal</th>
                                    <td>: <?php echo date('d-m-Y', strtotime($tugasTambahan->tanggal)); ?></td>
                                </tr>
                                <tr>
                                    <th>Semester/TA</th>
                                    <td>: <?php echo $tugasTambahan->semester; ?> / <?php echo $tugasTambahan->tahun_ajaran; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php if(!empty($tugasTambahan->deskripsi)): ?>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6>Deskripsi Tugas:</h6>
                            <p><?php echo nl2br($tugasTambahan->deskripsi); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if($success_msg): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if($error_msg): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_msg; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">Input Nilai Siswa</h5>
                </div>
                <div class="card-body">
                    <?php if($assigned_students->rowCount() > 0): ?>
                    <form action="" method="POST">
                        <input type="hidden" name="update_nilai" value="1">

                        <div class="table-responsive">
                            <table class="table table-bordered" id="tableNilai" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Siswa</th>
                                        <th>Status</th>
                                        <th>Nilai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while($row = $assigned_students->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo $row['nis']; ?></td>
                                        <td><?php echo $row['nama_siswa']; ?></td>
                                        <td>
                                            <select class="form-select" name="status[<?php echo $row['id']; ?>]" onchange="toggleNilaiInput(this, '<?php echo $row['id']; ?>')">
                                                <option value="belum_dikerjakan" <?php echo $row['status'] == 'belum_dikerjakan' ? 'selected' : ''; ?>>Belum Dikerjakan</option>
                                                <option value="sudah_dikerjakan" <?php echo $row['status'] == 'sudah_dikerjakan' ? 'selected' : ''; ?>>Sudah Dikerjakan</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" class="form-control nilai-input" id="nilai_<?php echo $row['id']; ?>" name="nilai[<?php echo $row['id']; ?>]" min="0" max="100" step="0.01" value="<?php echo $row['nilai']; ?>" <?php echo $row['status'] == 'belum_dikerjakan' ? 'disabled' : ''; ?>>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between mt-3">
                            <a href="../nilai/index.php?semester=<?php echo $tugasTambahan->semester; ?>&tahun_ajaran=<?php echo $tugasTambahan->tahun_ajaran; ?>" class="btn btn-success">
                                <i class="fas fa-exchange-alt"></i> Gunakan untuk Penggantian Nilai
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Nilai
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-info">
                        Belum ada siswa yang ditambahkan ke tugas tambahan ini.
                        <a href="assign.php?id=<?php echo $tugas_id; ?>" class="alert-link">Klik di sini</a> untuk menambahkan siswa.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableNilai').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});

function toggleNilaiInput(selectElement, siswaId) {
    var nilaiInput = document.getElementById('nilai_' + siswaId);
    if(selectElement.value === 'sudah_dikerjakan') {
        nilaiInput.disabled = false;
        nilaiInput.focus();
    } else {
        nilaiInput.disabled = true;
        nilaiInput.value = '';
    }
}
</script>

<?php
require_once '../template/footer.php';
?>
