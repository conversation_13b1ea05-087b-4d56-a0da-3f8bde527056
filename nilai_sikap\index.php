<?php
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/NilaiSikap.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';

// Cek jika user belum login atau bukan guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: ../login.php");
    exit();
}

$nilaiSikap = new NilaiSikap();
$kelas = new Kelas();
$mataPelajaran = new MataPelajaran();
$guru = new Guru();
$jadwal = new JadwalPelajaran();
$tahunAjaran = new TahunAjaran();

// Get active period
$periodeAktif = new PeriodeAktif();
$periodeAktif->getActive();

// Ambil data guru berdasarkan user_id
$stmt = $guru->getByUserId($_SESSION['user_id']);
$dataGuru = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$dataGuru) {
    $_SESSION['error'] = "Anda tidak memiliki akses sebagai guru aktif";
    header("Location: ../index.php");
    exit();
}

// Ambil data untuk filter
$dataKelas = $kelas->getAll()->fetchAll(PDO::FETCH_ASSOC);

// Get academic years from TahunAjaran table instead of Kelas table for better reliability
$dataTahunAjaran = [];
$tahunAjaranResult = $tahunAjaran->getAll();
while ($row = $tahunAjaranResult->fetch(PDO::FETCH_ASSOC)) {
    $dataTahunAjaran[] = $row['tahun_ajaran'];
}

// If no academic years exist, create a default one
if (empty($dataTahunAjaran)) {
    $currentYear = date('Y');
    $defaultTahunAjaran = $currentYear . '/' . ($currentYear + 1);
    $dataTahunAjaran[] = $defaultTahunAjaran;
}

// Ambil parameter filter
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$semester = isset($_GET['semester']) ? $_GET['semester'] : ($periodeAktif->semester ?: '1');
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : ($periodeAktif->tahun_ajaran ?: date('Y') . '/' . (date('Y') + 1));
$mapel_id = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';

// Ambil mata pelajaran berdasarkan kelas yang dipilih
$dataMapel = [];
if ($kelas_id) {
    $dataMapel = $jadwal->getMapelByKelasAndGuru($kelas_id, $dataGuru['id'])->fetchAll(PDO::FETCH_ASSOC);
}

// Ambil data siswa dan nilai sikap sesuai filter
$data = [];
if ($kelas_id && $mapel_id) {
    // Get all students in the class for the selected period, with their attitude scores if any
    $query = "SELECT s.id as siswa_id, s.nis, s.nama_siswa, k.nama_kelas, mp.nama_mapel,
                     ns.id as nilai_id, ns.nilai_spiritual, ns.deskripsi_spiritual,
                     ns.nilai_sosial, ns.deskripsi_sosial, ns.created_at, ns.updated_at
              FROM siswa s
              JOIN siswa_periode sp ON s.id = sp.siswa_id
              JOIN kelas k ON sp.kelas_id = k.id
              JOIN mata_pelajaran mp ON mp.id = :mapel_id
              LEFT JOIN nilai_sikap ns ON (s.id = ns.siswa_id
                                          AND ns.kelas_id = sp.kelas_id
                                          AND ns.mapel_id = :mapel_id
                                          AND ns.semester = :semester
                                          AND ns.tahun_ajaran = :tahun_ajaran)
              WHERE sp.kelas_id = :kelas_id
              AND sp.tahun_ajaran = :tahun_ajaran
              AND sp.semester = :semester
              AND sp.is_current = 1
              ORDER BY s.nama_siswa ASC";

    $database = new Database();
    $conn = $database->getConnection();
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':kelas_id', $kelas_id);
    $stmt->bindParam(':mapel_id', $mapel_id);
    $stmt->bindParam(':semester', $semester);
    $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
    $stmt->execute();

    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

?>

<script>
$(document).ready(function() {
    // Fungsi untuk submit form
    function submitForm() {
        $('form').submit();
    }

    // Event handler untuk setiap perubahan pada dropdown
    $('#kelas_id').change(function() {
        var kelas_id = $(this).val();
        if(kelas_id) {
            $.ajax({
                url: '../nilai_sikap/get_mapel_by_kelas.php',
                type: 'POST',
                data: {kelas_id: kelas_id},
                success: function(response) {
                    $('#mapel_id').html(response);
                    submitForm();
                }
            });
        } else {
            $('#mapel_id').html('<option value="">Pilih Mata Pelajaran</option>');
            submitForm();
        }
    });

    // Trigger submit form ketika dropdown berubah
    $('#mapel_id, #semester, #tahun_ajaran').change(function() {
        submitForm();
    });
});
</script>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Data Nilai Sikap</h3>
                    <?php if ($kelas_id && $semester && $tahun_ajaran): ?>
                    <div>
                        <a href="../nilai_sikap/export_pdf.php?kelas_id=<?= $kelas_id ?>&semester=<?= $semester ?>&tahun_ajaran=<?= $tahun_ajaran ?>&mapel_id=<?= $mapel_id ?>" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </a>
                        <a href="../nilai_sikap/export_excel.php?kelas_id=<?= $kelas_id ?>&semester=<?= $semester ?>&tahun_ajaran=<?= $tahun_ajaran ?>&mapel_id=<?= $mapel_id ?>" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <!-- Form Filter -->
                    <form method="GET" action="" class="mb-4">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="kelas_id">Kelas</label>
                                    <select name="kelas_id" id="kelas_id" class="form-control">
                                        <option value="">Pilih Kelas</option>
                                        <?php foreach ($dataKelas as $k): ?>
                                        <option value="<?= $k['id'] ?>" <?= $kelas_id == $k['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($k['nama_kelas']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="mapel_id">Mata Pelajaran</label>
                                    <select name="mapel_id" id="mapel_id" class="form-control">
                                        <option value="">Pilih Mata Pelajaran</option>
                                        <?php foreach ($dataMapel as $m): ?>
                                        <option value="<?= $m['id'] ?>" <?= $mapel_id == $m['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($m['nama_mapel']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="semester">Semester</label>
                                    <select name="semester" id="semester" class="form-control">
                                        <option value="">Pilih Semester</option>
                                        <option value="1" <?= $semester == '1' ? 'selected' : '' ?>>1</option>
                                        <option value="2" <?= $semester == '2' ? 'selected' : '' ?>>2</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="tahun_ajaran">Tahun Ajaran</label>
                                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-control">
                                        <option value="">Pilih Tahun Ajaran</option>
                                        <?php foreach ($dataTahunAjaran as $ta): ?>
                                        <option value="<?= $ta ?>" <?= $tahun_ajaran == $ta ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($ta) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <?php if ($kelas_id && $mapel_id && $semester && $tahun_ajaran): ?>
                                <a href="create.php?kelas_id=<?= $kelas_id ?>&semester=<?= $semester ?>&tahun_ajaran=<?= $tahun_ajaran ?>&mapel_id=<?= $mapel_id ?>" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Input Nilai
                                </a>
                                <?php elseif ($kelas_id && !$mapel_id): ?>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle"></i> Pilih mata pelajaran untuk dapat menginput nilai sikap.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>

                    <!-- Pesan informasi jika data tidak ditemukan -->
                    <?php if ($kelas_id && $mapel_id && empty($data)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Tidak ada siswa ditemukan untuk kelas dan mata pelajaran yang dipilih.
                        <br>
                        <small>Pastikan kelas memiliki siswa yang terdaftar untuk periode akademik yang dipilih.</small>
                    </div>
                    <?php elseif ($kelas_id && $mapel_id && !empty($data)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Menampilkan <?= count($data) ?> siswa.
                        Siswa yang belum memiliki nilai sikap dapat ditambahkan melalui tombol "Input Nilai".
                    </div>
                    <?php endif; ?>

                    <!-- Tabel Data -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Siswa</th>
                                    <th>Kelas</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Nilai Spiritual</th>
                                    <th>Deskripsi Spiritual</th>
                                    <th>Nilai Sosial</th>
                                    <th>Deskripsi Sosial</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($data)): ?>
                                <tr>
                                    <td colspan="10" class="text-center">Tidak ada data</td>
                                </tr>
                                <?php else: ?>
                                    <?php
                                    $no = 1;
                                    foreach ($data as $row):
                                    ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= htmlspecialchars($row['nis']) ?></td>
                                        <td><?= htmlspecialchars($row['nama_siswa']) ?></td>
                                        <td><?= htmlspecialchars($row['nama_kelas']) ?></td>
                                        <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                        <td class="text-center">
                                            <?= $row['nilai_spiritual'] ? htmlspecialchars($row['nilai_spiritual']) : '<span class="text-muted">-</span>' ?>
                                        </td>
                                        <td style="max-width: 300px;">
                                            <?= $row['deskripsi_spiritual'] ? htmlspecialchars($row['deskripsi_spiritual']) : '<span class="text-muted">Belum ada deskripsi</span>' ?>
                                        </td>
                                        <td class="text-center">
                                            <?= $row['nilai_sosial'] ? htmlspecialchars($row['nilai_sosial']) : '<span class="text-muted">-</span>' ?>
                                        </td>
                                        <td style="max-width: 300px;">
                                            <?= $row['deskripsi_sosial'] ? htmlspecialchars($row['deskripsi_sosial']) : '<span class="text-muted">Belum ada deskripsi</span>' ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <?php if ($row['nilai_id']): ?>
                                                    <!-- Student has attitude scores -->
                                                    <a href="view.php?id=<?= $row['nilai_id'] ?>" class="btn btn-info btn-sm" title="Lihat">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit.php?id=<?= $row['nilai_id'] ?>" class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete.php?id=<?= $row['nilai_id'] ?>" class="btn btn-danger btn-sm" title="Hapus" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <!-- Student doesn't have attitude scores yet -->
                                                    <span class="text-muted">Belum ada nilai</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>