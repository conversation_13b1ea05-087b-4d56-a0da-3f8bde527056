<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/MultiRppExam.php';
require_once '../models/Guru.php';

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

$multiRppExam = new MultiRppExam();

// Handle delete request
if (isset($_GET['delete']) && isset($_GET['id'])) {
    $exam_id = $_GET['id'];
    
    // Verify ownership
    $stmt = $multiRppExam->getOne($exam_id);
    $exam_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($exam_data && $exam_data['guru_id'] == $guru_id) {
        if ($multiRppExam->delete($exam_id)) {
            $_SESSION['success'] = "Ujian multi-RPP berhasil dihapus.";
        } else {
            $_SESSION['error'] = "Gagal menghapus ujian multi-RPP.";
        }
    } else {
        $_SESSION['error'] = "Ujian tidak ditemukan atau bukan milik Anda.";
    }
    
    header("Location: multi_rpp_list.php");
    exit();
}

// Get all multi-RPP exams for this teacher
$exams_stmt = $multiRppExam->getAllByGuru($guru_id);

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list-alt"></i> Daftar Ujian Multi-RPP
            </h5>
            <div>
                <a href="multi_rpp_generate.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buat Ujian Multi-RPP
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke RPP
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($exams_stmt && $exams_stmt->rowCount() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Judul Ujian</th>
                                <th>Jenis</th>
                                <th>Semester</th>
                                <th>Tahun Ajaran</th>
                                <th>Durasi</th>
                                <th>Total Soal</th>
                                <th>Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while ($exam = $exams_stmt->fetch(PDO::FETCH_ASSOC)): 
                                // Get question count for this exam
                                $questions_stmt = $multiRppExam->getQuestionsByExamId($exam['id']);
                                $question_count = $questions_stmt->rowCount();
                            ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($exam['exam_title']) ?></strong>
                                        <?php if (!empty($exam['additional_notes'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($exam['additional_notes'], 0, 50)) ?><?= strlen($exam['additional_notes']) > 50 ? '...' : '' ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= htmlspecialchars($exam['exam_type']) ?></span>
                                    </td>
                                    <td><?= htmlspecialchars($exam['semester']) ?></td>
                                    <td><?= htmlspecialchars($exam['tahun_ajaran']) ?></td>
                                    <td><?= htmlspecialchars($exam['exam_duration']) ?> menit</td>
                                    <td>
                                        <span class="badge bg-info"><?= $question_count ?> soal</span>
                                    </td>
                                    <td>
                                        <small><?= date('d/m/Y H:i', strtotime($exam['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="multi_rpp_detail.php?exam_id=<?= $exam['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($question_count > 0): ?>
                                                <a href="multi_rpp_export.php?exam_id=<?= $exam['id'] ?>&format=pdf" 
                                                   class="btn btn-sm btn-outline-danger" title="Export PDF" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                <a href="multi_rpp_export.php?exam_id=<?= $exam['id'] ?>&format=word" 
                                                   class="btn btn-sm btn-outline-primary" title="Export Word">
                                                    <i class="fas fa-file-word"></i>
                                                </a>
                                                <a href="multi_blueprint_generate.php?exam_id=<?= $exam['id'] ?>" 
                                                   class="btn btn-sm btn-outline-success" title="Generate Kisi-kisi">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(<?= $exam['id'] ?>, '<?= htmlspecialchars($exam['exam_title']) ?>')" 
                                                    title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-layer-group fa-3x text-muted"></i>
                    </div>
                    <h5 class="text-muted">Belum Ada Ujian Multi-RPP</h5>
                    <p class="text-muted mb-4">
                        Anda belum membuat ujian multi-RPP. Ujian multi-RPP memungkinkan Anda membuat soal 
                        komprehensif yang menggabungkan materi dari beberapa RPP/chapter sekaligus.
                    </p>
                    <a href="multi_rpp_generate.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Buat Ujian Multi-RPP Pertama
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus ujian multi-RPP:</p>
                <p><strong id="examTitle"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Peringatan:</strong> Tindakan ini akan menghapus ujian beserta semua soal yang terkait dan tidak dapat dibatalkan.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <a href="#" id="deleteConfirmBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Ya, Hapus
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(examId, examTitle) {
    document.getElementById('examTitle').textContent = examTitle;
    document.getElementById('deleteConfirmBtn').href = 'multi_rpp_list.php?delete=1&id=' + examId;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.badge {
    font-size: 0.75rem;
}

.card-title {
    color: #495057;
}

.fa-3x {
    font-size: 3rem;
}
</style>

<?php require_once '../template/footer.php'; ?>
