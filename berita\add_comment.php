<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Silakan login terlebih dahulu']);
    exit();
}

require_once '../models/KomentarBerita.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$komentar = new KomentarBerita();
$komentar->berita_id = $_POST['berita_id'] ?? null;
$komentar->user_id = $_SESSION['user_id'];
$komentar->komentar = trim($_POST['komentar'] ?? '');
$komentar->parent_id = !empty($_POST['parent_id']) ? $_POST['parent_id'] : null;

if (empty($komentar->berita_id) || empty($komentar->komentar)) {
    echo json_encode(['success' => false, 'message' => 'Berita ID dan komentar harus diisi']);
    exit();
}

if ($komentar->create()) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Gagal menambahkan komentar']);
}
?>
