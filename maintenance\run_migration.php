<?php
/**
 * Database Migration Runner
 * Runs the migration script to fix restore conflicts
 * Created: 2025-08-08
 */

require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';

// Only admin can access this tool
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        $database = new Database();
        $pdo = $database->getConnection();
        
        // Read the migration file
        $migration_file = __DIR__ . '/../database/migration_fix_restore_conflicts.sql';
        
        if (!file_exists($migration_file)) {
            throw new Exception("File migrasi tidak ditemukan: {$migration_file}");
        }
        
        $migration_sql = file_get_contents($migration_file);
        if ($migration_sql === false) {
            throw new Exception("Gagal membaca file migrasi");
        }
        
        // Split the SQL into individual statements
        $statements = explode(';', $migration_sql);
        $executed_count = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            
            // Skip empty statements and comments
            if (empty($statement) || strpos($statement, '--') === 0 || strpos($statement, '/*') === 0) {
                continue;
            }
            
            try {
                $pdo->exec($statement);
                $executed_count++;
            } catch (PDOException $e) {
                // Log the error but continue with other statements
                $errors[] = "Statement error: " . $e->getMessage();
                error_log("Migration statement error: " . $e->getMessage());
            }
        }
        
        // Test the created procedures
        try {
            $test_result = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()")->fetchAll(PDO::FETCH_ASSOC);
            $procedure_count = count($test_result);
            
            $message = "Migrasi berhasil dijalankan! " .
                      "Dieksekusi: {$executed_count} statement. " .
                      "Stored procedures tersedia: {$procedure_count}. ";
            
            if (!empty($errors)) {
                $message .= "Beberapa warning: " . count($errors) . " statement mengalami error minor.";
            }
            
        } catch (Exception $test_error) {
            $message = "Migrasi selesai dengan {$executed_count} statement dieksekusi, namun gagal memverifikasi hasil.";
        }
        
    } catch (Exception $e) {
        $error = "Gagal menjalankan migrasi: " . $e->getMessage();
        error_log("Migration error: " . $e->getMessage());
    }
}

require_once __DIR__ . '/../template/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Migrasi Database</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Tentang Migrasi Database</h6>
                        <p>Script migrasi ini akan membuat stored procedures yang membantu menangani konflik saat restore database:</p>
                        <ul>
                            <li><strong>SafeDropViews:</strong> Menghapus views dengan aman</li>
                            <li><strong>SafeDropTables:</strong> Menghapus tabel dengan aman (untuk emergency)</li>
                            <li><strong>PreRestorationCleanup:</strong> Pembersihan sebelum restore</li>
                            <li><strong>PostRestorationValidation:</strong> Validasi setelah restore</li>
                        </ul>
                    </div>

                    <?php
                    // Check current database status
                    try {
                        $database = new Database();
                        $pdo = $database->getConnection();
                        
                        // Check if procedures already exist
                        $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()")->fetchAll(PDO::FETCH_ASSOC);
                        $procedure_names = array_column($procedures, 'Name');
                        
                        $required_procedures = ['SafeDropViews', 'SafeDropTables', 'PreRestorationCleanup', 'PostRestorationValidation'];
                        $existing_procedures = array_intersect($required_procedures, $procedure_names);
                        $missing_procedures = array_diff($required_procedures, $procedure_names);
                        
                        echo "<div class='card mb-4'>";
                        echo "<div class='card-header'><h6 class='mb-0'>Status Stored Procedures</h6></div>";
                        echo "<div class='card-body'>";
                        
                        if (!empty($existing_procedures)) {
                            echo "<div class='alert alert-success'>";
                            echo "<strong>Procedures yang sudah ada:</strong><br>";
                            echo implode(', ', $existing_procedures);
                            echo "</div>";
                        }
                        
                        if (!empty($missing_procedures)) {
                            echo "<div class='alert alert-warning'>";
                            echo "<strong>Procedures yang belum ada:</strong><br>";
                            echo implode(', ', $missing_procedures);
                            echo "</div>";
                        }
                        
                        if (empty($missing_procedures)) {
                            echo "<div class='alert alert-info'>";
                            echo "<i class='fas fa-check-circle'></i> Semua stored procedures sudah tersedia. ";
                            echo "Anda dapat menjalankan migrasi lagi untuk memperbarui procedures jika diperlukan.";
                            echo "</div>";
                        }
                        
                        echo "</div></div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-warning'>Tidak dapat memeriksa status database: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    ?>

                    <div class="text-center">
                        <form method="post" onsubmit="return confirm('Apakah Anda yakin ingin menjalankan migrasi database? Ini akan membuat/memperbarui stored procedures.')">
                            <button type="submit" name="run_migration" value="1" class="btn btn-primary btn-lg">
                                <i class="fas fa-play"></i> Jalankan Migrasi Database
                            </button>
                        </form>
                    </div>

                    <div class="mt-4">
                        <h6>Cara Penggunaan Setelah Migrasi</h6>
                        <div class="alert alert-secondary">
                            <p>Setelah migrasi berhasil, Anda dapat menggunakan procedures ini secara manual jika diperlukan:</p>
                            <pre><code>-- Sebelum restore database
CALL PreRestorationCleanup();

-- Setelah restore database  
CALL PostRestorationValidation();</code></pre>
                            <p><strong>Catatan:</strong> Proses restore otomatis sudah menggunakan procedures ini.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
