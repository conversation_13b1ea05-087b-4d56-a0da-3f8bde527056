<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../config/database.php';

// Create database connection
$database = new Database();
$conn = $database->getConnection();

// Read SQL file
$sql = file_get_contents(__DIR__ . '/../database/profil_sekolah.sql');

// Make sure the SQL includes the new columns for Wilayah Indonesia API
if (strpos($sql, 'kode_provinsi') === false) {
    $sql .= "
    ALTER TABLE profil_sekolah
    ADD COLUMN kode_provinsi VARCHAR(10) NULL AFTER misi,
    ADD COLUMN kode_kabupaten VARCHAR(10) NULL AFTER kode_provinsi,
    ADD COLUMN kode_kecamatan VARCHAR(10) NULL AFTER kode_kabupaten,
    ADD COLUMN kode_desa VARCHAR(10) NULL AFTER kode_kecamatan;
    ";
}

// Execute SQL
try {
    $conn->exec($sql);
    echo "Tabel profil_sekolah berhasil dibuat!";

    // Check if table is empty
    $query = "SELECT COUNT(*) as count FROM profil_sekolah";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($row['count'] == 0) {
        // Insert default data
        $query = "INSERT INTO profil_sekolah
                (nama_sekolah, npsn, status_sekolah, jenjang_pendidikan,
                alamat_jalan, desa_kelurahan, kecamatan, kabupaten_kota, provinsi,
                kode_pos, no_telepon, email, website,
                nama_kepala_sekolah, nip_kepala_sekolah,
                kode_provinsi, kode_kabupaten, kode_kecamatan, kode_desa)
                VALUES
                ('Nama Sekolah', '12345678', 'Negeri', 'SMA',
                'Jl. Pendidikan No. 123', 'Desa Maju', 'Kecamatan Sejahtera', 'Kota Bahagia', 'Provinsi Makmur',
                '12345', '021-1234567', '<EMAIL>', 'https://www.sekolah.sch.id',
                'Nama Kepala Sekolah', '198001012005011001',
                NULL, NULL, NULL, NULL)";

        // Tambahkan pesan tentang API Wilayah Indonesia
        echo "<br><br><div style='padding: 10px; background-color: #f8f9fa; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
        echo "<h4 style='color: #17a2b8;'>Informasi API Wilayah Indonesia</h4>";
        echo "<p>Modul Profil Sekolah telah diintegrasikan dengan API Wilayah Indonesia untuk memudahkan pemilihan lokasi.</p>";
        echo "<p>Untuk menggunakan fitur ini, silakan kunjungi halaman edit profil sekolah.</p>";
        echo "</div>";
        $conn->exec($query);
        echo "<br>Data default berhasil ditambahkan!";
    }

    echo "<br><br><a href='index.php' class='btn btn-primary'>Kembali ke Profil Sekolah</a>";
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
