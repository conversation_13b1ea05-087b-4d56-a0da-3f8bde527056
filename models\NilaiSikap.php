<?php
require_once __DIR__ . '/../config/database.php';

class NilaiSikap {
    private $conn;
    private $table_name = "nilai_sikap";

    public $id;
    public $siswa_id;
    public $kelas_id;
    public $mapel_id;
    public $semester;
    public $tahun_ajaran;
    public $nilai_spiritual;
    public $deskripsi_spiritual;
    public $nilai_sosial;
    public $deskripsi_sosial;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getAll() {
        $query = "SELECT ns.*, s.nama_siswa as nama_siswa, s.nis, k.nama_kelas, mp.nama_mapel 
                FROM " . $this->table_name . " ns
                JOIN siswa s ON ns.siswa_id = s.id
                JOIN kelas k ON ns.kelas_id = k.id
                LEFT JOIN mata_pelajaran mp ON ns.mapel_id = mp.id
                ORDER BY k.nama_kelas ASC, s.nama_siswa ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                (siswa_id, kelas_id, mapel_id, semester, tahun_ajaran, 
                nilai_spiritual, deskripsi_spiritual, 
                nilai_sosial, deskripsi_sosial)
                VALUES
                (:siswa_id, :kelas_id, :mapel_id, :semester, :tahun_ajaran,
                :nilai_spiritual, :deskripsi_spiritual,
                :nilai_sosial, :deskripsi_sosial)";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':siswa_id', $this->siswa_id);
        $stmt->bindParam(':kelas_id', $this->kelas_id);
        $stmt->bindParam(':mapel_id', $this->mapel_id);
        $stmt->bindParam(':semester', $this->semester);
        $stmt->bindParam(':tahun_ajaran', $this->tahun_ajaran);
        $stmt->bindParam(':nilai_spiritual', $this->nilai_spiritual);
        $stmt->bindParam(':deskripsi_spiritual', $this->deskripsi_spiritual);
        $stmt->bindParam(':nilai_sosial', $this->nilai_sosial);
        $stmt->bindParam(':deskripsi_sosial', $this->deskripsi_sosial);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET nilai_spiritual = :nilai_spiritual,
                    deskripsi_spiritual = :deskripsi_spiritual,
                    nilai_sosial = :nilai_sosial,
                    deskripsi_sosial = :deskripsi_sosial
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':nilai_spiritual', $this->nilai_spiritual);
        $stmt->bindParam(':deskripsi_spiritual', $this->deskripsi_spiritual);
        $stmt->bindParam(':nilai_sosial', $this->nilai_sosial);
        $stmt->bindParam(':deskripsi_sosial', $this->deskripsi_sosial);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }

    public function getById($id) {
        $query = "SELECT ns.*, s.nama_siswa, s.nis, k.nama_kelas, mp.nama_mapel 
                FROM " . $this->table_name . " ns
                JOIN siswa s ON ns.siswa_id = s.id
                JOIN kelas k ON ns.kelas_id = k.id
                LEFT JOIN mata_pelajaran mp ON ns.mapel_id = mp.id
                WHERE ns.id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            foreach ($row as $key => $value) {
                $this->$key = $value;
            }
            return true;
        }
        return false;
    }

    public function getBySiswaAndPeriode($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT ns.*, s.nama_siswa, s.nis, k.nama_kelas, mp.nama_mapel 
                FROM " . $this->table_name . " ns
                JOIN siswa s ON ns.siswa_id = s.id
                JOIN kelas k ON ns.kelas_id = k.id
                LEFT JOIN mata_pelajaran mp ON ns.mapel_id = mp.id
                WHERE ns.siswa_id = :siswa_id 
                AND ns.semester = :semester 
                AND ns.tahun_ajaran = :tahun_ajaran";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByKelas($kelas_id, $semester, $tahun_ajaran, $mapel_id = null) {
        $query = "SELECT ns.*, s.nama_siswa, s.nis, k.nama_kelas, mp.nama_mapel 
                FROM " . $this->table_name . " ns
                JOIN siswa s ON ns.siswa_id = s.id
                JOIN kelas k ON ns.kelas_id = k.id
                LEFT JOIN mata_pelajaran mp ON ns.mapel_id = mp.id
                WHERE ns.kelas_id = :kelas_id 
                AND ns.semester = :semester 
                AND ns.tahun_ajaran = :tahun_ajaran";
        
        if ($mapel_id) {
            $query .= " AND ns.mapel_id = :mapel_id";
        }
        
        $query .= " ORDER BY s.nama_siswa ASC";
                
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        
        if ($mapel_id) {
            $stmt->bindParam(':mapel_id', $mapel_id);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function generateDeskripsiSpiritual($nilai) {
        $deskripsi = '';
        switch ($nilai) {
            case 'A':
                $deskripsi = 'Sangat baik dalam ketaatan beribadah, berperilaku syukur, berdoa sebelum dan sesudah melakukan kegiatan, dan toleransi dalam beribadah';
                break;
            case 'B':
                $deskripsi = 'Baik dalam ketaatan beribadah, berperilaku syukur, berdoa sebelum dan sesudah melakukan kegiatan, dan toleransi dalam beribadah';
                break;
            case 'C':
                $deskripsi = 'Cukup dalam ketaatan beribadah, berperilaku syukur, berdoa sebelum dan sesudah melakukan kegiatan, dan toleransi dalam beribadah';
                break;
            case 'D':
                $deskripsi = 'Kurang dalam ketaatan beribadah, berperilaku syukur, berdoa sebelum dan sesudah melakukan kegiatan, dan toleransi dalam beribadah';
                break;
        }
        return $deskripsi;
    }

    public function generateDeskripsiSosial($nilai) {
        $deskripsi = '';
        switch ($nilai) {
            case 'A':
                $deskripsi = 'Sangat baik dalam berperilaku jujur, disiplin, tanggung jawab, santun, peduli, dan percaya diri dalam berinteraksi dengan keluarga, teman, dan guru';
                break;
            case 'B':
                $deskripsi = 'Baik dalam berperilaku jujur, disiplin, tanggung jawab, santun, peduli, dan percaya diri dalam berinteraksi dengan keluarga, teman, dan guru';
                break;
            case 'C':
                $deskripsi = 'Cukup dalam berperilaku jujur, disiplin, tanggung jawab, santun, peduli, dan percaya diri dalam berinteraksi dengan keluarga, teman, dan guru';
                break;
            case 'D':
                $deskripsi = 'Kurang dalam berperilaku jujur, disiplin, tanggung jawab, santun, peduli, dan percaya diri dalam berinteraksi dengan keluarga, teman, dan guru';
                break;
        }
        return $deskripsi;
    }

    public function checkNilaiExists($siswa_id, $kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT COUNT(*) FROM " . $this->table_name . "
                WHERE siswa_id = :siswa_id 
                AND kelas_id = :kelas_id 
                AND semester = :semester 
                AND tahun_ajaran = :tahun_ajaran";
                
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();
        
        $count = $stmt->fetchColumn();
        return $count > 0;
    }
}
